package com.midea.pam.ctc.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.util.BeanUtils;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.DictDto;
import com.midea.pam.common.basedata.dto.GlPeriodDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.dto.StorageInventoryDto;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.entity.Material;
import com.midea.pam.common.basedata.entity.StorageInventory;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.MaterialGetOrReturnActualAmountDto;
import com.midea.pam.common.ctc.dto.MaterialReturnDetailCheckDto;
import com.midea.pam.common.ctc.dto.MaterialReturnDetailDto;
import com.midea.pam.common.ctc.dto.MaterialReturnDto;
import com.midea.pam.common.ctc.dto.MaterialReturnImportDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetSummaryDto;
import com.midea.pam.common.ctc.dto.TicketTasksDetailDTO;
import com.midea.pam.common.ctc.entity.MaterialGetDetail;
import com.midea.pam.common.ctc.entity.MaterialGetDetailExample;
import com.midea.pam.common.ctc.entity.MaterialGetHeader;
import com.midea.pam.common.ctc.entity.MaterialGetHeaderExample;
import com.midea.pam.common.ctc.entity.MaterialReturnDetail;
import com.midea.pam.common.ctc.entity.MaterialReturnDetailExample;
import com.midea.pam.common.ctc.entity.MaterialReturnHeader;
import com.midea.pam.common.ctc.entity.MaterialReturnHeaderExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectActivity;
import com.midea.pam.common.ctc.entity.ProjectExample;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.entity.TicketTasks;
import com.midea.pam.common.ctc.entity.TicketTasksDetail;
import com.midea.pam.common.ctc.entity.TicketTasksDetailExample;
import com.midea.pam.common.ctc.entity.TicketTasksExample;
import com.midea.pam.common.ctc.excelVo.MaterialReturnDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.MaterialReturnHeaderExcelVo;
import com.midea.pam.common.ctc.vo.MaterialKeepVo;
import com.midea.pam.common.ctc.vo.MaterialReturnDetailExportExcelVo;
import com.midea.pam.common.ctc.vo.MaterialReturnDetailImportExcelVo;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.DictType;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.MaterialGetStatus;
import com.midea.pam.common.enums.MaterialReturnStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.enums.SubBusinessTypeEnums;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.PublicUtil;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.MaterialGetDetailExtMapper;
import com.midea.pam.ctc.mapper.MaterialGetDetailMapper;
import com.midea.pam.ctc.mapper.MaterialGetHeaderMapper;
import com.midea.pam.ctc.mapper.MaterialReturnDetailExtMapper;
import com.midea.pam.ctc.mapper.MaterialReturnDetailMapper;
import com.midea.pam.ctc.mapper.MaterialReturnHeaderExtMapper;
import com.midea.pam.ctc.mapper.MaterialReturnHeaderMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.ProjectActivityExtMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectWbsBudgetSummaryExtMapper;
import com.midea.pam.ctc.mapper.TicketTasksDetailMapper;
import com.midea.pam.ctc.mapper.TicketTasksMapper;
import com.midea.pam.ctc.service.AgencySynService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.MaterialExtService;
import com.midea.pam.ctc.service.MaterialGetService;
import com.midea.pam.ctc.service.MaterialReturnService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectBudgetMaterialService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ResendExecuteService;
import com.midea.pam.ctc.service.TicketTasksService;
import com.midea.pam.ctc.wbs.service.ProjectActivityService;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.midea.pam.common.util.StringUtils.buildGetUrl;

/**
 * <AUTHOR>
 * @Description 退料模块服务
 * @date 2019-5-17
 */
public class MaterialReturnServiceImpl implements MaterialReturnService {

    private static final String RETURNPREFIX = "TL";

    private static Logger logger = LoggerFactory.getLogger(MaterialReturnServiceImpl.class);

    @Resource
    private MaterialReturnHeaderMapper headerMapper;
    @Resource
    private ProjectActivityService projectActivityService;
    @Resource
    private MaterialReturnDetailMapper detailMapper;
    @Resource
    private MaterialReturnDetailExtMapper detailExtMapper;
    @Resource
    private ProjectService projectService;
    @Autowired
    private BasedataExtService basedataExtService;
    @Resource
    ResendExecuteService resendExecuteService;
    @Resource
    private AgencySynService agencySynService;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private ProjectBudgetMaterialService projectBudgetMaterialService;
    @Resource
    private MaterialReturnHeaderExtMapper materialReturnHeaderExtMapper;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;
    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;
    @Resource
    private TicketTasksMapper ticketTasksMapper;
    @Resource
    private TicketTasksDetailMapper ticketTasksDetailMapper;
    @Resource
    private TicketTasksService ticketTasksService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;
    @Resource
    private MaterialExtService materialExtService;
    @Resource
    private Validator validator;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ProjectWbsBudgetSummaryExtMapper projectWbsBudgetSummaryExtMapper;
    @Resource
    private ProjectActivityExtMapper projectActivityExtMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private MaterialGetDetailExtMapper materialGetDetailExtMapper;
    @Resource
    private MaterialGetDetailMapper materialGetDetailMapper;
    @Resource
    private MaterialGetHeaderMapper materialGetHeaderMapper;
    @Resource
    private MaterialGetService materialGetService;


    @Override
    public MaterialReturnDto draft(MaterialReturnDto dto) {
        materialGetService.projectStatusCheck(dto.getProjectId());
        Set<String> forbiddenSubinventoryCodeList = organizationCustomDictService.queryByName(Constants.FORBIDDEN_SUBINVENTORY, dto.getOrganizationId(), OrgCustomDictOrgFrom.INVENTORY_ORG);
        if (forbiddenSubinventoryCodeList.contains(dto.getInventoryCode())) {
            throw new BizException(Code.ERROR, String.format("子库：%s，已禁用", dto.getInventoryCode()));
        }
        MaterialReturnHeader header = null;
        if (dto.getId() != null) {
            header = headerMapper.selectByPrimaryKey(dto.getId());
        }
        if (header == null) {
            header = new MaterialReturnHeader();
            BeanUtils.copyProperties(dto, header, "id", "status", "fillUserId", "fillUserName", "auditingUserId", "auditingUserName",
                    "totalApplyAmount", "totalActualAmount", "totalDifferenceAmount", "applyTime", "erpCode", "erpMsg", "deletedFlag", "createAt",
                    "createBy", "updateAt", "updateBy");
//                header.setReturnCode(RETURNPREFIX + CacheDataUtils.generateSequence(4, RETURNPREFIX));
            header.setReturnCode(generateReturnCode(SystemContext.getUnitId()));
            header.setTotalActualAmount(BigDecimal.ZERO);
            header.setTotalDifferenceAmount(BigDecimal.ZERO);
            header.setFillUserId(dto.getFillUserId());
            header.setFillUserName(dto.getFillUserName());
            header.setCreateAt(new Date());
            header.setCreateBy(SystemContext.getUserId());
            if (null != dto.getTicketTaskId()) header.setTicketTaskId(dto.getTicketTaskId());//工单任务id
            if (StringUtils.isNotBlank(dto.getTicketTaskCode()))
                header.setTicketTaskCode(dto.getTicketTaskCode());//工单任务编号
            if (null != dto.getMaterialGetType()) header.setMaterialGetType(dto.getMaterialGetType());//领料类型
            if (StringUtils.isNotBlank(dto.getTheassemblyDes()))
                header.setTheassemblyDes(dto.getTheassemblyDes());//装配件描述
            if (StringUtils.isNotBlank(dto.getModuleCode())) header.setModuleCode(dto.getModuleCode());//模组编号
            if (StringUtils.isNotBlank(dto.getModuleName())) header.setModuleName(dto.getModuleName());//模组名称
            if (StringUtils.isNotBlank(dto.getTheassemblyCode()))
                header.setTheassemblyCode(dto.getTheassemblyCode());//装配件编号


        } else {
            BeanUtils.copyProperties(dto, header, "id", "status", "fillUserId", "fillUserName", "auditingUserId", "auditingUserName",
                    "totalApplyAmount", "totalActualAmount", "totalDifferenceAmount", "applyTime", "erpCode", "erpMsg", "deletedFlag", "createAt",
                    "createBy", "updateAt", "updateBy");
            header.setUpdateAt(new Date());
            header.setUpdateBy(SystemContext.getUserId());
        }
        header.setDeletedFlag(Boolean.FALSE);
        header.setStatus(MaterialReturnStatus.DRAFT.code());


        if (header.getId() == null) {
            headerMapper.insertSelective(header);
        } else {
            headerMapper.updateByPrimaryKeySelective(header);
        }

        Long id = header.getId();
        MaterialReturnHeader headerCopy = BeanConverter.copy(header, MaterialReturnHeader.class);
        //先删除要删除的明细数据
        if (StringUtils.isNotBlank(dto.getDeleteDetailIds())) {
            Arrays.stream(dto.getDeleteDetailIds().trim().split(",")).forEach(s -> {
                try {
                    detailMapper.deleteByPrimaryKey(Long.parseLong(s.trim()));
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            });
        }

        AtomicReference<BigDecimal> totalApplyAmount = new AtomicReference<>(BigDecimal.ZERO);
        List<MaterialReturnDetail> detailList = Lists.newArrayList();
        dto.getDetails().forEach(materialReturnDetail -> {
            MaterialReturnDetail details = null;
            if (dto.getId() != null) {
                details = detailMapper.selectByPrimaryKey(materialReturnDetail.getId());
            }
            if (details == null) {
                details = new MaterialReturnDetail();
                BeanUtils.copyProperties(materialReturnDetail, details, "id", "actualAmount", "differenceAmount", "headerId", "deletedFlag",
                        "createAt", "createBy", "updateAt", "updateBy");
                details.setHeaderId(id);
                details.setActualAmount(BigDecimal.ZERO);
                details.setCreateAt(new Date());
                details.setCreateBy(SystemContext.getUserId());
            } else {
                if (details.getHeaderId().equals(id)) {
                    BeanUtils.copyProperties(materialReturnDetail, details, "id", "actualAmount", "differenceAmount", "headerId", "deletedFlag",
                            "createAt", "createBy", "updateAt", "updateBy");
                    details.setUpdateAt(new Date());
                    details.setUpdateBy(SystemContext.getUserId());
                }
            }
            if (details.getApplyAmount() == null) {
                details.setApplyAmount(BigDecimal.ZERO);
            }
            // 活动事项校验
            projectActivityService.checkActivityCode(dto.getProjectId(), dto.getProjectCode(),
                    materialReturnDetail.getActivityCode(), materialReturnDetail.getWbsSummaryCode());
            details.setProjectId(dto.getProjectId());
            details.setProjectCode(dto.getProjectCode());
            details.setWbsFullCode(fromatWbsFullCode(dto.getProjectCode(), materialReturnDetail.getWbsSummaryCode()));
            totalApplyAmount.getAndAccumulate(details.getApplyAmount(), BigDecimal::add);
            details.setDifferenceAmount(BigDecimal.ZERO);
            details.setInventoryId(headerCopy.getInventoryId());
            details.setInventoryCode(headerCopy.getInventoryCode());
            details.setInventoryName(headerCopy.getInventoryName());
//                details.setLocationId(headerCopy.getLocationId());
            details.setLocationCode(headerCopy.getLocationCode());
            details.setLocationName(headerCopy.getLocationName());
            details.setDeletedFlag(Boolean.FALSE);
            if (materialReturnDetail.getTicketTaskId() != null)
                details.setTicketTaskId(materialReturnDetail.getTicketTaskId());//工单任务id
            if (StringUtils.isNotBlank(materialReturnDetail.getTicketTaskCode()))
                details.setTicketTaskCode(materialReturnDetail.getTicketTaskCode());//工单任务编号
            if (StringUtils.isNotBlank(materialReturnDetail.getShelves()))
                details.setShelves(materialReturnDetail.getShelves());//货架
            if (com.midea.pam.common.util.StringUtils.isNotEmpty(materialReturnDetail.getTicketTaskIdStr()))
                details.setTicketTaskId(Long.parseLong(materialReturnDetail.getTicketTaskIdStr()));//工单任务id

            if (com.midea.pam.common.util.StringUtils.isNotEmpty(details.getTicketTaskCode()) && null == details.getTicketTaskId()) {
                TicketTasksExample example = new TicketTasksExample();
                example.createCriteria().andTicketTaskCodeEqualTo(details.getTicketTaskCode()).andDeletedFlagEqualTo(Boolean.FALSE);
                List<TicketTasks> list = ticketTasksMapper.selectByExample(example);
                //根据工单编码设置工单id
                if (ListUtils.isNotEmpty(list)) {
                    TicketTasks ticketTasks = list.get(0);
                    TicketTasksDetailExample dettailExample = new TicketTasksDetailExample();
                    dettailExample.createCriteria().andTicketTasksIdEqualTo(ticketTasks.getId()).andErpCodeEqualTo(details.getMaterialCode());
                    List<TicketTasksDetail> ticketTasksDetails = ticketTasksDetailMapper.selectByExample(dettailExample);
                    if (ListUtils.isNotEmpty(ticketTasksDetails)) {
                        for (TicketTasksDetail t : ticketTasksDetails) {
                            if (null != t.getApplyedNumber()) {
                                if (t.getApplyedNumber().compareTo(details.getApplyAmount()) == 1) {
                                    details.setTicketTaskId(t.getId());
                                }
                            }
                        }
                        if (null == details.getTicketTaskId()) {
                            TicketTasksDetail t = ticketTasksDetails.get(0);
                            details.setTicketTaskId(t.getId());
                        }

                    }
                }
            }

            if (details.getId() == null) {
                detailMapper.insertSelective(details);
            } else {
                detailMapper.updateByPrimaryKeySelective(details);
            }
            detailList.add(details);
            if (null != details.getTicketTaskId()) {
                //刷新工单领料
                TicketTasksDetail ticketTasks = ticketTasksDetailMapper.selectByPrimaryKey(details.getTicketTaskId());
                if (null != ticketTasks) {
                    /*if (null != ticketTasks.getMilepostDesignDetailId()) {
                        milepostDesignPlanService.setMRP(ticketTasks.getMilepostDesignDetailId());
                        //工單更新
                        List<MilepostDesignPlanDetailDto> plans = milepostDesignPlanDetailService.getDesignPlanDetail(ticketTasks
                        .getMilepostDesignDetailId());
                        ticketTasksService.createTicketTasksByDesignPlanDetail(plans,null,null, null);
                    }*/
                    materialExtService.updateNumber(ticketTasks);
                }
            }
        });
        header.setTotalApplyAmount(totalApplyAmount.get());
        header.setTotalActualAmount(BigDecimal.ZERO);
        header.setTotalDifferenceAmount(BigDecimal.ZERO);
        headerMapper.updateByPrimaryKeySelective(header);
        MaterialReturnDto returnDto = BeanConverter.copy(header, MaterialReturnDto.class);
        List<MaterialReturnDetailDto> detailsData = BeanConverter.copy(detailList, MaterialReturnDetailDto.class);
        //是否开启超退检查,需要配置查字典值
        checkReturnMaterialNum(detailsData, header);
        returnDto.setDetails(detailsData);
        return returnDto;
    }

    @Override
    public MaterialReturnDto save(MaterialReturnDto dto) {
        MaterialReturnDto returnDto = draft(dto);
        //更新草稿
        MaterialReturnHeader header = new MaterialReturnHeader();
        header.setId(returnDto.getId());
        header.setApplyTime(new Date());
        header.setFillUserName(SystemContext.getUserName());
        header.setFillUserId(SystemContext.getUserId());
        header.setStatus(MaterialReturnStatus.PENDING.code());
        headerMapper.updateByPrimaryKeySelective(header);
        returnDto.setApplyTime(header.getApplyTime());
        returnDto.setFillUserId(header.getFillUserId());
        returnDto.setFillUserName(header.getFillUserName());
        returnDto.setStatus(header.getStatus());
        return returnDto;
    }

    @Override
    public Boolean discard(Long id) {
        // 校验单据状态
        MaterialReturnHeader materialReturnHeader = headerMapper.selectByPrimaryKey(id);
        if (Objects.equals(materialReturnHeader.getStatus(), MaterialReturnStatus.APPROVING.code())
                || (Objects.equals(materialReturnHeader.getStatus(), MaterialReturnStatus.PROCESSED.code())
                && !Objects.equals(materialReturnHeader.getErpCode(), ResponseCodeEnums.FAULT.getCode()))) {
            //状态为已处理的时,如果状态为同步失败时,此时需要可以遗弃
            throw new BizException(Code.ERROR, "退料单当前状态下不能进行此操作!");
        }
        MaterialReturnHeader header = new MaterialReturnHeader();
        header.setId(id);
        header.setKeeperUserId(SystemContext.getUserId());
        header.setKeeperUserName(SystemContext.getUserName());
        header.setUpdateAt(new Date());
        header.setUpdateBy(SystemContext.getUserId());
        header.setStatus(MaterialReturnStatus.DISCARD.code());
        headerMapper.updateByPrimaryKeySelective(header);
        return true;
    }

    @Override
    @Transactional
    public Boolean callBack(Long id, Boolean isSuccess, String msg, String actualCost) {
        //重复同步情况不处理 BUG2023030326971
        if (msg.contains("处理过")) {
            isSuccess = Boolean.TRUE;
            msg = "";
        }
        MaterialReturnDetail backDetail = detailMapper.selectByPrimaryKey(id);
        //无论成功与否都需要遍历全部的数据,判断状态,如果全成功则更新头表的状态数据
        //如果全部失败,则需要根据错误信息判断,头表的同步信息展示
        List<MaterialReturnDetail> details = queryByHeadId(backDetail.getHeaderId());

        logger.info("获取的退料单详情为:{}", JSON.toJSONString(details));
        MaterialReturnHeader header = new MaterialReturnHeader();
        header.setId(backDetail.getHeaderId());
        AtomicInteger size = new AtomicInteger(0);
        if (!isSuccess) {
            if (PublicUtil.isNotEmpty(details)) {
                for (MaterialReturnDetail detail : details) {
                    if (detail.getId().equals(backDetail.getId())) {
                        detail.setErpCode("3");
                        detail.setErpMsg(msg);
                        detailMapper.updateByPrimaryKeySelective(detail);
                    }

                    //记录同步失败的数据大小
                    if (Objects.equals(detail.getErpCode(), "3")) {
                        size.incrementAndGet();
                    }
                }

            }

            header.setErpCode(ResponseCodeEnums.FAULT.getCode());
            if (details.size() == size.get()) {
                Map<String, List<MaterialReturnDetail>> listMap = details.stream().
                        collect(Collectors.groupingBy(MaterialReturnDetail::getErpMsg));
                //判断map集合的key值集合大小
                Set<String> keySet = listMap.keySet();
                int count = keySet.size();
                logger.info("获取退料单明细数据错误信息集合的错误类型的数量为:{},错误信息为:{}", count, JSON.toJSONString(keySet));
                if (count > 1) { //存在多个错误信息
                    header.setErpMsg("单据行存在同步异常信息，详见单据详情行明细");
                } else {
                    header.setErpMsg(msg);
                }


            } else {
                //有一条失败就是失败
                header.setErpMsg(msg);
            }

        } else {

            if (PublicUtil.isNotEmpty(details)) {
                details.forEach(detail -> {
                    if (detail.getId().equals(backDetail.getId())) {
                        detail.setErpCode("2");
                        detail.setErpMsg("");
                        //事务处理价格回传
                        if (!StringUtils.isEmpty(actualCost)) {
                            String[] resultArr = actualCost.split(",");
                            detail.setActualCost(new BigDecimal(resultArr[0]));//成本
                            if (resultArr.length > 1) {
                                detail.setItemCostIsNull(1);//是否来源估价
                            } else {
                                detail.setItemCostIsNull(0);
                            }
                        }
                        detailMapper.updateByPrimaryKeySelective(detail);
                        // 异步回写工单数量/MRP关联数量
                        if (null != detail.getTicketTaskId()) {
                            //刷新工单领料
                            TicketTasksDetail ticketTasks = ticketTasksDetailMapper.selectByPrimaryKey(detail.getTicketTaskId());
                            if (null != ticketTasks) {
                                materialExtService.updateNumber(ticketTasks);
                            }
                        }
                    }
                    if (detail.getErpCode() != null && detail.getErpCode().equals("2")) {
                        size.incrementAndGet();
                    }
                });
            }
            if (details.size() == size.get()) {
                header.setErpCode(ResponseCodeEnums.SUCESS.getCode());
                header.setErpMsg(msg);
            }

            //如果涉及成本转移单的退料单同步成功后,需要生成同步领料的单据
            createMaterialGetEsb(header.getId());
        }
        headerMapper.updateByPrimaryKeySelective(header);
        return true;
    }

    private void createMaterialGetEsb(Long headerId) {
        //查询退料单表
        MaterialReturnHeader returnHeader = headerMapper.selectByPrimaryKey(headerId);
        if (returnHeader != null && StringUtils.isNotEmpty(returnHeader.getCostTransferCode())) {
            //退料单来源于成本转移单生成的单据
            MaterialGetHeaderExample example = new MaterialGetHeaderExample();
            example.createCriteria().andCostTransferCodeEqualTo(returnHeader.getCostTransferCode()).andDeletedFlagEqualTo(0);
            List<MaterialGetHeader> headerList = materialGetHeaderMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(headerList)) {
                MaterialGetHeader materialGetHeader = headerList.get(0);
                logger.info("成本转移单:{}生成退料单:{}同步成功,生成对应的领料单:{}同步单据", returnHeader.getCostTransferCode(), returnHeader.getReturnCode(), materialGetHeader.getGetCode());
                //查询领料单对应的明细信息
                MaterialGetDetailExample detailExample = new MaterialGetDetailExample();
                detailExample.createCriteria().andHeaderIdEqualTo(materialGetHeader.getId()).andDeletedFlagEqualTo(0);
                List<MaterialGetDetail> list = materialGetDetailMapper.selectByExample(detailExample);
                logger.info("成本转移单:{}获取领料单明细数据为:{}", returnHeader.getCostTransferCode(), JSON.toJSONString(list));
                if (!CollectionUtils.isEmpty(list)) {
                    for (MaterialGetDetail detail : list) {
                        if (detail == null)
                            continue;
                        if (detail.getActualAmount().compareTo(BigDecimal.ZERO) <= 0)
                            continue;
                        ResendExecute resendExecute = new ResendExecute();
                        resendExecute.setApplyNo(detail.getHeaderId().toString());
                        resendExecute.setSubApplyNo(detail.getId().toString());
                        resendExecute.setBusinessType(BusinessTypeEnums.GET_MATERIAL.getCode());
                        resendExecute.setSubBusinessType(SubBusinessTypeEnums.GET_MATERIAL.getCode());
                        resendExecute.setBatch(true);
                        HandleDispatcher.route(resendExecute);
                        //刷新工单信息
                        if (null != detail.getTicketTaskId()) {
                            //刷新工单领料
                            TicketTasksDetail ticketTasksDetail = ticketTasksDetailMapper.selectByPrimaryKey(detail.getTicketTaskId());
                            if (null != ticketTasksDetail) {
                                materialExtService.updateNumber(ticketTasksDetail);
                            }
                        }
                    }
                }

                //修改同步状态
                MaterialGetHeader updateGetHeader = new MaterialGetHeader();
                updateGetHeader.setId(materialGetHeader.getId());
                updateGetHeader.setErpCode("3");//同步状态
                updateGetHeader.setUpdateBy(SystemContext.getUserId());
                updateGetHeader.setUpdateAt(new Date());
                //updateGetHeader.setKeeperUserId(SystemContext.getUserId());//账户批处理时添加字段
                //updateGetHeader.setKeeperUserName(SystemContext.getUserName());//账户批处理时添加字段
                materialGetHeaderMapper.updateByPrimaryKeySelective(updateGetHeader);
            }
        }


    }

    @Override
    public Integer addPrintCount(Long id) {
        MaterialReturnHeader header = headerMapper.selectByPrimaryKey(id);
        if (null != header && header.getStatus().equals(MaterialReturnStatus.PENDING.code())) {
            MaterialReturnHeader headerUP = new MaterialReturnHeader();
            headerUP.setId(id);
            headerUP.setPrintCount(header.getPrintCount() == null ? 1 : header.getPrintCount() + 1);
            headerMapper.updateByPrimaryKeySelective(headerUP);
            return headerUP.getPrintCount();
        }
        logger.error(SystemContext.getUserName() + "非法打单：" + SystemContext.getUserId());
        return 0;
    }

    /**
     * 递归获取开放的会计期间
     * zhuym13
     *
     * @param applicationId 分类详见GlPeriod
     * @param ledgerId      分类详见GlPeriod
     * @param localDate     需要查询的日期
     * @param reTry         递归次数
     * @return GlPeriodDto 开放的会计期间
     */
    private GlPeriodDto buildReturnAt(Long applicationId, Long ledgerId, LocalDate localDate, int reTry) {
        GlPeriodDto dto = CacheDataUtils.getOpenOrgGlPeriod(ledgerId, localDate, 6);
        return dto;
    }


    @Override
    public Boolean processed(Long id, List<MaterialReturnDetailDto> details) {
        MaterialReturnHeader header = null;
        header = headerMapper.selectByPrimaryKey(id);
        if (null != header) {
            //如果单据状态为已处理，且同步状态不为同步失败，无需处理
            if (Objects.equals(header.getStatus(), MaterialGetStatus.PROCESSED.code()) && !Objects.equals(header.getErpCode(),
                    ResponseCodeEnums.FAULT.getCode())) {
                return true;
            }
            MaterialReturnDetailExample example = new MaterialReturnDetailExample();
            example.createCriteria().andHeaderIdEqualTo(id);
            List<MaterialReturnDetail> dbDetails = detailMapper.selectByExample(example);

            Map<Long, MaterialReturnDetailDto> returnDetailMap = details.stream().collect(Collectors.toMap(MaterialReturnDetailDto::getId,
                    Function.identity()));

            if (PublicUtil.isNotEmpty(dbDetails)) {
                AtomicInteger size = new AtomicInteger(0);
                AtomicReference<BigDecimal> totalActualAmount = new AtomicReference<>(BigDecimal.ZERO);
                for (MaterialReturnDetail dbDetail : dbDetails) {
                    MaterialReturnDetail detail = BeanConverter.copy(returnDetailMap.get(dbDetail.getId()), MaterialReturnDetail.class);
                    dbDetail.setUpdateBy(SystemContext.getUserId());
                    dbDetail.setUpdateAt(new Date());
                    dbDetail.setActualAmount(detail.getActualAmount());
                    dbDetail.setDifferenceAmount(detail.getActualAmount().subtract(detail.getApplyAmount()));
                    //实际退料日期
                    if (detail.getActualReturnAt() == null) { //账务批处理操作页面无法填日期
                        //从缓存取最近打开的会计日期
                        Date glDate = this.getGlPeriod(header.getOrganizationId());
                        if (glDate != null) {
                            dbDetail.setActualReturnAt(glDate.after(new Date()) ? new Date() : glDate);
                        }
                    } else {
                        //如果是成本转移单的单据,则不检查,因为检查会导致单据无法审核通过
                        //成本转移单的单据一定会有关联成本转移单编号
                        if (StringUtils.isEmpty(header.getCostTransferCode())) {
                            this.checkGlPeriod(detail, header.getOrganizationId());
                        } else if (StringUtils.isNotEmpty((header.getCostTransferCode())) && Objects.equals(header.getErpCode(),
                                ResponseCodeEnums.FAULT.getCode())) { //是转移单生成的领料单,但是是同步失败的状态,此时也需要做校验
                            this.checkGlPeriod(detail, header.getOrganizationId());
                        }
                        dbDetail.setActualReturnAt(detail.getActualReturnAt());
                    }
                    dbDetail.setRemark(detail.getRemark());
                    dbDetail.setCollectionDate(DateUtil.getCurrentDate());//记录处理的系统日期，用于成本归集
                    detailMapper.updateByPrimaryKeySelective(dbDetail);
                    //如果实际处理数量等于0时，则此行数据不推送接口
                    if (dbDetail != null && dbDetail.getActualAmount().compareTo(BigDecimal.ZERO) > 0) {
                        //已同步成功的退料明细行不再重新同步
                        if (Objects.equals(detail.getErpCode(), "2")) {
                            continue;
                        }
                        ResendExecute resendExecute = new ResendExecute();
                        resendExecute.setApplyNo(dbDetail.getHeaderId().toString());
                        resendExecute.setSubApplyNo(dbDetail.getId().toString());
                        resendExecute.setBusinessType(BusinessTypeEnums.RETURN_MATERIAL.getCode());
                        resendExecute.setSubBusinessType(SubBusinessTypeEnums.RETURN_MATERIAL.getCode());
                        resendExecute.setBatch(true);
                        HandleDispatcher.route(resendExecute);
                    }
                    totalActualAmount.getAndAccumulate(dbDetail.getActualAmount(), BigDecimal::add);
                    size.incrementAndGet();
                }
                if (dbDetails.size() == size.get()) {
                    header.setStatus(MaterialReturnStatus.PROCESSED.code());
                }
                if (details.stream().allMatch(detail -> detail.getActualAmount().compareTo(BigDecimal.ZERO) == 0)) {
                    header.setErpCode(ResponseCodeEnums.SUCESS.getCode());
                }
                header.setKeeperUserId(SystemContext.getUserId());
                header.setKeeperUserName(SystemContext.getUserName());
                header.setUpdateBy(SystemContext.getUserId());
                header.setUpdateAt(new Date());
                header.setTotalActualAmount(totalActualAmount.get());
                header.setTotalDifferenceAmount(header.getTotalActualAmount().subtract(header.getTotalApplyAmount()));
                if (MaterialReturnStatus.PROCESSED.code().equals(header.getStatus()) &&
                        //同步状态为空或者同步状态为失败的,改为同步中
                        (StringUtils.isEmpty(header.getErpCode()) || Objects.equals(header.getErpCode(), ResponseCodeEnums.FAULT.getCode()))) {
                    header.setErpCode("3");// 更新领料表的状态 为同步中  ERP同步状态(000000-已同步,999999-同步失败,3-同步中,-1-未同步,null-未同步)
                }
                headerMapper.updateByPrimaryKeySelective(header);
            }

        } else {
            logger.error("没有这个退料单：" + id);
            return false;
        }
        return true;
    }

    @Override
    public Boolean batchProcessed(List<Long> ids) {
        if (PublicUtil.isNotEmpty(ids)) {
            ids.forEach(id -> {
                MaterialReturnDetailExample example = new MaterialReturnDetailExample();
                example.createCriteria().andHeaderIdEqualTo(id);
                List<MaterialReturnDetail> dbDetails = detailMapper.selectByExample(example);
                if (PublicUtil.isNotEmpty(dbDetails)) {
                    dbDetails.forEach(dbDetail -> {
                        dbDetail.setActualAmount(dbDetail.getApplyAmount());
                    });
                    List<MaterialReturnDetailDto> detailsData = BeanConverter.copy(dbDetails, MaterialReturnDetailDto.class);
                    processed(id, detailsData);
                }
            });
        } else {
            return false;
        }
        return true;
    }

    @Override
    public MaterialReturnDto queryById(Long id) {
        MaterialReturnDto dto = new MaterialReturnDto();
        if (id != null) {
            MaterialReturnHeader header = materialReturnHeaderExtMapper.selectByPrimaryKey(id);
            BeanUtils.copyProperties(header, dto);
            //获取最近的gl打开日期
            GlPeriodDto periodDto = CacheDataUtils.getOpenOrgGlPeriod(header.getOrganizationId(), LocalDate.now(), 12);
            if (null != periodDto) {
                dto.setActualReturnAtBegin(periodDto.getStartDate());
                dto.setActualReturnAtEnd(periodDto.getEndDate());
            }
            //获取项目经理 & 项目业务分类
            if (header.getProjectId() != null) {
                Project project = projectMapper.selectByPrimaryKey(header.getProjectId());
                if (project != null) {
                    dto.setManagerName(project.getManagerName());
                    dto.setProjectUnitName(CacheDataUtils.findUnitNameById(project.getUnitId()));
                }
            }
            //获取明细信息
            MaterialReturnDetailExample detailsExample = new MaterialReturnDetailExample();
            detailsExample.setOrderByClause("material_code asc");
            detailsExample.createCriteria().andHeaderIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE);
            List<MaterialReturnDetail> details = detailMapper.selectByExample(detailsExample);
            List<MaterialReturnDetailDto> detailsData = BeanConverter.copy(details, MaterialReturnDetailDto.class);
            if(ListUtils.isNotEmpty(detailsData)){
                List<MaterialDto> materialDtoList = basedataExtService.getMaterialListByMaterialIds(detailsData.stream().map(MaterialReturnDetail::getMaterialId).filter(Objects::nonNull).collect(Collectors.toList()));
                if(ListUtils.isNotEmpty(materialDtoList)){
                    Map<Long, MaterialDto> materialMap = materialDtoList.stream()
                            .collect(Collectors.toMap(
                                    MaterialDto::getId,
                                    Function.identity(),
                                    (existing, replacement) -> replacement // 冲突时保留最后一个
                            ));

                    detailsData.forEach(detail -> {
                        Long materialId = detail.getMaterialId();
                        if (materialId != null) { // 避免 NPE
                            MaterialDto materialDto = materialMap.get(materialId);
                            if (materialDto != null) {
                                detail.setShelves(materialDto.getShelves());
                            }
                        }
                    });
                }
            }
            checkReturnMaterialNum(detailsData, header);
            dto.setDetails(detailsData);

        }
        return dto;
    }

    /**
     * 移动审批时SystemContext.getUnitId()无法获取值,不做超退
     *
     * @return
     */
    private MaterialReturnDto queryByIdNoCt(Long id) {
        MaterialReturnDto dto = new MaterialReturnDto();
        if (id != null) {
            MaterialReturnHeader header = materialReturnHeaderExtMapper.selectByPrimaryKey(id);
            BeanUtils.copyProperties(header, dto);
            //获取最近的gl打开日期
            GlPeriodDto periodDto = CacheDataUtils.getOpenOrgGlPeriod(header.getOrganizationId(), LocalDate.now(), 12);
            if (null != periodDto) {
                dto.setActualReturnAtBegin(periodDto.getStartDate());
                dto.setActualReturnAtEnd(periodDto.getEndDate());
            }
            //获取项目经理
            if (header.getProjectId() != null) {
                Project project = projectMapper.selectByPrimaryKey(header.getProjectId());
                if (project != null) dto.setManagerName(project.getManagerName());
            }
            //获取明细信息
            MaterialReturnDetailExample detailsExample = new MaterialReturnDetailExample();
            detailsExample.setOrderByClause("material_code asc");
            detailsExample.createCriteria().andHeaderIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE);
            List<MaterialReturnDetail> details = detailMapper.selectByExample(detailsExample);
            List<MaterialReturnDetailDto> detailsData = BeanConverter.copy(details, MaterialReturnDetailDto.class);
            dto.setDetails(detailsData);
        }
        return dto;
    }

    @Override
    public void sync(String businessType, Long applyNo) {
        MaterialReturnHeader header = headerMapper.selectByPrimaryKey(applyNo);
        if (Objects.equals(header.getErpCode(), ResponseCodeEnums.IN_SYNC.getCode())
                || Objects.equals(header.getErpCode(), ResponseCodeEnums.SUCESS.getCode())) {
            throw new BizException(Code.ERROR, "同步中或已同步的退料单不能进行此操作!");
        }
        header.setErpCode(ResponseCodeEnums.IN_SYNC.getCode());
        header.setErpMsg(null);
        MaterialReturnDetailExample example = new MaterialReturnDetailExample();
        example.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andHeaderIdEqualTo(applyNo);
        List<MaterialReturnDetail> details = detailMapper.selectByExample(example);
        if (ListUtils.isNotEmpty(details)) {
            for (MaterialReturnDetail detail : details) {
                //已同步成功的退料明细行不再重新同步
                if (Objects.equals(detail.getErpCode(), "2")) {
                    continue;
                }
                ResendExecute resendExecute = new ResendExecute();
                resendExecute.setApplyNo(applyNo.toString());
                resendExecute.setSubApplyNo(detail.getId().toString());
                resendExecute.setBusinessType(businessType);
                resendExecute.setSubBusinessType(SubBusinessTypeEnums.RETURN_MATERIAL.getCode());
                resendExecute.setBatch(true);
                HandleDispatcher.route(resendExecute);
            }
        }
        headerMapper.updateByPrimaryKey(header);
        agencySynService.syndataToErp(businessType, applyNo);
    }

    @Override
    public List<MaterialReturnDetail> queryByHeadId(Long id) {
        MaterialReturnDetailExample detailsExample = new MaterialReturnDetailExample();
        detailsExample.createCriteria()
                .andHeaderIdEqualTo(id)
                .andDeletedFlagEqualTo(Boolean.FALSE)
                .andActualAmountGreaterThan(BigDecimal.ZERO);
        return detailMapper.selectByExample(detailsExample);
    }

    @Override
    public PageInfo<MaterialReturnDto> query(String returnCode, String projectCode, String returnUserName, String fillUserName,
                                             Long organizationId, String applyTimeMin, String applyTimeMax, Boolean isDifference, Integer status,
                                             Integer page, Integer pageSize) {

        MaterialReturnHeaderExample example = new MaterialReturnHeaderExample();
        MaterialReturnHeaderExample.Criteria criteria = example.createCriteria();

        if (PublicUtil.isNotEmpty(returnCode)) {
            criteria.andReturnCodeLike(com.midea.pam.common.util.StringUtils.buildSqlLikeCondition(returnCode));
        }
        if (PublicUtil.isNotEmpty(projectCode)) {
            criteria.andProjectCodeLike(com.midea.pam.common.util.StringUtils.buildSqlLikeCondition(projectCode));
        }
        if (PublicUtil.isNotEmpty(returnUserName)) {
            criteria.andReturnUserNameLike(com.midea.pam.common.util.StringUtils.buildSqlLikeCondition(returnUserName));
        }
        if (PublicUtil.isNotEmpty(fillUserName)) {
            criteria.andFillUserNameLike(com.midea.pam.common.util.StringUtils.buildSqlLikeCondition(fillUserName));
        }
        if (null != organizationId) {
            criteria.andOrganizationIdEqualTo(organizationId);
        }
        if (PublicUtil.isNotEmpty(applyTimeMin) && PublicUtil.isNotEmpty(applyTimeMax)) {
            criteria.andApplyTimeBetween(DateUtil.parseDate(applyTimeMin, DateUtil.TIMESTAMP_PATTERN), DateUtil.parseDate(applyTimeMax,
                    DateUtil.TIMESTAMP_PATTERN));
        }
        if (null != status) {
            criteria.andStatusEqualTo(status);
        }
        if (null != isDifference) {
            criteria.andTotalDifferenceAmountIsNotNull();
            if (isDifference) {
                criteria.andTotalDifferenceAmountNotEqualTo(BigDecimal.ZERO);
            }

        }
        example.setOrderByClause("create_at desc");

        PageHelper.startPage(page, pageSize);
        PageInfo<MaterialReturnDto> pageInfo = BeanConverter.convertPage(headerMapper.selectByExample(example), MaterialReturnDto.class);
        return pageInfo;

    }

    @Override
    public BigDecimal getReturnQuantity(Long projectId, String materialCode) {
        Asserts.notEmpty(projectId, ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(materialCode, ErrorCode.CTC_ITEM_CODE_NOT_NULL);

        BigDecimal sum = new BigDecimal(0);

        /*MaterialReturnHeaderExample returnHeaderExample = new MaterialReturnHeaderExample();
        MaterialReturnHeaderExample.Criteria returnHeaderCriteria = returnHeaderExample.createCriteria();
        returnHeaderCriteria.andDeletedFlagEqualTo(false);
        returnHeaderCriteria.andStatusEqualTo(MaterialReturnStatus.PROCESSED.code());
        returnHeaderCriteria.andProjectIdEqualTo(projectId);
        List<MaterialReturnHeader> materialReturnHeaders = headerMapper.selectByExample(returnHeaderExample);

        if (ListUtils.isNotEmpty(materialReturnHeaders)) {
            List<Long> headerIds = ListUtil.map(materialReturnHeaders, "id");
            MaterialReturnDetailExample returnDetailExample = new MaterialReturnDetailExample();
            MaterialReturnDetailExample.Criteria returnDetailCriteria = returnDetailExample.createCriteria();
            returnDetailCriteria.andDeletedFlagEqualTo(false);
            returnDetailCriteria.andMaterialCodeEqualTo(materialCode);
            returnDetailCriteria.andHeaderIdIn(headerIds);
            List<MaterialReturnDetail> materialReturnDetails = detailMapper.selectByExample(returnDetailExample);
            for (MaterialReturnDetail materialReturnDetail : materialReturnDetails) {
                sum = sum.add(materialReturnDetail.getActualAmount());
            }
        }*/
        List<MaterialReturnDetail> materialReturnDetails = materialReturnHeaderExtMapper.getMaterialGetDetailByProjecAndMaterialCode(projectId,
                materialCode);
        for (MaterialReturnDetail materialReturnDetail : materialReturnDetails) {
            sum = sum.add(materialReturnDetail.getActualAmount());
        }
        return sum;
    }

    @Override
    public ResponseMap getMaterialReturnApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<Map<String, String>> detailMapList = new ArrayList<>();
        MaterialReturnDto dto = queryByIdNoCt(id);
        if (ObjectUtils.isEmpty(dto)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("头信息不能为空");
        }
        Asserts.notEmpty(dto, ErrorCode.BASEDATA_MATERIAL_NOT_FIND);
        headMap.put("returnCode", dto.getReturnCode()); //退料单编号
        headMap.put("projectName", dto.getProjectName()); // 退料项目名称
        headMap.put("projectCode", dto.getProjectCode());// 退料项目编号
        headMap.put("inventoryName", dto.getInventoryName()); // 退料仓库名称
        headMap.put("totalApplyAmount", dto.getTotalApplyAmount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()); // 申请总量
        headMap.put("remark", dto.getRemark()); // 备注
        headMap.put("reason", dto.getReason()); //退料原因
        headMap.put("returnUserName", dto.getReturnUserName()); // 退料人
        headMap.put("fillUserName", dto.getFillUserName()); //制单人
        headMap.put("organizationName", dto.getOrganizationName()); // 库存组织
        responseMap.setHeadMap(headMap);
        //附件
        List<CtcAttachmentDto> attachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(id, CtcAttachmentModule.MATERIAL_GET_ITEM.code(), null);
        if (!CollectionUtils.isEmpty(attachmentDtos)) {
            CtcAttachmentDto ctcAttachmentDto = attachmentDtos.get(0);
            List<AduitAtta> fileList = new ArrayList<>();
            AduitAtta aduitAtta = new AduitAtta();
            aduitAtta.setFdId(ctcAttachmentDto.getAttachId().toString());
            aduitAtta.setFileName(ctcAttachmentDto.getFileName());
            aduitAtta.setFileSize(ctcAttachmentDto.getFileSize().toString());
            fileList.add(aduitAtta);
            responseMap.setFileList(fileList);
        }
        responseMap.setStatus("success");
        responseMap.setMsg("success");
        return responseMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long submit(Long id, Long fillUserId, String fileId, String fileName, String fileSize) {
        Guard.notNull(id, "退料单ID不能为空");

        MaterialReturnHeader header = headerMapper.selectByPrimaryKey(id);
        Guard.notNull(header, String.format("退料单ID：%s不存在", id));

        //提交审批，补充制单人信息
        UserInfo userInfo = CacheDataUtils.findUserById(fillUserId);
        if (userInfo != null) {
            header.setFillUserId(fillUserId);
            header.setFillUserName(userInfo.getName());
        }

        header.setApplyTime(new Date());//申请时间
        header.setStatus(MaterialReturnStatus.APPROVING.code());//待审批
        headerMapper.updateByPrimaryKeySelective(header);
        //保存退料明细附件
        if (StringUtils.isNotBlank(fileId) && StringUtils.isNotBlank(fileName) && StringUtils.isNotBlank(fileSize)) {
            Long currentUserId = SystemContext.getUserId();
            CtcAttachmentDto dto = new CtcAttachmentDto();
            dto.setModule(CtcAttachmentModule.MATERIAL_GET_ITEM.code());
            dto.setDeletedFlag(false);
            dto.setModuleId(id);
            dto.setAttachId(Long.valueOf(fileId));
            dto.setFileName(fileName);
            dto.setFileSize(Long.valueOf(fileSize).intValue());
            dto.setUpdateAt(new Date());
            dto.setUpdateBy(currentUserId);
            dto.setCreateAt(new Date());
            dto.setCreateBy(currentUserId);
            dto.setStatus(1);
            ctcAttachmentService.add(dto);
        }

        return id;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long pass(Long id, Long auditingUserId) {
        return this.approve(id, auditingUserId, MaterialReturnStatus.PENDING.code());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long reject(Long id, Long auditingUserId) {
        return this.approve(id, auditingUserId, MaterialReturnStatus.REJECT.code());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long abandon(Long id, Long auditingUserId) {
        return this.approve(id, auditingUserId, MaterialReturnStatus.DISCARD.code());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long draftReturn(Long formInstanceId, Long handlerUserId) {
        return this.approve(formInstanceId, handlerUserId, MaterialReturnStatus.DRAFT.code());
    }

    @Override
    public DataResponse<Object> importMaTerialReturnDetail(List<MaterialReturnDetailImportExcelVo> materialReturnDetailImportExcelVo) {
        DataResponse<Object> response = Response.dataResponse();
        if (ListUtils.isEmpty(materialReturnDetailImportExcelVo)) {
            return response;
        }

        MaterialReturnDetailImportExcelVo excelVo = materialReturnDetailImportExcelVo.get(0);
        Long projectId = excelVo.getProjectId();
        String projectCode = excelVo.getProjectCode();
        Long organizationId = excelVo.getOrganizationId();
        Long ticketTasksId = excelVo.getTicketTasksId();
        Boolean keep = excelVo.getKeep();
        List<MaterialReturnImportDto> materialReturnDetails = BeanConverter.copy(materialReturnDetailImportExcelVo, MaterialReturnImportDto.class);
        materialReturnDetails.forEach(m -> {
            String materialCode = m.getMaterialCode();
            if (StringUtils.isBlank(materialCode)) {
                response.setCode(1);
                response.setMsg(String.format("物料编码不能为空"));
                return;
            }
            DataResponse<Object> dataResponse = checkMaterialReturnDetail(materialCode, projectId, projectCode, ticketTasksId, keep);
            if (dataResponse.getCode() == 1) {
                response.setCode(1);
                response.setMsg(dataResponse.getMsg());
                return;
            }
            List<Material> material = materialReturnHeaderExtMapper.getMaterial(materialCode, organizationId);
            if (ListUtils.isEmpty(material)) {
                response.setCode(1);
                response.setMsg(String.format("物料编码：%s，库存组织ID：%s对应的物料不存在", materialCode, organizationId));
                return;
            }
            m.setItemInfo(material.get(0).getItemInfo());
            m.setUnit(material.get(0).getUnit());
            m.setMaterialId(material.get(0).getId());
            m.setShelves(material.get(0).getShelves());
            response.setCode(0);
            response.setMsg("SUCCESS!");
        });

        for (int i = 0; i < materialReturnDetails.size(); i++) {
            materialReturnDetails.get(i).setNum(i + 3);
        }
        //唯一性条件：erpCode
        Map<String, List<MaterialReturnImportDto>> repeatMap = materialReturnDetails.stream().collect(Collectors.groupingBy(MaterialReturnImportDto::getMaterialCode));
        for (String key : repeatMap.keySet()) {
            List<MaterialReturnImportDto> repeatList = repeatMap.get(key);
            if (repeatList != null && repeatList.size() > 1) {
                String nums = repeatList.stream().map(s -> "行：" + s.getNum()).collect(Collectors.joining("、"));
                response.setCode(1);
                response.setMsg(String.format("%s重复，请合并填写", nums));
                break;
            }
        }
        response.setData(materialReturnDetails);
        return response;
    }

    @Override
    public Map<String, Object> exportMaTerialReturnErrorDetail(MaterialReturnDetailImportExcelVo importExcelVos) {
        Map<String, Object> resultMap = new HashMap<>();
        if (null != importExcelVos) {
            List<MaterialReturnDetailExportExcelVo> materialReturnDetails = BeanConverter.copy(importExcelVos.getImportExcelVos(),
                    MaterialReturnDetailExportExcelVo.class);
            materialReturnDetails.forEach(m -> {
                MilepostDesignPlanDetailExample milepostDesignPlanDetail = new MilepostDesignPlanDetailExample();
                milepostDesignPlanDetail.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(importExcelVos.getImportExcelVos().get(0).getProjectId()).andErpCodeEqualTo(m.getMaterialCode());
                List<MilepostDesignPlanDetail> milepostDesignPlanDetails = milepostDesignPlanDetailMapper.selectByExample(milepostDesignPlanDetail);
                if (ListUtils.isEmpty(milepostDesignPlanDetails)) {
                    m.setErrorReason("物料编码在项目" + importExcelVos.getImportExcelVos().get(0).getProjectCode() + "中不存在");
                }
                if (importExcelVos.getKeep() != null && importExcelVos.getKeep()) {
                    List<MilepostDesignPlanDetailDto> milepostPlanDesignByTicketTasks =
                            milepostDesignPlanService.getMilepostPlanDesignByTicketTasksCode(importExcelVos.getTicketTasksId());
                    if (ListUtils.isEmpty(milepostPlanDesignByTicketTasks) || !milepostPlanDesignByTicketTasks.contains(m.getMaterialCode())) {
                        m.setErrorReason("物料编码在工单" + importExcelVos.getTicketTasksId() + "中不存在");
                    }
                }

            });
            if (ListUtils.isNotEmpty(materialReturnDetails)) {
                resultMap.put("materialReturnDetailList", materialReturnDetails);
            } else {
                resultMap.put("materialReturnDetailList", com.google.common.collect.Lists.newArrayList());
            }
        }
        return resultMap;

    }

    @Override
    public DataResponse<Object> checkMaterialIsKeep(MaterialKeepVo materialKeepVo) {
        DataResponse<Object> response = Response.dataResponse();
        if (null != materialKeepVo) {
            materialKeepVo.getMaterials().forEach(m -> {
                List<TicketTasksDetailDTO> ticketTasksDetailByTicketTasks =
                        ticketTasksService.getTicketTasksDetailByTicketTasksId(materialKeepVo.getTicketTasksId(),
                                materialKeepVo.getOrganizationId(), materialKeepVo.getSubinventoryCode(), null);
                //List<MilepostDesignPlanDetailDto> milepostPlanDesignByTicketTasks = milepostDesignPlanService
                // .getMilepostPlanDesignByTicketTasksCode(materialKeepVo.getTicketTasksId());
                if (ticketTasksDetailByTicketTasks
                        .stream()
                        .noneMatch(s -> s.getPamCode().equals(m.getPamCode()))) {
                    response.setCode(1);
                    response.setMsg(m.getItemCode() + "不存在工单中！");
                    response.setData(false);
                } else {
                    response.setCode(0);
                    response.setMsg("SUCCESS!");
                    response.setData(true);
                }
            });
        }
        return response;
    }

    private DataResponse<Object> checkMaterialReturnDetail(String materialCode, Long projectId, String projectCode, Long ticketTasksId,
                                                           Boolean keep) {
        DataResponse<Object> response = Response.dataResponse();
        MilepostDesignPlanDetailExample milepostDesignPlanDetail = new MilepostDesignPlanDetailExample();
        milepostDesignPlanDetail.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(projectId).andErpCodeEqualTo(materialCode);
        List<MilepostDesignPlanDetail> milepostDesignPlanDetails = milepostDesignPlanDetailMapper.selectByExample(milepostDesignPlanDetail);
        if (ListUtils.isNotEmpty(milepostDesignPlanDetails)) {
            if (keep) {
                List<MilepostDesignPlanDetailDto> milepostPlanDesignByTicketTasks =
                        milepostDesignPlanService.getMilepostPlanDesignByTicketTasksCode(ticketTasksId);
                if (ListUtils.isNotEmpty(milepostPlanDesignByTicketTasks)
                        && milepostPlanDesignByTicketTasks.stream().anyMatch(e -> Objects.equals(e.getErpCode(), materialCode))) {
                    response.setCode(0);
                } else {
                    response.setCode(1);
                    response.setMsg(String.format("物料编码：%s，项目编码：%s，工单ID：%s对应的详细设计数据不存在", materialCode, projectCode, ticketTasksId));
                }
            } else {
                response.setCode(0);
            }

        } else {
            response.setCode(1);
            response.setMsg(String.format("物料编码：%s，项目编码：%s对应的详细设计数据不存在", materialCode, projectCode));
        }
        return response;
    }

    private void checkGlPeriod(MaterialReturnDetail detail, Long organizationId) {
        if (detail.getActualReturnAt() != null) {
            String yearMonth = DateUtils.format(detail.getActualReturnAt(), "yyyy-MM");
            GlPeriodDto glPeriodDto = CacheDataUtils.findCacheOrgPeriod(organizationId, yearMonth);
            Guard.isFalse(ObjectUtils.isEmpty(glPeriodDto) || !GlPeriodDto.OPEN_STATUS.equals(glPeriodDto.getShowStatus()), "处理日期不在打开的会计期间内");
        }
    }

    private Date getGlPeriod(Long organizationId) {
        GlPeriodDto glPeriodDto = CacheDataUtils.getOpenOrgGlPeriod(organizationId, LocalDate.now(), 6);
        if (null == glPeriodDto) {
            throw new BizException(Code.ERROR, "无打开的会计期间!");
        } else {
            return glPeriodDto.getEndDate();
        }
    }

    /**
     * 退料单编号规则： 代码公司段(美云:M，机器人:R)+TL+YY+MM+DD+3位流水码
     *
     * @return
     */
    @Override
    public String generateReturnCode(Long unitId) {
        StringBuffer returnCode = new StringBuffer();
        String seqPerfix = basedataExtService.getUnitSeqPerfix(unitId);
        returnCode.append(seqPerfix);
        returnCode.append(RETURNPREFIX);
        returnCode.append(CacheDataUtils.generateSequence(Constants.MATERIAL_RETURN_CODE_LENGTH, seqPerfix + RETURNPREFIX,
                DateUtil.DATE_YYMMDD_PATTERN));
        return returnCode.toString();
    }

    @Transactional(rollbackFor = Exception.class)
    public Long approve(Long id, Long auditingUserId, Integer status) {
        Guard.notNull(id, "退料单ID不能为空");

        MaterialReturnHeader header = headerMapper.selectByPrimaryKey(id);
        Guard.notNull(header, String.format("退料单ID：%s不存在", id));

        //审批回调，补充审批人信息
        UserInfo userInfo = CacheDataUtils.findUserById(auditingUserId);
        if (userInfo != null) {
            header.setAuditingUserId(auditingUserId);
            header.setAuditingUserName(userInfo.getName());
        }

        header.setStatus(status);//通过 或 驳回
        headerMapper.updateByPrimaryKeySelective(header);

        List<MaterialReturnDetail> list = queryByHeadId(id);
        if (ListUtils.isNotEmpty(list)) {
            for (MaterialReturnDetail details : list) {
                if (null != details.getTicketTaskId()) {
                    //刷新工单领料
                    TicketTasksDetail ticketTasks = ticketTasksDetailMapper.selectByPrimaryKey(details.getTicketTaskId());
                    if (null != ticketTasks) {
                        materialExtService.updateNumber(ticketTasks);
                    }
                }
            }
        }

        return id;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<MaterialReturnHeaderExcelVo> importMaterialReturnHeader(List<MaterialReturnHeaderExcelVo> excelVoList) {

        boolean isPass = validMaterialReturnHeaderExcelVo(excelVoList);

        if (isPass) {
            List<List<MaterialReturnHeader>> materialReturnHeaderNewLists = new ArrayList<>(100);
            List<MaterialReturnHeader> materialReturnHeaderNewList = new ArrayList<>(500);
            for (MaterialReturnHeaderExcelVo vo : excelVoList) {
                MaterialReturnHeader materialReturnHeader = BeanConverter.copy(vo, MaterialReturnHeader.class);
                materialReturnHeader.setIsImport(1);
                materialReturnHeader.setStatus(5);
                materialReturnHeader.setTotalActualAmount(materialReturnHeader.getTotalApplyAmount());
                materialReturnHeader.setTotalDifferenceAmount(BigDecimal.ZERO);
                materialReturnHeader.setErpCode("000000");
                materialReturnHeader.setDeletedFlag(Boolean.FALSE);
                materialReturnHeader.setAccountAliasConcat("133602.0.**********.0.0.0.0");
                if (materialReturnHeader.getId() == null) {
                    materialReturnHeader.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    materialReturnHeader.setCreateAt(new Date());
                    materialReturnHeaderNewList.add(materialReturnHeader);
                    if (materialReturnHeaderNewList.size() >= 500) {
                        materialReturnHeaderNewLists.add(materialReturnHeaderNewList);
                        materialReturnHeaderNewList = new ArrayList<>(500);
                    }
                } else {
                    materialReturnHeader.setUpdateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                    materialReturnHeader.setUpdateAt(new Date());
                    headerMapper.updateByPrimaryKeySelective(materialReturnHeader);
                }
            }
            if (materialReturnHeaderNewList.size() > 0) {
                materialReturnHeaderNewLists.add(materialReturnHeaderNewList);
            }
            materialReturnHeaderNewLists.forEach(subList -> {
                materialReturnHeaderExtMapper.batchInsert(subList);
            });

            return Collections.emptyList();
        }
        return excelVoList.stream().filter(e -> StringUtils.isNotEmpty(e.getErrMsg())).collect(Collectors.toList());
    }

    /**
     * 校验导入的退料单数据
     *
     * @param excelVoList 退料单数据集合
     * @return 校验通过返回true
     */
    private boolean validMaterialReturnHeaderExcelVo(List<MaterialReturnHeaderExcelVo> excelVoList) {

        excelVoList.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (excelVoList.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getErrMsg()))) {
            return false;
        }

        List<String> returnUserMip = excelVoList.stream().map(MaterialReturnHeaderExcelVo::getReturnUserMip)
                .distinct().collect(Collectors.toList());
        List<String> fillUserMip = excelVoList.stream().map(MaterialReturnHeaderExcelVo::getFillUserMip)
                .distinct().collect(Collectors.toList());
        List<String> keeperUserMip = excelVoList.stream().map(MaterialReturnHeaderExcelVo::getKeeperUserMip)
                .distinct().collect(Collectors.toList());
        Set<String> usernames = new HashSet<>(returnUserMip);
        usernames.addAll(fillUserMip);
        usernames.addAll(keeperUserMip);
        Map<String, UserInfoDto> userMap = getUserByUsernames(usernames).stream()
                .collect(Collectors.toMap(UserInfoDto::getUsername, e -> e));

        List<String> projectCodes = excelVoList.stream().map(MaterialReturnHeaderExcelVo::getProjectCode).distinct().collect(Collectors.toList());
        ProjectExample projectExample = new ProjectExample();
        projectExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE).andCodeIn(projectCodes);
        Map<String, Project> projectMap = projectMapper.selectByExample(projectExample).stream()
                .collect(Collectors.toMap(Project::getCode, e -> e));

        List<String> orgNames = excelVoList.stream().map(MaterialReturnHeaderExcelVo::getOrganizationName)
                .distinct().collect(Collectors.toList());
        Map<String, OrganizationRelDto> organizationRelMap = getOrganizationRelByOrgNames(orgNames).stream()
                .collect(Collectors.toMap(OrganizationRelDto::getOrganizationName, e -> e));

        List<String> inventoryCodes = excelVoList.stream().map(MaterialReturnHeaderExcelVo::getInventoryCode)
                .distinct().collect(Collectors.toList());
        Map<String, Map<Long, StorageInventoryDto>> storageInventoryMap = new HashMap<>(inventoryCodes.size() * 8);
        getStorageInventoryByInventoryCodes(inventoryCodes).forEach(e -> {
            storageInventoryMap.computeIfAbsent(e.getSecondaryInventoryName(), k -> new HashMap<>(8)).put(e.getOrganizationId(), e);
        });

        List<String> returnCodes = excelVoList.stream().map(MaterialReturnHeaderExcelVo::getReturnCode).distinct().collect(Collectors.toList());
        MaterialReturnHeaderExample materialReturnHeaderExample = new MaterialReturnHeaderExample();
        materialReturnHeaderExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andReturnCodeIn(returnCodes);
        Map<String, Long> materialReturnHeaderMap = headerMapper.selectByExample(materialReturnHeaderExample).stream()
                .collect(Collectors.toMap(MaterialReturnHeader::getReturnCode, MaterialReturnHeader::getId));

        excelVoList.forEach(vo -> {
            List<String> errMsgs = new ArrayList<>();

            vo.setId(materialReturnHeaderMap.get(vo.getReturnCode()));

            switch (vo.getMaterialGetTypeName()) {
                case "工单退料":
                    vo.setMaterialGetType(0);
                    break;
                case "项目退料":
                    vo.setMaterialGetType(1);
                    break;
                case "项目退料（WBS）":
                    vo.setMaterialGetType(2);
                    break;
                default:
                    errMsgs.add("不支持的退料类型");
            }

            UserInfoDto user = userMap.get(vo.getReturnUserMip());
            if (user != null) {
                vo.setReturnUserName(user.getName());
                vo.setReturnUserId(user.getId());
            } else {
                vo.setReturnUserName(vo.getReturnUserMip());
            }

            user = userMap.get(vo.getFillUserMip());
            if (user != null) {
                vo.setFillUserName(user.getName());
                vo.setFillUserId(user.getId());
            } else {
                vo.setFillUserName(vo.getFillUserMip());
            }

            user = userMap.get(vo.getKeeperUserMip());
            if (user != null) {
                vo.setFillUserName(user.getName());
                vo.setFillUserId(user.getId());
            } else {
                vo.setFillUserName(vo.getKeeperUserMip());
            }

            Project project = projectMap.get(vo.getProjectCode());
            if (project != null) {
                vo.setProjectId(project.getId());
                vo.setProjectName(project.getName());
            } else {
                errMsgs.add("项目不存在");
            }

            OrganizationRelDto orgRel = organizationRelMap.get(vo.getOrganizationName());
            if (orgRel != null) {
                vo.setOrganizationId(orgRel.getOrganizationId());
                vo.setOrganizationCode(orgRel.getOrganizationCode());
                vo.setOrganizationName(orgRel.getOrganizationName());

                StorageInventoryDto storageInventory = storageInventoryMap.get(vo.getInventoryCode()).get(orgRel.getOrganizationId());
                if (storageInventory != null) {
                    vo.setInventoryId(storageInventory.getId());
                    if (!storageInventory.getDescription().equals(vo.getInventoryName())) {
                        errMsgs.add("仓库名称不匹配");
                    }
                }
            } else {
                errMsgs.add("库存组织不存在");
            }

            vo.setErrMsg(String.join(",", errMsgs));
        });
        return excelVoList.stream().allMatch(e -> StringUtils.isEmpty(e.getErrMsg()));
    }

    private List<OrganizationRelDto> getOrganizationRelByOrgNames(List<String> orgNames) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "organizationRel/listByOrgNames";
        String res = restTemplate.postForObject(url, orgNames, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<OrganizationRelDto>>() {
        });
    }

    private List<StorageInventoryDto> getStorageInventoryByInventoryCodes(List<String> inventoryCodes) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "storageInventory/listByInventoryNames";
        String res = restTemplate.postForObject(url, inventoryCodes, String.class);
        Asserts.notNull(res, Code.ERROR);
        DataResponse<List<StorageInventoryDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<StorageInventoryDto>>>() {
        });
        if (response != null && response.getCode() == 0) {
            return response.getData();
        }
        return new ArrayList<>();
    }

    private List<UserInfoDto> getUserByUsernames(Set<String> usernames) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "employeeInfo/listUserByUserNames";
        String res = restTemplate.postForObject(url, usernames, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<UserInfoDto>>() {
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<MaterialReturnDetailExcelVo> importMaterialReturnDetail(List<MaterialReturnDetailExcelVo> excelVoList, Long unitId) {
        unitId = unitId != null ? unitId : SystemContext.getUnitId();
        boolean isPass = validMaterialReturnDetailExcelVo(excelVoList, unitId);
        if (isPass) {
            List<List<MaterialReturnDetail>> materialReturnDetailLists = new ArrayList<>(100);
            List<MaterialReturnDetail> materialReturnDetailList = new ArrayList<>(500);
            for (MaterialReturnDetailExcelVo vo : excelVoList) {
                MaterialReturnDetail materialReturnDetail = BeanConverter.copy(vo, MaterialReturnDetail.class);
                materialReturnDetail.setCreateBy(Long.parseLong(DateUtils.format(new Date(), "yyyyMMdd")));
                materialReturnDetail.setCreateAt(new Date());
                materialReturnDetail.setIsImport(1);
                materialReturnDetail.setItemCostIsNull(0);
                materialReturnDetail.setDifferenceAmount(materialReturnDetail.getApplyAmount().subtract(materialReturnDetail.getActualAmount()));
                materialReturnDetail.setErpCode("2");
                materialReturnDetail.setDeletedFlag(Boolean.FALSE);
                materialReturnDetail.setTransitionDate(materialReturnDetail.getActualReturnAt());
                materialReturnDetail.setCollectionDate(DateUtils.getShortDate(materialReturnDetail.getActualReturnAt()));
                materialReturnDetailList.add(materialReturnDetail);
                if (materialReturnDetailList.size() >= 500) {
                    materialReturnDetailLists.add(materialReturnDetailList);
                    materialReturnDetailList = new ArrayList<>(500);
                }
            }
            if (materialReturnDetailList.size() > 0) {
                materialReturnDetailLists.add(materialReturnDetailList);
            }
            materialReturnDetailLists.forEach(subList ->
                    detailExtMapper.batchInsert(subList)
            );
            return Collections.emptyList();
        }
        return excelVoList.stream().filter(e -> StringUtils.isNotEmpty(e.getErrMsg())).collect(Collectors.toList());
    }

    @Override
    public Map<String, BigDecimal> queryReturnNumMapByProjectId(Long projectId) {
        Map<String, BigDecimal> returnNumMap = new HashMap<>();
        List<MaterialReturnDetail> materialReturnDetails =
                materialReturnHeaderExtMapper.sumMaterialGetDetailByProjecAndMaterialCode(projectId);
        if (ListUtils.isNotEmpty(materialReturnDetails)) {
            returnNumMap = materialReturnDetails.stream().collect(Collectors.toMap(
                    MaterialReturnDetail::getMaterialCode, MaterialReturnDetail::getActualAmount, (e1, e2) -> e1));
        }
        return returnNumMap;
    }

    @Override
    public MaterialReturnDetailCheckDto checkMaterialReturn(MaterialReturnDetailCheckDto checkDto) {
        //判断单位层级的组织参数
        //当前的单位ID
        Long unitId = SystemContext.getUnitId();
        logger.info("获取的单位ID为:{}", unitId);
        String name = "退料单超退提示";
        Set<String> set = organizationCustomDictService.queryByName(name, unitId, OrgCustomDictOrgFrom.COMPANY);
        logger.info("获取单位层级的组织参数为:{}", JSON.toJSONString(set));
        if (!set.contains("1")) { //参数值配置为：1时，才启用上述功能
            logger.info("不进行超退校验");
            return checkDto;
        }
        //查询费用仓
        List<StorageInventory> costWarehouse = getCostWarehouse(checkDto.getOrganizationId());
        //费用仓
        Map<String, StorageInventory> storageInventoryMap =
                costWarehouse.stream().filter(x -> x.getAssetInventory().equals("2")).collect(Collectors.toMap(StorageInventory::getSecondaryInventoryName, Function.identity()));
        logger.info("当前库存组织ID:{},下的费用仓为:{}", checkDto.getOrganizationId(), JSON.toJSONString(storageInventoryMap));
        //如果是费用仓并且非ERP的，那这种退料单是要剔除的，所以不需要超退提示
        if (!"ERP".equals(checkDto.getFillUserName()) && storageInventoryMap.containsKey(checkDto.getInventoryCode())) {
            logger.info("制单人为非ERP并且仓库为费用仓,不进行超退校验");
            return checkDto;
        }
        List<String> materialCodeList = new ArrayList<>();
        String materialCode = checkDto.getMaterialCode();
        materialCodeList.add(materialCode);
        // 当前项目下,涉及当前物料的已处理的退料单总数
        Long projectId = checkDto.getProjectId();
        String organizationCode = checkDto.getOrganizationCode();
        List<MaterialGetOrReturnActualAmountDto> materialReturnActualAmountList =
                detailExtMapper.selectMaterialReturnActualAmountByProjectId(projectId, organizationCode, materialCodeList);
//剔除不满足要求的数据
        Map<String, List<MaterialGetOrReturnActualAmountDto>> listReturnMap = materialReturnActualAmountList.stream().filter(x -> {
            //排查制单人为非ERP,且仓库为费用仓
            return !(!x.getFillUserName().equals("ERP") && storageInventoryMap.containsKey(x.getInventoryCode()));
        }).collect(Collectors.groupingBy(MaterialGetOrReturnActualAmountDto::getMaterialCode));
        logger.info("满足要求的退料单数据为:{}", JSON.toJSONString(listReturnMap));        // 当前项目下,涉及当前物料的已处理的领料单总数
        List<MaterialGetOrReturnActualAmountDto> materialGetActualAmountList =
                materialGetDetailExtMapper.selectMaterialGetActualAmountByProjectId(projectId, organizationCode, materialCodeList);
        Map<String, List<MaterialGetOrReturnActualAmountDto>> listGetMap = materialGetActualAmountList.stream().filter(x -> {
            //排查制单人为非ERP,且仓库为费用仓
            return !(!x.getFillUserName().equals("ERP") && storageInventoryMap.containsKey(x.getInventoryCode()));
        }).collect(Collectors.groupingBy(MaterialGetOrReturnActualAmountDto::getMaterialCode));
        logger.info("满足要求的领料单数据为:{}", JSON.toJSONString(listGetMap));        //该物料领料的总数
        BigDecimal totalGetActualAmount = listGetMap.containsKey(materialCode) ?
                listGetMap.get(materialCode).stream().map(e -> Optional.ofNullable(e.getActualAmount()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add) : new BigDecimal(0);
        //该物料退料的总数
        BigDecimal totalReturnActualAmount = listReturnMap.containsKey(materialCode) ?
                listReturnMap.get(materialCode).stream().map(e -> Optional.ofNullable(e.getActualAmount()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add) : new BigDecimal(0);
        //当前申请退料的总数 > (该物料领料的总数 - 该物料退料的总数) : 超退
        //查看详情的时候需要判断是否已经是已处理退料的
        BigDecimal applyAmount = Optional.ofNullable(checkDto.getApplyAmount()).orElse(BigDecimal.ZERO);
        BigDecimal actualAmount = Optional.ofNullable(checkDto.getActualAmount()).orElse(BigDecimal.ZERO);
        //BigDecimal amount = new BigDecimal(0);
        // compareTo > 1; = 0; < -1
        logger.info("物料:{}的领料总数为:{},退料总数为:{},当前申请退料数为:{},实际退回数量:{}",
                materialCode, totalGetActualAmount, totalReturnActualAmount, applyAmount, actualAmount);
        if (applyAmount.compareTo(totalGetActualAmount.subtract(totalReturnActualAmount)) > 0) { //超退
            String projectCode = checkDto.getProjectCode();
            // "项目：XX，该物料，已领料数量：A1，已退料数量：A2，当前申请退料数量：xx，请知悉";
            String msg = String.format("项目：%s，该物料，已领料数量：%s，已退料数量：%s，当前申请退料数量：%s，请知悉",
                    projectCode, totalGetActualAmount, totalReturnActualAmount, applyAmount);
            checkDto.setExceedMsg(msg);
        }
        return checkDto;
    }

    @Override
    public List<MaterialReturnDetailDto> exportDetails(Long id) {
        MaterialReturnDetailExample detailsExample = new MaterialReturnDetailExample();
        detailsExample.setOrderByClause("material_code asc");
        detailsExample.createCriteria().andHeaderIdEqualTo(id).andDeletedFlagEqualTo(Boolean.FALSE);
        List<MaterialReturnDetail> details = detailMapper.selectByExample(detailsExample);
        List<MaterialReturnDetailDto> detailsData = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(details)) {
            // 获取计量单位
            Map<String, String> unitMap = basedataExtService.getLtcDict(DictType.MEASUREMENT_UNIT.code(), null, null)
                    .stream().collect(Collectors.toMap(DictDto::getCode, DictDto::getName, (key1, key2) -> key2));
            detailsData = details.stream().map(x -> {
                MaterialReturnDetailDto detailDto = BeanConverter.copy(x, MaterialReturnDetailDto.class);
                detailDto.setUnitName(unitMap.get(x.getUnit()));
                return detailDto;
            }).collect(Collectors.toList());
        }
        return detailsData;
    }

    private boolean validMaterialReturnDetailExcelVo(List<MaterialReturnDetailExcelVo> excelVoList, Long unitId) {
        excelVoList.forEach(c -> c.setErrMsg(validator.validate(c).stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.joining(","))));

        if (excelVoList.stream().anyMatch(c -> StringUtils.isNotEmpty(c.getErrMsg()))) {
            return false;
        }

        List<String> getCodes = excelVoList.stream().map(MaterialReturnDetailExcelVo::getReturnCode).distinct().collect(Collectors.toList());
        MaterialReturnHeaderExample materialReturnHeaderExample = new MaterialReturnHeaderExample();
        materialReturnHeaderExample.createCriteria().andDeletedFlagEqualTo(Boolean.FALSE)
                .andReturnCodeIn(getCodes);
        Map<String, MaterialReturnHeader> materialReturnHeaderMap = headerMapper.selectByExample(materialReturnHeaderExample).stream()
                .collect(Collectors.toMap(MaterialReturnHeader::getReturnCode, e -> e, (e1, e2) -> e1));

        List<String> materialCode = excelVoList.stream().map(MaterialReturnDetailExcelVo::getMaterialCode).distinct().collect(Collectors.toList());
        Map<String, MaterialDto> materialMap = getMaterialByItemCodes(materialCode).stream()
                .collect(Collectors.toMap(MaterialDto::getItemCode, e -> e, (e1, e2) -> e1));

        List<String> summaryCodes = excelVoList.stream().map(MaterialReturnDetailExcelVo::getWbsSummaryCode).distinct().collect(Collectors.toList());
        Map<String, String> projectWbsBudgetSummaryMap = projectWbsBudgetSummaryExtMapper.selectBySummaryCodes(summaryCodes).stream()
                .collect(Collectors.toMap(ProjectWbsBudgetSummaryDto::getSummaryCode, ProjectWbsBudgetSummaryDto::getDescription));

        List<String> activityCodes = excelVoList.stream().map(MaterialReturnDetailExcelVo::getActivityCode).distinct().collect(Collectors.toList());
        Map<String, ProjectActivity> projectActivityMap = projectActivityExtMapper.selectByCodeAndUnitId(activityCodes, unitId).stream()
                .collect(Collectors.toMap(ProjectActivity::getCode, e -> e, (e1, e2) -> e1));

        excelVoList.forEach(vo -> {
            List<String> errMsgList = new ArrayList<>();

            MaterialReturnHeader header = materialReturnHeaderMap.get(vo.getReturnCode());
            if (header != null) {
                vo.setHeaderId(header.getId());
                vo.setOrgId(header.getOrganizationId());
                vo.setInventoryId(header.getInventoryId());
                vo.setInventoryCode(header.getInventoryCode());
                vo.setInventoryName(header.getInventoryName());
            } else {
                errMsgList.add("退料单头信息不存在");
            }

            MaterialDto material = materialMap.get(vo.getMaterialCode());
            if (material != null) {
                vo.setMaterialId(material.getId());
                vo.setMaterialErpId(material.getItemId());
                vo.setMaterialName(material.getItemInfo());
                vo.setUnit(material.getUnit());
            } else {
                errMsgList.add("物料不存在");
            }

            String description = projectWbsBudgetSummaryMap.get(vo.getWbsSummaryCode());
            if (description != null) {
                vo.setWbsDescription(description);
            }

            ProjectActivity projectActivity = projectActivityMap.get(vo.getActivityCode());

            vo.setErrMsg(String.join(",", errMsgList));
        });
        return excelVoList.stream().allMatch(e -> StringUtils.isEmpty(e.getErrMsg()));
    }

    private List<MaterialDto> getMaterialByItemCodes(List<String> materialCodes) {
        String url = ModelsEnum.BASEDATA.getBaseUrl() + "material/listByItemCodes";
        String res = restTemplate.postForObject(url, materialCodes, String.class);
        Asserts.notNull(res, Code.ERROR);
        return JSON.parseObject(res, new TypeReference<List<MaterialDto>>() {
        });
    }

    private void checkReturnMaterialNum(List<MaterialReturnDetailDto> detailList, MaterialReturnHeader header) {
        // a)	找出该项目下，状态为：已处理的领料单和退料单,要剔除：子库是MA1107费用仓库并且制单人非ERP
        // 如果 detailList 为空,则不需要检查
        if (detailList.isEmpty()) {
            logger.info("不存在详情数据,不进行超退校验");
            return;
        }
        //判断单位层级的组织参数
        //当前的单位ID
        Long unitId = SystemContext.getUnitId();
        logger.info("获取的单位ID为:{}", unitId);
        String name = "退料单超退提示";
        Set<String> set = organizationCustomDictService.queryByName(name, unitId, OrgCustomDictOrgFrom.COMPANY);
        logger.info("获取单位层级的组织参数为:{}", JSON.toJSONString(set));
        if (!set.contains("1")) { //参数值配置为：1时，才启用上述功能
            logger.info("不进行超退校验");
            return;
        }
        //查询费用仓
        List<StorageInventory> costWarehouse = getCostWarehouse(header.getOrganizationId());
        logger.info("当前库存组织ID:{},下的所以的仓库为:{}", header.getOrganizationId(), JSON.toJSONString(costWarehouse));
        //费用仓
        Map<String, StorageInventory> storageInventoryMap =
                costWarehouse.stream().filter(x -> x.getAssetInventory().equals("2")).collect(Collectors.toMap(StorageInventory::getSecondaryInventoryName, Function.identity()));
        logger.info("当前库存组织ID:{},下的费用仓为:{}", header.getOrganizationId(), JSON.toJSONString(storageInventoryMap));
        //如果是费用仓并且非ERP的，那这种退料单是要剔除的，所以不需要超退提示
        if (!"ERP".equals(header.getFillUserName()) && storageInventoryMap.containsKey(header.getInventoryCode())) {
            logger.info("制单人为非ERP并且仓库为费用仓,不进行超退校验");
            return;
        }
        List<String> materialCodeList = detailList.stream().map(detail -> detail.getMaterialCode()).collect(Collectors.toList());
        // 当前项目下,涉及当前物料的已处理的退料单总数
        Long projectId = header.getProjectId();
        String organizationCode = header.getOrganizationCode();
        List<MaterialGetOrReturnActualAmountDto> materialReturnActualAmountList =
                detailExtMapper.selectMaterialReturnActualAmountByProjectId(projectId, organizationCode, materialCodeList);
        logger.info("获取总的退料数据为:{}", JSON.toJSONString(materialReturnActualAmountList));
        //剔除不满足要求的数据
        Map<String, List<MaterialGetOrReturnActualAmountDto>> listReturnMap = materialReturnActualAmountList.stream().filter(x -> {
            //排除制单人为非ERP,且仓库为费用仓
//            boolean flag = true;
//            if(x.getFillUserName().equals("ERP")){
//                flag = storageInventoryMap.containsKey(x.getInventoryCode()) ? false: true;
//            }
//            return flag;
            return !(!x.getFillUserName().equals("ERP") && storageInventoryMap.containsKey(x.getInventoryCode()));
        }).collect(Collectors.groupingBy(MaterialGetOrReturnActualAmountDto::getMaterialCode));
        logger.info("满足要求的退料单数据为:{}", JSON.toJSONString(listReturnMap));
        // 当前项目下,涉及当前物料的已处理的领料单总数
        List<MaterialGetOrReturnActualAmountDto> materialGetActualAmountList =
                materialGetDetailExtMapper.selectMaterialGetActualAmountByProjectId(projectId, organizationCode, materialCodeList);
        //剔除不满足要求的数据
        logger.info("获取总的退料数据为:{}", JSON.toJSONString(materialGetActualAmountList));
        Map<String, List<MaterialGetOrReturnActualAmountDto>> listGetMap = materialGetActualAmountList.stream().filter(x -> {
            //排除制单人为非ERP,且仓库为费用仓
            return !(!x.getFillUserName().equals("ERP") && storageInventoryMap.containsKey(x.getInventoryCode()));
        }).collect(Collectors.groupingBy(MaterialGetOrReturnActualAmountDto::getMaterialCode));
        logger.info("满足要求的领料单数据为:{}", JSON.toJSONString(listGetMap));
        for (MaterialReturnDetailDto dto : detailList) {
            String materialCode = dto.getMaterialCode();
            //该物料领料的总数
            BigDecimal totalGetActualAmount = listGetMap.keySet().contains(materialCode) ?
                    listGetMap.get(materialCode).stream().map(MaterialGetOrReturnActualAmountDto::getActualAmount).reduce(BigDecimal.ZERO,
                            BigDecimal::add) : new BigDecimal(0);
            //该物料退料的总数
            BigDecimal totalReturnActualAmount = listReturnMap.keySet().contains(materialCode) ?
                    listReturnMap.get(materialCode).stream().map(MaterialGetOrReturnActualAmountDto::getActualAmount).reduce(BigDecimal.ZERO,
                            BigDecimal::add) : new BigDecimal(0);
            //当前申请退料的总数 > (该物料领料的总数 - 该物料退料的总数) : 超退
            //查看详情的时候需要判断是否已经是已处理退料的
            BigDecimal applyAmount = dto.getApplyAmount();
            BigDecimal actualAmount = dto.getActualAmount();
            //BigDecimal amount = new BigDecimal(0);
            if (header.getStatus().equals(MaterialReturnStatus.PROCESSED.code())) { //状态为已处理时,需要剔除本身
                //查询已处理的详情情况,已经计算在总的申请数量中了,因此查询这条记录时,总退料数需要剔除自身
                //amount = applyAmount.subtract(actualAmount);
                totalReturnActualAmount = totalReturnActualAmount.subtract(actualAmount);
            }

            // compareTo > 1; = 0; < -1
            logger.info("物料:{}的领料总数为:{},退料总数为:{},当前申请退料数为:{},实际退回数量:{}", materialCode, totalGetActualAmount, totalReturnActualAmount, applyAmount,
                    actualAmount);
            if (applyAmount.compareTo(totalGetActualAmount.subtract(totalReturnActualAmount)) > 0) { //超退
                String projectCode = header.getProjectCode();
                // "项目：XX，该物料，已领料数量：A1，已退料数量：A2，当前申请退料数量：xx，请知悉";
                String msg = String.format("项目：%s，该物料，已领料数量：%s，已退料数量：%s，当前申请退料数量：%s，请知悉",
                        projectCode, totalGetActualAmount, totalReturnActualAmount, applyAmount);
                dto.setExceedMsg(msg);
            }
        }
    }

    private List<StorageInventory> getCostWarehouse(Long organizationId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("organizationId", organizationId);
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/storageInventory/listInventory", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<StorageInventory>> listDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<StorageInventory>>>() {
        });
        return listDataResponse.getData();
    }

    private String fromatWbsFullCode(String projectCode, String wbsSummaryCode) {
        // 检查 projectCode 和 wbsSummaryCode 是否为空
        if (StringUtils.isBlank(projectCode) || StringUtils.isBlank(wbsSummaryCode)) {
            return null;
        }
        // 检查 wbsSummaryCode 是否以 projectCode 开头，并且包含 "-"
        if (wbsSummaryCode.startsWith(projectCode + "-")) {
            // 从 wbsSummaryCode 中提取 wbsFullCode
            return wbsSummaryCode.substring(projectCode.length() + 1);
        }
        // 如果格式不正确，返回 null 或者抛出异常
        return null; // 或者  throw new BizException(Code.ERROR, "转换错误");
    }

}
