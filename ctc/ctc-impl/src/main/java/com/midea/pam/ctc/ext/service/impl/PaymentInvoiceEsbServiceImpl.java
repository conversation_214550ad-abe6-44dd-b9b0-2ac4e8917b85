package com.midea.pam.ctc.ext.service.impl;

import com.alibaba.fastjson.JSON;
import com.midea.mcomponent.core.util.Assert;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.VendorSiteBankDto;
import com.midea.pam.common.basedata.entity.Dict;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.dto.BusiSceneNonSaleDetailDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailErpDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceErpDto;
import com.midea.pam.common.ctc.entity.MaterialOutsourcingContractConfig;
import com.midea.pam.common.ctc.entity.PaymentApply;
import com.midea.pam.common.ctc.entity.PaymentInvoice;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetail;
import com.midea.pam.common.ctc.entity.PaymentInvoiceDetailExample;
import com.midea.pam.common.ctc.entity.PaymentInvoiceExample;
import com.midea.pam.common.ctc.entity.PaymentPenaltyProfit;
import com.midea.pam.common.ctc.entity.PaymentPenaltyProfitExample;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractPunishment;
import com.midea.pam.common.ctc.entity.ResendExecute;
import com.midea.pam.common.ctc.vo.PurchaseContractPunishmentVo;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.InvoiceDetailStatus;
import com.midea.pam.common.enums.PaymentApplyEsbStatus;
import com.midea.pam.common.enums.PaymentInvoiceAccountEntryTypeEnum;
import com.midea.pam.common.enums.PaymentInvoiceErpStatusEnum;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.common.util.Utils;
import com.midea.pam.ctc.carrybill.service.MaterialOutsourcingContractConfigService;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.contract.service.PaymentApplyService;
import com.midea.pam.ctc.contract.service.PurchaseContractPunishmentService;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailExtMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceDetailMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceExtMapper;
import com.midea.pam.ctc.mapper.PaymentInvoiceMapper;
import com.midea.pam.ctc.mapper.PaymentPenaltyProfitMapper;
import com.midea.pam.ctc.mapper.PurchaseContractMapper;
import com.midea.pam.ctc.mapper.PurchaseContractPunishmentExtMapper;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.BusiSceneNonSaleService;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.PaymentInvoiceDetailService;
import com.midea.pam.ctc.service.PaymentInvoiceService;
import com.midea.pam.ctc.service.SdpService;
import com.midea.pam.framework.core.exception.Guard;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * @program: pam
 * @description: PaymentInvoiceEsbServiceImpl
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
public class PaymentInvoiceEsbServiceImpl extends AbstractCommonBusinessService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private EsbService esbService;
    @Resource
    private PaymentInvoiceDetailMapper paymentInvoiceDetailMapper;
    @Resource
    private PaymentPenaltyProfitMapper paymentPenaltyProfitMapper;
    @Resource
    private PaymentInvoiceMapper paymentInvoiceMapper;
    @Resource
    private PaymentInvoiceService paymentInvoiceService;
    @Resource
    private PaymentApplyService paymentApplyService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private BusiSceneNonSaleService busiSceneNonSaleService;
    @Resource
    private PurchaseContractMapper purchaseContractMapper;
    @Resource
    private PurchaseContractPunishmentExtMapper punishmentExtMapper;
    @Resource
    private PaymentInvoiceDetailService paymentInvoiceDetailService;
    @Resource
    private PurchaseContractPunishmentService purchaseContractPunishmentService;

    @Resource
    private MaterialOutsourcingContractConfigService materialOutsourcingContractConfigService;

    @Resource
    private SdpService sdpService;


    @Override
    public EsbResponse execute(ResendExecute resendExecute) {
        logger.info("应付发票写入开始：" + resendExecute);
        String applyNo = resendExecute.getApplyNo();
        Assert.notNull(applyNo, "applyNo不能为空");
        List<PaymentInvoiceErpDto> dtoList = new ArrayList<>();
        PaymentInvoiceErpDto erpDto = new PaymentInvoiceErpDto();
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(Long.parseLong(applyNo));
        Guard.notNull(paymentInvoice, String.format("发票信息ID:%s不存在", applyNo));
        PaymentApply paymentApply = Objects.isNull(paymentInvoice.getPaymentApplyId()) ? null : paymentApplyService.findById(paymentInvoice.getPaymentApplyId());
        //入账类型
        Integer accountEntryType = paymentInvoice.getAccountEntryType();
        if (Objects.isNull(paymentApply)) {
            if(Objects.equals(accountEntryType,PaymentInvoiceAccountEntryTypeEnum.REGULAR_INVOICE.getCode())){
                //从供应商表获取付款信息
                PaymentInvoiceDetailExample example = new PaymentInvoiceDetailExample();
                example.createCriteria().andPaymentInvoiceIdEqualTo(paymentInvoice.getId())
                        .andDeletedFlagEqualTo(false);
                List<PaymentInvoiceDetail> paymentInvoiceDetails = paymentInvoiceDetailMapper.selectByExample(example);
                Guard.notNullOrEmpty(paymentInvoiceDetails, String.format("发票信息ID:%s找不到对应的发票信息详情", paymentInvoice.getId()));
                VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(paymentInvoiceDetails.get(0).getVendorId());
                Guard.notNull(vendorSiteBankDto, String.format("发票信息ID:%s对应的发票信息详情找不到供应商信息", paymentInvoice.getId()));
                Long ouId = paymentInvoiceDetails.get(0).getOuId();
                erpDto.setOrgId(ouId != null ? new BigDecimal(ouId) : null);
                Date glDate = Utils.getFirstNotNull(Arrays.asList(
                        paymentInvoice.getGlDate(),
                        paymentInvoice.getAuditDate(),
                        new Date()));
                erpDto.setGlDate(DateUtils.formatDate(glDate));
                erpDto.setVendorId(new BigDecimal(vendorSiteBankDto.getErpVendorId()));//供应商ID
                erpDto.setVendorSiteCode(new BigDecimal(vendorSiteBankDto.getErpVendorSiteId()));//供应商地点ID
                erpDto.setPaymentMethodCode(vendorSiteBankDto.getPaymentMethodCode());//付款方法代码
                erpDto.setPaymentMethod(vendorSiteBankDto.getPaymentMethodName());//付款方法名称
                erpDto.setDescription("PAM发票导入，" + paymentInvoice.getApInvoiceCode());// 发票描述
            }else if (Objects.equals(accountEntryType,PaymentInvoiceAccountEntryTypeEnum.NO_INVOICE_PENALTY.getCode())){
                List<PurchaseContractPunishmentVo> purchaseContractPunishmentVos = punishmentExtMapper.selectListByPaytmentInvoiceId(paymentInvoice.getId());
                if(ListUtils.isNotEmpty(purchaseContractPunishmentVos)){
                    PurchaseContractPunishmentVo firstPunishmentVo = purchaseContractPunishmentVos.get(0);
                    PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(firstPunishmentVo.getPurchaseContractId());
                    VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(purchaseContract.getVendorId());
                    Guard.notNull(vendorSiteBankDto, String.format("发票信息ID:%s对应的发票信息详情找不到供应商信息", paymentInvoice.getId()));
                    Long ouId = firstPunishmentVo.getOuId();
                    erpDto.setOrgId(ouId != null ? new BigDecimal(ouId) : null);
                    Date glDate = Utils.getFirstNotNull(Arrays.asList(
                            paymentInvoice.getGlDate(),
                            paymentInvoice.getAuditDate(),
                            new Date()));
                    erpDto.setGlDate(DateUtils.formatDate(glDate));
                    erpDto.setVendorId(new BigDecimal(vendorSiteBankDto.getErpVendorId()));//供应商ID
                    erpDto.setVendorSiteCode(new BigDecimal(vendorSiteBankDto.getErpVendorSiteId()));//供应商地点ID
                    erpDto.setPaymentMethodCode(vendorSiteBankDto.getPaymentMethodCode());//付款方法代码
                    erpDto.setPaymentMethod(vendorSiteBankDto.getPaymentMethodName());//付款方法名称
                    erpDto.setDescription("PAM发票导入，" + paymentInvoice.getApInvoiceCode());// 发票描述
                }
            }

        } else {
            Long ouId = paymentApply.getOuId();
            erpDto.setOrgId(ouId != null ? new BigDecimal(ouId) : null);
            Date glDate = Utils.getFirstNotNull(Arrays.asList(
                    paymentInvoice.getGlDate(),
                    paymentInvoice.getAuditDate(),
                    paymentApply.getAuditDate(),
                    new Date()));
            erpDto.setGlDate(DateUtils.formatDate(glDate));
            erpDto.setVendorId(new BigDecimal(paymentApply.getVendorErpId()));//供应商ID
            erpDto.setVendorSiteCode(new BigDecimal(paymentApply.getErpVendorSiteId()));//供应商地点ID
            Long paymentMethodId = paymentApply.getPaymentMethodId();//付款方式ID
            if (paymentMethodId != null && paymentMethodId > 0) {
                Dict dict = basedataExtService.findDictById(paymentMethodId);
                if (dict != null) {
                    erpDto.setPaymentMethodCode(dict.getCode());
                    erpDto.setPaymentMethod(dict.getName());
                }
            }
            erpDto.setDescription("PAM发票导入，" + paymentApply.getPaymentApplyCode());// 发票描述
        }

        List<PaymentInvoiceDetailErpDto> detailErpDtoList = new ArrayList<>();
        //头信息
        erpDto.setId(paymentInvoice.getId());

        erpDto.setBatchName(paymentInvoice.getPaymentApplyCode() == null ?
                paymentInvoice.getApInvoiceCode() : paymentInvoice.getPaymentApplyCode());
        erpDto.setInvoiceNum(paymentInvoice.getApInvoiceCode());
        erpDto.setInvoiceTypeLookupCode("MIXED");
        erpDto.setInvoiceDate(DateUtils.formatDate(paymentInvoice.getInvoiceDate()));


        erpDto.setAttribute3(Optional.ofNullable(paymentApply).map(PaymentApply::getItemNumber).orElse(paymentInvoice.getItemNumber()));

        erpDto.setTermsName("立即付款");
        erpDto.setInvoiceAmount(paymentInvoice.getTotalInvoiceIncludedPrice());// 发票额
        //发票税额
        erpDto.setTaxAmount(BigDecimal.ZERO);
        //发票税码
        //erpDto.setTaxClassification(paymentInvoice.getTaxCode());
        erpDto.setAttribute7("\\付");//凭证类型

        //不含税发票金额行信息
        PaymentInvoiceDetailExample detailExample = new PaymentInvoiceDetailExample();
        detailExample.createCriteria().andPaymentInvoiceIdEqualTo(paymentInvoice.getId())
                .andDeletedFlagEqualTo(false)
                .andInvoiceStatusEqualTo(InvoiceDetailStatus.QUOTE.getCode());
        List<PaymentInvoiceDetail> detailList = paymentInvoiceDetailMapper.selectByExample(detailExample);
        if (null != detailList && detailList.size() > 0) {
            erpDto.setCurrencyCode(Optional.ofNullable(detailList.get(0).getCurrency()).orElse("CNY"));//币种
            BigDecimal taxAmount = BigDecimal.valueOf(0.00);
            for (PaymentInvoiceDetail detail : detailList) {
                taxAmount = detail.getTaxAmount();
                //不含税金额
                PaymentInvoiceDetailErpDto detailErpDto = new PaymentInvoiceDetailErpDto();
                detailErpDto.setId(detail.getId() + "");
                detailErpDto.setInvoiceNum(paymentInvoice.getApInvoiceCode());
                detailErpDto.setOrgId(new BigDecimal(detail.getOuId()));
                detailErpDto.setLineTypeLookUpCode("ITEM");
                //罚扣发票金额*-1
                if (Constants.Prefix.INVOICE_INV002.equals(detail.getAttribute())) {
                    detailErpDto.setAmount(new BigDecimal(-1).multiply(detail.getTaxIncludedPrice().subtract(detail.getTaxAmount())));//行金额，不含税
                } else {
                    detailErpDto.setAmount(detail.getTaxIncludedPrice().subtract(detail.getTaxAmount()));//行金额，不含税
                }
                String remark = "";
                if (Constants.Prefix.INVOICE_INV001.equals(detail.getAttribute())) {
                    //常规发票
                    remark =  detail.getInvoiceDetailCode();
                } else {
                    //罚扣/返利发票
                    remark = "关联的发票号" + detail.getInvoiceDetailCode();
                }
                detailErpDto.setDescription(remark);
                //查询组织参数配置--区分含税不含税
                String code = detail.getPurchaseContractCode();
                String accountingSubjectProject = this.checkBusiSceneNonSale(detail.getOuId(), detailErpDto, code);
                detailErpDtoList.add(detailErpDto);

                //税额行信息
                PaymentInvoiceDetailErpDto detailErpDto2 = new PaymentInvoiceDetailErpDto();
                detailErpDto2.setId(detail.getId() + "");
                detailErpDto2.setInvoiceNum(paymentInvoice.getApInvoiceCode());
                detailErpDto2.setOrgId(new BigDecimal(detail.getOuId()));
                detailErpDto2.setLineTypeLookUpCode("ITEM");

                //罚扣发票金额*-1
                if (Constants.Prefix.INVOICE_INV002.equals(detail.getAttribute())) {
                    detailErpDto2.setAmount(new BigDecimal(-1).multiply(detail.getTaxAmount()));//行金额，含税
                } else {
                    detailErpDto2.setAmount(detail.getTaxAmount());//行金额，含税
                }
                detailErpDto2.setDescription(remark);
                //查询组织参数配置--区分含税不含税(现在不区分AP,只按配置推送)
                 this.getAccountGroupDebitOfBusiSceneNonSaleDetail(detail.getOuId(),detail.getTaxRate().toString(), detailErpDto2);
                detailErpDtoList.add(detailErpDto2);
                //更新发票行入账科目
                paymentInvoiceDetailService.updateInvoiceAccountingSubject(detail.getId(),accountingSubjectProject);
            }
        }else if(Objects.equals(accountEntryType, PaymentInvoiceAccountEntryTypeEnum.NO_INVOICE_PENALTY.getCode())){
            PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(paymentInvoice.getPurchaseContractId());
            String code = purchaseContract.getCode();
            //设置币种
            erpDto.setCurrencyCode(purchaseContract.getCurrency());
            //获取关联罚扣信息
            List<PurchaseContractPunishmentVo> purchaseContractPunishments = punishmentExtMapper.selectListByPaytmentInvoiceId(paymentInvoice.getId());
            if(ListUtils.isNotEmpty(purchaseContractPunishments)){
                for (PurchaseContractPunishmentVo punishment : purchaseContractPunishments) {

                    PaymentInvoiceDetailErpDto detailErpDto = new PaymentInvoiceDetailErpDto();
                    detailErpDto.setId(punishment.getId() + "");
                    detailErpDto.setInvoiceNum(paymentInvoice.getApInvoiceCode());
                    detailErpDto.setOrgId(new BigDecimal(punishment.getOuId()));
                    detailErpDto.setLineTypeLookUpCode("ITEM");
                    detailErpDto.setAmount(punishment.getAmount() .multiply(new BigDecimal(-1)));
                    detailErpDto.setDescription("罚扣编号 " + punishment.getCode());

                    detailErpDto.setSegment1(paymentInvoiceService.getBuyerNumberByOu(punishment.getOuId()));
                    detailErpDto.setSegment2("0");
                    detailErpDto.setSegment3(punishment.getAccountGroupDebit().substring(9, 19));
                    detailErpDto.setSegment4("0");
                    detailErpDto.setSegment5("0");
                    detailErpDto.setSegment6("0");
                    detailErpDto.setSegment7("0");

                    detailErpDtoList.add(detailErpDto);

                    //税额行信息
                    PaymentInvoiceDetailErpDto detailErpDto2 = new PaymentInvoiceDetailErpDto();
                    detailErpDto2.setId(punishment.getId() + "");
                    detailErpDto2.setInvoiceNum(paymentInvoice.getApInvoiceCode());
                    detailErpDto2.setOrgId(new BigDecimal(punishment.getOuId()));
                    detailErpDto2.setLineTypeLookUpCode("ITEM");
                    detailErpDto2.setAmount(BigDecimal.ZERO);
                    detailErpDto2.setDescription("罚扣编号 "+punishment.getCode());
                    this.getAccountGroupDebitOfBusiSceneNonSaleDetail(punishment.getOuId(), punishment.getTaxRate(), detailErpDto2);

                    detailErpDtoList.add(detailErpDto2);

                    //更新罚扣入账科目
                    purchaseContractPunishmentService.updateInvoiceAccountingSubject(punishment.getId(),punishment.getOuId(),punishment.getAccountGroupDebit());

                    logger.info("应付发票写入_单个,罚扣不开票入账 item1:{}", JSON.toJSONString(detailErpDto));
                   /* logger.info("应付发票写入_单个,罚扣不开票入账 item2:{}",JSON.toJSONString(detailErpDto2));*/
                }
            }
        }

        //不含税罚扣不开票行信息
        if (paymentApply != null) {
            PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(paymentInvoice.getPurchaseContractId());
            String code = purchaseContract.getCode();
            PaymentPenaltyProfitExample penaltyProfitExample = new PaymentPenaltyProfitExample();
            penaltyProfitExample.createCriteria().andPaymentApplyIdEqualTo(paymentApply.getId())
                    .andPayPenaltyTypeEqualTo(Constants.Prefix.PENALTY_TYPE_FK0001).andDeletedFlagEqualTo(0);
            List<PaymentPenaltyProfit> penaltyProfits = paymentPenaltyProfitMapper.selectByExample(penaltyProfitExample);
            for (PaymentPenaltyProfit penaltyProfit : penaltyProfits) {
                PaymentInvoiceDetailErpDto detailErpDto = new PaymentInvoiceDetailErpDto();
                detailErpDto.setId(penaltyProfit.getId() + "");
                detailErpDto.setInvoiceNum(paymentInvoice.getApInvoiceCode());
                detailErpDto.setOrgId(new BigDecimal(paymentApply.getOuId()));
                detailErpDto.setLineTypeLookUpCode("ITEM");
                //罚扣发票金额*-1
                detailErpDto.setAmount(new BigDecimal(-1).multiply(penaltyProfit.getAmount().subtract(null != penaltyProfit.getTaxAmount() ? penaltyProfit.getTaxAmount() : BigDecimal.ZERO)));//行金额，不含税
                //查询组织参数配置--区分含税不含税
                this.checkBusiSceneNonSale(paymentApply.getOuId(), detailErpDto, code);
                detailErpDto.setDescription("罚扣类型");
                detailErpDtoList.add(detailErpDto);

            }
        }
        erpDto.setDetailErpDtos(detailErpDtoList);

        // 设置汇率
        PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(paymentInvoice.getPurchaseContractId());
        //查询当前业务实体下对应的本位币
        List<OrganizationRel> organizationRels = basedataExtService.queryByOuId(contract.getOuId());
        if (ListUtils.isNotEmpty(organizationRels)) {
            if (null == organizationRels.get(0).getCurrency()) {
                throw new BizException(Code.ERROR, String.format("请配置当前业务实体的本位币，ouId=%s", contract.getOuId()));
            }
        } else {
            throw new BizException(Code.ERROR, String.format("请配置当前业务实体的本位币，ouId=%s", contract.getOuId()));
        }
        //非本位币才设值
        if (!Objects.equals(erpDto.getCurrencyCode(), organizationRels.get(0).getCurrency())) {
            if (StringUtils.isEmpty(contract.getConversionType())) {
                throw new BizException(Code.ERROR, String.format("发票信息ID:%s外币发票的汇率类型不能为空，", paymentInvoice.getId()));
            }
            erpDto.setExchangeRateType("Corporate".equals(contract.getConversionType()) ? "公司" : contract.getConversionType());  //汇率类型
            erpDto.setExchangeRateDate(DateUtils.formatDate(contract.getConversionDate())); //汇率日期
            if("用户".equals(erpDto.getExchangeRateType())) {
                erpDto.setExchangeRate(contract.getConversionRate()); //汇率，公司留空
            }
        }
        dtoList.add(erpDto);

        if (ListUtils.isNotEmpty(dtoList)) {
            return sdpService.callErpPaymentInvoice(dtoList);
        }
        return new EsbResponse();
    }

    @Resource
    private RestTemplate restTemplate;

    /**
     * 执行批量发送报文.
     *
     * @param resendExecutes
     */
    @Override
    public EsbResponse execute(List resendExecutes) {
        logger.info("应付发票写入开始：" + resendExecutes);
        List<ResendExecute> list = BeanConverter.copy(resendExecutes, ResendExecute.class);
        List<Long> ids = ListUtil.map(list, "applyNo");
        List<PaymentInvoiceErpDto> dtoList = new ArrayList<>();
        PaymentInvoiceExample example = new PaymentInvoiceExample();
        example.createCriteria().andIdIn(ids);
        List<PaymentInvoice> paymentInvoices = paymentInvoiceMapper.selectByExample(example);
        for (PaymentInvoice dto : paymentInvoices) {
            PaymentInvoiceErpDto erpDto = new PaymentInvoiceErpDto();
            PaymentApply paymentApply = Objects.isNull(dto.getPaymentApplyId()) ? null : paymentApplyService.findById(dto.getPaymentApplyId());
            //入账类型
            Integer accountEntryType = dto.getAccountEntryType();
            if (paymentApply == null) {
                if(Objects.equals(accountEntryType,PaymentInvoiceAccountEntryTypeEnum.REGULAR_INVOICE.getCode())) {
                    //从供应商表获取付款信息
                    PaymentInvoiceDetailExample detailExample = new PaymentInvoiceDetailExample();
                    detailExample.createCriteria().andPaymentInvoiceIdEqualTo(dto.getId())
                            .andDeletedFlagEqualTo(false);
                    List<PaymentInvoiceDetail> paymentInvoiceDetails = paymentInvoiceDetailMapper.selectByExample(detailExample);
                    Guard.notNullOrEmpty(paymentInvoiceDetails, String.format("发票信息ID:%s找不到对应的发票信息详情", dto.getId()));
                    VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(paymentInvoiceDetails.get(0).getVendorId());
                    Guard.notNull(vendorSiteBankDto, String.format("发票信息ID:%s对应的发票信息详情找不到供应商信息", dto.getId()));
                    Long ouId = paymentInvoiceDetails.get(0).getOuId();
                    erpDto.setOrgId(ouId != null ? new BigDecimal(ouId) : null);
                    Date glDate = Utils.getFirstNotNull(Arrays.asList(
                            dto.getGlDate(),
                            dto.getAuditDate(),
                            new Date()));
                    erpDto.setGlDate(DateUtils.formatDate(glDate));
                    erpDto.setVendorId(new BigDecimal(vendorSiteBankDto.getErpVendorId()));//供应商ID
                    erpDto.setVendorSiteCode(new BigDecimal(vendorSiteBankDto.getErpVendorSiteId()));//供应商地点ID
                    erpDto.setPaymentMethodCode(vendorSiteBankDto.getPaymentMethodCode());//付款方法代码
                    erpDto.setPaymentMethod(vendorSiteBankDto.getPaymentMethodName());//付款方法名称

                    erpDto.setDescription("PAM发票导入，" + dto.getApInvoiceCode());// 发票描述
                }else if (Objects.equals(accountEntryType,PaymentInvoiceAccountEntryTypeEnum.NO_INVOICE_PENALTY.getCode())){
                    List<PurchaseContractPunishmentVo> purchaseContractPunishmentVos = punishmentExtMapper.selectListByPaytmentInvoiceId(dto.getId());
                    if(ListUtils.isNotEmpty(purchaseContractPunishmentVos)){
                        PurchaseContractPunishmentVo firstPunishmentVo = purchaseContractPunishmentVos.get(0);
                        PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(firstPunishmentVo.getPurchaseContractId());
                        VendorSiteBankDto vendorSiteBankDto = basedataExtService.getVendorSiteBankDto(purchaseContract.getVendorId());
                        Guard.notNull(vendorSiteBankDto, String.format("发票信息ID:%s对应的发票信息详情找不到供应商信息", dto.getId()));
                        Long ouId = firstPunishmentVo.getOuId();
                        erpDto.setOrgId(ouId != null ? new BigDecimal(ouId) : null);
                        Date glDate = Utils.getFirstNotNull(Arrays.asList(
                                dto.getGlDate(),
                                dto.getAuditDate(),
                                new Date()));
                        erpDto.setGlDate(DateUtils.formatDate(glDate));
                        erpDto.setVendorId(new BigDecimal(vendorSiteBankDto.getErpVendorId()));//供应商ID
                        erpDto.setVendorSiteCode(new BigDecimal(vendorSiteBankDto.getErpVendorSiteId()));//供应商地点ID
                        erpDto.setPaymentMethodCode(vendorSiteBankDto.getPaymentMethodCode());//付款方法代码
                        erpDto.setPaymentMethod(vendorSiteBankDto.getPaymentMethodName());//付款方法名称
                        erpDto.setDescription("PAM发票导入，" + dto.getApInvoiceCode());// 发票描述
                    }
                }
            } else {
                erpDto.setOrgId(paymentApply.getOuId() != null ? new BigDecimal(paymentApply.getOuId()) : null);
                Date glDate = Utils.getFirstNotNull(Arrays.asList(
                        dto.getGlDate(),
                        dto.getAuditDate(),
                        paymentApply.getAuditDate(),
                        new Date()));
                erpDto.setGlDate(DateUtils.formatDate(glDate));
                erpDto.setVendorId(new BigDecimal(paymentApply.getVendorErpId()));//供应商ID
                erpDto.setVendorSiteCode(new BigDecimal(paymentApply.getErpVendorSiteId()));//供应商地点ID
                Long paymentMethodId = paymentApply.getPaymentMethodId();//付款方式ID
                if (paymentMethodId != null && paymentMethodId > 0) {
                    Dict dict = basedataExtService.findDictById(paymentMethodId);
                    if (dict != null) {
                        erpDto.setPaymentMethodCode(dict.getCode());
                        erpDto.setPaymentMethod(dict.getName());
                    }
                }
                erpDto.setDescription("PAM发票导入，" + paymentApply.getPaymentApplyCode());// 发票描述

            }

            List<PaymentInvoiceDetailErpDto> detailErpDtoList = new ArrayList<>();
            //头信息
            erpDto.setId(dto.getId());
            erpDto.setBatchName(dto.getPaymentApplyCode() == null ? dto.getApInvoiceCode() : dto.getPaymentApplyCode());
            erpDto.setInvoiceNum(dto.getApInvoiceCode());
            erpDto.setInvoiceTypeLookupCode("MIXED");
            erpDto.setInvoiceDate(DateUtils.formatDate(dto.getInvoiceDate()));
            erpDto.setAttribute3(Optional.ofNullable(paymentApply).map(PaymentApply::getItemNumber).orElse(dto.getItemNumber()));
            erpDto.setTermsName("立即付款");
            erpDto.setInvoiceAmount(dto.getTotalInvoiceIncludedPrice());// 发票额
            //发票税额  设置为0
            erpDto.setTaxAmount(BigDecimal.ZERO);
            //erpDto.setTaxClassification(dto.getTaxCode());
            erpDto.setAttribute7("\\付");//凭证类型

            //不含税发票金额行信息
            PaymentInvoiceDetailExample detailExample = new PaymentInvoiceDetailExample();
            detailExample.createCriteria().andPaymentInvoiceIdEqualTo(dto.getId()).andDeletedFlagEqualTo(false).andInvoiceStatusEqualTo(InvoiceDetailStatus.QUOTE.getCode());
            List<PaymentInvoiceDetail> detailList = paymentInvoiceDetailMapper.selectByExample(detailExample);
            if (null != detailList && detailList.size() > 0) {
                erpDto.setCurrencyCode(Optional.ofNullable(detailList.get(0).getCurrency()).orElse("CNY"));//币种
                BigDecimal taxAmount = BigDecimal.valueOf(0.00);
                for (PaymentInvoiceDetail detail : detailList) {
                    taxAmount = taxAmount.add(detail.getTaxAmount());//税额汇总
                    PaymentInvoiceDetailErpDto detailErpDto = new PaymentInvoiceDetailErpDto();
                    detailErpDto.setId(detail.getId() + "");
                    detailErpDto.setInvoiceNum(dto.getApInvoiceCode());
                    detailErpDto.setOrgId(new BigDecimal(detail.getOuId()));
                    detailErpDto.setLineTypeLookUpCode("ITEM");
                    //罚扣发票金额*-1
                    if (Constants.Prefix.INVOICE_INV002.equals(detail.getAttribute())) {
                        detailErpDto.setAmount(new BigDecimal(-1).multiply(detail.getTaxIncludedPrice().subtract(detail.getTaxAmount())));//行金额，不含税
                    } else {
                        detailErpDto.setAmount(detail.getTaxIncludedPrice().subtract(detail.getTaxAmount()));//行金额，不含税
                    }
                    //查询组织参数配置--区分含税不含税
                    String code = detail.getPurchaseContractCode();
                    String accountingSubjectProject = this.checkBusiSceneNonSale(detail.getOuId(), detailErpDto, code);
                    String remark = "";
                    if (Constants.Prefix.INVOICE_INV001.equals(detail.getAttribute())) {
                        //常规发票
                        remark =  detail.getInvoiceDetailCode();
                    } else {
                        //罚扣/返利发票
                        remark = "关联的发票号" + detail.getInvoiceDetailCode();
                    }
                    detailErpDto.setDescription(remark);
                    detailErpDtoList.add(detailErpDto);

                    //税额行信息
                    PaymentInvoiceDetailErpDto detailErpDto2 = new PaymentInvoiceDetailErpDto();
                    detailErpDto2.setId(detail.getId() + "");
                    detailErpDto2.setInvoiceNum(dto.getApInvoiceCode());
                    detailErpDto2.setOrgId(new BigDecimal(detail.getOuId()));
                    detailErpDto2.setLineTypeLookUpCode("ITEM");

                    //罚扣发票金额*-1
                    if (Constants.Prefix.INVOICE_INV002.equals(detail.getAttribute())) {
                        detailErpDto2.setAmount(new BigDecimal(-1).multiply(detail.getTaxAmount()));//行金额，含税
                    } else {
                        detailErpDto2.setAmount(detail.getTaxAmount());//行金额，含税
                    }
                    detailErpDto2.setDescription(remark);
                    this.getAccountGroupDebitOfBusiSceneNonSaleDetail(detail.getOuId(),detail.getTaxRate().toString(), detailErpDto2);
                    detailErpDtoList.add(detailErpDto2);
                    //更新发票行入账科目
                    paymentInvoiceDetailService.updateInvoiceAccountingSubject(detail.getId(),accountingSubjectProject);
                }
            }else if(Objects.equals(accountEntryType, PaymentInvoiceAccountEntryTypeEnum.NO_INVOICE_PENALTY.getCode())){
                PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(dto.getPurchaseContractId());
                String code = purchaseContract.getCode();
                //设置币种
                erpDto.setCurrencyCode(purchaseContract.getCurrency());
                //获取关联罚扣信息
                List<PurchaseContractPunishmentVo> purchaseContractPunishments = punishmentExtMapper.selectListByPaytmentInvoiceId(dto.getId());
                if(ListUtils.isNotEmpty(purchaseContractPunishments)){
                    for (PurchaseContractPunishmentVo punishment : purchaseContractPunishments) {

                        PaymentInvoiceDetailErpDto detailErpDto = new PaymentInvoiceDetailErpDto();
                        detailErpDto.setId(punishment.getId() + "");
                        detailErpDto.setInvoiceNum(dto.getApInvoiceCode());
                        detailErpDto.setOrgId(new BigDecimal(punishment.getOuId()));
                        detailErpDto.setLineTypeLookUpCode("ITEM");
                        detailErpDto.setAmount(punishment.getAmount().multiply(new BigDecimal(-1)));
                        detailErpDto.setDescription("罚扣编号 " + punishment.getCode());

                        detailErpDto.setSegment1(paymentInvoiceService.getBuyerNumberByOu(punishment.getOuId()));
                        detailErpDto.setSegment2("0");
                        detailErpDto.setSegment3(punishment.getAccountGroupDebit().substring(9, 19));
                        detailErpDto.setSegment4("0");
                        detailErpDto.setSegment5("0");
                        detailErpDto.setSegment6("0");
                        detailErpDto.setSegment7("0");

                        detailErpDtoList.add(detailErpDto);

                        //税额行信息
                        PaymentInvoiceDetailErpDto detailErpDto2 = new PaymentInvoiceDetailErpDto();
                        detailErpDto2.setId(punishment.getId() + "");
                        detailErpDto2.setInvoiceNum(dto.getApInvoiceCode());
                        detailErpDto2.setOrgId(new BigDecimal(punishment.getOuId()));
                        detailErpDto2.setLineTypeLookUpCode("ITEM");
                        detailErpDto2.setAmount(BigDecimal.ZERO);
                        detailErpDto2.setDescription("罚扣编号 "+ punishment.getCode());
                        this.getAccountGroupDebitOfBusiSceneNonSaleDetail(punishment.getOuId(), punishment.getTaxRate(), detailErpDto2);

                        detailErpDtoList.add(detailErpDto2);

                        //更新罚扣入账科目
                        purchaseContractPunishmentService.updateInvoiceAccountingSubject(punishment.getId(),punishment.getOuId(),punishment.getAccountGroupDebit());

                        logger.info("应付发票写入,罚扣不开票入账 item1:{}", JSON.toJSONString(detailErpDto));
                       /* logger.info("应付发票写入,罚扣不开票入账 item2:{}",JSON.toJSONString(detailErpDto2));*/
                    }
                }
            }

            if (paymentApply != null) {
                PurchaseContract purchaseContract = purchaseContractMapper.selectByPrimaryKey(dto.getPurchaseContractId());
                String code = purchaseContract.getCode();
                //不含税罚扣不开票行信息
                PaymentPenaltyProfitExample penaltyProfitExample = new PaymentPenaltyProfitExample();
                penaltyProfitExample.createCriteria().andPaymentApplyIdEqualTo(paymentApply.getId())
                        .andPayPenaltyTypeEqualTo(Constants.Prefix.PENALTY_TYPE_FK0001).andDeletedFlagEqualTo(0);
                List<PaymentPenaltyProfit> penaltyProfits = paymentPenaltyProfitMapper.selectByExample(penaltyProfitExample);
                for (PaymentPenaltyProfit penaltyProfit : penaltyProfits) {
                    PaymentInvoiceDetailErpDto detailErpDto = new PaymentInvoiceDetailErpDto();
                    detailErpDto.setId(penaltyProfit.getId() + "");
                    detailErpDto.setInvoiceNum(dto.getApInvoiceCode());
                    detailErpDto.setOrgId(new BigDecimal(paymentApply.getOuId()));
                    detailErpDto.setLineTypeLookUpCode("ITEM");
                    //罚扣发票金额*-1
                    detailErpDto.setAmount(new BigDecimal(-1).multiply(penaltyProfit.getAmount().subtract(null != penaltyProfit.getTaxAmount() ? penaltyProfit.getTaxAmount() : BigDecimal.ZERO)));//行金额，不含税
                    //查询组织参数配置--区分含税不含税
                    this.checkBusiSceneNonSale(paymentApply.getOuId(), detailErpDto, code);
                    detailErpDto.setDescription("罚扣类型");
                    detailErpDtoList.add(detailErpDto);

                }
            }
            erpDto.setDetailErpDtos(detailErpDtoList);

            // 设置汇率
            PurchaseContract contract = purchaseContractMapper.selectByPrimaryKey(dto.getPurchaseContractId());
            //查询当前业务实体下对应的本位币
            List<OrganizationRel> organizationRels = basedataExtService.queryByOuId(contract.getOuId());
            if (ListUtils.isNotEmpty(organizationRels)) {
                if (null == organizationRels.get(0).getCurrency()) {
                    throw new BizException(Code.ERROR, String.format("请配置当前业务实体的本位币，ouId=%s", contract.getOuId()));
                }
            } else {
                throw new BizException(Code.ERROR, String.format("请配置当前业务实体的本位币，ouId=%s", contract.getOuId()));
            }
            //非本位币才设值
            if (!Objects.equals(erpDto.getCurrencyCode(), organizationRels.get(0).getCurrency())) {
                if (StringUtils.isEmpty(contract.getConversionType())) {
                    throw new BizException(Code.ERROR, String.format("发票信息ID:%s外币发票的汇率类型不能为空，", dto.getId()));
                }
                erpDto.setExchangeRateType("Corporate".equals(contract.getConversionType()) ? "公司" : contract.getConversionType());  //汇率类型
                erpDto.setExchangeRateDate(DateUtils.formatDate(contract.getConversionDate())); //汇率日期
                if("用户".equals(erpDto.getExchangeRateType())) {
                    erpDto.setExchangeRate(contract.getConversionRate()); //汇率，公司留空
                }
            }
            dtoList.add(erpDto);
        }
        if (ListUtils.isNotEmpty(dtoList)) {
            return sdpService.callErpPaymentInvoice(dtoList);
        }
        return new EsbResponse();
    }

    /**
     * 回调.
     *
     * @param resendExecute
     */
    @Override
    public void callback(ResendExecute resendExecute) {
        logger.info("应付发票写入开始：" + resendExecute);
        String applyNo = resendExecute.getApplyNo();
        Assert.notNull(applyNo, "applyNo不能为空");
        List<PaymentInvoiceErpDto> dtoList = new ArrayList<>();
        PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(Long.parseLong(applyNo));
        if (!Objects.equals(resendExecute.getResponCode(), ResponseCodeEnums.SUCESS.getCode())) {
            if (paymentInvoice.getPaymentApplyId() != null) {
                PaymentApply paymentApply = paymentApplyService.findById(paymentInvoice.getPaymentApplyId());
                paymentApply.setEsbStatus(PaymentApplyEsbStatus.ABNORMAL.getCode());
                paymentApply.setErpMsg(resendExecute.getResponMsg());
                paymentApply.setUpdateAt(new Date());
                paymentApplyService.update(paymentApply);
            }
            paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.CANCEL.code());//ERP同步状态(0-验证中1-已验证2-已取消)
            paymentInvoice.setErpMsg(resendExecute.getResponMsg());
            paymentInvoiceMapper.updateByPrimaryKey(paymentInvoice);
        }
    }

    @Override
    public void callback(List resendExecutes) {
        logger.info("应付发票批量写入开始：" + resendExecutes);
        List<ResendExecute> list = BeanConverter.copy(resendExecutes, ResendExecute.class);
        for (ResendExecute execute : list) {
            String applyNo = execute.getApplyNo();
            Assert.notNull(applyNo, "applyNo不能为空");
            List<PaymentInvoiceErpDto> dtoList = new ArrayList<>();
            PaymentInvoice paymentInvoice = paymentInvoiceMapper.selectByPrimaryKey(Long.parseLong(applyNo));
            if (!Objects.equals(execute.getResponCode(), ResponseCodeEnums.SUCESS.getCode())) {
                if (paymentInvoice.getPaymentApplyId() != null) {
                    PaymentApply paymentApply = paymentApplyService.findById(paymentInvoice.getPaymentApplyId());
                    paymentApply.setEsbStatus(PaymentApplyEsbStatus.ABNORMAL.getCode());
                    paymentApply.setErpMsg(execute.getResponMsg());
                    paymentApply.setUpdateAt(new Date());
                    paymentApplyService.update(paymentApply);
                }
                paymentInvoice.setErpStatus(PaymentInvoiceErpStatusEnum.CANCEL.code());//ERP同步状态(0-验证中1-已验证2-已取消)
                paymentInvoice.setErpMsg(execute.getResponMsg());
                paymentInvoiceMapper.updateByPrimaryKey(paymentInvoice);
            }
        }
    }

    /**
     * 查询非销售业务场景配置--区分含税不含税
     *
     * @param ouId
     */
    public String checkBusiSceneNonSale(Long ouId, PaymentInvoiceDetailErpDto detailErpDto, String code) {
        MaterialOutsourcingContractConfig materialOutsourcingContractConfig = materialOutsourcingContractConfigService.selectByPurchaseContractCode(code);
        if (null == materialOutsourcingContractConfig){
            throw new BizException(Code.ERROR, "采购合同配置未维护，请先维护再推送，谢谢！");
        }
        String accountGroupDebit = materialOutsourcingContractConfig.getAccountGroupDebit();
        detailErpDto.setSegment1(paymentInvoiceService.getBuyerNumberByOu(ouId));
        detailErpDto.setSegment2("0");
        String[] segments = accountGroupDebit.split("\\.");
        detailErpDto.setSegment3(segments[2]);
        detailErpDto.setSegment4("0");
        detailErpDto.setSegment5("0");
        detailErpDto.setSegment6("0");
        detailErpDto.setSegment7("0");
        String codeResult = String.join(".",
                detailErpDto.getSegment1(),
                detailErpDto.getSegment2(),
                detailErpDto.getSegment3(),
                detailErpDto.getSegment4(),
                detailErpDto.getSegment5(),
                detailErpDto.getSegment6(),
                detailErpDto.getSegment7());
        return codeResult;
    }

    /**
     * 获取非销售业务场景详情的借方科目组
     * 根据组织单元ID和税率获取采购合同发票入账的成本负债科目配置
     *
     * @param ouId 组织单元ID
     * @param taxRate 税率（BigDecimal转换的字符串格式，如：3.00、6.00、9.00）
     * @param detailErpDto ERP详情DTO，用于设置segment字段
     * @return 借方科目组代码
     * @throws BizException 当配置未维护或税率不在配置范围内时抛出异常
     */
    private String getAccountGroupDebitOfBusiSceneNonSaleDetail(Long ouId,String taxRate,PaymentInvoiceDetailErpDto detailErpDto){
        logger.info("开始获取非销售业务场景详情的借方科目组，ouId：{}，原始taxRate：{}", ouId, taxRate);

        // 0. 税率格式转换：将BigDecimal字符串转换为百分比格式
        String formattedTaxRate = convertTaxRateToPercentageFormat(taxRate);
        logger.info("税率格式转换结果，原始：{}，转换后：{}", taxRate, formattedTaxRate);

        // 1. 查询采购合同发票入账的业务场景配置
        List<BusiSceneNonSaleDetailDto> busiSceneDetailList = busiSceneNonSaleService.getBusiSceneDetailListByName("采购合同发票入账", ouId);
        logger.info("查询业务场景配置结果，配置数量：{}", busiSceneDetailList != null ? busiSceneDetailList.size() : 0);

        if(ListUtils.isEmpty(busiSceneDetailList)){
            logger.error("采购合同发票入账的成本负债科目配置未维护，ouId：{}", ouId);
            throw new BizException(Code.ERROR, "采购合同发票入账的成本负债科目配置未维护，请先维护再推送，谢谢！");
        }

        // 2. 查找所有标识为AP_2的配置项
        List<BusiSceneNonSaleDetailDto> ap2List = busiSceneDetailList.stream()
                .filter(b -> Objects.equals("AP_2", b.getFlag()))
                .collect(Collectors.toList());
        logger.info("查找AP_2标识的配置项结果，找到配置数量：{}", ap2List.size());

        if(ListUtils.isEmpty(ap2List)){
            logger.error("未找到标识为AP_2的采购合同发票入账配置，ouId：{}", ouId);
            throw new BizException(Code.ERROR, "采购合同发票入账的成本负债科目配置未维护，请先维护再推送，谢谢！");
        }

        // 3. 遍历AP_2配置，查找匹配税率的配置
        for (int i = 0; i < ap2List.size(); i++) {
            BusiSceneNonSaleDetailDto ap2Config = ap2List.get(i);
            logger.info("检查第{}个AP_2配置，配置ID：{}", i + 1, ap2Config.getId());

            String taxRateSet = ap2Config.getTaxRateSet();
            logger.info("第{}个AP_2配置的税率配置集合：{}", i + 1, taxRateSet);

            if(StringUtils.isEmpty(taxRateSet)){
                logger.warn("第{}个AP_2配置的税率配置集合为空，跳过该配置", i + 1);
                continue;
            }

            // 4. 验证税率是否在当前配置范围内
            //taxRateSet 格式:3%,6%,9%
            String[] taxRateSetArray = taxRateSet.split(",");
            logger.info("第{}个AP_2配置解析税率数组：{}，待验证税率：{}", i + 1, Arrays.toString(taxRateSetArray), formattedTaxRate);

            if(Arrays.asList(taxRateSetArray).contains(formattedTaxRate)){
                // 5. 找到匹配的配置，返回借方科目组
                String accountGroupDebit = ap2Config.getAccountGroupDebit();
                logger.info("在第{}个AP_2配置中找到匹配税率，成功获取借方科目组：{}", i + 1, accountGroupDebit);
                detailErpDto.setSegment1(paymentInvoiceService.getBuyerNumberByOu(ouId));
                detailErpDto.setSegment2("0");
                String[] segments = accountGroupDebit.split("\\.");
                detailErpDto.setSegment3(segments[2]);
                detailErpDto.setSegment4("0");
                detailErpDto.setSegment5("0");
                detailErpDto.setSegment6("0");
                detailErpDto.setSegment7("0");
                return accountGroupDebit;
            } else {
                logger.info("第{}个AP_2配置的税率范围不包含当前税率，继续检查下一个配置", i + 1);
            }
        }

        // 6. 所有AP_2配置都不匹配当前税率
        logger.error("所有AP_2配置都不包含当前税率，ouId：{}，原始taxRate：{}，转换后taxRate：{}，AP_2配置数量：{}", ouId, taxRate, formattedTaxRate, ap2List.size());
        throw new BizException(Code.ERROR, "没有符合税率的科目，请检查");
    }

    /**
     * 将BigDecimal税率字符串转换为百分比格式
     * 例如：将"3.00"转换为"3%"，将"6.0"转换为"6%"
     *
     * @param taxRateStr BigDecimal转换的税率字符串
     * @return 百分比格式的税率字符串
     */
    private String convertTaxRateToPercentageFormat(String taxRateStr) {
        if (StringUtils.isEmpty(taxRateStr)) {
            logger.warn("税率字符串为空，返回默认值0%");
            return "0%";
        }

        try {
            // 将字符串转换为BigDecimal，去除尾随零，然后转换为整数字符串
            BigDecimal taxRate = new BigDecimal(taxRateStr);
            // 去除尾随零并转换为整数
            int intTaxRate = taxRate.stripTrailingZeros().intValue();
            String result = intTaxRate + "%";
            logger.debug("税率转换：{} -> {}", taxRateStr, result);
            return result;
        } catch (NumberFormatException e) {
            logger.error("税率格式转换失败，无效的税率字符串：{}，错误：{}", taxRateStr, e.getMessage());
            return "0%";
        }
    }
}
