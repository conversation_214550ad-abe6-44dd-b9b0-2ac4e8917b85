package com.midea.pam.ctc.service.impl;

import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.entity.Contract;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBackUp;
import com.midea.pam.common.ctc.entity.ProjectBudgetFee;
import com.midea.pam.common.ctc.entity.ProjectBudgetFeeBackUp;
import com.midea.pam.common.ctc.entity.ProjectBudgetFeeExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetHuman;
import com.midea.pam.common.ctc.entity.ProjectBudgetHumanBackUp;
import com.midea.pam.common.ctc.entity.ProjectBudgetHumanExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterial;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialBackUp;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterialExample;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravel;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravelBackUp;
import com.midea.pam.common.ctc.entity.ProjectBudgetTravelExample;
import com.midea.pam.common.ctc.entity.ProjectDeliveries;
import com.midea.pam.common.ctc.entity.ProjectDeliveriesBackUp;
import com.midea.pam.common.ctc.entity.ProjectDeliveriesExample;
import com.midea.pam.common.ctc.entity.ProjectIncomeCostPlan;
import com.midea.pam.common.ctc.entity.ProjectIncomeCostPlanBackUp;
import com.midea.pam.common.ctc.entity.ProjectIncomeCostPlanExample;
import com.midea.pam.common.ctc.entity.ProjectMember;
import com.midea.pam.common.ctc.entity.ProjectMemberBackUp;
import com.midea.pam.common.ctc.entity.ProjectMemberExample;
import com.midea.pam.common.ctc.entity.ProjectMilepost;
import com.midea.pam.common.ctc.entity.ProjectMilepostBackUp;
import com.midea.pam.common.ctc.entity.ProjectMilepostExample;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.ctc.entity.ProjectProfitBackUp;
import com.midea.pam.common.ctc.entity.ProjectProfitExample;
import com.midea.pam.common.ctc.entity.ProjectReopenHeader;
import com.midea.pam.common.ctc.entity.ProjectReopenHeaderExample;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.TicketTasks;
import com.midea.pam.common.ctc.entity.TicketTasksExample;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CostMethod;
import com.midea.pam.common.enums.EmailStatus;
import com.midea.pam.common.enums.IncomePoint;
import com.midea.pam.common.enums.MilepostStatus;
import com.midea.pam.common.enums.NoticeBusinessType;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ProjectReopenEnum;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.ProjectSyncStatus;
import com.midea.pam.common.enums.TicketTasksEnum;
import com.midea.pam.common.enums.projectReopenHeaderStatusEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.FormInstanceExample;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.contract.service.ContractService;
import com.midea.pam.ctc.ext.service.config.HandleDispatcher;
import com.midea.pam.ctc.mapper.ContractMapper;
import com.midea.pam.ctc.mapper.FormInstanceExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.ProjectBackUpMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetFeeBackUpMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetFeeMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetHumanBackUpMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetHumanMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetMaterialBackUpMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetMaterialMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetTravelBackUpMapper;
import com.midea.pam.ctc.mapper.ProjectBudgetTravelMapper;
import com.midea.pam.ctc.mapper.ProjectDeliveriesBackUpMapper;
import com.midea.pam.ctc.mapper.ProjectDeliveriesMapper;
import com.midea.pam.ctc.mapper.ProjectIncomeCostPlanBackUpMapper;
import com.midea.pam.ctc.mapper.ProjectIncomeCostPlanChangeHistoryMapper;
import com.midea.pam.ctc.mapper.ProjectIncomeCostPlanExtMapper;
import com.midea.pam.ctc.mapper.ProjectIncomeCostPlanMapper;
import com.midea.pam.ctc.mapper.ProjectMapper;
import com.midea.pam.ctc.mapper.ProjectMemberBackUpMapper;
import com.midea.pam.ctc.mapper.ProjectMemberMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostBackUpMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostExtMapper;
import com.midea.pam.ctc.mapper.ProjectMilepostMapper;
import com.midea.pam.ctc.mapper.ProjectProfitBackUpMapper;
import com.midea.pam.ctc.mapper.ProjectProfitMapper;
import com.midea.pam.ctc.mapper.ProjectReopenHeaderMapper;
import com.midea.pam.ctc.mapper.ProjectTerminationCheckRelExtMapper;
import com.midea.pam.ctc.mapper.ProjectTypeMapper;
import com.midea.pam.ctc.mapper.TicketTasksMapper;
import com.midea.pam.ctc.service.CodeRuleService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.NoticeService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectReopenService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectTypeService;
import com.midea.pam.ctc.service.WbsPushToErpService;
import com.midea.pam.ctc.service.event.ProjectSynchroEvent;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description
 * Created by lintx
 * Date 2021/12/22 17:49
 */
public class ProjectReopenServiceImpl extends ProjectServiceImpl implements ProjectReopenService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProjectBusinessServiceImpl.class);

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectBusinessService projectBusinessService;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private ProjectTypeMapper projectTypeMapper;

    @Resource
    private ContractService contractService;

    @Resource
    private ContractMapper contractMapper;

    @Resource
    private ProjectBackUpMapper projectBackUpMapper;

    @Resource
    private ProjectBudgetFeeBackUpMapper projectBudgetFeeBackUpMapper;

    @Resource
    private ProjectBudgetFeeMapper projectBudgetFeeMapper;

    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;

    @Resource
    private ProjectBudgetHumanMapper projectBudgetHumanMapper;

    @Resource
    private ProjectBudgetHumanBackUpMapper projectBudgetHumanBackUpMapper;

    @Resource
    private ProjectBudgetMaterialBackUpMapper projectBudgetMaterialBackUpMapper;

    @Resource
    private ProjectBudgetMaterialMapper projectBudgetMaterialMapper;

    @Resource
    private ProjectBudgetTravelBackUpMapper projectBudgetTravelBackUpMapper;

    @Resource
    private ProjectBudgetTravelMapper projectBudgetTravelMapper;

    @Resource
    private ProjectMemberBackUpMapper projectMemberBackUpMapper;

    @Resource
    private ProjectMemberMapper projectMemberMapper;

    @Resource
    private ProjectMilepostBackUpMapper projectMilepostBackUpMapper;

    @Resource
    private ProjectDeliveriesBackUpMapper projectDeliveriesBackUpMapper;

    @Resource
    private ProjectDeliveriesMapper projectDeliveriesMapper;

    @Resource
    private ProjectIncomeCostPlanBackUpMapper projectIncomeCostPlanBackUpMapper;

    @Resource
    private ProjectIncomeCostPlanMapper projectIncomeCostPlanMapper;

    @Resource
    private ProjectIncomeCostPlanChangeHistoryMapper projectIncomeCostPlanChangeHistoryMapper;

    @Resource
    private ProjectProfitBackUpMapper projectProfitBackUpMapper;

    @Resource
    private ProjectProfitMapper projectProfitMapper;

    @Resource
    private ProjectMilepostMapper projectMilepostMapper;

    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;

    @Resource
    private ProjectIncomeCostPlanExtMapper projectIncomeCostPlanExtMapper;

    @Resource
    private WbsPushToErpService wbsPushToErpService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private NoticeService noticeService;
    @Resource
    private ProjectMilepostExtMapper projectMilepostExtMapper;

    @Resource
    private TicketTasksMapper ticketTasksMapper;
    @Resource
    private ProjectTerminationCheckRelExtMapper projectTerminationCheckRelExtMapper;

    @Resource
    private ProjectReopenHeaderMapper projectReopenHeaderMapper;

    @Resource
    private FormInstanceExtMapper formInstanceExtMapper;

    @Value("${route.projectUrl}")
    private String projectUrl;

    private static Logger logger = LoggerFactory.getLogger(ProjectReopenServiceImpl.class);


    @Override
    public ProjectDto getBaseInfoByProjectId(Long projectId) {
        ProjectDto projectDto = projectBusinessService.getBaseInfoById(projectId, "");
        return projectDto;
    }

    @Override
    public ProjectDto save(ProjectDto projectDto) {
        Assert.notNull(projectDto, "保存的项目不能为空");

        Project project = new Project();
        project.setReopenStatus(ProjectReopenEnum.REOPEN.getCode());
        project.setReopenReason(projectDto.getReopenReason());
        project.setReopenImportAnnex(projectDto.getReopenImportAnnex());
        project.setId(projectDto.getId());
        projectService.updateByPrimaryKeySelective(project);
        ProjectDto dto = projectBusinessService.getBaseInfoById(projectDto.getId(), "");

        //保存项目重新打开更头表
        ProjectReopenHeader projectReopenHeader = new ProjectReopenHeader();
        projectReopenHeader.setProjectId(projectDto.getId());
        projectReopenHeader.setStatus(projectReopenHeaderStatusEnum.DRAFT.getCode());
        projectReopenHeader.setDeletedFlag(false);
        projectReopenHeaderMapper.insert(projectReopenHeader);
        Long projectReopenHeaderId = projectReopenHeader.getId();
        dto.setId(projectReopenHeaderId);
        return dto;
    }

    @Override
    public void updateProjectDesign(ProjectDto projectDto, Integer status) {
//        projectBusinessService.updateProjectDesign(projectDto, status);
    }

    /**
     * 审批通过回调.
     *
     * @param formInstanceId 表单实例id-项目id
     *                       结项的项目：最后一个“通过”的里程碑变更为进行中，对应的里程碑交付的审批流删除（以便可以重新做结项里程碑交付）
     *                       终止的项目：所有“终止”状态的里程碑变更为进行中
     *                       成本方法为：成本百分比并且收入确认时点为：月度确认的项目，收入成本计划生成一个审批通过日期当月的、结转状态为：未结转的节点（如该节点已存在，不需要重复生成）
     *                       (通过已有调度任务实现，不通过流程审批通过触发，而是调度任务查到符合条件的触发这个生成的动作)
     *                       项目号写入EMS（接口卡：PAM-EMS-004 项目号写入），项目状态传：4-项目进行中
     *                       对应的项目经理和项目财务收到邮件提醒，邮件内容如下：
     *                       邮件标题：PAM项目重新打开：xxx_yyyy（xxx为项目号，yyyy为项目名称）
     *                       邮件内容：xxx_yyyy 项目重新打开流程已审批通过，请关注后续的项目跟进
     */
    @Override
    @Transactional
    public void approvedHandler(Long formInstanceId, Long companyId) {
        final Project project = selectByPrimaryKey(formInstanceId);
        Assert.notNull(project, "项目不存在");

        project.setStatus(ProjectStatus.APPROVALED.getCode());
        project.setEmsStatus(2);//ems同步中
        updateByPrimaryKeySelective(project);

        // 更新里程碑数据
        updateProjectMilepost(formInstanceId);
        applicationEventPublisher.publishEvent(new ProjectSynchroEvent(this, formInstanceId));

        // 生成收入成本方法：“成本百分比+月度”，追加月度收入节点
        projectBusinessService.addLatestIncomeMonth(project, new Date());

        // 项目的工单任务状态“已完成”改为“已发布”
        TicketTasksExample tasksExample = new TicketTasksExample();
        tasksExample.createCriteria().andProjectIdEqualTo(formInstanceId).andDeletedFlagEqualTo(DeletedFlag.VALID.code());
        List<TicketTasks> ticketTasksList = ticketTasksMapper.selectByExample(tasksExample);
        for (TicketTasks ticketTasks : ticketTasksList) {
            ticketTasks.setTaskStatus(TicketTasksEnum.PUBLISHED.code());
            ticketTasksMapper.updateByPrimaryKeySelective(ticketTasks);
        }

        // 项目号写入EMS
        String url = "";
        String res = "";
        final Map<String, Object> query = new HashMap<>();
        if (companyId == null) {
            companyId = SystemContext.getUnitId();
        }

        OrgCustomDictOrgFrom orgCustomDictOrgFrom = OrgCustomDictOrgFrom.getOrgCustomDictOrgFrom("company");
        List<OrganizationCustomDict> customDictList = organizationCustomDictService.queryByOrdId(companyId, "EMS写入", orgCustomDictOrgFrom);
        if (ListUtils.isNotEmpty(customDictList)) {
            //项目号同步ems插入resend表
            wbsPushToErpService.wbsPushToErp("project", project.getId());
        }

        // 当项目最后一个收入节点的里程碑交付审批已通过，项目变为收入确认已完成状态，通过接口卡statue字段，传7给到EMS系统
        // 当项目已变为收入确认已完成状态，用户又新增收入节点导致最后一个收入节点里程碑已交付这一条件不成立时，再次传输4给到EMS系统
        try {
            Boolean completed = projectService.checkIncomeCompleted(project.getId());
            if (!completed) {
                // 收入确认未完成
                HandleDispatcher.syncProjectStatusToEms(project.getId(), ProjectSyncStatus.APPROVALED.getCode());
            }
        } catch (Exception e) {
            logger.error("项目重新打开同步项目状态到EMS系统失败", e);
        }

        // 删除结项流程实例
        deleteLastMilepostWorkFlow(formInstanceId);

        // 更新终止记录
        deleteTerminiation(formInstanceId);

        // 发送邮件提醒
        this.sendEmailInfo(project);
    }

    private void deleteTerminiation(Long projectId) {
        // 更新终止记录
        projectTerminationCheckRelExtMapper.deleteTerminiationRecord(projectId);

        // 删除终止流程实例
        projectTerminationCheckRelExtMapper.deleteTerminiationAppWorkFlow(projectId);
    }

    /**
     * 删除项目最后一个主里程碑对应的流程实例
     *
     * @param projectId 项目id
     */
    private void deleteLastMilepostWorkFlow(Long projectId) {
        ProjectMilepostExample example = new ProjectMilepostExample();
        example.createCriteria()
                .andProjectIdEqualTo(projectId)
                .andHelpFlagEqualTo(Boolean.FALSE)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        example.setOrderByClause("order_num desc");
        List<ProjectMilepost> projectMileposts = projectMilepostMapper.selectByExample(example);
        if (!projectMileposts.isEmpty()) {
            projectMilepostExtMapper.deleteWorkFlowByMilepostId(projectMileposts.get(0).getId());
        }
    }

    private void sendEmailInfo(Project project) {
        Email email = new Email();
        String subject = "PAM项目重新打开：" + project.getCode() + "_" + project.getName();
        email.setSubject(subject);
        String content = buildHeader(project.getId(), project.getName(), project.getCode());
        email.setContent(content);
        email.setLanguage("zh-CN");
        email.setDeletedFlag(Boolean.FALSE);
        email.setStatus(EmailStatus.TO_DO.getCode());
        UserInfo manager = CacheDataUtils.findUserById(project.getManagerId());
        UserInfo financial = CacheDataUtils.findUserById(project.getFinancial());
        if ((manager == null || StringUtils.isEmpty(manager.getEmail()))
                && (financial == null || StringUtils.isEmpty(financial.getEmail()))) {
            logger.error("项目经理和财务都不存在，项目id：{}", project.getId());
            return;
        }
        String receiver = null;
        if (manager != null) {
            receiver = manager.getEmail();
            if (financial != null) {
                receiver = receiver + "," + financial.getEmail();
            }
        } else {
            receiver = financial.getEmail();
        }
        email.setReceiver(receiver);
        email.setCreateAt(new Date());
        email.setFromAddress("pam");
        email.setBusinessType(NoticeBusinessType.PROJECT_REOPEN_REMIND.getType());
        logger.info("PAM项目重新打开提醒邮件待推送数据：{}", email.toString());
        // 邮件推送
        noticeService.sendMail(email);
    }

    private String buildHeader(Long projectId, String projectName, String projectCode) {
        String url = projectUrl + projectId;
        String urlStr = "<a href='" + url + "' target='_blank'>" + projectCode + "_" + projectName + "</a>";
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        urlStr = "<div style='font-size: 12px;'>" + urlStr + " 项目重新打开流程已审批通过，请关注后续的项目跟进!</div><br/>";
        sb.append(urlStr);
        sb.append("</html>");
        return sb.toString();
    }

    private String addMonth(String applyMonth) {
        if (StringUtils.isEmpty(applyMonth)) {
            return null;
        }

        String[] split = applyMonth.split("-");
        if (split.length == 2) {
            Integer year = Integer.valueOf(split[0]);
            Integer month = Integer.valueOf(split[1]);

            if (month == 12) {
                year = year + 1;
                month = 1;
            } else {
                month = month + 1;
            }
            String monthStr = month < 10 ? "0" + month : "" + month;
            return year + "-" + monthStr;
        }
        return null;
    }

    public void updateProjectMilepost(final Long projectId) {
        ProjectMilepostExample example = new ProjectMilepostExample();
        ProjectMilepostExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(false);
        criteria.andProjectIdEqualTo(projectId);
        example.setOrderByClause(" order_num desc");
        List<ProjectMilepost> list = projectMilepostMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            boolean first = true;
            for (ProjectMilepost projectMilepost : list) {
                /**
                 * 结项的项目：最后一个“通过”的里程碑变更为进行中，对应的里程碑交付的审批流删除;
                 * 终止的项目：所有“终止”状态的里程碑变更为进行中;
                 */
                if (first) {
                    first = false;
                    projectMilepost.setStatus(MilepostStatus.PROCESSING.getCode());
                } else if (MilepostStatus.PASSED.getCode().equals(list.get(0).getStatus()) || MilepostStatus.MILEPOS_TERMINAL.getCode().equals(projectMilepost.getStatus())) {
                    /* 实际开始时间手动录入，实际开始时间=null，里程碑状态=未开始，否则里程碑状态=进行中 */
                    ProjectMilepostDto.initStartStatus(projectMilepost);
                }
                projectMilepost.setUpdateAt(new Date());
                projectMilepost.setUpdateBy(SystemContext.getUserId());
                projectMilepostMapper.updateByPrimaryKey(projectMilepost);
            }
        }
    }

    private void saveBackUp(Long formInstanceId, Project project) {
        //保存项目快照
        ProjectBackUp projectBackUp = new ProjectBackUp();
        projectBackUp = com.midea.pam.ctc.common.utils.BeanConverter.copyProperties(project, projectBackUp);
        projectBackUp.setCreateAt(new Date());
        projectBackUp.setStatus(4);
        projectBackUp.setUpdateBy(null);
        projectBackUp.setUpdateAt(null);
        projectBackUp.setCreateBy(SystemContext.getUserId());
        projectBackUpMapper.insertSelective(projectBackUp);

        //保存项目预算快照
        ProjectBudgetFeeExample projectBudgetFeeExample = new ProjectBudgetFeeExample();
        projectBudgetFeeExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(formInstanceId);
        List<ProjectBudgetFee> projectBudgetFees = projectBudgetFeeMapper.selectByExample(projectBudgetFeeExample);
        if (ListUtils.isNotEmpty(projectBudgetFees)) {
            projectBudgetFees.forEach(p -> {
                ProjectBudgetFeeBackUp projectBudgetFeeBackUp = com.midea.pam.ctc.common.utils.BeanConverter.copyProperties(p,
                        ProjectBudgetFeeBackUp.class);
                projectBudgetFeeBackUp.setCreateAt(new Date());
                projectBudgetFeeBackUp.setUpdateBy(null);
                projectBudgetFeeBackUp.setUpdateAt(null);
                projectBudgetFeeBackUp.setCreateBy(SystemContext.getUserId());
                projectBudgetFeeBackUpMapper.insertSelective(projectBudgetFeeBackUp);
            });

        }

        //保存项目人力预算快照
        ProjectBudgetHumanExample projectBudgetHumanExample = new ProjectBudgetHumanExample();
        projectBudgetHumanExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(formInstanceId);
        List<ProjectBudgetHuman> projectBudgetHumen = projectBudgetHumanMapper.selectByExample(projectBudgetHumanExample);
        if (ListUtils.isNotEmpty(projectBudgetHumen)) {
            projectBudgetHumen.forEach(p -> {
                ProjectBudgetHumanBackUp projectBudgetHumanBackUp = com.midea.pam.ctc.common.utils.BeanConverter.copyProperties(p,
                        ProjectBudgetHumanBackUp.class);
                projectBudgetHumanBackUp.setCreateAt(new Date());
                projectBudgetHumanBackUp.setUpdateBy(null);
                projectBudgetHumanBackUp.setUpdateAt(null);
                projectBudgetHumanBackUp.setCreateBy(SystemContext.getUserId());
                projectBudgetHumanBackUpMapper.insertSelective(projectBudgetHumanBackUp);
            });
        }

        //保存项目物料预算快照
        ProjectBudgetMaterialExample projectBudgetMaterialExample = new ProjectBudgetMaterialExample();
        projectBudgetMaterialExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(formInstanceId);
        List<ProjectBudgetMaterial> projectBudgetMaterials = projectBudgetMaterialMapper.selectByExample(projectBudgetMaterialExample);
        if (ListUtils.isNotEmpty(projectBudgetMaterials)) {
            projectBudgetMaterials.forEach(p -> {
                ProjectBudgetMaterialBackUp projectBudgetMaterialBackUp = com.midea.pam.ctc.common.utils.BeanConverter.copyProperties(p,
                        ProjectBudgetMaterialBackUp.class);
                projectBudgetMaterialBackUp.setCreateAt(new Date());
                projectBudgetMaterialBackUp.setUpdateBy(null);
                projectBudgetMaterialBackUp.setUpdateAt(null);
                projectBudgetMaterialBackUp.setCreateBy(SystemContext.getUserId());
                projectBudgetMaterialBackUpMapper.insertSelective(projectBudgetMaterialBackUp);
            });
        }

        //保存项目差旅预算快照
        ProjectBudgetTravelExample budgetTravelExample = new ProjectBudgetTravelExample();
        budgetTravelExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(formInstanceId);
        List<ProjectBudgetTravel> projectBudgetTravels = projectBudgetTravelMapper.selectByExample(budgetTravelExample);
        if (ListUtils.isNotEmpty(projectBudgetTravels)) {
            projectBudgetTravels.forEach(p -> {
                ProjectBudgetTravelBackUp projectBudgetTravelBackUp = com.midea.pam.ctc.common.utils.BeanConverter.copyProperties(p,
                        ProjectBudgetTravelBackUp.class);
                projectBudgetTravelBackUp.setCreateAt(new Date());
                projectBudgetTravelBackUp.setUpdateBy(null);
                projectBudgetTravelBackUp.setUpdateAt(null);
                projectBudgetTravelBackUp.setCreateBy(SystemContext.getUserId());
                projectBudgetTravelBackUpMapper.insertSelective(projectBudgetTravelBackUp);
            });
        }


        //保存项目成员快照
        ProjectMemberExample projectMemberExample = new ProjectMemberExample();
        projectMemberExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(formInstanceId);
        List<ProjectMember> projectMembers = projectMemberMapper.selectByExample(projectMemberExample);
        if (ListUtils.isNotEmpty(projectMembers)) {
            projectMembers.forEach(p -> {
                ProjectMemberBackUp projectMemberBackUp = com.midea.pam.ctc.common.utils.BeanConverter.copyProperties(p, ProjectMemberBackUp.class);
                projectMemberBackUp.setCreateAt(new Date());
                projectMemberBackUp.setUpdateBy(null);
                projectMemberBackUp.setUpdateAt(null);
                projectMemberBackUp.setCreateBy(SystemContext.getUserId());
                projectMemberBackUpMapper.insertSelective(projectMemberBackUp);
            });
        }

        //保存项目里程碑快照
        ProjectMilepostExample milepostExample = new ProjectMilepostExample();
        milepostExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(formInstanceId);
        List<ProjectMilepost> projectMileposts = projectMilepostMapper.selectByExample(milepostExample);
        if (ListUtils.isNotEmpty(projectMileposts)) {
            projectMileposts.forEach(p -> {
                ProjectMilepostBackUp projectMemberBackUp = com.midea.pam.ctc.common.utils.BeanConverter.copyProperties(p,
                        ProjectMilepostBackUp.class);
                projectMemberBackUp.setCreateAt(new Date());
                projectMemberBackUp.setUpdateBy(null);
                projectMemberBackUp.setUpdateAt(null);
                projectMemberBackUp.setCreateBy(SystemContext.getUserId());
                projectMilepostBackUpMapper.insertSelective(projectMemberBackUp);

                //保存项目里程碑交付物快照
                ProjectDeliveriesExample projectDeliveriesExample = new ProjectDeliveriesExample();
                projectDeliveriesExample.createCriteria().andDeletedFlagEqualTo(false).andProjectMilepostIdEqualTo(p.getId());
                List<ProjectDeliveries> projectDeliveries = projectDeliveriesMapper.selectByExample(projectDeliveriesExample);
                if (ListUtils.isNotEmpty(projectDeliveries)) {
                    projectDeliveries.forEach(d -> {
                        ProjectDeliveriesBackUp projectDeliveriesBackUp = com.midea.pam.ctc.common.utils.BeanConverter.copyProperties(d,
                                ProjectDeliveriesBackUp.class);
                        projectDeliveriesBackUp.setCreateAt(new Date());
                        projectDeliveriesBackUp.setUpdateBy(null);
                        projectDeliveriesBackUp.setUpdateAt(null);
                        projectDeliveriesBackUp.setCreateBy(SystemContext.getUserId());
                        projectDeliveriesBackUpMapper.insertSelective(projectDeliveriesBackUp);
                    });
                }
            });
        }

        //保存项目计划成本收入快照
        ProjectIncomeCostPlanExample projectIncomeCostPlanExample = new ProjectIncomeCostPlanExample();
        projectIncomeCostPlanExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(formInstanceId);
        List<ProjectIncomeCostPlan> projectIncomeCostPlans = projectIncomeCostPlanMapper.selectByExample(projectIncomeCostPlanExample);
        if (ListUtils.isNotEmpty(projectIncomeCostPlans)) {
            projectIncomeCostPlans.forEach(p -> {
                ProjectIncomeCostPlanBackUp projectIncomeCostPlanBackUp = com.midea.pam.ctc.common.utils.BeanConverter.copyProperties(p,
                        ProjectIncomeCostPlanBackUp.class);
                projectIncomeCostPlanBackUp.setCreateAt(new Date());
                projectIncomeCostPlanBackUp.setUpdateBy(null);
                projectIncomeCostPlanBackUp.setUpdateAt(null);
                projectIncomeCostPlanBackUp.setCreateBy(SystemContext.getUserId());
                projectIncomeCostPlanBackUpMapper.insertSelective(projectIncomeCostPlanBackUp);
            });
        }

        //保存项目计划成本收入快照
        ProjectProfitExample projectProfitExample = new ProjectProfitExample();
        projectProfitExample.createCriteria().andDeletedFlagEqualTo(false).andProjectIdEqualTo(formInstanceId);
        List<ProjectProfit> projectProfits = projectProfitMapper.selectByExample(projectProfitExample);
        if (ListUtils.isNotEmpty(projectProfits)) {
            projectProfits.forEach(p -> {
                ProjectProfitBackUp projectProfitBackUp = BeanConverter.copyProperties(p, ProjectProfitBackUp.class);
                projectProfitBackUp.setCreateAt(new Date());
                projectProfitBackUp.setUpdateBy(null);
                projectProfitBackUp.setUpdateAt(null);
                projectProfitBackUp.setCreateBy(SystemContext.getUserId());
                projectProfitBackUpMapper.insertSelective(projectProfitBackUp);
            });
        }
    }

    @Override
    public ResponseMap getProjectReopenApp(Long id) {
        ResponseMap result = new ResponseMap();
        result.setStatus("fail");
        Assert.notNull(id, "项目不存在");
        ProjectDto projectDto = projectBusinessService.getBaseInfoById(id, "");

        Map<String, String> headMap = new HashMap<String, String>();
        headMap.put("projectName", projectDto.getName());
        headMap.put("projectCode", projectDto.getCode());
        if (projectDto.getType() != null) {
            ProjectType projectType = projectTypeMapper.selectByPrimaryKey(projectDto.getType());
            if (projectType != null) {
                headMap.put("projectTypeName", projectType.getName());
            } else {
                headMap.put("projectTypeName", null);
            }
        }
        // 项目经理
        if (projectDto.getManagerId() != null) {
            final UserInfo manager = CacheDataUtils.findUserById(projectDto.getManagerId());
            if (manager != null) {
                headMap.put("managerName", manager.getName());
            }
        } else {
            headMap.put("managerName", "");
        }
        // 项目财务
        if (projectDto.getFinancial() != null) {
            final UserInfo financial = CacheDataUtils.findUserById(projectDto.getFinancial());
            if (financial != null) {
                headMap.put("financialName", financial.getName());
            }
        } else {
            headMap.put("financialName", "");
        }

        //合同ID
        Long contractId = projectDto.getContractId();
        if (contractId != null) {
            projectDto.setContractIsView(contractService.isContractIsView(contractId));
            //子合同信息
            Contract contract = contractMapper.selectByPrimaryKey(contractId);
            if (contract != null) {
                headMap.put("contractCode", contract.getCode());
                headMap.put("contractName", contract.getName());
                // 主合同销售经理
                Contract parentContract = contractMapper.selectByPrimaryKey(contract.getParentId());
                if (parentContract.getSalesManager() != null) {
                    projectDto.setParentContractSalesManagerId(parentContract.getSalesManager());
                    final UserInfo userInfo = CacheDataUtils.findUserById(parentContract.getSalesManager());
                    if (userInfo != null) {
                        headMap.put("parentContractSalesManagerName", userInfo.getName());
                    }
                } else {
                    headMap.put("parentContractSalesManagerName", "");
                }
            }
        }

        headMap.put("reopenReason", projectDto.getReopenReason());
        //附件
        headMap.put("reopenImportAnnex", projectDto.getReopenImportAnnex());

        result.setHeadMap(headMap);
        result.setStatus("success");
        result.setMsg("success");
        result.setMsg("请求成功");
        return result;
    }

    @Override
    public Long getProjectIdByReopenHeader(Long projectReopenHeaderId) {
        ProjectReopenHeader projectReopenHeader = projectReopenHeaderMapper.selectByPrimaryKey(projectReopenHeaderId);
        if (null == projectReopenHeader) {
            throw new BizException(Code.ERROR, "项目重新打开变更信息不存在");
        }
        Long projectId = projectReopenHeader.getProjectId();
        return projectId;
    }

    @Override
    public String updateReopenHeaderStatus(ProjectReopenHeader projectReopenHeader) {
        Long id = projectReopenHeader.getId();
        ProjectReopenHeader info = projectReopenHeaderMapper.selectByPrimaryKey(id);
        if (info != null){
            info.setStatus(projectReopenHeader.getStatus());
            info.setUpdateBy(SystemContext.getUserId());
            projectReopenHeaderMapper.updateByPrimaryKey(info);
            return "修改项目重新打开变更信息状态成功";
        }
        return "修改项目重新打开变更信息状态失败";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String initProjectReopenHeader(ProjectReopenHeader projectReopenHeader) {
        //情况1：有传projectId
        if (null != projectReopenHeader.getProjectId()) {
            Long projectId = projectReopenHeader.getProjectId();
            FormInstanceExample example = new FormInstanceExample();
            example.createCriteria().andFormInstanceIdEqualTo(projectId).andFormUrlEqualTo("projectReopenApp");
            List<FormInstance> formInstancesList = formInstanceExtMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(formInstancesList)) {
                List<FormInstance> distinctFormInstances = getFormInstances(formInstancesList);

                if (CollectionUtils.isNotEmpty(distinctFormInstances)) {
                    for (FormInstance formInstance : distinctFormInstances) {
                        Long formInstanceId = formInstance.getFormInstanceId();
                        ProjectReopenHeader projectReopenHeader1 = projectReopenHeaderMapper.selectByPrimaryKey(formInstanceId);
                        if (projectReopenHeader1 != null){
                            continue;
                        }
                        String wfStatus = formInstance.getWfStatus();
                        ProjectReopenHeader newHeader = new ProjectReopenHeader();
                        newHeader.setId(formInstanceId);
                        newHeader.setProjectId(formInstanceId);
                        newHeader.setDeletedFlag(false);
                        setStauts(wfStatus, newHeader);
                        projectReopenHeaderMapper.insert(newHeader);

                        if (formInstance.getDeletedFlag().equals(true)){
                            formInstance.setDeletedFlag(false);
                            formInstanceExtMapper.updateDeletedFlag(formInstance);
                        }
                    }
                }
                return "插入数据成功";
            }
        } else {
            //情况2：没有传projectId
            ProjectReopenHeaderExample example = new ProjectReopenHeaderExample();
            ProjectReopenHeaderExample.Criteria criteria = example.createCriteria();
            criteria.andDeletedFlagEqualTo(false);
            List<ProjectReopenHeader> projectReopenHeaders = projectReopenHeaderMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(projectReopenHeaders)) {
                List<Long> projectIds = projectReopenHeaders.stream()
                        .map(ProjectReopenHeader::getProjectId)
                        .distinct()
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(projectIds)) {
                    List<FormInstance> formInstances = formInstanceExtMapper.selectFormInstanceIdNotInFormInstanceId(projectIds);
                    List<FormInstance> distinctFormInstances = getFormInstances(formInstances);
                    if (CollectionUtils.isNotEmpty(distinctFormInstances)) {
                        for (FormInstance formInstance : distinctFormInstances) {
                            Long formInstanceId = formInstance.getFormInstanceId();//表单实例ID
                            ProjectReopenHeader projectReopenHeader1 = projectReopenHeaderMapper.selectByPrimaryKey(formInstance.getFormInstanceId());
                            if (projectReopenHeader1 != null){
                                continue;
                            }

                            String wfStatus = formInstance.getWfStatus();
                            ProjectReopenHeader newHeader = new ProjectReopenHeader();
                            newHeader.setId(formInstanceId);
                            newHeader.setProjectId(formInstanceId);
                            newHeader.setDeletedFlag(false);
                            setStauts(wfStatus, newHeader);
                            projectReopenHeaderMapper.insert(newHeader);
                            if (formInstance.getDeletedFlag().equals(true)){
                                formInstance.setDeletedFlag(false);
                                formInstanceExtMapper.updateDeletedFlag(formInstance);
                            }
                        }
                    }
                    return "插入数据成功";
                }
            } else {
                List<FormInstance> formInstances = formInstanceExtMapper.selectFormInstanceIdforformUrl();
                if (CollectionUtils.isNotEmpty(formInstances)) {
                    List<FormInstance> formInstances1 = getFormInstances(formInstances);
                    if (CollectionUtils.isNotEmpty(formInstances1)) {
                        for (FormInstance formInstance : formInstances1) {
                            Long formInstanceId = formInstance.getFormInstanceId();
                            ProjectReopenHeader projectReopenHeader1 = projectReopenHeaderMapper.selectByPrimaryKey(formInstance.getFormInstanceId());
                            if (projectReopenHeader1 != null){
                                continue;
                            }
                            String wfStatus = formInstance.getWfStatus();
                            ProjectReopenHeader newHeader = new ProjectReopenHeader();
                            newHeader.setId(formInstanceId);
                            newHeader.setProjectId(formInstanceId);
                            newHeader.setDeletedFlag(false);
                            setStauts(wfStatus, newHeader);
                            projectReopenHeaderMapper.insert(newHeader);
                            if (formInstance.getDeletedFlag().equals(true)){
                                formInstance.setDeletedFlag(false);
                                formInstanceExtMapper.updateDeletedFlag(formInstance);
                            }
                        }
                    }
                }
                return "插入数据成功";
            }
        }
        return "插入数据失败";
    }

    //删除跟作废不用恢复失效标识，其余改为0
    private static void setStauts(String wfStatus, ProjectReopenHeader newHeader) {
        if (org.apache.commons.lang3.StringUtils.isBlank(wfStatus)) {
            newHeader.setStatus(projectReopenHeaderStatusEnum.DELETE.getCode());
        } else if ("00".equals(wfStatus)) {
            newHeader.setStatus(projectReopenHeaderStatusEnum.DELETE.getCode());
        } else if ("10".equals(wfStatus)) {
            newHeader.setStatus(projectReopenHeaderStatusEnum.RETURN.getCode());
        } else if ("11".equals(wfStatus)) {
            newHeader.setStatus(projectReopenHeaderStatusEnum.REFUSE.getCode());
        } else if ("20".equals(wfStatus)) {
            newHeader.setStatus(projectReopenHeaderStatusEnum.PENDING.getCode());
        } else if ("21".equals(wfStatus)) {
            newHeader.setStatus(projectReopenHeaderStatusEnum.DELETE.getCode());
        } else if ("30".equals(wfStatus)) {
            newHeader.setStatus(projectReopenHeaderStatusEnum.PASS_THROUGH.getCode());
        } else {
            newHeader.setStatus(projectReopenHeaderStatusEnum.DELETE.getCode());
        }
    }

    private static List<FormInstance> getFormInstances(List<FormInstance> formInstances) {
        List<FormInstance> distinctFormInstances = new ArrayList<>(formInstances.stream()
                .collect(Collectors.toMap(
                        FormInstance::getFormInstanceId,
                        instance -> instance,
                        (oldInstance, newInstance) ->
                                oldInstance.getCreateAt().after(newInstance.getCreateAt())
                                        ? oldInstance
                                        : newInstance
                ))
                .values());
        return distinctFormInstances;
    }
}
