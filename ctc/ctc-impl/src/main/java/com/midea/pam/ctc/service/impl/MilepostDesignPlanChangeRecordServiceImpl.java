package com.midea.pam.ctc.service.impl;

import cn.hutool.core.bean.copier.BeanCopier;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.midea.mip.core.util.BeanUtils;
import com.midea.pam.common.basedata.dto.MaterialCostDto;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.AsyncRequestResultDto;
import com.midea.pam.common.ctc.dto.CtcAttachmentDto;
import com.midea.pam.common.ctc.dto.DesignPlanChangeReportDTO;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanChangeRecordDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailChangeDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanChangeRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanChangeRecordExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChange;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChangeExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChangeRecord;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectProfit;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.WbsTemplateInfo;
import com.midea.pam.common.ctc.vo.DesignPlanDetailChangeHistoryVO;
import com.midea.pam.common.ctc.vo.MilepostDesignPlanDetailApprovedVO;
import com.midea.pam.common.enums.AttachmentStatus;
import com.midea.pam.common.enums.ChangeType;
import com.midea.pam.common.enums.ErpOrganizationId;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.HistoryType;
import com.midea.pam.common.enums.NoticeBusinessType;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ReceiptsConfirmModeEnum;
import com.midea.pam.common.enums.RequirementStatusEnum;
import com.midea.pam.common.enums.RequirementTypeEnum;
import com.midea.pam.common.enums.WorkFlowDraftSubmitTemporaryRecordStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.gateway.entity.RocketMQMsgParameter;
import com.midea.pam.common.gateway.entity.WorkFlowDraftSubmitTemporaryRecord;
import com.midea.pam.common.gateway.entity.WorkFlowDraftSubmitTemporaryRecordExample;
import com.midea.pam.common.rocketmq.dto.MilepostDesignPlanChangeSubmitDto;
import com.midea.pam.common.rocketmq.dto.MsgSendDto;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DistributedCASLock;
import com.midea.pam.common.util.ExceptionUtils;
import com.midea.pam.common.util.JsonUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.ctc.common.enums.ChangeTypeEnum;
import com.midea.pam.ctc.common.enums.CheckStatus;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.common.enums.DeletedFlag;
import com.midea.pam.ctc.common.enums.MilepostDesignPlanDetailModelStatus;
import com.midea.pam.ctc.common.utils.BeanConverter;
import com.midea.pam.ctc.common.utils.DateUtil;
import com.midea.pam.ctc.common.utils.ListUtil;
import com.midea.pam.ctc.mapper.AsyncRequestResultMapper;
import com.midea.pam.ctc.mapper.EmailExtMapper;
import com.midea.pam.ctc.mapper.EmployeeInfoExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanChangeRecordExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanChangeRecordMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper;
import com.midea.pam.ctc.mapper.MilepostDesignPlanDetailMapper;
import com.midea.pam.ctc.mapper.RocketMQMsgParameterExtMapper;
import com.midea.pam.ctc.mapper.WbsTemplateInfoMapper;
import com.midea.pam.ctc.mapper.WorkFlowDraftSubmitTemporaryRecordExtMapper;
import com.midea.pam.ctc.requirement.service.ProjectWbsReceiptsService;
import com.midea.pam.ctc.rocketmq.service.CommonRocketMQProducerService;
import com.midea.pam.ctc.service.AsyncRequestResultService;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.MaterialCostExtService;
import com.midea.pam.ctc.service.MaterialGetService;
import com.midea.pam.ctc.service.MaterialReturnService;
import com.midea.pam.ctc.service.MilepostDesignPlanChangeRecordService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailChangeRecordService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailChangeService;
import com.midea.pam.ctc.service.MilepostDesignPlanDetailService;
import com.midea.pam.ctc.service.MilepostDesignPlanService;
import com.midea.pam.ctc.service.MilepostDesignPlanSubmitRecordService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.ProjectBusinessService;
import com.midea.pam.ctc.service.ProjectMilepostService;
import com.midea.pam.ctc.service.ProjectProfitService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.ProjectWbsReceiptsDesignPlanRelService;
import com.midea.pam.ctc.service.PurchaseProgressService;
import com.midea.pam.ctc.service.event.DesignPlanDetailChangeApprovalEvent;
import com.midea.pam.ctc.service.event.DesignPlanDetailChangeEvent;
import com.midea.pam.ctc.workflowcallback.WorkflowCallbackService;
import com.midea.pam.framework.core.exception.ApplicationBizException;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.system.SystemContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

public class MilepostDesignPlanChangeRecordServiceImpl implements MilepostDesignPlanChangeRecordService {

    private static final Logger logger = LoggerFactory.getLogger(MilepostDesignPlanChangeRecordServiceImpl.class);

    private static final Long MAX_WAIT_TIME = (long) 1000 * 60 * 10;//10分钟

    private final static String MATERIAL_CLASS = "物料";

    @Value("${route.milepostDesignPlanChangeUrl}")
    private String milepostDesignPlanChangeUrl;

    @Value("${rocketmq.ctc.flexible-mdp-change-submit.topic}")
    private String ctcFlexibleMDPChangeSubmitTopic;

    @Resource
    private MilepostDesignPlanChangeRecordMapper milepostDesignPlanChangeRecordMapper;
    @Resource
    private MilepostDesignPlanDetailService milepostDesignPlanDetailService;
    @Resource
    private MilepostDesignPlanDetailChangeService milepostDesignPlanDetailChangeService;
    @Resource
    private MaterialGetService materialGetService;
    @Resource
    private MaterialReturnService materialReturnService;
    @Resource
    private PurchaseProgressService purchaseProgressService;
    @Resource
    private ProjectBusinessService projectBusinessService;
    @Resource
    private ProjectProfitService projectProfitService;
    @Resource
    private ProjectMilepostService projectMilepostService;
    @Resource
    private CtcAttachmentService ctcAttachmentService;
    @Resource
    private MilepostDesignPlanSubmitRecordService milepostDesignPlanSubmitRecordService;
    @Resource
    private MaterialCostExtService materialCostExtService;
    @Resource
    private AsyncRequestResultService asyncRequestResultService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private AsyncRequestResultMapper asyncRequestResultMapper;
    @Resource
    private ProjectService projectService;
    @Resource
    private MilepostDesignPlanDetailMapper milepostDesignPlanDetailMapper;
    @Resource
    private MilepostDesignPlanService milepostDesignPlanService;
    @Resource
    private BasedataExtService basedataExtService;
    @Resource
    private ProjectWbsReceiptsService projectWbsReceiptsService;
    @Resource
    private WorkFlowDraftSubmitTemporaryRecordExtMapper workFlowDraftSubmitTemporaryRecordExtMapper;
    @Resource
    private EmailExtMapper emailExtMapper;
    @Resource
    private MilepostDesignPlanChangeRecordExtMapper milepostDesignPlanChangeRecordExtMapper;
    @Resource
    private WorkflowCallbackService workflowCallbackService;
    @Resource
    private CommonRocketMQProducerService commonRocketMQProducerService;
    @Resource
    private RocketMQMsgParameterExtMapper rocketMQMsgParameterExtMapper;
    @Resource
    private MilepostDesignPlanDetailChangeRecordService milepostDesignPlanDetailChangeRecordService;
    @Resource
    private EmployeeInfoExtMapper employeeInfoExtMapper;
    @Autowired
    private ProjectWbsReceiptsDesignPlanRelService projectWbsReceiptsDesignPlanRelService;
    @Resource
    private MilepostDesignPlanDetailExtMapper milepostDesignPlanDetailExtMapper;
    @Resource
    private OrganizationCustomDictService organizationCustomDictService;
    @Resource
    private WbsTemplateInfoMapper wbsTemplateInfoMapper;


    @Override
    public MilepostDesignPlanChangeRecordDto add(MilepostDesignPlanChangeRecordDto dto) {
        dto.setDeletedFlag(DeletedFlag.VALID.code());
        MilepostDesignPlanChangeRecord entity = BeanConverter.copy(dto, MilepostDesignPlanChangeRecord.class);
        milepostDesignPlanChangeRecordMapper.insert(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public MilepostDesignPlanChangeRecordDto update(MilepostDesignPlanChangeRecordDto dto) {
        MilepostDesignPlanChangeRecord entity = BeanConverter.copy(dto, MilepostDesignPlanChangeRecord.class);
        milepostDesignPlanChangeRecordMapper.updateByPrimaryKeySelective(entity);
        BeanConverter.copy(entity, dto);
        return dto;
    }

    @Override
    public MilepostDesignPlanChangeRecordDto save(MilepostDesignPlanChangeRecordDto dto, Long userBy) {
        if (dto.getId() == null) {
            dto.setCreateBy(userBy);
            return this.add(dto);
        } else {
            dto.setUpdateBy(userBy);
            return this.update(dto);
        }
    }

    @Override
    public MilepostDesignPlanChangeRecordDto getById(Long id) {
        MilepostDesignPlanChangeRecord entity = milepostDesignPlanChangeRecordMapper.selectByPrimaryKey(id);
        MilepostDesignPlanChangeRecordDto dto = BeanConverter.copy(entity, MilepostDesignPlanChangeRecordDto.class);
        return dto;
    }

    @Override
    public List<MilepostDesignPlanChangeRecordDto> selectList(MilepostDesignPlanChangeRecordDto query) {
//        List<MilepostDesignPlanChangeRecord> list = milepostDesignPlanChangeRecordMapper.selectByExample
//        (buildCondition(query));
//        List<MilepostDesignPlanChangeRecordDto> dtos = BeanConverter.copy(list, MilepostDesignPlanChangeRecordDto
//        .class);
//        for (MilepostDesignPlanChangeRecordDto dto : dtos) {
//            if (dto.getPublisherBy() != null) {
//                UserInfo publisher = CacheDataUtils.findUserById(dto.getPublisherBy());
//                if (publisher != null) {
//                    dto.setPublisherName(publisher.getName());
//                }
//            }
//        }
//        return dtos;
        return milepostDesignPlanChangeRecordMapper.selectByExample(buildCondition(query)).stream().map(i -> {
            MilepostDesignPlanChangeRecordDto copy = BeanCopier.create(i, new MilepostDesignPlanChangeRecordDto(),
                    CopyOptions.create()).copy();
            if (copy.getPublisherBy() != null) {
                UserInfo publisher = CacheDataUtils.findUserById(copy.getPublisherBy());
                if (publisher != null) {
                    copy.setPublisherName(publisher.getName());
                }
            }
            return copy;
        }).collect(Collectors.toList());

    }

    @Override
    public PageInfo<MilepostDesignPlanChangeRecordDto> selectPage(MilepostDesignPlanChangeRecordDto query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<MilepostDesignPlanChangeRecord> list =
                milepostDesignPlanChangeRecordMapper.selectByExample(buildCondition(query));
        PageInfo<MilepostDesignPlanChangeRecordDto> page = BeanConverter.convertPage(list,
                MilepostDesignPlanChangeRecordDto.class);
        return page;
    }

    @Override
    public MilepostDesignPlanDto getModelWithSon(Long projectId, Long modelDesignPlanDetailId) {
        Project project = projectBusinessService.selectByPrimaryKey(projectId);
        MilepostDesignPlanDto planDto = new MilepostDesignPlanDto();
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = new ArrayList<>();

        MilepostDesignPlanDetailDto modelDesignPlanDetail =
                milepostDesignPlanDetailService.getDesignPlanDetailForBusiness(null, modelDesignPlanDetailId, null);
        if (ListUtil.isPresent(modelDesignPlanDetail.getSonDtos())) {
            milepostDesignPlanDetailService.batchSetNumSum(modelDesignPlanDetail.getSonDtos(), project);
        }
        designPlanDetailDtos.add(modelDesignPlanDetail);
        planDto.setDesignPlanDetailDtos(designPlanDetailDtos);
        planDto.setProjectId(projectId);

        if (project != null) {
            planDto.setProjectName(project.getName());
            planDto.setProjectStatus(project.getStatus());
            //所处里程碑阶段
            planDto.setTheLastestMilepostName(getLastestMilepostName(projectId));
            planDto.setProjectCode(project.getCode());
            //查询库存组织
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null) {
                planDto.setStorageId(projectProfit.getStorageId());
            }
        }

        MilepostDesignPlanDetailDto parent =
                milepostDesignPlanDetailService.getById(modelDesignPlanDetail.getParentId());
        StringBuffer uploadPath = new StringBuffer();
        if (parent != null) {
            uploadPath.append(parent.getMaterielDescr());
        }
        uploadPath.append("/");
        uploadPath.append(modelDesignPlanDetail.getMaterielDescr());
        planDto.setUploadPath(uploadPath.toString());

        if (projectId != null) {
            List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();
            attachmentDtos.addAll(milepostDesignPlanSubmitRecordService.allAttachFromAllSubmitRecord(projectId));
            //所有通过的变更记录对应的附件记录
            attachmentDtos.addAll(this.allAttachFromAllPassedChangeRecord(projectId));
            //过滤掉审批未通过的附件
            List<CtcAttachmentDto> attachments = attachmentDtos.stream()
                    .filter(attachment -> Objects.equals(AttachmentStatus.PASSED.code(), attachment.getStatus())).collect(Collectors.toList());
            planDto.setAttachmentDtos(attachments);
        } else {
            planDto.setAttachmentDtos(Collections.emptyList());
        }

        return planDto;
    }

    @Override
    public MilepostDesignPlanDto getModelWithSonNew(Long projectId, Long modelDesignPlanDetailId) {
        Project project = projectBusinessService.selectByPrimaryKey(projectId);
        MilepostDesignPlanDto planDto = new MilepostDesignPlanDto();
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = new ArrayList<>();
        MilepostDesignPlanDetailDto modelDesignPlanDetail =
                milepostDesignPlanDetailService.getDesignPlanDetailForBusiness(null, modelDesignPlanDetailId, false);
        if (ListUtil.isPresent(modelDesignPlanDetail.getSonDtos())) {
            milepostDesignPlanDetailService.batchSetNumSum(modelDesignPlanDetail.getSonDtos(), project);
        }

        designPlanDetailDtos.add(modelDesignPlanDetail);
        planDto.setDesignPlanDetailDtos(designPlanDetailDtos);
        planDto.setProjectId(projectId);

        if (project != null) {
            planDto.setProjectName(project.getName());
            planDto.setProjectStatus(project.getStatus());
            //所处里程碑阶段
            planDto.setTheLastestMilepostName(getLastestMilepostName(projectId));
            planDto.setProjectCode(project.getCode());
            //查询库存组织
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null) {
                planDto.setStorageId(projectProfit.getStorageId());
            }
        }

        MilepostDesignPlanDetailDto parent =
                milepostDesignPlanDetailService.getById(modelDesignPlanDetail.getParentId());
        StringBuffer uploadPath = new StringBuffer();
        if (parent != null) {
            uploadPath.append(parent.getMaterielDescr());
        }
        uploadPath.append("/");
        uploadPath.append(modelDesignPlanDetail.getMaterielDescr());
        planDto.setUploadPath(uploadPath.toString());

        if (projectId != null) {
            List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();
            attachmentDtos.addAll(milepostDesignPlanSubmitRecordService.allAttachFromAllSubmitRecord(projectId));
            //所有通过的变更记录对应的附件记录
            attachmentDtos.addAll(this.allAttachFromAllPassedChangeRecord(projectId));
            //过滤掉审批未通过的附件
            List<CtcAttachmentDto> attachments = attachmentDtos.stream()
                    .filter(attachment -> Objects.equals(AttachmentStatus.PASSED.code(), attachment.getStatus())).collect(Collectors.toList());
            planDto.setAttachmentDtos(attachments);
        } else {
            planDto.setAttachmentDtos(Collections.emptyList());
        }

        return planDto;
    }

    @Override
    public MilepostDesignPlanDto getModelWithSonNewWbs(Long projectId, Long modelDesignPlanDetailId) {
        Project project = projectBusinessService.selectByPrimaryKey(projectId);
        MilepostDesignPlanDto planDto = new MilepostDesignPlanDto();
        List<MilepostDesignPlanDetailDto> designPlanDetailDtos = new ArrayList<>();
        MilepostDesignPlanDetailDto modelDesignPlanDetail =
                milepostDesignPlanDetailService.getDesignPlanDetailForBusiness(null, modelDesignPlanDetailId, false);
        if (ListUtil.isPresent(modelDesignPlanDetail.getSonDtos())) {
            milepostDesignPlanDetailService.batchSetNumSum(modelDesignPlanDetail.getSonDtos(), project);
        }

        designPlanDetailDtos.add(modelDesignPlanDetail);
        planDto.setDesignPlanDetailDtos(designPlanDetailDtos);
        planDto.setProjectId(projectId);

        if (project != null) {
            planDto.setProjectName(project.getName());
            planDto.setProjectStatus(project.getStatus());
            //所处里程碑阶段
            planDto.setTheLastestMilepostName(getLastestMilepostName(projectId));
            planDto.setProjectCode(project.getCode());
            //查询库存组织
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null) {
                planDto.setStorageId(projectProfit.getStorageId());
            }
        }

        MilepostDesignPlanDetailDto parent =
                milepostDesignPlanDetailService.getById(modelDesignPlanDetail.getParentId());
        StringBuffer uploadPath = new StringBuffer();
        if (parent != null) {
            uploadPath.append(parent.getMaterielDescr());
        }
        uploadPath.append("/");
        uploadPath.append(modelDesignPlanDetail.getMaterielDescr());
        planDto.setUploadPath(uploadPath.toString());

        if (projectId != null) {
            List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();
            attachmentDtos.addAll(milepostDesignPlanSubmitRecordService.allAttachFromAllSubmitRecord(projectId));
            //所有通过的变更记录对应的附件记录
            attachmentDtos.addAll(this.allAttachFromAllPassedChangeRecord(projectId));
            //过滤掉审批未通过的附件
            List<CtcAttachmentDto> attachments = attachmentDtos.stream()
                    .filter(attachment -> Objects.equals(AttachmentStatus.PASSED.code(), attachment.getStatus())).collect(Collectors.toList());
            planDto.setAttachmentDtos(attachments);
        } else {
            planDto.setAttachmentDtos(Collections.emptyList());
        }
        return planDto;
    }

    @Override
    public MilepostDesignPlanChangeRecordDto getDetailById(Long id) {
        MilepostDesignPlanChangeRecordDto dto = this.getById(id);
        packageDto(dto);
        return dto;
    }

    private void packageDto(MilepostDesignPlanChangeRecordDto dto) {
        if (dto != null) {
            if (dto.getPublisherBy() != null) {
                UserInfo publisher = CacheDataUtils.findUserById(dto.getPublisherBy());
                if (publisher != null) {
                    dto.setPublisherName(publisher.getName());
                }
            }

            if (dto.getUploadPathId() != null) {
                //构建详细设计变更信息
                this.buildDesignPlanDetailChangeHistoryVO(dto);
            }
            //附件信息
            this.buildCtcAttachmentDto(dto);

            if (dto.getProjectId() != null) {
                Project project = projectBusinessService.selectByPrimaryKey(dto.getProjectId());
                if (project != null) {
                    dto.setProjectName(project.getName());
                    dto.setProjectCode(project.getCode());
                    //所处里程碑阶段
                    dto.setTheLastestMilepostName(getLastestMilepostName(dto.getProjectId()));
                    //查询库存组织
                    ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
                    if (projectProfit != null) {
                        dto.setStorageId(projectProfit.getStorageId());
                    }
                }
            }
        }
    }

    /**
     * 构建附件信息
     */
    private void buildCtcAttachmentDto(MilepostDesignPlanChangeRecordDto dto) {
        List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();
        if (dto.getMilepostId() != null) {
            attachmentDtos.addAll(milepostDesignPlanSubmitRecordService.allAttachFromAllSubmitRecord(dto.getProjectId()));
            attachmentDtos.addAll(this.allAttachFromAllPassedChangeRecord(dto.getProjectId()));
        }
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code());
        attachmentQuery.setModuleId(dto.getId());
        attachmentDtos.addAll(ctcAttachmentService.selectList(attachmentQuery));
        Map<Long, CtcAttachmentDto> attachmentMap = new HashMap<Long, CtcAttachmentDto>();
        for (CtcAttachmentDto attachmentDto : attachmentDtos) {
            if (!attachmentDto.getDeletedFlag()) {//过滤掉已删除的附件
                attachmentMap.put(attachmentDto.getAttachId(), attachmentDto);
            }
        }
        //只显示变更中的
        dto.setAttachmentDtos(new ArrayList(attachmentMap.values()));
    }

    /**
     * 构建详细设计变更信息
     */
    private void buildDesignPlanDetailChangeHistoryVO(MilepostDesignPlanChangeRecordDto dto) {
        DesignPlanDetailChangeHistoryVO contrastWithSons =
                milepostDesignPlanDetailChangeService.getContrastWithSonsByModelId(dto.getUploadPathId(), dto.getId());
        //过滤掉未发生变更的数据
        List<DesignPlanDetailChangeHistoryVO> sonVos = contrastWithSons.getSonVos();
        if (!CollectionUtils.isEmpty(sonVos)) {
            List<DesignPlanDetailChangeHistoryVO> filterSonVos = sonVos.stream()
                    .filter(sonVo -> !Objects.equals(sonVo.getType(), ChangeType.NO.code())).collect(Collectors.toList());
            contrastWithSons.setSonVos(filterSonVos);
        }
        dto.setDesignPlanDetailChangeHistoryVO(contrastWithSons);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MilepostDesignPlanChangeRecordDto saveChangeRecordWithDetail(MilepostDesignPlanChangeRecordDto dto, Long userBy) {
        this.checkChangeRecordParams(dto);
        this.checkMilepostDesignPlanDetail(dto);
        MilepostDesignPlanDetailChangeDto changeDto = dto.getDetailChangeDto();
        List<CtcAttachmentDto> attachmentDtos = dto.getAttachmentDtos();
        dto.setPublishAt(DateUtil.getCurrentDate());
        dto.setPublisherBy(userBy);
        if (StringUtils.hasText(dto.getFormInstanceId())) {
            dto.setId(Long.parseLong(dto.getFormInstanceId()));
        }
        MilepostDesignPlanChangeRecordDto resultDto = this.save(dto, userBy);

        //TODO:BUG2019082332696,【生产】模组变更时不要校验模组是否变更中，允许继续进行变更（影响业务进展）
        //TODO:2019-08-23去掉校验,同时前端不允许修改模组的集成外包,否则集成外包发生变化,多人发起变更将产生采购需求数据混乱
        //checkModelCanChange(resultDto.getUploadPathId());

        //1.获取模组变更前数据
        MilepostDesignPlanDetailDto modelDesignPlanDetail =
                milepostDesignPlanDetailService.getDesignPlanDetailForBusiness(null, dto.getUploadPathId(), null);

        //2.如果第一次变更，则保存模组变更前数据到变更表；如第二次重新编辑(审批撤回/驳回)，则不需要再保存
        Project project = projectService.selectByPrimaryKey(dto.getProjectId());
        Long storageId = ErpOrganizationId.ROBOT.code();
        //查询库存组织
        if (project != null) {
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null && projectProfit.getStorageId() != null) {
                storageId = projectProfit.getStorageId();
            }
        }
        if (!StringUtils.hasText(dto.getFormInstanceId())) {
            logger.info("changeDto的HISTORY:{}", JsonUtils.toString(modelDesignPlanDetail));
            this.saveMilepostDesignPlanDetailChangeBefore(project, modelDesignPlanDetail, resultDto, userBy, storageId);
        }
        //TODO:BUG2019082332696,【生产】模组变更时不要校验模组是否变更中，允许继续进行变更（影响业务进展）
        //TODO:2019-08-23去掉校验,同时前端不允许修改模组的集成外包,否则集成外包发生变化,多人发起变更将产生采购需求数据混乱
        //TODO:强制模组的集成外包数据保持一致
        changeDto.setExt(modelDesignPlanDetail.getExt());
        //3.保存模组变更后数据到变更表
        logger.info("changeDto的CHANGE:{}", JsonUtils.toString(changeDto));
        //每次提交都先物理删除变没有id的更项数据
        milepostDesignPlanDetailChangeService.deleteChangeHistoryType(resultDto.getId());
        milepostDesignPlanDetailChangeService.recursiveSave(project, changeDto, modelDesignPlanDetail.getParentId(), resultDto.getId(),
                resultDto.getProjectId(), resultDto.getMilepostId(), userBy, HistoryType.CHANGE.getCode(), storageId);
        //4.保存新增的附件
        this.saveAttachmentDtos(attachmentDtos, resultDto, userBy);

        //修改详设中间表数据
        if (null != dto.getMarkId()) {
            this.updateMilepostDesignPlanMiddle(dto);
        }

        return resultDto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProjectWbsReceiptsDto saveChangeReceiptsWithDetail(ProjectWbsReceiptsDto dto, Long userBy) {
//        this.checkChangeRecordParamsWbs(dto);
//        this.checkMilepostDesignPlanDetailWbs(dto);
        MilepostDesignPlanDetailChangeDto changeDto = dto.getDetailChangeDto();
        dto.setPublishAt(DateUtil.getCurrentDate());
        dto.setPublisherBy(userBy);
        if (StringUtils.hasText(dto.getFormInstanceId())) {
            Long originFormInstanceId = milepostDesignPlanDetailChangeRecordService.getOriginFormInstanceId(Long.parseLong(dto.getFormInstanceId()));
            dto.setId(originFormInstanceId);
        }
        StringBuilder releaseLotNumber = new StringBuilder();
        //查询库存组织
        Project project = projectService.selectByPrimaryKey(dto.getProjectId());
        Long storageId = null;
        if (project != null) {
            ProjectProfit projectProfit = projectProfitService.findProjectProfit(project.getId());
            if (projectProfit != null && projectProfit.getStorageId() != null) {
                storageId = projectProfit.getStorageId();
            }
            //生成发布批次号
            releaseLotNumber.append(project.getCode() + "_");
            releaseLotNumber.append(dto.getReasonSuffix() + "_");
            String sequence = CacheDataUtils.generateSequence(Constants.DESIGN_DETAIL_PUBLISH_LENGTH, project.getCode() + dto.getReasonSuffix());
            releaseLotNumber.append(sequence.substring(sequence.length() - 5));
            dto.setDesignReleaseLotNumber(releaseLotNumber.toString());
        }
        ProjectWbsReceiptsDto resultDto = projectWbsReceiptsService.saveReceiptsForChange(dto, userBy);

        //TODO:BUG2019082332696,【生产】模组变更时不要校验模组是否变更中，允许继续进行变更（影响业务进展）
        //TODO:2019-08-23去掉校验,同时前端不允许修改模组的集成外包,否则集成外包发生变化,多人发起变更将产生采购需求数据混乱
//        checkModelCanChange(resultDto.getUploadPathList().stream().map(MilepostDesignPlanDetail::getId).collect
//        (Collectors.toList()));

        findAndEditNoUpdateRecords(changeDto);

        //1.获取模组变更前数据
        List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos = milepostDesignPlanService.packagePlanDetail(dto.getUploadPathList());

        MilepostDesignPlanDetailDto historyDesignPlanDetail = new MilepostDesignPlanDetailDto();
        //避免错误数据的影响 筛选出最上级获取整个树形结构
        if (ListUtils.isNotEmpty(milepostDesignPlanDetailDtos)) {
            List<MilepostDesignPlanDetailDto> parentDtoList = milepostDesignPlanDetailDtos.stream()
                    .filter(historyDto -> null == historyDto.getParentId() || historyDto.getParentId() == -1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(parentDtoList)) {
                historyDesignPlanDetail = parentDtoList.get(0);
            }
        }


        //2.如果第一次变更，则保存模组变更前数据到变更表；如第二次重新编辑(审批撤回/驳回)，则不需要再保存
        if (!StringUtils.hasText(dto.getFormInstanceId())) {
            MilepostDesignPlanDetailChangeExample changeExample = new MilepostDesignPlanDetailChangeExample();
            changeExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andProjectWbsReceiptsIdEqualTo(resultDto.getId())
                    .andHistoryTypeEqualTo(HistoryType.HISTORY.getCode());
            List<MilepostDesignPlanDetailChange> milepostDesignPlanDetailChange =
                    milepostDesignPlanDetailChangeService.selectByExample(changeExample);
            if (ListUtils.isNotEmpty(milepostDesignPlanDetailChange)) {
                milepostDesignPlanDetailChangeService.deleteByIds(milepostDesignPlanDetailChange.stream().map(MilepostDesignPlanDetailChange::getId).collect(Collectors.toList()));
            }
            this.saveMilepostDesignPlanDetailChangeBefore(project, historyDesignPlanDetail, resultDto, userBy, storageId, dto.getUnitId(),
                    releaseLotNumber.toString());
        }
        //TODO:BUG2019082332696,【生产】模组变更时不要校验模组是否变更中，允许继续进行变更（影响业务进展）
        //TODO:2019-08-23去掉校验,同时前端不允许修改模组的集成外包,否则集成外包发生变化,多人发起变更将产生采购需求数据混乱
        //TODO:强制模组的集成外包数据保持一致
        changeDto.setExt(historyDesignPlanDetail.getExt());

        MilepostDesignPlanDetailChangeExample changeExample = new MilepostDesignPlanDetailChangeExample();
        changeExample.createCriteria().andDeletedFlagEqualTo(DeletedFlag.VALID.code()).andProjectWbsReceiptsIdEqualTo(resultDto.getId()).andHistoryTypeEqualTo(HistoryType.CHANGE.getCode());
        List<MilepostDesignPlanDetailChange> milepostDesignPlanDetailChange = milepostDesignPlanDetailChangeService.selectByExample(changeExample);
        if (ListUtils.isNotEmpty(milepostDesignPlanDetailChange)) {
            milepostDesignPlanDetailChangeService.deleteByIds(milepostDesignPlanDetailChange.stream().map(MilepostDesignPlanDetailChange::getId).collect(Collectors.toList()));
        }

        //3.保存模组变更后数据到变更表
        milepostDesignPlanDetailChangeService.recursiveSave(project, changeDto, historyDesignPlanDetail.getParentId(), resultDto.getId(),
                resultDto.getProjectId(), userBy, HistoryType.CHANGE.getCode(), storageId, dto.getUnitId(), releaseLotNumber.toString());

        // 提交逻辑处理完后把对应的提交到临时表的数据状态变更为“已提交”
        Long milepostDesignPlanDetailChangeRecordId =
                milepostDesignPlanDetailChangeRecordService.getMilepostDesignPlanDetailChangeRecordId(dto.getId(), dto.getFormTemplateId());
        workFlowDraftSubmitTemporaryRecordExtMapper.updateStatus(dto.getFormTemplateId(), milepostDesignPlanDetailChangeRecordId,
                WorkFlowDraftSubmitTemporaryRecordStatus.DONE.getIndex());

        // 整个提交接口是需求发布后
        if (Boolean.TRUE.equals(dto.getHasConfirmed())) {
            sendEmailForMilepostDesignPlanChange(resultDto, resultDto.getHandleBy(), NoticeBusinessType.PROJECT_WBS_RECEIPTS_TODO);
        }
        return resultDto;
    }

    private void sendEmailForMilepostDesignPlanChange(ProjectWbsReceipts projectWbsReceipts, Long receiverId, NoticeBusinessType noticeBusinessType) {
        if (null == projectWbsReceipts) {
            return;
        }

        try {
            String subject = String.format("PAM详细设计单据提醒：请关注详细设计单据“%s”的处理情况（待处理）", projectWbsReceipts.getRequirementCode());
            StringBuilder content = new StringBuilder("<html><div style='font-size: 12px;'>");
            content.append(String.format("<div>PAM详细设计单据提醒：请关注详细设计单据“%s”的处理情况</div>", projectWbsReceipts.getRequirementCode()));
            content.append(String.format("<div><a href='%s' target='_blank'>点击跳转至PAM处理</a></div>",
                    milepostDesignPlanChangeUrl + projectWbsReceipts.getId()
                            + "?id=" + projectWbsReceipts.getId() + "&requirementStatus=" + projectWbsReceipts.getRequirementStatus()));
            content.append(String.format("<div>项目名称：%s</div>", projectWbsReceipts.getProjectName()));
            content.append(String.format("<div>单据号：%s</div>", projectWbsReceipts.getRequirementCode()));
            content.append(String.format("<div>单据状态：%s</div>",
                    Optional.ofNullable(RequirementStatusEnum.getNameByCode(projectWbsReceipts.getRequirementStatus())).orElse("")));
            content.append(String.format("<div>制单人：%s</div>", projectWbsReceipts.getProducerName()));
            content.append(String.format("<div>处理人：%s</div>", projectWbsReceipts.getHandleName()));
            content.append(String.format("<div>单据类型：%s</div>",
                    Optional.ofNullable(RequirementTypeEnum.getNameByCode(projectWbsReceipts.getRequirementType())).orElse("")));
            content.append(String.format("<div>确认方式：%s</div>",
                    Optional.ofNullable(ReceiptsConfirmModeEnum.getNameByCode(projectWbsReceipts.getConfirmMode())).orElse("")));
            content.append("</div></html>");

            sendEmail(Optional.ofNullable(receiverId).orElse(projectWbsReceipts.getCreateBy()), null, noticeBusinessType.getType(),
                    subject, content.toString());
        } catch (Exception e) {
            logger.error("{}邮件发送失败", noticeBusinessType.getName(), e);
        }
    }

    /**
     * 发送邮件
     *
     * @param receiveMipAccount ：接收人Mip账号
     * @param receiverId        ：接收人id，无receiveMipAccount，则通过receiverId查询receiverMipAccount
     * @param subject           : 邮件主题
     * @param content           ：邮件内容
     */
    private void sendEmail(Long receiverId, String receiveMipAccount, int businessType, String subject, String content) {
        Email email = new Email();
        email.setFromAddress("pam");
        email.setStatus(0);
        email.setLanguage("zh-CN");
        email.setBusinessType(businessType);
        if (StringUtils.isEmpty(receiveMipAccount)) {
            UserInfo user = employeeInfoExtMapper.selectByPrimaryKey(receiverId);
            receiveMipAccount = user.getUsername();
            if (StringUtils.isEmpty(receiveMipAccount)) {
                logger.error("接收人{}的Mip账号为空", receiverId);
                return;
            }
        }
        email.setReceiver(receiveMipAccount);
        email.setSubject(subject);
        email.setContent(content);
        email.setDeletedFlag(Boolean.FALSE);
        emailExtMapper.insert(email);
    }

    private void updateMilepostDesignPlanMiddle(MilepostDesignPlanChangeRecordDto dto) {
        basedataExtService.updateMilepostDesignPlanMiddle(dto.getMarkId());
    }

    /**
     * 变更记录参数校验
     */
    private void checkChangeRecordParams(MilepostDesignPlanChangeRecordDto dto) {
        Asserts.notEmpty(dto.getProjectId(), ErrorCode.CTC_PROJECT_ID_NULL);
        Asserts.notEmpty(dto.getMilepostId(), ErrorCode.CTC_MILEPOST_ID_NULL);
        //详细设计方案变更控制同一物料不能同时发起变更
        if (!StringUtils.hasText(dto.getFormInstanceId()) && //流程驳回/撤回后，重新编辑不需要校验
                Objects.nonNull(dto.getDetailChangeDto()) && Objects.nonNull(dto.getDetailChangeDto().getSonDtos())) {
            List<Long> changeDetailIds = dto.getDetailChangeDto().getSonDtos().stream()
                    .filter(changeDetail -> Objects.nonNull(changeDetail.getDesignPlanDetailId()))
                    .map(MilepostDesignPlanDetailChangeDto::getDesignPlanDetailId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(changeDetailIds)) {
                List<MilepostDesignPlanDetailChange> changeDetails =
                        milepostDesignPlanDetailChangeService.findMilepostDesignPlanDetailChange(changeDetailIds);
                if (!CollectionUtils.isEmpty(changeDetails)) {
                    //拼接ERP编码
                    //拼接ERP编码
                    String errorCodes = this.buildErrorCodes(changeDetails);
                    throw new IllegalArgumentException(errorCodes);
                }
            }
            //1. 采购件 新增。--1.同一个装配件下，不能增加相同的物料。
            //4. 装配件新增。 --"1.同一个装配件下，不能增加相同的装配件，物料名称不能重复，通过物料名称判断。 2.上层编码是否存在工单任务。提示状态"
            /*boolean hadidentical = identical(dto.getDetailChangeDto().getSonDtos());
            if(hadidentical){
                throw new IllegalArgumentException("同一个装配件下，不能增加相同的装配件");
            }*/
            //2. 采购件删除。-- "1.检查是否生成采购需求。2.是否存在工单任务。提示状态。
            //5.装配件删除 -- "1.上层编码，下层编码是否存在工单任务。提示状态。  2.如果下层采购件，已经生成采购需求，则提示采购需求状态。 3. 下层有物料提前采购，则不允许删除上层装配件"
            /*String deleteAccessories = deleteAccessories(dto.getDetailChangeDto());
            if(StringUtils.isNotEmpty(deleteAccessories)){
                throw new IllegalArgumentException(deleteAccessories);
            }*/
            //3. 采购件数量变更。--"数量增加：需要检查是否有工单任务，提醒任务状态 数量减少：需要检查是否有生成采购需求，工单任务，提示状态"
            //6. 装配件数量变更。 --"数量增加：需要检查上层编码，下层编码是否存在工单任务。提示状态。 数量减少：需要检查是否有采购需求，工单任务，提示状态。 需要检查下层物料是否有提前采购物料，给出提示。"
            /*String numberChange = numberChange(dto.getDetailChangeDto());
            if(StringUtils.isNotEmpty(numberChange)){
                throw new IllegalArgumentException(numberChange);
            }*/
        }
    }


    public boolean identical(List<MilepostDesignPlanDetailChangeDto> plans) {
        boolean hadidentical = false;
        for (MilepostDesignPlanDetailChangeDto plan : plans) {
            int i = 0;
            if (null != plan.getSonDtos() && plan.getSonDtos().size() > 0) {
                hadidentical = identical(plan.getSonDtos());
            }
            if (!hadidentical) {
                for (MilepostDesignPlanDetailChangeDto p : plans) {
                    if (StringUtils.isNotEmpty(plan.getPamCode()) && StringUtils.isNotEmpty(plan.getName())) {
                        if (plan.getPamCode().equals(p.getPamCode()) || plan.getName().equals(p.getName())) {
                            i++;
                        }
                    }
                }
                if (i > 1) {
                    return true;
                }
            }

        }
        return hadidentical;
    }

    public String deleteAccessories(MilepostDesignPlanDetailChangeDto plan) {
        String str = "";
        plan.getId();
        if (null != plan.getDeletedFlag()) {
            if (plan.getDeletedFlag() == true) {
                if (plan.getGenerateRequirement() == true) {
                    str += plan.getPamCode() + "已生成采购需求 ";
                }
            /*if(plan.getGenerateRequirement() == true){
                str += plan.getPamCode() + "已在工单任务 ";
            }*/
                return str;
            }
        }

        List<MilepostDesignPlanDetailChangeDto> plans = plan.getSonDtos();
        for (MilepostDesignPlanDetailChangeDto p : plans) {
            str += deleteAccessories(p);
        }
        return str;
    }

    public String numberChange(MilepostDesignPlanDetailChangeDto plan) {
        String str = "";
        MilepostDesignPlanDetailChangeDto dto = milepostDesignPlanDetailChangeService.getById(plan.getId());
        //数量减少
        if (null != plan.getNumber()) {
            if (dto.getNumber().compareTo(plan.getNumber()) > 0) {
                if (dto.getGenerateRequirement() == true) {
                    str += plan.getPamCode() + "已生成采购需求 ";
                }
            }
            //数量增加
            if (dto.getNumber().compareTo(plan.getNumber()) < 0) {
                //if(dto.getGenerateRequirement() == true){
                //str += plan.getPamCode() + "已有工单任务 ";
                //}
            }
        }
        List<MilepostDesignPlanDetailChangeDto> plans = plan.getSonDtos();
        for (MilepostDesignPlanDetailChangeDto p : plans) {
            str += numberChange(p);
        }
        return str;
    }

    /**
     * 拼接ERP编码
     */
    private String buildErrorCodes(List<MilepostDesignPlanDetailChange> changeDetails) {
        StringBuffer sb = new StringBuffer();
        sb.append("物料[ ");
        List<String> codes = changeDetails.stream().map(this::getCode).collect(Collectors.toList());
        String errorCodes = org.apache.commons.lang.StringUtils.join(codes, ",");
        sb.append(errorCodes);
        sb.append(" ]还在变更审批中，不能再进行变更");
        return sb.toString();
    }

    private String getCode(MilepostDesignPlanDetailChange changeDetail) {
        return StringUtils.hasText(changeDetail.getErpCode()) ? changeDetail.getErpCode() :
                changeDetail.getPamCode() + ",";
    }

    /**
     * 保存模组变更前数据到变更表
     */
    private void saveMilepostDesignPlanDetailChangeBefore(Project project,
                                                          MilepostDesignPlanDetailDto modelDesignPlanDetail,
                                                          MilepostDesignPlanChangeRecordDto resultDto, Long userBy,
                                                          Long storageId) {
        List<MilepostDesignPlanDetailDto> sonDesignPlanDetail = modelDesignPlanDetail.getSonDtos();
        //模组父节点id
        Long modelParentId = modelDesignPlanDetail.getParentId();
        MilepostDesignPlanDetailChangeDto historyDto = copyMilepostDesignPlanDetai(modelDesignPlanDetail);
        //BeanConverter.copy(modelDesignPlanDetail, MilepostDesignPlanDetailChangeDto.class);
        /*List<MilepostDesignPlanDetailChangeDto> historySonDtos = new ArrayList<>();//BeanConverter.copy
        (sonDesignPlanDetail, MilepostDesignPlanDetailChangeDto.class);
        for(MilepostDesignPlanDetailDto son:sonDesignPlanDetail){
            MilepostDesignPlanDetailChangeDto sonchange = copyMilepostDesignPlanDetai(son);//BeanConverter.copy(son,
            MilepostDesignPlanDetailChangeDto.class);
            historySonDtos.add(sonchange);
        }*/
        /*historyDto.setDesignPlanDetailId(historyDto.getId());
        historyDto.setId(null);
        for (MilepostDesignPlanDetailChangeDto historySonDto : historySonDtos) {
            historySonDto.setDesignPlanDetailId(historySonDto.getId());
            historySonDto.setId(null);
        }*/
        //historyDto.setSonDtos(historySonDtos);
        milepostDesignPlanDetailChangeService.recursiveSave(project, historyDto, modelParentId, resultDto.getId(),
                resultDto.getProjectId(),
                resultDto.getMilepostId(), userBy, HistoryType.HISTORY.getCode(), storageId);
    }

    /**
     * 保存模组变更前数据到变更表
     */
    private void saveMilepostDesignPlanDetailChangeBefore(Project project,
                                                          MilepostDesignPlanDetailDto modelDesignPlanDetail,
                                                          ProjectWbsReceiptsDto resultDto, Long userBy,
                                                          Long storageId, Long unitId, String releaseLotNumber) {
        MilepostDesignPlanDetailChangeDto historyDto = copyMilepostDesignPlanDetai(modelDesignPlanDetail);
        milepostDesignPlanDetailChangeService.recursiveSave(project, historyDto, modelDesignPlanDetail.getParentId(),
                resultDto.getId(),
                resultDto.getProjectId(), userBy, HistoryType.HISTORY.getCode(), storageId, unitId, releaseLotNumber);
    }


    public MilepostDesignPlanDetailChangeDto copyMilepostDesignPlanDetai(MilepostDesignPlanDetailDto detail) {
        MilepostDesignPlanDetailChangeDto sonchange = BeanConverter.copy(detail,
                MilepostDesignPlanDetailChangeDto.class);
        sonchange.setDesignPlanDetailId(sonchange.getId());
        sonchange.setId(null);
        if (null != detail.getSonDtos()) {
            List<MilepostDesignPlanDetailChangeDto> historySonDtos = new ArrayList<>();
            List<MilepostDesignPlanDetailDto> sons = detail.getSonDtos();
            for (MilepostDesignPlanDetailDto d : sons) {
                MilepostDesignPlanDetailChangeDto s = copyMilepostDesignPlanDetai(d);
                historySonDtos.add(s);
            }
            sonchange.setSonDtos(historySonDtos);
        }
        return sonchange;
    }

    /**
     * 保存新增的附件
     */
    private void saveAttachmentDtos(List<CtcAttachmentDto> attachmentDtos,
                                    MilepostDesignPlanChangeRecordDto resultDto, Long userBy) {
        if (ListUtil.isPresent(attachmentDtos)) {
            for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                if (attachmentDto.getId() == null && !Objects.equals(Boolean.TRUE, attachmentDto.getDeletedFlag())) {
                    attachmentDto.setModule(CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code());
                    attachmentDto.setModuleId(resultDto.getId());
                    attachmentDto.setStatus(AttachmentStatus.CHECKING.code());//审批中
                    if (StringUtils.isEmpty(attachmentDto.getAttachName())) {
                        attachmentDto.setAttachName(attachmentDto.getFileName());
                    }
                }
                //删除已有附件时,且该附件ModuleId不一致时，复制该条附件，以提供查看审批明细查看,但查看的时候需要注意过滤
                if (Objects.nonNull(attachmentDto.getId())
                        && !Objects.equals(attachmentDto.getModuleId(), resultDto.getId())
                        && Objects.equals(Boolean.TRUE, attachmentDto.getDeletedFlag())) {
                    attachmentDto.setId(null);
                    attachmentDto.setModule(CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code());
                    attachmentDto.setModuleId(resultDto.getId());
                    attachmentDto.setStatus(AttachmentStatus.CHECKING.code());
                    attachmentDto.setDeletedFlag(Boolean.TRUE);
                    if (StringUtils.isEmpty(attachmentDto.getFileName())) {
                        attachmentDto.setFileName(attachmentDto.getAttachName());
                    }
                }

                ctcAttachmentService.save(attachmentDto, userBy);
            }
        }
    }

    @Override
    public void saveChangeRecordWithDetail(Long asyncRequestResultId, MilepostDesignPlanChangeRecordDto dto, Long userBy) {
        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult.setId(asyncRequestResultId);

        try {
            if (null != dto) {
                MilepostDesignPlanChangeRecordDto changeRecordDto = this.saveChangeRecordWithDetail(dto, userBy);
                asyncRequestResult.setResponseCode(ErrorCode.SUCCESS.getCode());
                asyncRequestResult.setResponseMsg(ErrorCode.SUCCESS.getMsg());
                asyncRequestResult.setResponseData(String.valueOf(changeRecordDto.getId()));
            }
            asyncRequestResultMapper.updateByPrimaryKeySelective(asyncRequestResult);
        } catch (Exception e) {
            logger.error("系统未知异常: ", e);
            asyncRequestResult.setResponseCode(ErrorCode.ERROR.getCode());
            asyncRequestResult.setResponseMsg(e.getMessage());
            asyncRequestResultMapper.updateByPrimaryKeySelective(asyncRequestResult);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveChangeReceiptsWithDetail(Long asyncRequestResultId, ProjectWbsReceiptsDto dto, Long userBy) {
        logger.info("柔性详设变更提交的saveChangeReceiptsWithDetail的asyncRequestResultId：{}，dto：{}，userBy：{}",
                asyncRequestResultId, JsonUtils.toString(dto), userBy);
        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult.setId(asyncRequestResultId);

        try {
            if (null != dto) {
                ProjectWbsReceiptsDto receiptsDto = this.saveChangeReceiptsWithDetail(dto, userBy);
                asyncRequestResult.setResponseCode(ErrorCode.SUCCESS.getCode());
                asyncRequestResult.setResponseMsg(ErrorCode.SUCCESS.getMsg());
                asyncRequestResult.setResponseData(String.valueOf(receiptsDto.getId()));
            }
            asyncRequestResultMapper.updateByPrimaryKeySelective(asyncRequestResult);
        } catch (Exception e) {
            logger.error("系统未知异常: ", e);
            asyncRequestResult.setResponseCode(ErrorCode.ERROR.getCode());
            asyncRequestResult.setResponseMsg(e.getMessage());
            asyncRequestResultMapper.updateByPrimaryKeySelective(asyncRequestResult);
        }
    }

    @Override
    public List<String> saveChangeRecordWithDetailAsyncBefore(MilepostDesignPlanChangeRecordDto dto) {
        logger.info("saveChangeRecordWithDetailAsyncBefore的dto：{}", JsonUtils.toString(dto));

        MilepostDesignPlanDetailChangeDto changeDto = dto.getDetailChangeDto();
        if (null == changeDto) {
            return new ArrayList<>();
        }

        Long uploadPathId = dto.getUploadPathId();
        MilepostDesignPlanDetailDto detailDto = milepostDesignPlanDetailService.getById(uploadPathId);
        if (Objects.isNull(detailDto) || Boolean.TRUE.equals(detailDto.getDeletedFlag())) {
            return new ArrayList<>();
        }

        Set<String> pamCodeSet = new HashSet<>();
        BigDecimal number = detailDto.getNumber();
        if (Objects.isNull(number) || BigDecimal.ZERO.compareTo(number) == 0) {
            pamCodeSet.add(detailDto.getPamCode());
        }
        recursivePamCode(detailDto.getParentId(), pamCodeSet);

        return new ArrayList<>(pamCodeSet);
    }

    private void recursivePamCode(Long parentId, Set<String> pamCodeSet) {
        MilepostDesignPlanDetailDto grandDetailDto = milepostDesignPlanDetailService.getById(parentId);
        if (Objects.isNull(grandDetailDto) || Boolean.TRUE.equals(grandDetailDto.getDeletedFlag())) {
            return;
        }

        BigDecimal number = grandDetailDto.getNumber();
        if (Objects.isNull(number) || BigDecimal.ZERO.compareTo(number) == 0) {
            pamCodeSet.add(grandDetailDto.getPamCode());
        }
        recursivePamCode(grandDetailDto.getParentId(), pamCodeSet);
    }

    @Override
    public AsyncRequestResult saveChangeRecordWithDetailAsync(MilepostDesignPlanChangeRecordDto dto, Long userBy) {
        logger.info("saveChangeRecordWithDetailAsync的dto：{}，userBy：{}", JsonUtils.toString(dto), userBy);
        this.checkChangeRecordParams(dto);
        //this.checkMilepostDesignPlanDetail(dto);

        if (StringUtils.isEmpty(dto.getStorageId())) {
            dto.setStorageId(SystemContext.getUnitId());
        }

        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult = asyncRequestResultService.add(asyncRequestResult);
        applicationEventPublisher.publishEvent(new DesignPlanDetailChangeEvent(this, asyncRequestResult.getId(), dto, userBy));
        return asyncRequestResult;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AsyncRequestResultDto saveChangeReceiptsWithDetailAsync(ProjectWbsReceiptsDto dto, Long userBy) {
        logger.info("柔性详设变更提交的saveChangeReceiptsWithDetailAsync的dto：{}，userBy：{}", JsonUtils.toString(dto), userBy);
        Long projectId = dto.getProjectId();
        assembleFlatDesignPlanToHierarchy(dto);
        logger.info("assembleFlatDesignPlanToHierarchy 处理后的数据:{}",JsonUtils.toString(dto));
//        this.checkChangeRecordParamsWbs(dto);
        //this.checkMilepostDesignPlanDetail(dto);
        MilepostDesignPlanDetailChangeDto detailChangeDto = dto.getDetailChangeDto();
        Guard.notNull(detailChangeDto, "详设变更数据不能为空");
        //创建人
        Guard.equals(projectId, detailChangeDto.getProjectId(), String.format("前端入参有误，当前项目ID：%s 项目code：%s，与详设数据的项目ID：%s不一致",
                projectId, dto.getProjectCode(), detailChangeDto.getProjectId()));

        // 柔性详设发布、详设变更，提交审批时：新增数据 + 外购物料或看板物料  的"是否外包"或"是否急件"为空，不允许提交
        if (Boolean.TRUE.equals(detailChangeDto.getIsNew()) &&
                (Objects.equals("外购物料", detailChangeDto.getMaterialCategory()) || Objects.equals("看板物料", detailChangeDto.getMaterialCategory()))) {
            String pamCode = dto.getPamCode();
            Guard.notNull(detailChangeDto.getExtIs(), String.format("PAM编码：%s的是否外包不能为空", pamCode));
            Guard.notNull(detailChangeDto.getDispatchIs(), String.format("PAM编码：%s的是否急件不能为空", pamCode));
        }
        checkExtIsAndDispatchIs(detailChangeDto.getSonDtos());

        //新增校验，当前详设变更下的物料是否存在状态不为作废和生效状态的需求发布单，如果存在则该详设变更不允许发起
        //获取变更的物料信息，包含更新和删除的物料
        List<MilepostDesignPlanDetailChangeDto> milepostDesignPlanDetailChangeDtos = getChangeData(detailChangeDto.getSonDtos(), new ArrayList<>());
        //判空
        if (CollectionUtils.isNotEmpty(milepostDesignPlanDetailChangeDtos)) {
            //判断当前物料是否有在途的需求发布单据
            //先查询当前项目是否存在状态不为作废和生效状态的单据
            //如果id为空，则为新增详设，没必要校验
            if (Objects.nonNull(dto.getProjectId())) {
                List<ProjectWbsReceipts> projectWbsReceiptsIds = projectWbsReceiptsService.queryDesignInfoByProjectId(dto.getProjectId());
                Map<Long, ProjectWbsReceipts> projectWbsReceiptsMap = projectWbsReceiptsIds.stream().collect(Collectors.toMap(ProjectWbsReceipts::getId, p -> p));
                if (CollectionUtils.isNotEmpty(projectWbsReceiptsIds)) {
                    //判断在途单据是否有当前变更数据
                    List<Long> ids = projectWbsReceiptsIds.stream().map(ProjectWbsReceipts::getId).collect(Collectors.toList());
                    List<Long> designIds = projectWbsReceiptsDesignPlanRelService.queryDesignIdByProjectWbsReceiptsId(ids);
                    List<MilepostDesignPlanDetailChangeDto> changeDtos = new ArrayList<>();
                    Map<Long, MilepostDesignPlanDetailChangeDto> detailChangeDtoMap = milepostDesignPlanDetailChangeDtos.stream().collect(Collectors.toMap(MilepostDesignPlanDetailChangeDto::getDesignPlanDetailId, p -> p));
                    for (Long designId : designIds) {
                        MilepostDesignPlanDetailChangeDto milepostDesignPlanDetailChangeDto = detailChangeDtoMap.get(designId);
                        if (Objects.nonNull(milepostDesignPlanDetailChangeDto)) {
                            milepostDesignPlanDetailChangeDto.setDesignPlanDetailId(designId);
                            changeDtos.add(milepostDesignPlanDetailChangeDto);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(changeDtos)) {
                        StringBuilder errorMessage = new StringBuilder();
                        for (MilepostDesignPlanDetailChangeDto milepostDesignPlanDetailChangeDto : changeDtos) {
                            //查询详设单据号
                            String requirementCode = projectWbsReceiptsService.queryRequirementCodeByDesignId(milepostDesignPlanDetailChangeDto.getDesignPlanDetailId());
                            errorMessage.append("物料：").append(milepostDesignPlanDetailChangeDto.getPamCode()).append("，需求发布中，需求发布单据号：").append(requirementCode);
                        }
                        throw new ApplicationBizException(errorMessage.toString());
                    }
                }
            }
        }
        //checkModuleStatus(detailChangeDto);

        validateDesignDetailChangeInfo(milepostDesignPlanDetailChangeDtos);

        if (StringUtils.isEmpty(dto.getUnitId())) {
            dto.setUnitId(SystemContext.getUnitId());
        }

        if (dto.getId() != null || StringUtils.isNotEmpty(dto.getRequirementCode())) {
            if (dto.getId() != null) {
                ProjectWbsReceipts projectWbsReceipts = projectWbsReceiptsService.getById(dto.getId());
                if (StringUtils.isEmpty(projectWbsReceipts.getRequirementCode())) {
                    dto.setRequirementCode(projectWbsReceiptsService.generateRequirementCode());
                }
            } else {
                ProjectWbsReceipts receipts = projectWbsReceiptsService.getByRequirementCode(dto.getRequirementCode());
                if (null != receipts) {
                    dto.setId(receipts.getId());
                }
            }
        } else {
            dto.setRequirementCode(projectWbsReceiptsService.generateRequirementCode());
        }

        if (dto.getId() == null) {
            // 查询组织参数：不区分外包WBS模板
            Set<String> valueSet = organizationCustomDictService.queryByName(Constants.WBS_TEMPLATE_NEW_WEB_TYPE, SystemContext.getUnitId(), OrgCustomDictOrgFrom.COMPANY);
            if (!org.springframework.util.CollectionUtils.isEmpty(valueSet)) {
                Project project = projectService.selectByPrimaryKey(dto.getProjectId());
                Asserts.notNull(project, ErrorCode.CTC_PROJECT_NOT_FIND);
                WbsTemplateInfo wbsTemplateInfo = wbsTemplateInfoMapper.selectByPrimaryKey(project.getWbsTemplateInfoId());
                if (wbsTemplateInfo != null && valueSet.contains(wbsTemplateInfo.getTemplateName())) {
                    dto.setWebType(1);
                }
            }
        }
        projectWbsReceiptsService.save(dto, userBy);

        MilepostDesignPlanDetailChangeRecord oldChangeRecord = milepostDesignPlanDetailChangeRecordService.saveAndUpdate(dto.getId(), dto.getFormTemplateId());
        // 提交是先插入一条提交逻辑没有完成的数据
        Long milepostDesignPlanDetailChangeRecordId =
                milepostDesignPlanDetailChangeRecordService.getMilepostDesignPlanDetailChangeRecordId(dto.getId(), dto.getFormTemplateId());
        WorkFlowDraftSubmitTemporaryRecordExample example = new WorkFlowDraftSubmitTemporaryRecordExample();
        example.createCriteria().andFormUrlEqualTo(dto.getFormTemplateId()).andFormInstanceIdEqualTo(milepostDesignPlanDetailChangeRecordId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<WorkFlowDraftSubmitTemporaryRecord> recordList = workFlowDraftSubmitTemporaryRecordExtMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(recordList)) {
            WorkFlowDraftSubmitTemporaryRecord record = new WorkFlowDraftSubmitTemporaryRecord();
            record.setFormUrl(dto.getFormTemplateId());
            record.setFormInstanceId(milepostDesignPlanDetailChangeRecordId);
            record.setStatus(WorkFlowDraftSubmitTemporaryRecordStatus.DOING.getIndex());
            record.setSubmitBusinessFinished(Boolean.FALSE);
            record.setDeletedFlag(Boolean.FALSE);
            logger.info("saveChangeReceiptsWithDetailAsync的工作流提交临时记录add的record：{}", JSONObject.toJSONString(record));
            workFlowDraftSubmitTemporaryRecordExtMapper.insertSelective(record);
        } else {
            logger.info("saveChangeReceiptsWithDetailAsync的表单url：{}，表单实例id：{}对应的提交记录已存在", dto.getFormTemplateId(), dto.getId());
        }

        boolean hasConfirmed = checkParentModuleStatus(dto);

        AsyncRequestResult asyncRequestResult = new AsyncRequestResult();
        asyncRequestResult = asyncRequestResultService.add(asyncRequestResult);
        /*applicationEventPublisher.publishEvent(new DesignPlanDetailChangeEvent(this, asyncRequestResult.getId(), dto,
                userBy, project.getWbsEnabled()));*/

        MilepostDesignPlanChangeSubmitDto milepostDesignPlanChangeSubmitDto = new MilepostDesignPlanChangeSubmitDto();
        milepostDesignPlanChangeSubmitDto.setAsyncRequestResultId(asyncRequestResult.getId());
        milepostDesignPlanChangeSubmitDto.setReceiptsDto(dto);
        milepostDesignPlanChangeSubmitDto.setUserBy(userBy);

        RocketMQMsgParameter rocketMQMsgParameter = new RocketMQMsgParameter();
        rocketMQMsgParameter.setOperationId(String.valueOf(dto.getId()));
        rocketMQMsgParameter.setTopic(ctcFlexibleMDPChangeSubmitTopic);
        rocketMQMsgParameter.setParameter(JSONObject.toJSONString(milepostDesignPlanChangeSubmitDto, SerializerFeature.DisableCircularReferenceDetect).getBytes(StandardCharsets.UTF_8));
        rocketMQMsgParameter.setDeletedFlag(Boolean.FALSE);
        rocketMQMsgParameterExtMapper.insertSelective(rocketMQMsgParameter);

        MsgSendDto msgSendDto = new MsgSendDto();
        msgSendDto.setOperationId(String.valueOf(dto.getId()));
        msgSendDto.setTopic(ctcFlexibleMDPChangeSubmitTopic);
        msgSendDto.setMessages(rocketMQMsgParameter.getId());
        commonRocketMQProducerService.sendCommonMessages(msgSendDto);

        asyncRequestResult.setResponseData(dto.getRequirementCode());
        AsyncRequestResultDto resultDto = BeanConverter.copy(asyncRequestResult, AsyncRequestResultDto.class);
        //设置是否修改处理人标识，返回时gateway服务处理上一个处理人的流程实例
        if (oldChangeRecord != null) {
            resultDto.setMilepostDesignPlanDetailChangeRecordId(oldChangeRecord.getId());
            resultDto.setOldHandleUserMip(CacheDataUtils.findUserMipById(oldChangeRecord.getCreateBy()));
        }
        resultDto.setHasConfirmed(hasConfirmed);
        return resultDto;
    }

    /**
     * 检查详设变更中非模组详设所属上级的moduleStatus状态
     * 如果上级模组状态为已确认(1)或确认中(2)，则设置hasConfirmed为true
     *
     * @param dto 项目WBS收据DTO
     * @return 是否存在已确认或确认中的上级模组
     */
    private boolean checkParentModuleStatus(ProjectWbsReceiptsDto dto) {
        boolean hasConfirmed = false;
        logger.info("开始检查详设变更中非模组详设所属上级的moduleStatus状态");
        if (Objects.nonNull(dto.getDetailChangeDto()) && Objects.nonNull(dto.getDetailChangeDto().getSonDtos())) {
            // 获取所有需要检查的详设（新增、更新或删除的非模组详设）
            List<MilepostDesignPlanDetailChangeDto> checkDtos = getChangeData(dto.getDetailChangeDto().getSonDtos(), new ArrayList<>());
            logger.info("获取到需要检查的详设数量: {}", checkDtos.size());

            // 额外获取所有新增的记录
            List<MilepostDesignPlanDetailChangeDto> newAddedDtos = getNewAddedData(dto.getDetailChangeDto().getSonDtos(), new ArrayList<>());
            logger.info("获取到新增的详设数量: {}", newAddedDtos.size());

            // 合并需要检查的记录列表
            checkDtos.addAll(newAddedDtos);
            // 去重处理
            checkDtos = checkDtos.stream().distinct().collect(Collectors.toList());
            logger.info("合并后需要检查的详设总数量: {}", checkDtos.size());

            // 遍历检查每个详设的上级模组状态
            for (MilepostDesignPlanDetailChangeDto changeDto : checkDtos) {
//                // 跳过模组类型的详设 ,fix:不能跳过模组类型的详设，因为模组类型的详设可能有上级模组
//                if (Boolean.TRUE.equals(changeDto.getWhetherModel())) {
//                    logger.debug("跳过模组类型的详设, pamCode: {}", changeDto.getPamCode());
//                    continue;
//                }

                // 获取详设的上级ID
                Long parentId = changeDto.getParentId();
                logger.info("检查详设 pamCode: {}, 上级ID: {}", changeDto.getPamCode(), parentId);
                if (Objects.nonNull(parentId) && parentId != -1L) {
                    // 查询上级详设信息
                    MilepostDesignPlanDetail parentDetail = milepostDesignPlanDetailMapper.selectByPrimaryKey(parentId);
                    if (Objects.nonNull(parentDetail) && Objects.nonNull(parentDetail.getModuleStatus())) {
                        logger.info("详设 pamCode: {} 的上级模组状态: {}", changeDto.getPamCode(), parentDetail.getModuleStatus());
                        // 如果上级模组状态为已确认(1)或确认中(2)，则设置hasConfirmed为true
                        if (parentDetail.getModuleStatus() == 1 || parentDetail.getModuleStatus() == 2) {
                            logger.info("发现上级模组状态为已确认或确认中, 设置hasConfirmed=true, 详设pamCode: {}, 上级ID: {}, 上级moduleStatus: {}",
                                    changeDto.getPamCode(), parentId, parentDetail.getModuleStatus());
                            dto.setHasConfirmed(Boolean.TRUE);
                            hasConfirmed = Boolean.TRUE;
                            break; // 只要有一个符合条件，就可以终止后续查找
                        }
                    } else {
                        logger.info("详设 pamCode: {} 的上级模组不存在或moduleStatus为空", changeDto.getPamCode());
                    }
                } else {
                    logger.info("详设 pamCode: {} 没有有效的上级ID", changeDto.getPamCode());
                }
            }
            // 如果上级模组状态检查未确认，则检查修改或删除的详设是否已确认过
            if (!hasConfirmed) {
                logger.info("上级模组状态未确认，开始检查修改或删除的详设是否已确认过");
                // 筛选出修改或删除的详设
                List<MilepostDesignPlanDetailChangeDto> updateOrDeleteDtos = checkDtos.stream()
                        .filter(changeDto -> (Boolean.TRUE.equals(changeDto.getIsUpdate()) || Boolean.TRUE.equals(changeDto.getIsDelete())))
                        .collect(Collectors.toList());

                if (ListUtils.isNotEmpty(updateOrDeleteDtos)) {
                    // 获取所有修改或删除的详设ID
                    List<Long> detailIds = updateOrDeleteDtos.stream()
                            .map(MilepostDesignPlanDetailChangeDto::getDesignPlanDetailId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    if (ListUtils.isNotEmpty(detailIds)) {
                        logger.info("检查{}个修改或删除的详设是否已确认过", detailIds.size());
                        // 查询这些详设是否已确认过
                        List<ProjectWbsReceiptsDto> confirmedReceipts = projectWbsReceiptsService.selectByDetailIds(detailIds);

                        if (ListUtils.isNotEmpty(confirmedReceipts)) {
                            logger.info("发现{}个详设已被确认过，设置hasConfirmed=true", confirmedReceipts.size());
                            dto.setHasConfirmed(Boolean.TRUE);
                            hasConfirmed = Boolean.TRUE;
                        } else {
                            logger.info("未发现已确认过的详设");
                        }
                    }
                }
            }
        }
        logger.info("完成上级模组状态检查, hasConfirmed: {}", hasConfirmed);
        return hasConfirmed;
    }


    /**
     * 获取所有新增的详设记录
     *
     * @param designPlanDetailChangeDtos 详设变更DTO列表
     * @param newAddedList 新增记录结果列表
     * @return 新增的详设记录列表
     */
    private List<MilepostDesignPlanDetailChangeDto> getNewAddedData(List<MilepostDesignPlanDetailChangeDto> designPlanDetailChangeDtos,
                                                                    List<MilepostDesignPlanDetailChangeDto> newAddedList) {
        if (CollectionUtils.isEmpty(designPlanDetailChangeDtos)) {
            return newAddedList;
        }
        for (MilepostDesignPlanDetailChangeDto dto : designPlanDetailChangeDtos) {
            if (Objects.nonNull(dto.getIsNew()) && dto.getIsNew()) {
                newAddedList.add(dto);
            }
            // 递归处理子节点
            getNewAddedData(dto.getSonDtos(), newAddedList);
        }
        return newAddedList;
    }

    /**
     * 校验设计详情变更的有效性
     * 检查变更前后的数量和图纸版本是否一致，如不一致则抛出异常
     *
     * @param milepostDesignPlanDetailChangeDtos 设计详情变更DTO列表
     * @throws BizException 当数量或图纸版本不一致时抛出业务异常
     */
    private void validateDesignDetailChangeInfo(List<MilepostDesignPlanDetailChangeDto> milepostDesignPlanDetailChangeDtos) {
        // 从变更DTO列表中筛选出需要更新的详设ID列表
        List<Long> updateChangeDesignDetailIdList = milepostDesignPlanDetailChangeDtos.stream()
                .filter(dto -> Objects.nonNull(dto.getIsUpdate()) && dto.getIsUpdate())    // 过滤出标记为需要更新的记录
                .map(MilepostDesignPlanDetailChange::getDesignPlanDetailId)    // 获取详设ID
                .collect(Collectors.toList());    // 收集为List

        // 当存在需要更新的详设ID时进行校验
        if (ListUtils.isNotEmpty(updateChangeDesignDetailIdList)) {
            // 构建查询条件
            MilepostDesignPlanDetailExample milepostDesignPlanDetailExample = new MilepostDesignPlanDetailExample();
            milepostDesignPlanDetailExample.createCriteria()
                    .andIdIn(updateChangeDesignDetailIdList);    // 添加ID条件

            // 查询数据库中的详设记录
            List<MilepostDesignPlanDetail> persistencePlanDetail = milepostDesignPlanDetailMapper.selectByExample(milepostDesignPlanDetailExample);

            // 遍历数据库中的详设记录进行校验
            persistencePlanDetail.forEach(planDetail -> {
                Long planDetailId = planDetail.getId();
                // 查找对应的变更DTO
                MilepostDesignPlanDetailChangeDto changeDto = milepostDesignPlanDetailChangeDtos.stream()
                        .filter(dto -> Objects.equals(dto.getDesignPlanDetailId(), planDetailId))    // 匹配详设ID
                        .findFirst()    // 获取第一个匹配的记录
                        .orElse(null);    // 未找到时返回null

                // 当找到对应的变更DTO时进行数据校验
                if (Objects.nonNull(changeDto)) {
                    BigDecimal oldNumber = changeDto.getOldNumber();           // 获取原数量
                    String oldChartVersion = changeDto.getOldChartVersion();   // 获取原图纸版本

                    // 校验数量是否发生变化
                    if (Objects.nonNull(oldNumber) && Objects.nonNull(planDetail.getNumber()) && oldNumber.compareTo(planDetail.getNumber()) != 0) {
                        logger.info("校验设计详情变更的有效性 - 设计详情ID：{}，数量发生变化，传入数量：{}，持久化数量：{}", planDetailId, oldNumber, planDetail.getNumber());
                        throw new BizException(500, "物料：" + changeDto.getPamCode() + "，数量已变更，请重新发起变更");
                    }

                    // 校验图纸版本是否发生变化
                    if (Objects.nonNull(oldChartVersion) && StringUtils.isNotEmpty(planDetail.getChartVersion()) && !oldChartVersion.equals(planDetail.getChartVersion())) {
                        logger.info("校验设计详情变更的有效性 - 设计详情ID：{}，图纸版本发生变化，传入图纸版本：{}，持久化图纸版本：{}", planDetailId, oldChartVersion, planDetail.getChartVersion());
                        throw new BizException(500, "物料：" + changeDto.getPamCode() + "，图纸版本已变更，请重新发起变更");
                    }
                }
            });
        }
    }

    @Override
    public MilepostDesignPlanChangeRecordDto selectAddRecord(MilepostDesignPlanChangeRecordDto designPlanChangeRecordQuery) {
        MilepostDesignPlanChangeRecord milepostDesignPlanChangeRecord =
                milepostDesignPlanChangeRecordExtMapper.selectAddRecord(designPlanChangeRecordQuery);
        return BeanConverter.copy(milepostDesignPlanChangeRecord, MilepostDesignPlanChangeRecordDto.class);
    }

    private void checkExtIsAndDispatchIs(List<MilepostDesignPlanDetailChangeDto> designPlanDetailChangeDtos) {
        if (CollectionUtils.isEmpty(designPlanDetailChangeDtos)) {
            return;
        }
        for (MilepostDesignPlanDetailChangeDto dto : designPlanDetailChangeDtos) {
            if (Boolean.TRUE.equals(dto.getIsNew()) && (Objects.equals("外购物料", dto.getMaterialCategory()) || Objects.equals("看板物料",
                    dto.getMaterialCategory()))) {
                String pamCode = dto.getPamCode();
                Guard.notNull(dto.getExtIs(), String.format("PAM编码：%s的是否外包不能为空", pamCode));
                Guard.notNull(dto.getDispatchIs(), String.format("PAM编码：%s的是否急件不能为空", pamCode));
            }
            checkExtIsAndDispatchIs(dto.getSonDtos());
        }
    }

    private List<MilepostDesignPlanDetailChangeDto> getChangeData(List<MilepostDesignPlanDetailChangeDto> designPlanDetailChangeDtos, List<MilepostDesignPlanDetailChangeDto> changeDataList) {
        if (CollectionUtils.isEmpty(designPlanDetailChangeDtos)) {
            return changeDataList;
        }
        for (MilepostDesignPlanDetailChangeDto dto : designPlanDetailChangeDtos) {
            if ((Objects.nonNull(dto.getIsDelete()) && dto.getIsDelete()) || ((Objects.nonNull(dto.getIsUpdate())) && dto.getIsUpdate())) {
                changeDataList.add(dto);
            }
            getChangeData(dto.getSonDtos(), changeDataList);
        }
        return changeDataList;
    }

    private void checkModuleStatus(MilepostDesignPlanDetailChangeDto detailChangeDto) {
        Integer moduleStatus = detailChangeDto.getModuleStatus();
        Boolean isNew = detailChangeDto.getIsNew();
        if (Boolean.TRUE.equals(isNew) && Objects.equals(MilepostDesignPlanDetailModelStatus.CONFIRMING.code(), moduleStatus)) {
            throw new ApplicationBizException(String.format("PAM编码：%s的模组状态为确认中，不允许新增", detailChangeDto.getPamCode()));
        }
        Boolean isUpdate = detailChangeDto.getIsUpdate();
        if (Boolean.TRUE.equals(isUpdate) && Objects.equals(MilepostDesignPlanDetailModelStatus.CONFIRMING.code(), moduleStatus)) {
            throw new ApplicationBizException(String.format("PAM编码：%s的模组状态为确认中，不允许修改", detailChangeDto.getPamCode()));
        }
        Boolean isDelete = detailChangeDto.getIsDelete();
        if (Boolean.TRUE.equals(isDelete) && Objects.equals(MilepostDesignPlanDetailModelStatus.CONFIRMING.code(), moduleStatus)) {
            throw new ApplicationBizException(String.format("PAM编码：%s的模组状态为确认中，不允许删除", detailChangeDto.getPamCode()));
        }

        if (CollectionUtils.isNotEmpty(detailChangeDto.getSonDtos())) {
            for (MilepostDesignPlanDetailChangeDto sonDto : detailChangeDto.getSonDtos()) {
                checkModuleStatus(sonDto);
            }
        }
    }

    @Override
    public List<CtcAttachmentDto> allAttachFromAllPassedChangeRecord(Long projectId) {
        List<CtcAttachmentDto> attachmentDtos = new ArrayList<>();

        MilepostDesignPlanChangeRecordDto changeRecordQuery = new MilepostDesignPlanChangeRecordDto();
        changeRecordQuery.setProjectId(projectId);
        changeRecordQuery.setStatus(CheckStatus.MDP_CHANGE_PASSED.code());
        List<MilepostDesignPlanChangeRecordDto> changeRecordDtos = this.selectList(changeRecordQuery);
        if (ListUtil.isPresent(changeRecordDtos)) {
            List<Long> changeRecordIds = ListUtil.map(changeRecordDtos, "id");
            attachmentDtos.addAll(this.allAttachFromChangeRecordIds(changeRecordIds));
        }

        return attachmentDtos;
    }

    @Override
    public void updateBatchStatusAsync(MilepostDesignPlanDetailDto dto) {
        applicationEventPublisher.publishEvent(new DesignPlanDetailChangeApprovalEvent(this, dto));
    }

    @Override
    public List<MaterialCostDto> exportMaterialCostForChangeRecord(Long changeRecordId) {
        MilepostDesignPlanDetailChangeDto designPlanDetailChangeQuery = new MilepostDesignPlanDetailChangeDto();
        designPlanDetailChangeQuery.setChangeRecordId(changeRecordId);
        designPlanDetailChangeQuery.setDeletedFlag(DeletedFlag.VALID.code());
        designPlanDetailChangeQuery.setHistoryType(HistoryType.CHANGE.getCode());
        List<MilepostDesignPlanDetailChangeDto> designPlanDetailChangeDtos =
                milepostDesignPlanDetailChangeService.selectList(designPlanDetailChangeQuery);

        List<Long> materialCostIds = null;
        if (ListUtil.isPresent(designPlanDetailChangeDtos)) {
            materialCostIds = ListUtil.map(designPlanDetailChangeDtos, "designCostId");
        }
        return materialCostExtService.invokeMaterialCostApiSelectListByIds(materialCostIds);
    }

    @Override
    public List<CtcAttachmentDto> allAttachFromChangeRecordIds(List<Long> changeRecordIds) {
        if (ListUtil.isBlank(changeRecordIds)) {
            return new ArrayList<>();
        }
        CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
        attachmentQuery.setModule(CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code());
        attachmentQuery.setModuleIds(changeRecordIds);
        return ctcAttachmentService.selectList(attachmentQuery);
    }

    @Override
    public List<Long> idsForUploadPathIdIsNullByMilepostId(Long milepostId) {
        if (milepostId == null) {
            return new ArrayList<>();
        }
        MilepostDesignPlanChangeRecordDto query = new MilepostDesignPlanChangeRecordDto();
        query.setMilepostId(milepostId);
        query.setUploadPathIdIsNull(true);
        List<MilepostDesignPlanChangeRecordDto> changeRecordDtos = this.selectList(query);
        List<Long> ids = ListUtil.map(changeRecordDtos, "id");

        return ids;
    }

    @Override
    public List<Long> idsByUploadPathIds(List<Long> uploadPathIds) {
        if (ListUtil.isBlank(uploadPathIds)) {
            return new ArrayList<>();
        }
        MilepostDesignPlanChangeRecordDto query = new MilepostDesignPlanChangeRecordDto();
        query.setUploadPathIds(uploadPathIds);
        List<MilepostDesignPlanChangeRecordDto> changeRecordDtos = this.selectList(query);
        List<Long> ids = ListUtil.map(changeRecordDtos, "id");

        return ids;
    }

    private MilepostDesignPlanChangeRecordExample buildCondition(MilepostDesignPlanChangeRecordDto query) {
        MilepostDesignPlanChangeRecordExample example = new MilepostDesignPlanChangeRecordExample();
        MilepostDesignPlanChangeRecordExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedFlagEqualTo(DeletedFlag.VALID.code());

        if (query.getId() != null) {
            criteria.andIdEqualTo(query.getId());
        }
        if (query.getProjectId() != null) {
            criteria.andProjectIdEqualTo(query.getProjectId());
        }
        if (query.getMilepostId() != null) {
            criteria.andMilepostIdEqualTo(query.getMilepostId());
        }
        if (query.getStatus() != null) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        if (query.getStatusIsNull() != null) {
            if (query.getStatusIsNull()) {
                criteria.andStatusIsNull();
            } else {
                criteria.andStatusIsNotNull();
            }
        }
        if (query.getUploadPathIdIsNull() != null) {
            if (query.getUploadPathIdIsNull()) {
                criteria.andUploadPathIdIsNull();
            } else {
                criteria.andUploadPathIdIsNotNull();
            }
        }
        if (ListUtil.isPresent(query.getUploadPathIds())) {
            criteria.andUploadPathIdIn(query.getUploadPathIds());
        }

        return example;
    }

    private String getLastestMilepostName(Long projectId) {
        String theLastestMilepostName = null;
        ProjectMilepostDto projectMilepostDto = projectMilepostService.getCurrentMilepostByProjectId(projectId);
        if (projectMilepostDto != null) {
            theLastestMilepostName = projectMilepostDto.getName();
        }
        return theLastestMilepostName;
    }

    /**
     * 设置当前设计信息的各种数量之和
     *
     * @param designPlanDetail
     */
    @Async
    public void setNumSum(MilepostDesignPlanDetailDto designPlanDetail, Project project) {
        if (designPlanDetail.getProjectId() != null && StringUtils.isNotEmpty(designPlanDetail.getErpCode())) {
            if (null == project) {
                project = projectBusinessService.selectByPrimaryKey(designPlanDetail.getProjectId());
            }

            //接受/入库数量
            List<String> transactionTypes = new ArrayList<>();
            transactionTypes.add("RECEIVE");
            transactionTypes.add("RETURN TO RECEIVING");
            transactionTypes.add("DELIVER");
            transactionTypes.add("RETURN TO VENDOR");
            designPlanDetail.setWarehousingNumSum(purchaseProgressService.getTransactionQuantitySum(designPlanDetail.getErpCode(),
                    project.getCode(), transactionTypes));

            //领用数量
            BigDecimal recipientsNumSum = new BigDecimal(0);
            recipientsNumSum =
                    recipientsNumSum.add(materialGetService.getPickingQuantity(designPlanDetail.getProjectId(),
                            designPlanDetail.getErpCode()));
            recipientsNumSum =
                    recipientsNumSum.subtract(materialReturnService.getReturnQuantity(designPlanDetail.getProjectId()
                            , designPlanDetail.getErpCode()));
            designPlanDetail.setRecipientsNumSum(recipientsNumSum);
        }
    }

    @Override
    public MilepostDesignPlanChangeRecordDto getApprovalChangeDetail(Long changeRecordId) {
        MilepostDesignPlanChangeRecordDto dto = this.getById(changeRecordId);
        packageChangeRecordDto(dto);
        return dto;
    }

    @Override
    public MilepostDesignPlanDetailApprovedVO findApprovalingDesignPlanDetail(Long changeRecordId) {
        org.springframework.util.Assert.notNull(changeRecordId, "变更ID不能为空");
        MilepostDesignPlanDetailApprovedVO result = new MilepostDesignPlanDetailApprovedVO();
        //获取发布的所有详设物料
        MilepostDesignPlanChangeRecordDto changeRecordDto = this.getById(changeRecordId);
        packageChangeRecordDto(changeRecordDto);
        if (changeRecordDto != null && changeRecordDto.getDesignPlanDetailChangeHistoryVO() != null) {
            result.setProjectCode(changeRecordDto.getProjectCode());
            DesignPlanDetailChangeHistoryVO changeHistoryVO = changeRecordDto.getDesignPlanDetailChangeHistoryVO();
            MilepostDesignPlanDetailChangeDto designPlanDetailChangeDto = changeHistoryVO.getHistory();
            List<DesignPlanDetailChangeHistoryVO> changeHistoryVOList =
                    changeRecordDto.getDesignPlanDetailChangeHistoryVO().getSonVos();
            MultiValueMap<Long, MilepostDesignPlanDetailDto> detailMap = new LinkedMultiValueMap<>();
            List<MilepostDesignPlanDetailDto> details = new ArrayList<>();
            MilepostDesignPlanDetailDto designPlanDetailDto = BeanConverter.copy(designPlanDetailChangeDto,
                    MilepostDesignPlanDetailDto.class);
            designPlanDetailDto.setId(designPlanDetailChangeDto.getDesignPlanDetailId());
            details.add(designPlanDetailDto);
            detailMap.add(designPlanDetailChangeDto.getParentId(), BeanConverter.copy(designPlanDetailChangeDto,
                    MilepostDesignPlanDetailDto.class));
            if (ListUtils.isNotEmpty(changeHistoryVOList)) {
                for (DesignPlanDetailChangeHistoryVO detailChangeHistoryVO : changeHistoryVOList) {
                    //删除无需导出
                    if (!detailChangeHistoryVO.getType().equals(ChangeType.DEL.code())) {
                        MilepostDesignPlanDetailDto detail = BeanConverter.copy(detailChangeHistoryVO.getChange(),
                                MilepostDesignPlanDetailDto.class);
                        if (MATERIAL_CLASS.equals(detail.getMaterialClassification())) {
                            //统计需求量
                            BigDecimal totalNum =
                                    detail.getNumber().multiply(milepostDesignPlanDetailService.getParentSigmaById(detail.getParentId(), null));
                            detail.setTotalNum(totalNum);
                        }
                        detailMap.add(detailChangeHistoryVO.getChange().getParentId(), detail);
                    }
                }
                //树结构组合
                List<MilepostDesignPlanDetailDto> resultDetails = new ArrayList<>();
                this.buildDesignPlanDetailTree(resultDetails, details, detailMap, null);
                result.setDesignPlanDetailDtos(resultDetails);
            }
        }
        return result;
    }


    /**
     * 组合树
     */
    private void buildDesignPlanDetailTree(List<MilepostDesignPlanDetailDto> resultDetails,
                                           List<MilepostDesignPlanDetailDto> details,
                                           MultiValueMap<Long, MilepostDesignPlanDetailDto> detailMap,
                                           String parentIndex) {
        if (!CollectionUtils.isEmpty(details)) {
            int sonIndex = 1;
            for (MilepostDesignPlanDetailDto detail : details) {
                String serialNumber = this.buildSerialNumber(parentIndex, sonIndex);
                detail.setSerialNumber(serialNumber);
                resultDetails.add(detail);
                List<MilepostDesignPlanDetailDto> sonDetails = detailMap.get(detail.getId());
                if (!CollectionUtils.isEmpty(sonDetails)) {
                    this.buildDesignPlanDetailTree(resultDetails, sonDetails, detailMap, serialNumber);
                }
                ++sonIndex;
            }
        }
    }

    /**
     * 构建序号
     */
    private String buildSerialNumber(String parentIndex, int sonIndex) {
        if (org.springframework.util.StringUtils.hasText(parentIndex)) {
            return parentIndex + "." + sonIndex;
        }
        return sonIndex + "";
    }

    @Override
    public ResponseMap getMilepostDesignPlanChangeApp(Long id) {
        ResponseMap responseMap = new ResponseMap();
        Map<String, String> headMap = new HashMap<>();
        List<AduitAtta> fileList = new ArrayList<>();

        MilepostDesignPlanChangeRecordDto approvalChangeDetail = this.getApprovalChangeDetail(id);
        if (ObjectUtils.isEmpty(approvalChangeDetail)) {
            responseMap.setStatus("fail");
            responseMap.setMsg("数据为空");
        } else {
            DesignPlanDetailChangeHistoryVO designPlanDetailChangeHistoryVO =
                    approvalChangeDetail.getDesignPlanDetailChangeHistoryVO();
            List<CtcAttachmentDto> attachmentDtos = approvalChangeDetail.getAttachmentDtos();
            if (!ObjectUtils.isEmpty(designPlanDetailChangeHistoryVO)) {
                List<DesignPlanDetailChangeHistoryVO> sonVos = designPlanDetailChangeHistoryVO.getSonVos();
                if (CollectionUtils.isNotEmpty(sonVos)) {
                    BigDecimal hisAllListDesignCost = BigDecimal.ZERO;  //变更前所有列的设计成本之和
                    BigDecimal changeAllListDesignCost = BigDecimal.ZERO; //变更后所有列的设计成本之和
                    for (DesignPlanDetailChangeHistoryVO sonVo : sonVos) {
                        BigDecimal hisTotalDesignCost = BigDecimal.ZERO;  //变更前设计成本总计
                        BigDecimal changeTotalDesignCost = BigDecimal.ZERO; //变更后设计成本总计
                        MilepostDesignPlanDetailChangeDto history = sonVo.getHistory(); //变更前的数据
                        MilepostDesignPlanDetailChangeDto change = sonVo.getChange(); //变更后的数据
                        if (!ObjectUtils.isEmpty(history)) {
                            BigDecimal number = history.getNumber() == null ? BigDecimal.ZERO : history.getNumber();
                            BigDecimal designCost = history.getDesignCost() == null ? BigDecimal.ZERO :
                                    history.getDesignCost();
                            hisTotalDesignCost = hisTotalDesignCost.add(number.multiply(designCost));
                        }
                        if (!ObjectUtils.isEmpty(change)) {
                            BigDecimal number = change.getNumber() == null ? BigDecimal.ZERO : change.getNumber();
                            if (Objects.equals(change.getDeletedFlag(), Boolean.TRUE)) {
                                BigDecimal designCost = change.getDesignCost() == null ? BigDecimal.ZERO :
                                        change.getDesignCost().multiply(BigDecimal.valueOf(-1));
                                changeTotalDesignCost = changeTotalDesignCost.add(number.multiply(designCost));
                            } else {
                                BigDecimal designCost = change.getDesignCost() == null ? BigDecimal.ZERO :
                                        change.getDesignCost();
                                changeTotalDesignCost = changeTotalDesignCost.add(number.multiply(designCost));
                            }
                        }
                        hisAllListDesignCost = hisAllListDesignCost.add(hisTotalDesignCost);
                        changeAllListDesignCost = changeAllListDesignCost.add(changeTotalDesignCost);
                    }

                    BigDecimal costFluctuate = changeAllListDesignCost.subtract(hisAllListDesignCost); //设计成本增减
                    String costFluctuateStr = "";
                    if (BigDecimalUtils.isGreater(costFluctuate, BigDecimal.ZERO)) {
                        costFluctuateStr = "+" + costFluctuate.stripTrailingZeros().toPlainString();
                    } else {
                        costFluctuateStr = costFluctuate.stripTrailingZeros().toPlainString();
                    }
                    headMap.put("costFluctuate", costFluctuateStr);//设计成本增减
                } else {
                    headMap.put("costFluctuate", " ");
                }
            }
            headMap.put("projectName", approvalChangeDetail.getProjectName() == null ? "" :
                    approvalChangeDetail.getProjectName());
            headMap.put("projectCode", approvalChangeDetail.getProjectCode() == null ? "" :
                    approvalChangeDetail.getProjectCode());
            headMap.put("changeReason", approvalChangeDetail.getChangeReason() == null ? "" :
                    approvalChangeDetail.getChangeReason());
            headMap.put("uploadPath", approvalChangeDetail.getUploadPath() == null ? "" :
                    approvalChangeDetail.getUploadPath());
            headMap.put("currency", "CNY");
            headMap.put("remark", approvalChangeDetail.getRemark() == null ? "" : approvalChangeDetail.getRemark());

            if (CollectionUtils.isNotEmpty(attachmentDtos)) {
                for (CtcAttachmentDto attachmentDto : attachmentDtos) {
                    AduitAtta aduitAtta = new AduitAtta();
                    aduitAtta.setFdId(attachmentDto.getAttachId() == null ? "" :
                            attachmentDto.getAttachId().toString());
                    aduitAtta.setFileName(attachmentDto.getFileName() == null ? attachmentDto.getAttachName() :
                            attachmentDto.getFileName());
                    aduitAtta.setFileSize(attachmentDto.getFileSize() == null ? "" :
                            attachmentDto.getFileSize().toString());
                    fileList.add(aduitAtta);
                }
            }
            //变更明细附件
            CtcAttachmentDto attachmentQuery = new CtcAttachmentDto();
            attachmentQuery.setModule(CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY_DETAIL.code());
            attachmentQuery.setModuleId(id);
            attachmentDtos = ctcAttachmentService.selectList(attachmentQuery);
            if (CollectionUtils.isNotEmpty(attachmentDtos)) {
                CtcAttachmentDto attachmentDto = attachmentDtos.get(0);//驳回/撤回会重新生成，取最新的一个即可
                AduitAtta aduitAtta = new AduitAtta();
                aduitAtta.setFdId(attachmentDto.getAttachId() == null ? "" :
                        String.valueOf(attachmentDto.getAttachId()));
                aduitAtta.setFileName(attachmentDto.getFileName() == null ? attachmentDto.getAttachName() :
                        attachmentDto.getFileName());
                aduitAtta.setFileSize(attachmentDto.getFileSize().toString());
                fileList.add(aduitAtta);
            }
            responseMap.setStatus("success");
            responseMap.setMsg("sucess");
            responseMap.setHeadMap(headMap);
            responseMap.setFileList(fileList);
        }
        return responseMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatusForMilepostDesignPlan(MilepostDesignPlanDetailDto milepostDesignPlanDetailDto) {
        Long formInstanceId = milepostDesignPlanDetailDto.getFormInstanceId();
        String fdInstanceId = milepostDesignPlanDetailDto.getFdInstanceId();
        String formUrl = milepostDesignPlanDetailDto.getFormUrl();
        String eventName = milepostDesignPlanDetailDto.getEventName();
        String handlerId = milepostDesignPlanDetailDto.getHandlerId();
        Long companyId = milepostDesignPlanDetailDto.getCompanyId();
        Long createUserId = milepostDesignPlanDetailDto.getCreateUserId();
        Integer checkStatus = milepostDesignPlanDetailDto.getCheckStatus();
        logger.info("详细设计变更审批作废和删除回调 formInstanceId:{}, fdInstanceId:{}, formUrl:{}, eventName:{}, handlerId:{}, companyId:{}, " +
                "createUserId:{}", formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        String lockName = String.format("MilepostDesignPlanChangeRecord_changeStatusForMilepostDesignPlan_%s_%s_%s_%s",
                formInstanceId, fdInstanceId, formUrl, eventName);
        String currentDateStr = DateUtil.format(new Date(), "yyyyMMdd");
        try {
            if (DistributedCASLock.lock(lockName, currentDateStr, MAX_WAIT_TIME, MAX_WAIT_TIME * 2)) {
                // 查到数据说明该接口已经回调成功过
                if (workflowCallbackService.haveSuccessCallBack(formInstanceId, fdInstanceId, formUrl, eventName)) {
                    return;
                }

                MipCallbackLog successMipCallbackLog = null;
                FormInstance formInstance = null;
                try {
                    successMipCallbackLog = workflowCallbackService.getSuccessMipCallbackLog(formInstanceId, fdInstanceId, formUrl, eventName);
                    formInstance = workflowCallbackService.getDeleteAndAbandonFormInstance(formInstanceId, formUrl);

                    MilepostDesignPlanChangeRecord changeRecord = milepostDesignPlanChangeRecordMapper.selectByPrimaryKey(formInstanceId);
                    Guard.notNull(changeRecord, String.format("变更记录id：%s，在milepost_design_plan_change_record变更记录表中不存在", formInstanceId));
                    Guard.notNull(changeRecord.getUploadPathId(), String.format("变更记录id：%s，在milepost_design_plan_change_record变更记录表的上传路径id不能为空",
                            formInstanceId));
                    MilepostDesignPlanDetailDto historyModelDetailDto = milepostDesignPlanDetailService.getById(changeRecord.getUploadPathId());
                    historyModelDetailDto.setStatus(checkStatus);
                    milepostDesignPlanDetailService.save(historyModelDetailDto, createUserId);

                    if (Objects.equals(CheckStatus.MDP_CHANGE_CANCEL.code(), checkStatus)) {
                        //流程作废
                        MilepostDesignPlanChangeRecord record = new MilepostDesignPlanChangeRecord();
                        record.setId(changeRecord.getId());
                        record.setStatus(checkStatus);
                        milepostDesignPlanChangeRecordMapper.updateByPrimaryKeySelective(record);
                    } else if (Objects.equals(CheckStatus.MDP_CHANGE_DELETE.code(), checkStatus)) {
                        //流程删除
                        MilepostDesignPlanChangeRecord record = new MilepostDesignPlanChangeRecord();
                        record.setId(changeRecord.getId());
                        record.setDeletedFlag(Boolean.TRUE);
                        milepostDesignPlanChangeRecordMapper.updateByPrimaryKeySelective(record);
                    }

                    workflowCallbackService.dealSuccessCallbackByWhetherProcessEnd(formInstanceId, fdInstanceId, formUrl, eventName,
                            formInstance, false);
                } catch (ApplicationBizException e) {
                    logger.info("详细设计变更审批作废和删除回调接口失败:{}", e.getMessage());
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance, e.getMessage());
                    throw e;
                } catch (Exception e) {
                    logger.error("详细设计变更审批作废和删除回调接口失败", e);
                    workflowCallbackService.dealFailedCallback(successMipCallbackLog, createUserId, formInstance,
                            ExceptionUtils.getExceptionMessage(e));
                    throw e;
                }

            }
        } catch (Exception e) {
            logger.error("详细设计变更审批作废和删除回调加锁失败", e);
            throw e;
        } finally {
            DistributedCASLock.unLock(lockName, currentDateStr);
        }
    }

    private void packageChangeRecordDto(MilepostDesignPlanChangeRecordDto dto) {
        if (dto != null) {
            if (dto.getPublisherBy() != null) {
                UserInfo publisher = CacheDataUtils.findUserById(dto.getPublisherBy());
                if (publisher != null) {
                    dto.setPublisherName(publisher.getName());
                }
            }

            if (dto.getUploadPathId() != null) {
                //构建详细设计变更信息
                this.buildDesignPlanDetailChangeHistoryVO(dto);
            }
            //附件信息
            this.buildChangeRecordCtcAttachmentDto(dto);

            if (dto.getProjectId() != null) {
                Project project = projectBusinessService.selectByPrimaryKey(dto.getProjectId());
                if (project != null) {
                    dto.setProjectName(project.getName());
                    dto.setProjectCode(project.getCode());
                    //所处里程碑阶段
                    dto.setTheLastestMilepostName(getLastestMilepostName(dto.getProjectId()));
                }
            }
        }
    }

    /**
     * 构建变更记录的附件信息
     */
    private void buildChangeRecordCtcAttachmentDto(MilepostDesignPlanChangeRecordDto dto) {
        List<CtcAttachmentDto> ctcAttachmentDtos = ctcAttachmentService.findCtcAttachmentDtos(dto.getId(),
                CtcAttachmentModule.DETAILED_SOLUTION_DELIVERY.code(), null);
        dto.setAttachmentDtos(ctcAttachmentDtos);
    }

    /**
     * 检查提交的模组是否已经在进度确认中
     */
    private void checkMilepostDesignPlanDetail(MilepostDesignPlanChangeRecordDto dto) {
        Long projectId = dto.getProjectId();
        Long designPlanDetailId = dto.getDetailChangeDto().getDesignPlanDetailId();
        if (designPlanDetailId != null) {
            List<MilepostDesignPlanDetail> parentRecordsInApprovalList = milepostDesignPlanDetailExtMapper.selectParentRecordsInApprovalById(designPlanDetailId);
            if (ListUtils.isNotEmpty(parentRecordsInApprovalList)) {
                for (MilepostDesignPlanDetail milepostDesignPlanDetail : parentRecordsInApprovalList) {
                    Integer moduleStatus = milepostDesignPlanDetail.getModuleStatus();
                    if (Objects.equals(moduleStatus, MilepostDesignPlanDetailModelStatus.CONFIRMING.code())) {
                        String errorCode = "模组[" + milepostDesignPlanDetail.getMaterielDescr() + "]正在进度确认中，不能再进行变更";
                        throw new ApplicationBizException(errorCode);
                    }
                }
            }
        }
    }

    @Override
    public List<DesignPlanChangeReportDTO> designPlanChangeReport(List<MilepostDesignPlanDetailDto> dtos) {
        List<DesignPlanChangeReportDTO> list = new ArrayList<>();
        for (MilepostDesignPlanDetailDto dto : dtos) {
            List<DesignPlanChangeReportDTO> getList = getReport(dto);
            if (getList.size() > 0) {
                list.addAll(getList);
            }
        }


        int i = 1;
        for (DesignPlanChangeReportDTO l : list) {
            l.setIndex(i++);
        }
        return list;
    }

    //遍历
    public List<DesignPlanChangeReportDTO> getReport(MilepostDesignPlanDetailDto dto) {
        List<DesignPlanChangeReportDTO> list = new ArrayList<>();
        DesignPlanChangeReportDTO report = new DesignPlanChangeReportDTO();
        report = BeanConverter.copy(dto, DesignPlanChangeReportDTO.class);
        //PAM物料编码	ERP物料编码	物料描述	单位	中类	小类	项目	模组	数量	已产生采购需求	工单任务号	已领料	单价	金额	影响说明
        report.setDescr(dto.getMaterielDescr());
        report.setUnitName(dto.getUnit());
        report.setGenerateRequirement("否");
        String model = "否";
        if (null != dto.getWhetherModel()) {
            if (dto.getWhetherModel()) {
                model = "是";
            }
        }
        report.setModel(model);
        //report.setCodingMiddleclass(dto.getCodingMiddleClass());
        //report.setMaterialType(dto.getMaterielType());
        if (null != dto.getProjectId()) {
            Project project = projectBusinessService.selectByPrimaryKey(dto.getProjectId());
            if (null != project && StringUtils.isNotEmpty(project.getName())) {
                report.setProject(project.getName());
            }
        }
        //新增
        if (null == dto.getId()) {
            //金额
            BigDecimal budgetSubtotal = BigDecimal.ZERO;
            if (null != dto.getNumber() && null != dto.getBudgetUnitPrice()) {
                budgetSubtotal = dto.getNumber().multiply(dto.getBudgetUnitPrice());
            }
            report.setBudgetSubtotal(budgetSubtotal);

            list.add(report);
        } else if (null != dto.getDeletedFlag() && dto.getDeletedFlag()) {

            //删除物料
            //金额
            BigDecimal budgetSubtotal = BigDecimal.ZERO;
            //数量
            dto.setNumber(BigDecimal.ZERO.subtract(dto.getNumber()));
            if (null != dto.getNumber() && null != dto.getBudgetUnitPrice()) {
                budgetSubtotal = dto.getNumber().multiply(dto.getBudgetUnitPrice());
            }
            report.setBudgetSubtotal(budgetSubtotal);
            list.add(report);
        } else {
            List<MilepostDesignPlanDetailDto> oldDtos = getMilepostDesignPlanDetail(dto.getId());
            if (oldDtos.size() > 0) {
                MilepostDesignPlanDetailDto old = oldDtos.get(0);
                boolean hadChage = false;
                //数量变化
                if (null == old.getNumber()) {
                    old.setNumber(BigDecimal.ZERO);
                }
                if (null == dto.getNumber()) {
                    dto.setNumber(BigDecimal.ZERO);
                }
                if (dto.getNumber().compareTo(old.getNumber()) != 0) {
                    hadChage = true;

                }
                if (null != old.getGenerateRequirement()) {
                    if (old.getGenerateRequirement()) {
                        hadChage = true;
                    }
                }
                //是否数据变更
                if (hadChage) {
                    //已产生采购需求
                    if (null != old.getGenerateRequirement()) {
                        if (old.getGenerateRequirement()) {
                            report.setGenerateRequirement("是");
                        }
                    }
                    //变更数量
                    //数量
                    BigDecimal number = dto.getNumber().subtract(old.getNumber());
                    report.setNumber(number);
                    //金额
                    BigDecimal budgetSubtotal = BigDecimal.ZERO;
                    if (null != dto.getNumber() && null != dto.getBudgetUnitPrice()) {
                        budgetSubtotal = dto.getNumber().multiply(dto.getBudgetUnitPrice());
                    }
                    report.setBudgetSubtotal(budgetSubtotal);
                    list.add(report);
                }
            }
        }
        if (null != dto.getSonDtos() && dto.getSonDtos().size() > 0) {
            List<MilepostDesignPlanDetailDto> sons = dto.getSonDtos();
            for (MilepostDesignPlanDetailDto son : sons) {
                if (null == son.getProjectId()) {
                    son.setProjectId(dto.getProjectId());
                }
                List<DesignPlanChangeReportDTO> getList = getReport(son);
                if (getList.size() > 0) {
                    list.addAll(getList);
                }
            }
        }

        return list;
    }

    public List<MilepostDesignPlanDetailDto> getMilepostDesignPlanDetail(Long id) {
        MilepostDesignPlanDetailDto query = new MilepostDesignPlanDetailDto();
        query.setId(id);
        query.setDeletedFlag(false);
        List<MilepostDesignPlanDetailDto> dtos = milepostDesignPlanDetailService.selectList(query);
        return dtos;
    }

    /**
     * 查找所有没有isUpdate属性的记录比较持久化的记录，不同则将更新dto的值
     *
     * @param detailChangeDto 包含树形结构的DTO对象
     * @return 没有isUpdate属性的记录集合
     */
    public void findAndEditNoUpdateRecords(MilepostDesignPlanDetailChangeDto detailChangeDto) {

        // 处理根节点
        if (detailChangeDto != null) {
            if (detailChangeDto.getIsUpdate() == null) {
                checkNoUpdate(detailChangeDto);
            }

            // 如果存在子节点，递归处理所有子节点
            if (detailChangeDto.getSonDtos() != null && !detailChangeDto.getSonDtos().isEmpty()) {
                for (MilepostDesignPlanDetailChangeDto childDto : detailChangeDto.getSonDtos()) {
                    findAndEditNoUpdateRecords(childDto);
                }
            }
        }
    }

    private void checkNoUpdate(MilepostDesignPlanDetailChangeDto detailChangeDto) {
        Long designPlanDetailId = detailChangeDto.getDesignPlanDetailId();
        MilepostDesignPlanDetail persistenceDesignPlanDetail = milepostDesignPlanDetailMapper.selectByPrimaryKey(designPlanDetailId);
        if (Objects.isNull(persistenceDesignPlanDetail)) return;
        BigDecimal persistenceNumber = persistenceDesignPlanDetail.getNumber();
        String persistenceChartVersion = persistenceDesignPlanDetail.getChartVersion();
        BigDecimal changeNumber = detailChangeDto.getNumber();
        String changChartVersion = detailChangeDto.getChartVersion();
        //没有修改的详设跟持久化的详设数量判断
        if ((Objects.nonNull(changeNumber) && Objects.nonNull(persistenceNumber)) && changeNumber.compareTo(persistenceNumber) != 0) {
            detailChangeDto.setNumber(persistenceNumber);
        }
        if (!Objects.equals(changChartVersion, persistenceChartVersion)) {
            detailChangeDto.setChartVersion(persistenceChartVersion);
        }
    }

    /**
     * 组装平铺的详设数据为层级结构并计算需求量
     * 以前端传入的详设数据为准
     *
     * @param projectWbsReceiptsDto 包含平铺详设数据的DTO
     */
    private void assembleFlatDesignPlanToHierarchy(ProjectWbsReceiptsDto projectWbsReceiptsDto) {
        logger.info("开始组装平铺的详设数据为层级结构并计算需求量");
        List<MilepostDesignPlanDetailChangeDto> flatDetailChangeDtoList = projectWbsReceiptsDto.getFlatDetailChangeDto();
        if(ListUtils.isEmpty(flatDetailChangeDtoList)) {
            logger.info("无平铺详设数据，跳过组装处理");
            return;
        }

        logger.info("平铺的详设数据数量：{}", flatDetailChangeDtoList.size());

//        Long wbsReceiptsId = projectWbsReceiptsDto.getId();
//
//        //单据ID存在代表重新编辑提交,原parentId前端传入是变更表的ID，需要转换成详设正式表的parentId
//        if(Objects.nonNull(wbsReceiptsId)){
//            flatDetailChangeDtoList.forEach(detail->{
//                Long designPlanDetailId = detail.getDesignPlanDetailId();
//                MilepostDesignPlanDetailDto milepostDesignPlanDetailDto = milepostDesignPlanDetailService.getById(designPlanDetailId);
//                detail.setParentId(milepostDesignPlanDetailDto.getParentId());
//            });
//        }

        for (MilepostDesignPlanDetailChangeDto flatDetailChangeDto : flatDetailChangeDtoList) {
            Long designPlanDetailId = flatDetailChangeDto.getDesignPlanDetailId();
            if(Objects.nonNull(designPlanDetailId)){
                if(Objects.isNull(flatDetailChangeDto.getHasConfirmed())){
                    List<ProjectWbsReceiptsDto> projectWbsReceiptsDtos = projectWbsReceiptsService.selectByDetailIds(Lists.newArrayList(flatDetailChangeDto.getDesignPlanDetailId()));
                    if(ListUtils.isNotEmpty(projectWbsReceiptsDtos)){
                        flatDetailChangeDto.setHasConfirmed(true);
                    }
                }
            }
        }

        // 新增：处理designPlanParentId到tmpId的关联关系
        processDesignPlanParentIdToTmpIdRelation(flatDetailChangeDtoList);

        // 处理基于uid的多层级新增关系，构建独立树并从平铺列表中移除
        List<MilepostDesignPlanDetailChangeDto> uidBasedTrees = processUidBasedHierarchyAndRemoveFromFlat(flatDetailChangeDtoList);

        // 1. 收集所有需要查询的父节点ID和构建上传路径列表
        Set<Long> parentIdSet = new HashSet<>();
        List<MilepostDesignPlanDetailDto> uploadPathList = new ArrayList<>();
        List<Long> parentIds = new ArrayList<>();

        // 按父节点ID分组存储前端传入的详设
        Map<Long, List<MilepostDesignPlanDetailChangeDto>> flatMilepostDesignPlanDtoMap = new HashMap<>();

        // 创建前端传入数据的ID映射，用于后续优先使用前端数据
        Map<Long, MilepostDesignPlanDetailChangeDto> frontendDtoMap = new HashMap<>();

        for(MilepostDesignPlanDetailChangeDto dto : flatDetailChangeDtoList) {
            // 存储前端传入的数据到映射表
            Long dtoId = dto.getDesignPlanDetailId() != null ? dto.getDesignPlanDetailId() : dto.getId();
            if(dtoId != null) {
                frontendDtoMap.put(dtoId, dto);
            }

            if(dto.getParentId() != null && dto.getParentId() != -1L) {
                parentIdSet.add(dto.getParentId());

                // 构建uploadPathList
                MilepostDesignPlanDetailDto pathDto = new MilepostDesignPlanDetailDto();
                pathDto.setId(dto.getParentId()); // 设置为当前节点的parentId

                // 查找父节点的parentId
                MilepostDesignPlanDetail parentNode = milepostDesignPlanDetailMapper.selectByPrimaryKey(dto.getParentId());
                if(parentNode != null && !Boolean.TRUE.equals(parentNode.getDeletedFlag())) {
                    pathDto.setParentId(parentNode.getParentId());
                } else {
                    pathDto.setParentId(-1L);
                }

                if(Objects.nonNull(parentNode)){
                    uploadPathList.add(pathDto);
                }

                // 添加到parentIds列表
                if(!parentIds.contains(dto.getParentId())) {
                    parentIds.add(dto.getParentId());
                }

                // 按父节点ID分组
                flatMilepostDesignPlanDtoMap.computeIfAbsent(dto.getParentId(), k -> new ArrayList<>()).add(dto);
            } else {
                // 根节点或无父节点的情况
                flatMilepostDesignPlanDtoMap.computeIfAbsent(-1L, k -> new ArrayList<>()).add(dto);
            }
        }

        // 2. 使用milepostDesignPlanService.packagePlanDetail构建父级结构
        List<MilepostDesignPlanDetailDto> packagePlanDetailList = new ArrayList<>();
        if(!parentIdSet.isEmpty()) {
            // 转换为MilepostDesignPlanDetailDto列表并去重
            List<MilepostDesignPlanDetailDto> parentDetailDtos = uploadPathList.stream()
                    .filter(dto -> dto.getId() != null)
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MilepostDesignPlanDetailDto::getId))),
                            ArrayList::new
                    ));

            // 调用packagePlanDetail方法构建完整的父级结构
            packagePlanDetailList = milepostDesignPlanService.packagePlanDetail(parentDetailDtos);
            logger.info("使用packagePlanDetail构建的父级结构数量：{}", packagePlanDetailList.size());
        }

        // 3. 将前端传入的节点转换为Map，便于后续处理
        Map<Long, MilepostDesignPlanDetailChangeDto> flatDtoMap = new HashMap<>(frontendDtoMap);

        // 4. 处理模组层级的删除标记传递和子节点查询
        for(MilepostDesignPlanDetailChangeDto dto : flatDetailChangeDtoList) {
            // 如果是模组层且标记为删除，则递归设置其所有子节点为删除状态
            if(Boolean.TRUE.equals(dto.getIsDelete()) && isModuleLayer(dto)) {
                // 查询该模组的所有子节点
                Long moduleId = dto.getDesignPlanDetailId() != null ? dto.getDesignPlanDetailId() : dto.getId();
                List<MilepostDesignPlanDetail> children = getAllChildrenByParentId(moduleId);

                // 将子节点添加到Map并标记为删除
                for(MilepostDesignPlanDetail child : children) {
                    // 优先使用前端传入的数据
                    if(frontendDtoMap.containsKey(child.getId())) {
                        MilepostDesignPlanDetailChangeDto frontendDto = frontendDtoMap.get(child.getId());
                        frontendDto.setIsDelete(true);
                        flatDtoMap.put(child.getId(), frontendDto);
                    } else {
                        // 如果前端没有传入该节点，则创建新的DTO
                        MilepostDesignPlanDetailChangeDto childDto = BeanConverter.copy(child, MilepostDesignPlanDetailChangeDto.class);
                        childDto.setDesignPlanDetailId(child.getId());
                        childDto.setIsDelete(true);
                        flatDtoMap.put(child.getId(), childDto);
                    }
                }
            }

            //如果DTO对象的isDelete为true，则查询所有下级并添加到sonDtos中
            if(Boolean.TRUE.equals(dto.getIsDelete())) {
                Long entityId = dto.getDesignPlanDetailId() != null ? dto.getDesignPlanDetailId() : dto.getId();
                if(entityId != null) {
                    // 查询所有下级节点
                    List<MilepostDesignPlanDetail> allChildren = getAllChildrenByParentId(entityId);
                    if(ListUtils.isNotEmpty(allChildren)) {
                        // 初始化sonDtos列表
                        if(dto.getSonDtos() == null) {
                            dto.setSonDtos(new ArrayList<>());
                        }

                        // 将所有下级添加到sonDtos中
                        for(MilepostDesignPlanDetail child : allChildren) {
                            // 创建子节点DTO
                            MilepostDesignPlanDetailChangeDto childDto = BeanConverter.copy(child, MilepostDesignPlanDetailChangeDto.class);
                            childDto.setDesignPlanDetailId(child.getId());
                            childDto.setIsDelete(true); // 设置删除标记
                            childDto.setChangeType("2");  // changType 0 更新 1 新增 2 删除
                            List<ProjectWbsReceiptsDto> projectWbsReceiptsDtos = projectWbsReceiptsService.selectByDetailIds(Lists.newArrayList(childDto.getDesignPlanDetailId()));
                            if(ListUtils.isNotEmpty(projectWbsReceiptsDtos)){
                                childDto.setHasConfirmed(Boolean.TRUE);
                                childDto.setAfterRequirementPublish(Boolean.TRUE);
                            }
                            // 添加到sonDtos中
                            dto.getSonDtos().add(childDto);
                        }

                        logger.info("为删除的节点ID：{}添加了{}个子节点并设置为删除状态", entityId, allChildren.size());
                    }
                }
            }
        }

        // 5. 将packagePlanDetail构建的结构转换为MilepostDesignPlanDetailChangeDto
        if(ListUtils.isNotEmpty(packagePlanDetailList)) {
            MilepostDesignPlanDetailDto rootNode = packagePlanDetailList.get(0);
            logger.info("组装后的根节点ID：{}", rootNode.getId());

            // 将MilepostDesignPlanDetailDto转换为MilepostDesignPlanDetailChangeDto
            MilepostDesignPlanDetailChangeDto rootChangeDto = convertToChangeDto(rootNode);

            // 6. 将前端传入的节点追加到树结构中，确保以前端数据为准
            flatMilepostDesignPlanDtoMap.forEach((parentId, flatMilepostDesignPlanDetailDtos) -> {
                MilepostDesignPlanDetailChangeDto parentNode = findEntityById(rootChangeDto, parentId.toString());
                if (parentNode != null) {
                    if (parentNode.getSonDtos() == null) {
                        parentNode.setSonDtos(new ArrayList<>());
                    }

                    // 创建一个新的子节点列表，优先使用前端传入的数据
                    List<MilepostDesignPlanDetailChangeDto> newSonDtos = new ArrayList<>();

                    // 添加前端传入的子节点
                    for (MilepostDesignPlanDetailChangeDto frontendDto : flatMilepostDesignPlanDetailDtos) {
                        newSonDtos.add(frontendDto);
                    }

                    // 设置新的子节点列表
                    parentNode.setSonDtos(newSonDtos);
                }
            });

            // 7. 计算需求量
            calculateDemandTotalNum(rootChangeDto);

            // 8. 设置结果
            projectWbsReceiptsDto.setDetailChangeDto(rootChangeDto);

            // 设置uploadPathList (去重)
            List<MilepostDesignPlanDetailDto> distinctUploadPathList = uploadPathList.stream()
                    .filter(dto -> dto.getId() != null)
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MilepostDesignPlanDetailDto::getId))),
                            ArrayList::new
                    ));
            projectWbsReceiptsDto.setUploadPathList(distinctUploadPathList);

            // 设置batchUploadPathIds
            if(ListUtils.isNotEmpty(parentIds)) {
                String pathIds = parentIds.stream().map(String::valueOf).collect(Collectors.joining(","));
                projectWbsReceiptsDto.setBatchUploadPathIds(pathIds);
                logger.info("设置批量上传路径IDs：{}", pathIds);
            }

            // 处理装配件类型详设的特殊逻辑
            processAssemblyPartsMaterialCategory(projectWbsReceiptsDto, flatDetailChangeDtoList);
            logger.info("已完成装配件类型详设的特殊处理");

            // 新增：为传入详设的所有上级节点设置historyType=1
            // 收集所有需要设置historyType的父节点ID
            Set<Long> allParentIds = new HashSet<>();
            for(MilepostDesignPlanDetailChangeDto dto : flatDetailChangeDtoList) {
                if(dto.getParentId() != null && dto.getParentId() != -1L) {
                    // 收集所有上级节点ID
                    collectAllParentIds(dto.getParentId(), allParentIds);
                }
            }

            // 为收集到的所有上级节点设置historyType=1
            if(!allParentIds.isEmpty()) {
                setHistoryTypeForParentNodes(rootChangeDto, allParentIds);
                logger.info("已为{}个上级节点设置historyType=1", allParentIds.size());
            }


            // 检查外购物料或看板物料的上层详设moduleStatus状态
            checkParentConfirmStatusForMaterials(rootChangeDto);
            logger.info("已完成外购物料和看板物料的上层moduleStatus检查");

            logger.info("完成平铺详设数据的层级结构组装和需求量计算");
            //清空projectWbsReceiptsDto.getFlatDetailChangeDto()
            projectWbsReceiptsDto.setFlatDetailChangeDto(null);
        } else {
            logger.warn("未能构建有效的父级结构，无法组装层级结构");
        }

    }

    /**
     * 处理装配件类型详设的特殊逻辑
     * 当前端传入的详设(修改或删除)materialCategory=装配件时：
     * 1. uploadPathList需要将这个详设的父级ID对应的记录删除
     * 2. batchUploadPathIds需要将这条详设的父级ID删除，加入这条详设的ID
     *
     * @param projectWbsReceiptsDto 包含详设数据的DTO
     * @param flatDetailChangeDtoList 平铺的详设数据列表
     */
    private void processAssemblyPartsMaterialCategory(ProjectWbsReceiptsDto projectWbsReceiptsDto,
                                                      List<MilepostDesignPlanDetailChangeDto> flatDetailChangeDtoList) {
        logger.info("开始处理装配件类型详设的特殊逻辑");

        if (ListUtils.isEmpty(flatDetailChangeDtoList)) {
            logger.info("无详设数据，跳过处理");
            return;
        }

        // 需要从uploadPathList中移除的父级ID
        Set<Long> removeFromUploadPathIds = new HashSet<>();
        // 需要添加到uploadPathList的详设ID
        Set<Long> addToUploadPathIds = new HashSet<>();
        // 需要从batchUploadPathIds中移除的父级ID
        Set<Long> removeFromBatchUploadPathIds = new HashSet<>();
        // 需要添加到batchUploadPathIds的详设ID
        Set<Long> addToBatchUploadPathIds = new HashSet<>();
        // 装配件详设信息映射
        Map<Long, MilepostDesignPlanDetailChangeDto> assemblyPartsMap = new HashMap<>();

        // 遍历平铺的详设数据，只处理装配件类型的详设
        for (MilepostDesignPlanDetailChangeDto dto : flatDetailChangeDtoList) {
            // 检查是否为装配件且为修改或删除操作
            // 排除同时具有isNew=true和isDelete=true的装配件，这种情况不进行特殊处理
            if ("装配件".equals(dto.getMaterialCategory()) &&
                    (Boolean.TRUE.equals(dto.getIsUpdate()) || Boolean.TRUE.equals(dto.getIsDelete())) &&
                    !(Boolean.TRUE.equals(dto.getIsNew()) && Boolean.TRUE.equals(dto.getIsDelete()))) {

                Long detailId = dto.getDesignPlanDetailId() != null ? dto.getDesignPlanDetailId() : dto.getId();
                Long parentId = dto.getParentId();

                if (detailId != null && parentId != null && parentId != -1L) {
                    logger.info("发现装配件类型详设，ID: {}, 父级ID: {}", detailId, parentId);

                    // 保存装配件详设信息
                    assemblyPartsMap.put(detailId, dto);

                    // 标记需要从uploadPathList中移除的父级ID
                    removeFromUploadPathIds.add(parentId);

                    // 标记需要添加到uploadPathList的详设ID
                    addToUploadPathIds.add(detailId);

                    // 标记需要从batchUploadPathIds中移除的父级ID
                    removeFromBatchUploadPathIds.add(parentId);

                    // 标记需要添加到batchUploadPathIds的详设ID
                    addToBatchUploadPathIds.add(detailId);
                }
            }
        }

        // 如果没有找到装配件详设，直接返回，不做任何处理
        if (assemblyPartsMap.isEmpty()) {
            logger.info("未找到装配件类型详设，跳过处理");
            return;
        }

        // 处理uploadPathList
        if (!removeFromUploadPathIds.isEmpty() || !addToUploadPathIds.isEmpty()) {
            // 获取原始uploadPathList
            List<MilepostDesignPlanDetailDto> originalUploadPathList = projectWbsReceiptsDto.getUploadPathList();
            if (originalUploadPathList == null) {
                originalUploadPathList = new ArrayList<>();
            }

            // 创建新的uploadPathList，移除需要移除的父级ID
            List<MilepostDesignPlanDetailDto> newUploadPathList = originalUploadPathList.stream()
                    .filter(dto -> !removeFromUploadPathIds.contains(dto.getId()))
                    .collect(Collectors.toList());

            // 为每个需要添加的装配件详设创建上传路径记录
            for (Long detailId : addToUploadPathIds) {
                MilepostDesignPlanDetailChangeDto dto = assemblyPartsMap.get(detailId);
                if (dto != null) {
                    MilepostDesignPlanDetailDto pathDto = new MilepostDesignPlanDetailDto();
                    pathDto.setId(detailId);
                    pathDto.setParentId(dto.getParentId());
                    pathDto.setMaterielDescr(dto.getMaterielDescr());

                    newUploadPathList.add(pathDto);
                    logger.info("为装配件详设ID: {}创建上传路径记录", detailId);
                }
            }

            // 设置新的uploadPathList
            projectWbsReceiptsDto.setUploadPathList(newUploadPathList);
            logger.info("更新uploadPathList，移除了{}个父级ID，添加了{}个详设ID",
                    removeFromUploadPathIds.size(), addToUploadPathIds.size());
        }

        // 处理batchUploadPathIds
        if (!removeFromBatchUploadPathIds.isEmpty() || !addToBatchUploadPathIds.isEmpty()) {
            String batchUploadPathIds = projectWbsReceiptsDto.getBatchUploadPathIds();
            Set<String> pathIdSet = new HashSet<>();

            // 解析现有的batchUploadPathIds
            if (StringUtils.hasText(batchUploadPathIds)) {
                pathIdSet.addAll(Arrays.asList(batchUploadPathIds.split(",")));
            }

            // 移除需要删除的父级ID
            for (Long id : removeFromBatchUploadPathIds) {
                pathIdSet.remove(id.toString());
            }

            // 添加需要新增的详设ID
            for (Long id : addToBatchUploadPathIds) {
                pathIdSet.add(id.toString());
            }

            // 重新组装batchUploadPathIds
            String newBatchUploadPathIds = pathIdSet.stream().collect(Collectors.joining(","));
            projectWbsReceiptsDto.setBatchUploadPathIds(newBatchUploadPathIds);

            logger.info("更新batchUploadPathIds，移除了{}个父级ID，添加了{}个详设ID",
                    removeFromBatchUploadPathIds.size(), addToBatchUploadPathIds.size());
        }
    }

    /**
     * 递归检查外购物料或看板物料的上层详设确认状态
     * 如果上层详设的hasConfirmed为true，且当前详设有变更操作，则设置当前详设的afterRequirementPublish为true
     *
     * @param node 当前节点
     */
    private void checkParentConfirmStatusForMaterials(MilepostDesignPlanDetailChangeDto node) {
        if(node == null) {
            return;
        }
        // 递归处理子节点
        if(ListUtils.isNotEmpty(node.getSonDtos())) {
            for(MilepostDesignPlanDetailChangeDto childDto : node.getSonDtos()) {
                // 判断是否为外购物料或看板物料
                if("外购物料".equals(childDto.getMaterialCategory()) || "看板物料".equals(childDto.getMaterialCategory())) {
                    logger.info("检查详设 pamCode: {}, 物料类别: {}", childDto.getPamCode(), childDto.getMaterialCategory());

                    // 检查父节点的hasConfirmed
                    Boolean hasConfirmed = node.getHasConfirmed();
                    logger.info("详设 pamCode: {} 的上层详设hasConfirmed: {}", childDto.getPamCode(), hasConfirmed);

                    // 检查当前节点是否有变更操作（新增、更新或删除）
                    boolean isNew = Boolean.TRUE.equals(childDto.getIsNew());
                    boolean isUpdate = Boolean.TRUE.equals(childDto.getIsUpdate());
                    boolean isDelete = Boolean.TRUE.equals(childDto.getIsDelete());
                    boolean hasChangeOperation = isNew || isUpdate || isDelete;

                    logger.info("详设 pamCode: {} 的变更状态: isNew={}, isUpdate={}, isDelete={}, hasChangeOperation={}",
                            childDto.getPamCode(), isNew, isUpdate, isDelete, hasChangeOperation);

                    if(Boolean.TRUE.equals(hasConfirmed) && hasChangeOperation) {
                        childDto.setAfterRequirementPublish(true);
                        logger.info("符合条件：详设 pamCode: {} 的上层详设hasConfirmed为true且有变更操作, 设置afterRequirementPublish=true",
                                childDto.getPamCode());
                    } else {
                        logger.info("不符合条件：详设 pamCode: {} 的上层详设hasConfirmed={}, hasChangeOperation={}",
                                childDto.getPamCode(), hasConfirmed, hasChangeOperation);
                    }

                    List<ProjectWbsReceiptsDto> projectWbsReceiptsDtos = projectWbsReceiptsService.selectByDetailIds(Lists.newArrayList(childDto.getDesignPlanDetailId()));
                    if(ListUtils.isNotEmpty(projectWbsReceiptsDtos)){
                        childDto.setHasConfirmed(Boolean.TRUE);
                        childDto.setAfterRequirementPublish(Boolean.TRUE);
                    }
                } else {
                    logger.debug("跳过非外购/看板物料详设 pamCode: {}, 物料类别: {}",
                            childDto.getPamCode(), childDto.getMaterialCategory());
                }
                // 继续递归处理
                checkParentConfirmStatusForMaterials(childDto);
            }
        }
    }

    /**
     * 递归收集所有上级节点ID
     * @param parentId 当前父节点ID
     * @param allParentIds 收集的所有父节点ID集合
     */
    private void collectAllParentIds(Long parentId, Set<Long> allParentIds) {
        if(parentId == null || parentId == -1L) {
            return;
        }

        // 添加当前父节点ID
        allParentIds.add(parentId);

        // 查询当前父节点的父节点
        MilepostDesignPlanDetail parentNode = milepostDesignPlanDetailMapper.selectByPrimaryKey(parentId);
        if(parentNode != null && !Boolean.TRUE.equals(parentNode.getDeletedFlag()) &&
                parentNode.getParentId() != null && parentNode.getParentId() != -1L) {
            // 递归收集上级节点
            collectAllParentIds(parentNode.getParentId(), allParentIds);
        }
    }

    /**
     * 为指定的父节点设置historyType=1
     * @param node 当前节点
     * @param parentIds 需要设置historyType的父节点ID集合
     */
    private void setHistoryTypeForParentNodes(MilepostDesignPlanDetailChangeDto node, Set<Long> parentIds) {
        if(node == null) {
            return;
        }

        // 检查当前节点ID是否在需要设置的父节点集合中
        Long nodeId = node.getDesignPlanDetailId() != null ? node.getDesignPlanDetailId() : node.getId();
        if(nodeId != null && parentIds.contains(nodeId)) {
            node.setHistoryType(1);
            logger.debug("设置节点ID：{}的historyType为1", nodeId);
        }

        if(Objects.nonNull(node.getId())){
            node.setId(null);
        }

        // 递归处理子节点
        if(ListUtils.isNotEmpty(node.getSonDtos())) {
            for(MilepostDesignPlanDetailChangeDto childDto : node.getSonDtos()) {
                setHistoryTypeForParentNodes(childDto, parentIds);
            }
        }
    }

    /**
     * 将MilepostDesignPlanDetailDto转换为MilepostDesignPlanDetailChangeDto
     */
    private MilepostDesignPlanDetailChangeDto convertToChangeDto(MilepostDesignPlanDetailDto detailDto) {
        if (detailDto == null) {
            return null;
        }

        MilepostDesignPlanDetailChangeDto changeDto = BeanConverter.copy(detailDto, MilepostDesignPlanDetailChangeDto.class);

        // 设置designPlanDetailId
        changeDto.setDesignPlanDetailId(detailDto.getId());

        // 递归处理子节点
        if (ListUtils.isNotEmpty(detailDto.getSonDtos())) {
            List<MilepostDesignPlanDetailChangeDto> sonChangeDtos = new ArrayList<>();
            for (MilepostDesignPlanDetailDto sonDto : detailDto.getSonDtos()) {
                MilepostDesignPlanDetailChangeDto sonChangeDto = convertToChangeDto(sonDto);
                if (sonChangeDto != null) {
                    sonChangeDtos.add(sonChangeDto);
                }
            }
            changeDto.setSonDtos(sonChangeDtos);
        }

        return changeDto;
    }

    /**
     * 为已存在的节点设置designPlanDetailId
     */
    private void setDesignPlanDetailIdForExistingNodes(MilepostDesignPlanDetailChangeDto node) {
        if (node == null) {
            return;
        }

        // 确保designPlanDetailId已设置
        if (node.getDesignPlanDetailId() == null && node.getId() != null) {
            node.setDesignPlanDetailId(node.getId());
        }

        // 递归处理子节点
        if (ListUtils.isNotEmpty(node.getSonDtos())) {
            for (MilepostDesignPlanDetailChangeDto sonDto : node.getSonDtos()) {
                setDesignPlanDetailIdForExistingNodes(sonDto);
            }
        }
    }

    /**
     * 根据ID查找节点
     */
    private MilepostDesignPlanDetailChangeDto findEntityById(MilepostDesignPlanDetailChangeDto node, String id) {
        if (node == null) {
            return null;
        }

        // 检查当前节点
        Long nodeId = node.getDesignPlanDetailId() != null ? node.getDesignPlanDetailId() : node.getId();
        if (nodeId != null && nodeId.toString().equals(id)) {
            return node;
        }

        // 递归查找子节点
        if (ListUtils.isNotEmpty(node.getSonDtos())) {
            for (MilepostDesignPlanDetailChangeDto sonDto : node.getSonDtos()) {
                MilepostDesignPlanDetailChangeDto result = findEntityById(sonDto, id);
                if (result != null) {
                    return result;
                }
            }
        }

        return null;
    }

    /**
     * 判断是否为模组层级
     */
    private boolean isModuleLayer(MilepostDesignPlanDetailChangeDto dto) {
        // 根据业务规则判断是否为模组层级
        return  Boolean.TRUE.equals(dto.getWhetherModel());
    }

    /**
     * 获取指定父ID的所有子节点（递归）
     */
    private List<MilepostDesignPlanDetail> getAllChildrenByParentId(Long parentId) {
        if(parentId == null) {
            return new ArrayList<>();
        }

        List<MilepostDesignPlanDetail> allChildren = new ArrayList<>();

        // 查询直接子节点
        MilepostDesignPlanDetailExample example = new MilepostDesignPlanDetailExample();
        example.createCriteria().andParentIdEqualTo(parentId).andDeletedFlagEqualTo(false);
        List<MilepostDesignPlanDetail> directChildren = milepostDesignPlanDetailMapper.selectByExample(example);

        allChildren.addAll(directChildren);

        // 递归查询每个子节点的子节点
        for(MilepostDesignPlanDetail child : directChildren) {
            allChildren.addAll(getAllChildrenByParentId(child.getId()));
        }

        return allChildren;
    }

    /**
     * 计算需求总量
     * 修正字段名，使用ProDemandTotalNum而非TotalNum
     */
    private void calculateDemandTotalNum(MilepostDesignPlanDetailChangeDto node) {
        if(node == null) {
            return;
        }

        // 计算当前节点的乘积因子
        BigDecimal currentMultiplier = BigDecimal.ONE;
        if(node.getNumber() != null) {
            currentMultiplier = node.getNumber();
        }

        // 处理子节点
        if(ListUtils.isNotEmpty(node.getSonDtos())) {
            for(MilepostDesignPlanDetailChangeDto sonDto : node.getSonDtos()) {
                // 计算子节点的需求量
                if(sonDto.getNumber() != null && "外购物料".equals(sonDto.getMaterialCategory())) {
                    BigDecimal demandTotalNum = sonDto.getNumber().multiply(currentMultiplier);
                    // 使用正确的字段名
                    sonDto.setProDemandTotalNum(demandTotalNum.intValue());
                    logger.debug("计算节点需求量: id={}, pamCode={}, number={}, parentMultiplier={}, demandTotalNum={}",
                            sonDto.getId(), sonDto.getPamCode(), sonDto.getNumber(), currentMultiplier, demandTotalNum);
                }

                // 递归处理子节点，传递累积的乘积因子
                BigDecimal childMultiplier = currentMultiplier;
                if(sonDto.getNumber() != null) {
                    childMultiplier = currentMultiplier.multiply(sonDto.getNumber());
                }
                calculateDemandTotalNumWithMultiplier(sonDto, childMultiplier);
            }
        }
    }

    /**
     * 使用乘积因子计算需求总量
     * 添加辅助方法，传递累积的乘积因子
     */
    private void calculateDemandTotalNumWithMultiplier(MilepostDesignPlanDetailChangeDto node, BigDecimal parentMultiplier) {
        if(node == null) {
            return;
        }

        // 处理子节点
        if(ListUtils.isNotEmpty(node.getSonDtos())) {
            for(MilepostDesignPlanDetailChangeDto sonDto : node.getSonDtos()) {
                // 计算子节点的需求量
                if(sonDto.getNumber() != null && "外购物料".equals(sonDto.getMaterialCategory())) {
                    BigDecimal demandTotalNum = sonDto.getNumber().multiply(parentMultiplier);
                    sonDto.setProDemandTotalNum(demandTotalNum.intValue());
                    logger.debug("计算节点需求量: id={}, pamCode={}, number={}, parentMultiplier={}, demandTotalNum={}",
                            sonDto.getId(), sonDto.getPamCode(), sonDto.getNumber(), parentMultiplier, demandTotalNum);
                }

                // 递归处理子节点，传递累积的乘积因子
                BigDecimal childMultiplier = parentMultiplier;
                if(sonDto.getNumber() != null) {
                    childMultiplier = parentMultiplier.multiply(sonDto.getNumber());
                }
                calculateDemandTotalNumWithMultiplier(sonDto, childMultiplier);
            }
        }
    }

    /**
     * 根据子级designPlanParentId从tmpId字段查找对应父级并加入到父级的sonDtos集合
     *
     * 处理场景：子级节点的designPlanParentId对应某个父级节点的tmpId
     * 需要将这些子级节点正确关联到对应的父级节点的sonDtos集合中
     *
     * @param flatDetailChangeDtoList 平铺的详设变更数据列表
     */
    private void processDesignPlanParentIdToTmpIdRelation(List<MilepostDesignPlanDetailChangeDto> flatDetailChangeDtoList) {
        if (ListUtils.isEmpty(flatDetailChangeDtoList)) {
            logger.info("无详设数据，跳过designPlanParentId到tmpId的关联处理");
            return;
        }

        logger.info("开始处理designPlanParentId到tmpId的关联关系，数据量：{}", flatDetailChangeDtoList.size());

        // 1. 构建tmpId到节点的映射（这里假设tmpId就是uid字段，您可以根据实际情况调整）
        Map<String, MilepostDesignPlanDetailChangeDto> tmpIdToNodeMap = new HashMap<>();
        for (MilepostDesignPlanDetailChangeDto dto : flatDetailChangeDtoList) {
            // 假设tmpId就是uid字段，如果有其他字段作为tmpId，请在这里修改
            String tmpId = dto.getTempId();
            if (StringUtils.hasText(tmpId)) {
                tmpIdToNodeMap.put(tmpId, dto);
                logger.debug("构建tmpId映射: tmpId={}, pamCode={}", tmpId, dto.getPamCode());
            }
        }

        logger.info("构建tmpId映射完成，映射数量：{}", tmpIdToNodeMap.size());

        // 2. 处理designPlanParentId与tmpId的关联关系
        for (MilepostDesignPlanDetailChangeDto childDto : flatDetailChangeDtoList) {
            Long designPlanParentId = childDto.getDesignPlanParentId();

            if (designPlanParentId != null) {
                // 将designPlanParentId转换为字符串，用于查找对应的tmpId
                String parentTmpId = String.valueOf(designPlanParentId);

                // 查找对应的父级节点
                MilepostDesignPlanDetailChangeDto parentDto = tmpIdToNodeMap.get(parentTmpId);

                if (parentDto != null) {
                    // 初始化父级的sonDtos集合
                    if (parentDto.getSonDtos() == null) {
                        parentDto.setSonDtos(new ArrayList<>());
                    }

                    // 将子级添加到父级的sonDtos集合中
                    parentDto.getSonDtos().add(childDto);

                    logger.info("成功关联子级到父级: 子级pamCode={}, designPlanParentId={}, 父级tmpId={}, 父级pamCode={}",
                            childDto.getPamCode(), designPlanParentId, parentTmpId, parentDto.getPamCode());
                } else {
                    logger.debug("未找到designPlanParentId={}对应的tmpId父级节点，子级pamCode={}",
                            designPlanParentId, childDto.getPamCode());
                }
            }
        }

        logger.info("完成designPlanParentId到tmpId的关联处理");
    }

    /**
     * 处理基于uid的多层级新增关系，构建独立树并从平铺列表中移除子节点
     *
     * 处理场景：页面新增了父级后，在这个父级下又新增了子级（可能多层）
     * 新增子级的parentId等于父级的uid，需要按照这种关系组装成树状层级
     * 组装完成后将这些详设的子节点从flatDetailChangeDtoList中删除，保留顶层节点
     *
     * @param flatDetailChangeDtoList 平铺的详设变更数据列表（会被修改，移除处理过的子节点）
     * @return 构建好的基于uid关系的独立树列表
     */
    private List<MilepostDesignPlanDetailChangeDto> processUidBasedHierarchyAndRemoveFromFlat(
            List<MilepostDesignPlanDetailChangeDto> flatDetailChangeDtoList) {

        if (ListUtils.isEmpty(flatDetailChangeDtoList)) {
            logger.info("无详设数据，跳过uid层级处理");
            return new ArrayList<>();
        }

        logger.info("开始处理基于uid的多层级新增关系，共{}个节点", flatDetailChangeDtoList.size());

        // 1. 构建uid到节点的映射关系（只处理isAdd=true的节点）
        Map<String, MilepostDesignPlanDetailChangeDto> uidToNodeMap = new HashMap<>();
        for (MilepostDesignPlanDetailChangeDto dto : flatDetailChangeDtoList) {
            // 只处理isNew=true的详设
            if (StringUtils.isNotEmpty(dto.getUid()) && Boolean.TRUE.equals(dto.getIsNew())) {
                uidToNodeMap.put(dto.getUid(), dto);
                logger.debug("构建uid映射: uid={}, nodeId={}, pamCode={}, isNew={}",
                        dto.getUid(), dto.getId(), dto.getPamCode(), dto.getIsNew());
            }
        }

        logger.info("构建完成，uid映射数量：{}", uidToNodeMap.size());

        // 2. 识别基于uid的父子关系（只处理isAdd=true的节点）
        List<MilepostDesignPlanDetailChangeDto> uidBasedNodes = new ArrayList<>();
        // 只收集子节点的uid，用于后续删除
        Set<String> childUids = new HashSet<>();
        // 收集根节点的uid，用于后续保留
        Set<String> rootUids = new HashSet<>();

        for (MilepostDesignPlanDetailChangeDto dto : flatDetailChangeDtoList) {
            // 只处理isAdd=true的详设
            if (dto.getParentId() != null && Boolean.TRUE.equals(dto.getIsNew())) {
                String parentUid = String.valueOf(dto.getParentId());

                // 检查parentId是否对应某个节点的uid
                if (uidToNodeMap.containsKey(parentUid)) {
                    uidBasedNodes.add(dto);
                    childUids.add(dto.getUid());
                    logger.debug("发现uid父子关系: 子节点uid={}, parentUid={}, isNew={}",
                            dto.getUid(), parentUid, dto.getIsNew());
                }
            }
        }

        // 3. 同时收集所有作为父级的uid节点
        Set<String> parentUids = new HashSet<>();
        for (MilepostDesignPlanDetailChangeDto dto : uidBasedNodes) {
            String parentUid = String.valueOf(dto.getParentId());
            parentUids.add(parentUid);
        }

        // 将作为父级的节点也加入处理列表
        for (String parentUid : parentUids) {
            MilepostDesignPlanDetailChangeDto parentNode = uidToNodeMap.get(parentUid);
            if (parentNode != null && !childUids.contains(parentUid)) {
                uidBasedNodes.add(parentNode);
                rootUids.add(parentUid);
                logger.debug("添加父级节点: uid={}", parentUid);
            }
        }

        if (uidBasedNodes.isEmpty()) {
            logger.info("未发现基于uid的父子关系，跳过处理");
            return new ArrayList<>();
        }

        logger.info("发现{}个基于uid关系的节点", uidBasedNodes.size());

        // 4. 构建树状结构
        List<MilepostDesignPlanDetailChangeDto> trees = buildUidBasedTrees(uidBasedNodes, uidToNodeMap);

        clearParentIdInTrees(trees);

        // 6. 从原始列表中只移除子节点，保留顶层节点
        flatDetailChangeDtoList.removeIf(dto -> childUids.contains(dto.getUid()) && !rootUids.contains(dto.getUid()));

        logger.info("完成uid层级处理，构建了{}棵树，从原列表移除了{}个子节点，保留了{}个顶层节点",
                trees.size(), childUids.size(), rootUids.size());

        return trees;
    }

    /**
     * 基于uid关系构建树状结构
     *
     * @param uidBasedNodes 参与uid关系的所有节点
     * @param uidToNodeMap uid到节点的映射
     * @return 构建好的树列表
     */
    private List<MilepostDesignPlanDetailChangeDto> buildUidBasedTrees(
            List<MilepostDesignPlanDetailChangeDto> uidBasedNodes,
            Map<String, MilepostDesignPlanDetailChangeDto> uidToNodeMap) {

        // 找出根节点（parentId不在uid映射中的节点，或parentId为-1的节点）
        List<MilepostDesignPlanDetailChangeDto> rootNodes = new ArrayList<>();

        for (MilepostDesignPlanDetailChangeDto node : uidBasedNodes) {
            if (node.getParentId() == null) {
                rootNodes.add(node);
                logger.debug("发现根节点: uid={}, pamCode={}", node.getUid(), node.getPamCode());
            } else {
                String parentUid = String.valueOf(node.getParentId());
                if (!uidToNodeMap.containsKey(parentUid)) {
                    // parentId不在uid映射中，说明这是一个根节点
                    rootNodes.add(node);
                    logger.debug("发现根节点（parentId不在uid映射中）: uid={}, parentId={}, pamCode={}",
                            node.getUid(), node.getParentId(), node.getPamCode());
                }
            }
        }

        // 为每个根节点构建子树
        for (MilepostDesignPlanDetailChangeDto rootNode : rootNodes) {
            buildUidBasedChildTree(rootNode, uidBasedNodes, uidToNodeMap);
        }

        logger.info("构建完成，共{}棵uid树", rootNodes.size());
        return rootNodes;
    }

    /**
     * 递归构建基于uid的子树
     *
     * @param parentNode 父节点
     * @param allNodes 所有节点列表
     * @param uidToNodeMap uid到节点的映射
     */
    private void buildUidBasedChildTree(MilepostDesignPlanDetailChangeDto parentNode,
                                       List<MilepostDesignPlanDetailChangeDto> allNodes,
                                       Map<String, MilepostDesignPlanDetailChangeDto> uidToNodeMap) {

        if (StringUtils.isEmpty(parentNode.getUid())) {
            return;
        }

        List<MilepostDesignPlanDetailChangeDto> children = new ArrayList<>();

        // 查找所有以当前节点uid为parentId的子节点
        for (MilepostDesignPlanDetailChangeDto node : allNodes) {
            if (node.getParentId() != null ) {
                String parentUid = String.valueOf(node.getParentId());
                if (parentUid.equals(parentNode.getUid()) && !node.equals(parentNode)) {
                    children.add(node);
                    logger.debug("找到子节点: 父uid={}, 子uid={}, 子pamCode={}",
                            parentNode.getUid(), node.getUid(), node.getPamCode());
                }
            }
        }

        if (!children.isEmpty()) {
            parentNode.setSonDtos(children);

            // 递归构建每个子节点的子树
            for (MilepostDesignPlanDetailChangeDto child : children) {
                buildUidBasedChildTree(child, allNodes, uidToNodeMap);
            }
        }
    }

    /**
     * 清理树中parentId为UID节点详设的parentId
     *
     * @param trees 树列表
     */
    private void clearParentIdInTrees(List<MilepostDesignPlanDetailChangeDto> trees) {
        for (MilepostDesignPlanDetailChangeDto tree : trees) {
            clearParentIdInNode(tree);
        }
        logger.info("已清理{}棵树中所有节点的parentId", trees.size());
    }

    /**
     * 递归清理节点及其子节点的parentId
     *
     * @param node 当前节点
     */
    private void clearParentIdInNode(MilepostDesignPlanDetailChangeDto node) {
        if (node == null) {
            return;
        }

        // 获取节点ID
        Long parentId = node.getParentId();

        // 检查节点是否存在于数据库中
        if (parentId != null) {
            try {
                MilepostDesignPlanDetail detail = milepostDesignPlanDetailMapper.selectByPrimaryKey(parentId);
                if (detail == null) {
                    // 节点不存在于数据库中，可以安全地设置parentId为空
                    node.setParentId(null);
                    logger.debug("节点不存在于数据库中，清除parentId: nodeId={}", parentId);
                } else {
                    // 节点存在于数据库中，保留原有parentId
                    logger.debug("节点存在于数据库中，保留parentId: nodeId={}, parentId={}", parentId, node.getParentId());
                }
            } catch (Exception e) {
                logger.error("查询节点失败，默认清除parentId: nodeId={}, error={}", parentId, e.getMessage());
                node.setParentId(null);
            }
        } else {
            // 没有ID的节点（新增节点），直接清除parentId
            node.setParentId(null);
            logger.debug("新增节点，清除parentId: uid={}", node.getUid());
        }

        // 递归清理子节点
        if (ListUtils.isNotEmpty(node.getSonDtos())) {
            for (MilepostDesignPlanDetailChangeDto child : node.getSonDtos()) {
                clearParentIdInNode(child);
            }
        }
    }
}
