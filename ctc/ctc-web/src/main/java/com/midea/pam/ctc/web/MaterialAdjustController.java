package com.midea.pam.ctc.web;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.BatchUpdateStatusPassDto;
import com.midea.pam.common.ctc.dto.MaterialAdjustHeaderDTO;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.excelVo.MaterialAdjustDetailImportExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailKukaWbsBatchImportExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailKukaWbsImportExcelVo;
import com.midea.pam.common.enums.MaterialAdjustEnum;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.ctc.common.enums.CtcAttachmentModule;
import com.midea.pam.ctc.service.CtcAttachmentService;
import com.midea.pam.ctc.service.MaterialAdjustExtService;
import com.midea.pam.ctc.service.MaterialAdjustService;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/25
 * @desc 详细设计-物料新增和变更
 */
@Api("物料新增和变更")
@RestController
@RequestMapping("/material/adjust")
public class MaterialAdjustController {

    @Resource
    private MaterialAdjustService materialAdjustService;

    @Resource
    private MaterialAdjustExtService materialAdjustExtService;

    @Resource
    private CtcAttachmentService ctcAttachmentService;


    /**
     * 列表分页查询接口
     *
     * @param pageNum           页码
     * @param pageSize          每页数量
     * @param adjustCode        单据号
     * @param adjustTypeStr     变更类型（下拉多选）：新增物料 0，物料属性变更 1，物料类型变更 2
     * @param statusStr         单据流程状态：草稿（撤回，驳回都是） 0，审批中 1，作废 2，审批通过 3
     * @param organizationIdStr 库存组织ID（下拉多选）
     * @return 分页数据
     */
    @ApiOperation("列表分页查询")
    @GetMapping("/page")
    public Response page(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                         @RequestParam(required = false) String adjustCode,
                         @RequestParam(required = false) String adjustTypeStr,
                         @RequestParam(required = false) String applyByName,
                         @RequestParam(required = false) String handleByName,
                         @RequestParam(required = false) String statusStr,
                         @RequestParam(required = false) String organizationIdStr,
                         @RequestParam(required = false) Date createAtBegin,
                         @RequestParam(required = false) Date createAtEnd,
                         @RequestParam(required = false) String resourceStr,
                         @RequestParam(required = false) String syncStatusStr,
                         @RequestParam(required = false) Boolean me) {
        MaterialAdjustHeaderDTO materialAdjustHeaderDTO = new MaterialAdjustHeaderDTO();
        materialAdjustHeaderDTO.setAdjustCode(adjustCode);
        materialAdjustHeaderDTO.setAdjustTypeStr(adjustTypeStr);
        materialAdjustHeaderDTO.setApplyByName(applyByName);
        materialAdjustHeaderDTO.setHandleByName(handleByName);
        materialAdjustHeaderDTO.setStatusStr(statusStr);
        materialAdjustHeaderDTO.setOrganizationIdStr(organizationIdStr);
        materialAdjustHeaderDTO.setCreateAtBegin(createAtBegin);
        materialAdjustHeaderDTO.setCreateAtEnd(createAtEnd);
        materialAdjustHeaderDTO.setResourceStr(resourceStr);
        materialAdjustHeaderDTO.setSyncStatusStr(syncStatusStr);
        materialAdjustHeaderDTO.setMe(me);
        PageInfo<MaterialAdjustHeaderDTO> pageInfo = materialAdjustService.page(pageNum, pageSize, materialAdjustHeaderDTO);
        return Response.dataResponse().setData(pageInfo);
    }

    /**
     * 物料新增和变更详情
     *
     * @param id 数据ID
     * @return 详情数据
     */
    @ApiOperation(value = "物料新增和变更详情", response = MaterialAdjustHeaderDTO.class)
    @GetMapping("/getDetailById")
    public Response getDetailById(@RequestParam @ApiParam("ID") Long id) {
        MaterialAdjustHeaderDTO materialAdjustHeaderDTO = materialAdjustService.getDetailById(id);
        return Response.dataResponse().setData(materialAdjustHeaderDTO);
    }

    /**
     * 物料新增和变更保存
     *
     * @param dto 数据
     * @return 详情数据
     */
    @ApiOperation(value = "物料新增和变更保存", response = MaterialAdjustHeaderDTO.class)
    @PostMapping("/saveWithDetail")
    public Response saveWithDetail(@RequestBody MaterialAdjustHeaderDTO dto) {
        return Response.dataResponse().setData(materialAdjustService.saveWithDetail(dto));
    }

    @PostMapping("/validImportDetail")
    public Response validImportDetail(@RequestParam Long organizationId,
                                      @RequestBody List<MaterialAdjustDetailImportExcelVo> detailImportExcelVoList) {
        return materialAdjustService.validImportDetail(organizationId, detailImportExcelVoList);
    }

    /**
     * 物料新增和变更作废（将草稿状态改为废弃状态）
     * 只能删除草稿状态的数据
     *
     * @param id
     * @return
     */
    @ApiOperation("物料新增和变更作废")
    @GetMapping("/deleteDraft")
    public DataResponse deleteDraft(@RequestParam @ApiParam("ID") Long id) {
        DataResponse<Integer> response = Response.dataResponse();
        return response.setData(materialAdjustService.deleteDraft(id));
    }

    @ApiOperation(value = "物料新增和变更撤回", notes = "场景：物料编码规则的参数值=KUKA2022")
    @PutMapping("return/{id}")
    public DataResponse draftReturn(@ApiParam("ID") @PathVariable Long id) {
        DataResponse<Integer> response = Response.dataResponse();
        return response.setData(materialAdjustService.draftReturn(id));
    }

    @ApiOperation(value = "物料新增和变更退回", notes = "场景：物料编码规则的参数值=KUKA2022")
    @PutMapping("reject/{id}")
    public DataResponse reject(@ApiParam("ID") @PathVariable Long id) {
        DataResponse<Integer> response = Response.dataResponse();
        return response.setData(materialAdjustService.reject(id));
    }

    @ApiOperation(value = "发起审批")
    @PutMapping("updateStatusChecking/skipSecurityInterceptor")
    public String updateStatusChecking(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId,
                                       @RequestParam(required = false) String fileId,
                                       @RequestParam(required = false) String fileName,
                                       @RequestParam(required = false) String fileSize) {

        ctcAttachmentService.remove(CtcAttachmentModule.MATERIAL_ADJUST_ADD_APP.code(), formInstanceId);
        //保存生成的Excel信息
        if (StringUtils.isNotBlank(fileId) && StringUtils.isNotBlank(fileName)) {
            ctcAttachmentService.save(CtcAttachmentModule.MATERIAL_ADJUST_ADD_APP.code(),
                    formInstanceId, Long.valueOf(fileId), fileName, Long.valueOf(fileSize).intValue());
        }

        materialAdjustService.updateStatusChecking(formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId,
                MaterialAdjustEnum.PENDING.code());
        return "1";
    }

    @ApiOperation(value = "驳回")
    @PutMapping("updateStatusReject/skipSecurityInterceptor")
    public String updateStatusReject(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        materialAdjustService.updateStatusReject(formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId,
                MaterialAdjustEnum.DRAFT.code());
        return "1";
    }

    @ApiOperation(value = "通过")
    @PutMapping("updateStatusPass/skipSecurityInterceptor")
    public String updateStatusPass(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                   @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                   @RequestParam(required = false) String handlerId,
                                   @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        materialAdjustService.updateStatusPassCommon(formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId,
                MaterialAdjustEnum.APPROVED.code());
        return "1";
    }

    @ApiOperation(value = "撤回")
    @PutMapping("updateStatusReturn/skipSecurityInterceptor")
    public String updateStatusReturn(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        materialAdjustService.updateStatusDraftReturn(formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId,
                MaterialAdjustEnum.DRAFT.code());
        return "1";
    }

    @ApiOperation(value = "废弃")
    @PutMapping("abandon/skipSecurityInterceptor")
    public String abandon(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        materialAdjustService.abandon(formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId
                , MaterialAdjustEnum.CANCEL.code());
        return "1";
    }

    @ApiOperation(value = "删除")
    @PutMapping("delete/skipSecurityInterceptor")
    public String delete(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                         @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                         @RequestParam(required = false) String handlerId,
                         @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        materialAdjustService.delete(formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId,
                MaterialAdjustEnum.CANCEL.code());
        return "1";
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public String agree(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                        @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                        @RequestParam(required = false) String handlerId,
                        @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        return "1";
    }

    @ApiOperation(value = "物料新增-导入(20210906迭代)")
    @PostMapping("/validImportDetailNew")
    public Response validImportDetailNew(@RequestParam Long organizationId,
                                         @RequestBody List<MaterialAdjustDetailImportExcelVo> detailImportExcelVoList,
                                         @RequestParam(required = false) Long adjustHeaderId) {
        return materialAdjustService.validImportDetailNew(organizationId, detailImportExcelVoList, adjustHeaderId);
    }

    @PostMapping("/importDesignPlanDetailWithKuka2022")
    public Response importDesignPlanDetailWithKuka2022(@RequestBody List<MilepostDesignPlanDetailKukaWbsImportExcelVo> kukaDetailExcelVos,
                                                       @RequestParam(required = false, value = "storageId") Long storageId,
                                                       @RequestParam(required = false, value = "markId") Long markId,
                                                       @RequestParam(required = false, value = "wbsSummaryCode") String wbsSummaryCode,
                                                       @RequestParam(required = false, value = "wbsEnabled") Boolean wbsEnabled,
                                                       @RequestParam(required = false, value = "projectId") Long projectId,
                                                       @RequestParam(required = false, value = "isChange") Boolean isChange,
                                                       @RequestParam(required = false, value = "detailId") Long detailId) {
        List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo = kukaDetailExcelVos.get(0).getDesignPlanDetailDtoForWebInfo();

        List<MilepostDesignPlanDetailDto> detailDtoList = BeanConverter.copy(kukaDetailExcelVos, MilepostDesignPlanDetailDto.class);
        return materialAdjustExtService.validImportDesignPlanDetailMaterial(detailDtoList, storageId, SystemContext.getUnitId(),
                designPlanDetailDtoForWebInfo, markId, wbsSummaryCode, wbsEnabled, projectId, isChange, detailId,
                Boolean.FALSE);  // 不跳过多层级检查，这是导入下级场景
    }

    @PostMapping("/batchImportDesignPlanDetailWithKuka2022")
    public Response batchImportDesignPlanDetailWithKuka2022(@RequestBody List<MilepostDesignPlanDetailKukaWbsBatchImportExcelVo> kukaDetailExcelVos,
                                                       @RequestParam(required = false, value = "storageId") Long storageId,
                                                       @RequestParam(required = false, value = "markId") Long markId,
                                                       @RequestParam(required = false, value = "wbsEnabled") Boolean wbsEnabled,
                                                       @RequestParam(required = false, value = "wbsSummaryCode") String wbsSummaryCode,
                                                       @RequestParam(required = false, value = "projectId") Long projectId,
                                                       @RequestParam(required = false, value = "isChange") Boolean isChange,
                                                       @RequestParam(value = "operateType")  String operateType) {
        List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo = kukaDetailExcelVos.get(0).getDesignPlanDetailDtoForWebInfo();
        List<MilepostDesignPlanDetailDto> detailDtoList = BeanConverter.copy(kukaDetailExcelVos, MilepostDesignPlanDetailDto.class);
        return materialAdjustExtService.validBatchImportDesignPlanDetailMaterial(detailDtoList, storageId, SystemContext.getUnitId(),
                designPlanDetailDtoForWebInfo, markId, wbsEnabled, projectId, isChange,operateType);
    }

    @PostMapping("/importMaterialAdjustDetailFlexible")
    public Response importMaterialAdjustDetailFlexible(@RequestParam Long organizationId,
                                                       @RequestBody List<MaterialAdjustDetailImportExcelVo> detailImportExcelVoList) {
        return materialAdjustService.importMaterialAdjustDetailFlexible(organizationId, detailImportExcelVoList);
    }

    @ApiOperation(value = "通过")
    @PostMapping("batchUpdateStatusPass/skipSecurityInterceptor")
    public String batchUpdateStatusPass(@RequestBody BatchUpdateStatusPassDto dto) {
        List<Long> formInstanceIds = dto.getFormInstanceIds();
        for (Long formInstanceId : formInstanceIds) {
            materialAdjustService.updateStatusPass(formInstanceId, MaterialAdjustEnum.APPROVED.code(), dto.getCompanyId());
        }
        return "1";
    }

    @PostMapping("/importMaterialAdjustDetailFlexibleBackUp")
    public Response importMaterialAdjustDetailFlexibleBackUp(@RequestParam Long organizationId,
                                                             @RequestBody List<MaterialAdjustDetailImportExcelVo> detailImportExcelVoList) {
        return materialAdjustService.importMaterialAdjustDetailFlexibleBackUp(organizationId, detailImportExcelVoList);
    }

    @ApiOperation(value = "物料新增退回")
    @PostMapping("/materialAddReject")
    public Response materialAddReject(@RequestBody MaterialAdjustHeaderDTO dto) {
        DataResponse<Object> dataResponse = Response.dataResponse();
        return dataResponse.setData(materialAdjustService.materialAddReject(dto));
    }

    @ApiOperation(value = "业务删除")
    @PutMapping("deleteById/{id}")
    public Response deleteById(@ApiParam("id") @PathVariable Long id) {
        DataResponse<Boolean> response = Response.dataResponse();
        materialAdjustService.deleteById(id);
        return response.setData(Boolean.TRUE);
    }
}
