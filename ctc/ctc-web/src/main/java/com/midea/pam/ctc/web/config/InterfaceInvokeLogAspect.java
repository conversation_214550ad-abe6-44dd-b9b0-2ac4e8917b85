package com.midea.pam.ctc.web.config;

import com.alibaba.fastjson.JSON;
import com.midea.pam.annotation.InterfaceInvokeLog;
import com.midea.pam.common.base.CarrierResponse;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.ctc.entity.PassInterfaceInvokeLog;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.util.SerialNoContext;
import com.midea.pam.ctc.api.server.PassInterfaceInvokeLogService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Date;

/**
 * 接口调用日志记录切面
 * 统一处理接口调用的日志记录，包括请求参数、响应结果、执行状态等
 * 具体使用说明详见 ctc/docs/InterfaceInvokeLogAspect使用说明.md
 * 注解类详见 {@link InterfaceInvokeLog}
 *
 * <AUTHOR>
 */
@Component
@Aspect
public class InterfaceInvokeLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(InterfaceInvokeLogAspect.class);

    /**
     * 数据库字段最大长度限制
     */
    private static final int MAX_RESULT_LENGTH = 2000;

    @Resource
    private Environment environment;

    @Resource
    private PassInterfaceInvokeLogService passInterfaceInvokeLogService;


    @Around("@annotation(com.midea.pam.annotation.InterfaceInvokeLog)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        InterfaceInvokeLog annotation = method.getAnnotation(InterfaceInvokeLog.class);

        if (annotation == null) {
            return joinPoint.proceed();
        }

        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        String methodName = method.getDeclaringClass().getSimpleName() + "." + method.getName();

        // 生成交易流水号并存储到 ThreadLocal
        String serialNo = generateSerialNo(args, annotation);
        SerialNoContext.set(serialNo);

        // 提取业务编号
        String businessCode = extractBusinessCode(args, annotation.businessCodeField());

        // 记录接口调用开始日志
        logger.info("=== 接口调用开始 === 流水号: {}, 方法: {}, 接口类型: {}, 业务编号: {}",
                   serialNo, methodName, annotation.interfaceType().getSystemName(), businessCode);

        // 记录入参信息
        logRequestParameters(args, annotation, serialNo);

        PassInterfaceInvokeLog passInterfaceInvokeLog = new PassInterfaceInvokeLog();

        try {
            // 构建URL
            String url = buildHttpUrl(annotation.urlSuffix(), annotation.interfaceType());

            // 记录请求参数（如果需要）
            String requestParams = "";
            if (annotation.logParams() && args.length > 0) {
                requestParams = buildRequestParamsString(args);
            }

            // 插入日志记录
            Long logId = passInterfaceInvokeLogService.insertLog(
                    businessCode,
                    serialNo,
                    requestParams,
                    url,
                    annotation.interfaceType().getSystemName()
            );
            passInterfaceInvokeLog.setId(logId);

            logger.info("接口调用准备完成 - 流水号: {}, URL: {}, 数据库日志ID: {}", serialNo, url, logId);

            // 执行原方法
            long methodStartTime = System.currentTimeMillis();
            Object result = joinPoint.proceed();
            long methodExecutionTime = System.currentTimeMillis() - methodStartTime;

            // 记录方法执行时间
            logger.info("接口方法执行完成 - 流水号: {}, 执行耗时: {}ms", serialNo, methodExecutionTime);

            // 处理执行结果
            handleResult(result, passInterfaceInvokeLog, annotation.logResult(), annotation.interfaceType());

            // 记录响应结果摘要
            logResponseSummary(result, serialNo, annotation.interfaceType());

            // 自动设置 serialNo 到返回值
            autoSetSerialNoToResult(result, serialNo);

            return result;

        } catch (Exception e) {
            long errorTime = System.currentTimeMillis() - startTime;
            logger.error("=== 接口调用异常 === 流水号: {}, 方法: {}, 异常类型: {}, 异常信息: {}, 执行耗时: {}ms",
                        serialNo, methodName, e.getClass().getSimpleName(), e.getMessage(), errorTime, e);
            passInterfaceInvokeLog.setInvokeStatus(2);
            passInterfaceInvokeLog.setInvokeResult(truncateString(e.getMessage()));
            throw e;
        } finally {
            long totalTime = System.currentTimeMillis() - startTime;

            // 更新日志记录
            if (passInterfaceInvokeLog.getId() != null) {
                passInterfaceInvokeLogService.updateLog(passInterfaceInvokeLog);
                logger.info("数据库日志更新完成 - 流水号: {}, 日志ID: {}", serialNo, passInterfaceInvokeLog.getId());
            }

            // 记录接口调用结束日志
            logger.info("=== 接口调用结束 === 流水号: {}, 方法: {}, 总耗时: {}ms, 状态: {}",
                       serialNo, methodName, totalTime,
                       passInterfaceInvokeLog.getInvokeStatus() == 1 ? "成功" : "失败");

            // 清理 ThreadLocal，防止内存泄漏
            SerialNoContext.clear();
        }
    }

    /**
     * 从方法参数中提取业务编号
     */
    private String extractBusinessCode(Object[] args, String fieldName) {
        if (ObjectUtils.isEmpty(args) || ObjectUtils.isEmpty(fieldName)) {
            return "";
        }

        try {
            Object firstArg = args[0];
            if (firstArg == null) {
                return "";
            }

            // 支持嵌套属性访问，如 "dto.code"
            String[] fieldPath = fieldName.split("\\.");
            Object currentObj = firstArg;

            for (String field : fieldPath) {
                if (currentObj == null) {
                    break;
                }

                Field declaredField = getFieldIncludingInherited(currentObj.getClass(), field);
                if (declaredField == null) {
                    logger.warn("字段不存在: {} in class {}", field, currentObj.getClass().getName());
                    return "";
                }
                declaredField.setAccessible(true);
                currentObj = declaredField.get(currentObj);
            }

            return currentObj != null ? currentObj.toString() : "";

        } catch (Exception e) {
            logger.error("提取业务编号失败 - fieldName: {}, 异常详情: ", fieldName, e);
            return "";
        }
    }

    /**
     * 获取字段，包括继承的字段
     * 从当前类开始向上查找父类，直到找到指定字段或到达Object类
     */
    private Field getFieldIncludingInherited(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 在当前类中未找到，继续查找父类
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    /**
     * 处理方法执行结果
     */
    private void handleResult(Object result, PassInterfaceInvokeLog passInterfaceInvokeLog, boolean logResult, InterfaceInvokeLog.InterfaceType interfaceType) {
        // 设置调用状态
        if (result instanceof EsbResponse) {
            EsbResponse<?> esbResponse = (EsbResponse<?>) result;
            // 根据响应码判断执行状态
            if (ResponseCodeEnums.SUCESS.getCode().equals(esbResponse.getResponsecode())) {
                passInterfaceInvokeLog.setInvokeStatus(1); // 成功
            } else {
                passInterfaceInvokeLog.setInvokeStatus(2); // 失败
            }
        } else if (result instanceof CarrierResponse) {
            // 处理 CarrierResponse 类型（GEMS接口）
            CarrierResponse<?> carrierResponse = (CarrierResponse<?>) result;
            // GEMS接口成功判断：code为"0000"
            if ("0000".equals(carrierResponse.getResponsecode())) {
                passInterfaceInvokeLog.setInvokeStatus(1); // 成功
            } else {
                passInterfaceInvokeLog.setInvokeStatus(2); // 失败
            }
        } else {
            // 其他类型，默认为成功
            passInterfaceInvokeLog.setInvokeStatus(1);
        }

        // 记录响应结果（如果需要）
        if (logResult) {
            String resultStr = JSON.toJSONString(result);
            passInterfaceInvokeLog.setInvokeResult(truncateString(resultStr));
        }
    }

    /**
     * 生成交易流水号
     */
    private String generateSerialNo(Object[] args, InterfaceInvokeLog annotation) {
        // 如果指定了自定义流水号字段，尝试从参数中提取
        if (!annotation.customSerialNoField().isEmpty() && args.length > 0) {
            try {
                Object firstArg = args[0];
                if (firstArg != null) {
                    String customSerialNo = extractFieldValue(firstArg, annotation.customSerialNoField());
                    if (customSerialNo != null && !customSerialNo.trim().isEmpty()) {
                        return customSerialNo;
                    }
                }
            } catch (Exception e) {
                logger.warn("提取自定义流水号失败，使用默认生成规则: {}", e.getMessage());
            }
        }

        // 默认生成规则
        return EsbConstant.PAM_REQUEST_ID + new Date().getTime();
    }

    /**
     * 从对象中提取指定字段的值
     */
    private String extractFieldValue(Object obj, String fieldName) {
        try {
            String[] fieldPath = fieldName.split("\\.");
            Object currentObj = obj;

            for (String field : fieldPath) {
                if (currentObj == null) {
                    break;
                }

                Field declaredField = getFieldIncludingInherited(currentObj.getClass(), field);
                if (declaredField == null) {
                    logger.warn("字段不存在: {} in class {}", field, currentObj.getClass().getName());
                    return null;
                }
                declaredField.setAccessible(true);
                currentObj = declaredField.get(currentObj);
            }

            return currentObj != null ? currentObj.toString() : null;

        } catch (Exception e) {
            logger.warn("提取字段值失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 自动设置 serialNo 到返回值中
     */
    private void autoSetSerialNoToResult(Object result, String serialNo) {
        if (result instanceof EsbResponse) {
            EsbResponse<String> esbResponse = (EsbResponse<String>) result;
            // 如果 data 为空，则设置 serialNo
            if (esbResponse.getData() == null) {
                esbResponse.setData(serialNo);
            }
        }
        // CarrierResponse 通常不需要设置 serialNo，如有需要可以扩展
    }

    /**
     * 构建请求参数字符串，支持多个参数
     */
    private String buildRequestParamsString(Object[] args) {
        if (args == null || args.length == 0) {
            return "";
        }

        try {
            // 如果只有一个参数，直接序列化
            if (args.length == 1) {
                return JSON.toJSONString(args[0]);
            }

            // 多个参数时，构建参数数组的JSON
            StringBuilder paramsBuilder = new StringBuilder();
            paramsBuilder.append("[");

            for (int i = 0; i < args.length; i++) {
                if (i > 0) {
                    paramsBuilder.append(",");
                }

                // 为每个参数添加索引标识
                paramsBuilder.append("{\"paramIndex\":").append(i)
                           .append(",\"paramType\":\"").append(args[i] != null ? args[i].getClass().getSimpleName() : "null")
                           .append("\",\"paramValue\":");

                if (args[i] != null) {
                    paramsBuilder.append(JSON.toJSONString(args[i]));
                } else {
                    paramsBuilder.append("null");
                }

                paramsBuilder.append("}");
            }

            paramsBuilder.append("]");
            return paramsBuilder.toString();

        } catch (Exception e) {
            logger.warn("构建请求参数字符串失败: {}", e.getMessage());
            // 降级处理：只记录第一个参数
            return JSON.toJSONString(args[0]);
        }
    }

    /**
     * 构建HTTP请求URL
     * 使用InterfaceType枚举中的配置键动态获取基础URL
     */
    private String buildHttpUrl(String suffixUrl, InterfaceInvokeLog.InterfaceType interfaceType) {
        // 从配置中动态获取基础URL
        String baseUrl = environment.getProperty(interfaceType.getConfigKey(), "");

        if (baseUrl.isEmpty()) {
            logger.warn("URL构建失败 - 接口类型: {}, 配置键: {}, 原因: 未找到对应的URL配置", interfaceType.getSystemName(), interfaceType.getConfigKey());
            return "";
        }

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl + suffixUrl);
        String url = builder.build(true).toUri().toString();
        logger.info("URL构建成功 - 接口类型: {}, 基础URL: {}, 后缀: {}, 完整URL: {}",
                   interfaceType.getSystemName(), baseUrl, suffixUrl, url);
        return url;
    }

    /**
     * 记录请求参数信息
     */
    private void logRequestParameters(Object[] args, InterfaceInvokeLog annotation, String serialNo) {
        if (args == null || args.length == 0) {
            logger.info("请求参数为空 - 流水号: {}", serialNo);
            return;
        }

        logger.info("请求参数数量: {} - 流水号: {}", args.length, serialNo);

        // 记录详细参数信息（如果启用参数日志）
        if (annotation.logParams()) {
            for (int i = 0; i < args.length; i++) {
                Object arg = args[i];
                if (arg != null) {
                    String paramInfo = getParameterInfo(arg);
                    logger.info("参数[{}]: {} - 流水号: {}", i, paramInfo, serialNo);
                } else {
                    logger.info("参数[{}]: null - 流水号: {}", i, serialNo);
                }
            }
        } else {
            logger.info("参数日志已禁用 - 流水号: {}", serialNo);
        }
    }

    /**
     * 获取参数信息摘要
     */
    private String getParameterInfo(Object param) {
        if (param == null) {
            return "null";
        }

        String className = param.getClass().getSimpleName();

        // 对于基本类型和字符串，直接返回值
        if (param instanceof String || param instanceof Number || param instanceof Boolean) {
            String value = param.toString();
            // 限制长度避免日志过长
            return className + "(" + (value.length() > 100 ? value.substring(0, 100) + "..." : value) + ")";
        }

        // 对于复杂对象，返回类型和JSON长度
        try {
            String json = JSON.toJSONString(param);
            return className + "(JSON长度: " + json.length() + "字符)";
        } catch (Exception e) {
            return className + "(JSON序列化失败: " + e.getMessage() + ")";
        }
    }

    /**
     * 记录响应结果摘要
     */
    private void logResponseSummary(Object result, String serialNo, InterfaceInvokeLog.InterfaceType interfaceType) {
        if (result == null) {
            logger.info("响应结果为null - 流水号: {}", serialNo);
            return;
        }

        String resultType = result.getClass().getSimpleName();
        logger.info("响应结果类型: {} - 流水号: {}", resultType, serialNo);

        // 根据不同的响应类型记录关键信息
        if (result instanceof EsbResponse) {
            EsbResponse<?> esbResponse = (EsbResponse<?>) result;
            logger.info("ESB响应 - 流水号: {}, 响应码: {}, 响应消息: {}",
                       serialNo, esbResponse.getResponsecode(), esbResponse.getResponsemessage());
        } else if (result instanceof CarrierResponse) {
            CarrierResponse<?> carrierResponse = (CarrierResponse<?>) result;
            logger.info("Carrier响应 - 流水号: {}, 响应码: {}, 响应消息: {}",
                       serialNo, carrierResponse.getResponsecode(), carrierResponse.getResponsemessage());
        } else {
            // 其他类型的响应
            try {
                String json = JSON.toJSONString(result);
                logger.info("通用响应 - 流水号: {}, JSON长度: {}字符", serialNo, json.length());
            } catch (Exception e) {
                logger.warn("响应结果JSON序列化失败 - 流水号: {}, 错误: {}", serialNo, e.getMessage());
            }
        }
    }

    /**
     * 截断字符串，避免数据库字段溢出
     *
     * @param str 原始字符串
     * @return 截断后的字符串，如果原字符串为null则返回null
     */
    private String truncateString(String str) {
        if (str == null) {
            return null;
        }
        if (str.length() > MAX_RESULT_LENGTH) {
            return str.substring(0, MAX_RESULT_LENGTH);
        }
        return str;
    }
}
