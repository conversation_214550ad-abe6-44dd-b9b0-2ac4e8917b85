package com.midea.pam.ctc.service;

import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectReopenHeaderDto;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectReopenHeader;
import com.midea.pam.common.ctc.vo.ProjectVO;

/**
 * Description
 * Created by lintx
 * Date 2021/12/23 10:46
 */
public interface ProjectReopenService extends ProjectService{

    /**
     * 项目类型配置为“允许立项时上传详细设计方案”为是的项目
     *
     * @param projectDto 表单实例id-项目id
     * @param status  审批状态
     */
    void updateProjectDesign(ProjectDto projectDto, Integer status);

    /**
     * 审批通过回调.
     *
     * @param formInstanceId 表单实例id-项目id
     */
    void approvedHandler(Long formInstanceId, Long companyId);

    /**
     * 获取项目基本信息
     *
     * @param projectId 项目id
     * @return 项目详情
     */
    ProjectDto getBaseInfoByProjectId(Long projectId);

    /**
     * 保存项目
     *
     * @param projectDto 项目数据
     * @return ProjectDto 封装对象
     */
    ProjectDto save(ProjectDto projectDto);

    /**
     * 获取重新打开项目
     *
     * @param id
     * @return
     */
    ResponseMap getProjectReopenApp(Long id);

    Long getProjectIdByReopenHeader(Long projectReopenHeaderId);

    String updateReopenHeaderStatus(ProjectReopenHeader projectReopenHeader);


    String initProjectReopenHeader(ProjectReopenHeader projectReopenHeader);
}
