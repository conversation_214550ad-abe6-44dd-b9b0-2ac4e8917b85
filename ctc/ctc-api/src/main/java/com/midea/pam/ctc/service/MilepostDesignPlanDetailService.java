package com.midea.pam.ctc.service;

import com.midea.pam.common.basedata.dto.MaterialCostDto;
import com.midea.pam.common.basedata.entity.MaterialCost;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.entity.DesignPlanDetailMatchResult;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailMiddle;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.excelVo.DesignPlanDetailChangeImportExcelVO;
import com.midea.pam.common.ctc.excelVo.ImportDesignPlanDetailKUKA2022ExcelVo;
import com.midea.pam.common.ctc.excelVo.ImportMilepostDesignPlanDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailImportExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailKsImportExcelVo;
import com.midea.pam.common.ctc.excelVo.UpdateMilepostDesignPlanDetailExcelVo;
import com.midea.pam.common.ctc.query.DetailByProjectIdAndMilepostIdNewWbsQuery;
import com.midea.pam.common.util.idaas.TwoTuple;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface MilepostDesignPlanDetailService extends BaseService<MilepostDesignPlanDetailDto> {

    /**
     * 保存详细方案设计信息,并根据提交信息
     *
     * @param dto                  详细设计物料
     * @param userBy               用户ID
     * @param project              项目信息
     * @param isCreateMaterialCost 是否创建物料估计，true为是，false为否
     * @param storageId            库存组织
     * @return 详细设计物料
     */
    MilepostDesignPlanDetailDto saveWithMaterialCostAndMaterial(MilepostDesignPlanDetailDto dto, Long userBy, Project project,
                                                                Boolean isCreateMaterialCost, Long storageId);

    /**
     * 保存层级(N>=1)详细设计方案交付设计信息
     *
     * @param project          项目信息
     * @param dto              详细设计物料信息
     * @param parentId         物料父Id
     * @param releaseLotNumber 批次号
     * @param userBy           用户ID
     * @param level            物料BOM层级
     */
    Boolean recursiveSave(Project project, MilepostDesignPlanDetailDto dto, Long parentId,
                          String releaseLotNumber, Long userBy, int level, Long storageId,
                          ProjectWbsReceiptsDto recordDto, List<Long> historyIds, List<MilepostDesignPlanDetail> updatePlanDetailList,
                          List<TwoTuple<TwoTuple<Long, Boolean>, MilepostDesignPlanDetailDto>> twoTupleList);

    /**
     * 保存层级(N>=1)详细设计方案交付设计信息
     *
     * @param project        项目信息
     * @param dto            详细设计物料信息
     * @param parentId       物料父Id
     * @param submitRecordId 提交记录ID
     * @param milepostId     里程碑ID
     * @param userBy         用户ID
     * @param level          物料BOM层级
     */
    Boolean recursiveSave(Project project, MilepostDesignPlanDetailDto dto, Long parentId,
                          Long submitRecordId, Long milepostId, Long userBy, int level, Long storageId, Boolean storageStatus, Set<Long> uploadPathIdSet);


    /**
     * 获取层级(N>=1)详细设计方案交付设计信息
     * 封装部分业务逻辑
     *
     * @param id
     * @return
     */
    MilepostDesignPlanDetailDto getDesignPlanDetailForBusiness(MilepostDesignPlanDetailDto dto, Long id, Boolean haveSon);

    MilepostDesignPlanDetailDto getDesignPlanDetailForBusinessNew(MilepostDesignPlanDetailDto dto, Long id, Boolean haveSon,
                                                                  DetailByProjectIdAndMilepostIdNewWbsQuery query);

    Long queryMaterialCustomDict(String materialClassification, String materielType, Long storageId);

    /**
     * 获取层级(N>=1)详细设计方案交付设计信息
     *
     * @param id
     * @return
     */
    MilepostDesignPlanDetailDto getDesignPlanDetailWithSonsById(MilepostDesignPlanDetailDto dto, Long id, Boolean haveSon,
                                                                DetailByProjectIdAndMilepostIdNewWbsQuery query);

    /**
     * 批量保存设计信息
     *
     * @param dtos
     * @return
     */
    Boolean saveBatch(List<MilepostDesignPlanDetailDto> dtos, Long userBy);

    /**
     * 获取详细方案第一层设计信息
     *
     * @return
     */
    List<MilepostDesignPlanDetailDto> getTheFirstLevelDetailByMilepostId(Long milepostId);

    List<MilepostDesignPlanDetailDto> getTheFirstLevelDetailByProjectId(DetailByProjectIdAndMilepostIdNewWbsQuery query);

    /**
     * 匹配交付物物料编码以及设计成本
     *
     * @param detailExcelVos
     * @param remindType     throw或null：报错前端捕获提示（旧逻辑），excel：报错聚合，下载校验结果（新逻辑）
     * @return
     */
    List<MilepostDesignPlanDetailDto> matchingMaterialCodeEstimate(List<MilepostDesignPlanDetailImportExcelVo> detailExcelVos, String remindType) throws UnsupportedEncodingException;

    /**
     * 匹配交付物物料编码以及设计成本(昆山)
     *
     * @param ksDetailExcelVos
     * @param remindType       throw或null：报错前端捕获提示（旧逻辑），excel：报错聚合，下载校验结果（新逻辑）
     * @return
     */
    List<MilepostDesignPlanDetailDto> ksMatchingMaterialCodeEstimate(List<MilepostDesignPlanDetailKsImportExcelVo> ksDetailExcelVos, Long storageId
            , Long unitId, String remindType);

    /**
     * 匹配交付物物料编码以及设计成本(异步)
     *
     * @param detailExcelVos
     * @return
     */
    AsyncRequestResult matchingMaterialCodeEstimateAsync(List<MilepostDesignPlanDetailImportExcelVo> detailExcelVos) throws UnsupportedEncodingException;

    /**
     * 匹配交付物物料编码以及设计成本(异步)_昆山
     *
     * @param ksDetailExcelVos
     * @param storageId
     * @return
     * @throws UnsupportedEncodingException
     */
    AsyncRequestResult ksMatchingMaterialCodeEstimateAsync(List<MilepostDesignPlanDetailKsImportExcelVo> ksDetailExcelVos, Long storageId,
                                                           Long unitId) throws UnsupportedEncodingException;

    /**
     * 递归处理
     * <p>
     * 生成物料基本信息erp编码
     * 同步物料信息到erp系统
     * 更新物料成本erp编码
     * 更新设计信息的erp编码及设计成本
     *
     * @param detailPlanParentId
     * @param submitRecordId
     * @param detailPlanParentId
     * @param submitRecordId
     * @param materialIdWithNewErpCode
     */
    void handleRecursive(Long detailPlanParentId, Long submitRecordId, Map<Long, String> materialIdWithNewErpCode);

    /**
     * 根据项目物料预算id获取详设id
     *
     * @param projectBudgetMaterialId
     * @return
     */
    Long getIdByProjectBudgetMaterialId(Long projectBudgetMaterialId);

    /**
     * 获取当前设计信息到父节点的积数
     *
     * @param id
     * @param sigma
     * @return
     */
    BigDecimal getParentSigmaById(Long id, BigDecimal sigma);

    /**
     * 查询对应物料编号生成的详细设计处于审批中及审批通过的状态的数据
     *
     * @param pamCode 物料pamCode编码
     * @return 详细设计列表
     */
    List<MilepostDesignPlanDetail> listConfirmDetailsByPamCode(String pamCode);

    /**
     * 获取未生成采购申请的物料
     *
     * @param detailPlanParentId 物料父Id
     * @return 物料
     */
    List<MilepostDesignPlanDetailDto> findNotGenerateRequireDesignPlanDetails(Long detailPlanParentId);

    void updateMaterialCostRequirement(String pamCode, Long milepostId, Long organizationId);

    void saveMaterial(MilepostDesignPlanDetailDto dto, Long storageId, Long ouId);

    MaterialCostDto saveMaterialCostDto(MilepostDesignPlanDetailDto dto, Project project, Long userBy, Long storageId);

    /**
     * 批量更新详细设计方案的设计成本
     *
     * @param materialCost
     * @return
     */
    Boolean updateDesignCost(MaterialCost materialCost);

    /**
     * 项目详细设计方案中的采购物料未生成采购需求（不包含成品、模组、外包物料）
     *
     * @param projectId 项目ID
     * @return 详细设计
     */
    List<MilepostDesignPlanDetail> getNoGenerateRequirement(Long projectId);

    void deleteMaterialCost(Long detailPlanParentId, Long submitRecordId, Long organizationId);

    /**
     * 匹配需要变更的交付物属性
     *
     * @param detailExcelVos
     * @return
     */
    AsyncRequestResult matchingMaterialChangingAsync(List<DesignPlanDetailChangeImportExcelVO> detailExcelVos);

    List<DesignPlanDetailMatchResult> matchingMaterialChanging(List<DesignPlanDetailChangeImportExcelVO> detailExcelVos);

    List<MilepostDesignPlanDetailDto> buildTree(List<MilepostDesignPlanDetailDto> milepostDesignPlanDetailDtos, Long parentId);

    List<MilepostDesignPlanDetailDto> getDesignPlanDetailForStaging(Long submitRecordId);

    List<MilepostDesignPlanDetailDto> getDesignPlanDetail(Long id);

    void countRequirementNum(MilepostDesignPlanDetailDto dto, BigDecimal preNumber);

    List<MilepostDesignPlanDetailDto> selectMilepostDesignPlanDtoInfo(MilepostDesignPlanDetail detail);

    /**
     * 详细设计初始化
     *
     * @param ksDetailExcelVos
     * @return
     */
    List<MilepostDesignPlanDetail> importDesignPlan(List<ImportMilepostDesignPlanDetailExcelVo> ksDetailExcelVos);

    List<MilepostDesignPlanDetail> importDesignPlan2(List<ImportMilepostDesignPlanDetailExcelVo> ksDetailExcelVos);

    List<MilepostDesignPlanDetail> updateDesignPlan(List<UpdateMilepostDesignPlanDetailExcelVo> ksDetailExcelVos);

    MilepostDesignPlanDetailExample buildCondition(MilepostDesignPlanDetailDto planDetailQuery);

    Boolean matchingMaterialCode(MilepostDesignPlanDetailDto dto);

    void matchingMaterialUnitCode(MilepostDesignPlanDetailDto dto, String remindType, Map<String, String> unitMap);

    AsyncRequestResult matchingMaterialCodeAsync(MilepostDesignPlanDetailDto dto, Long organizationId, String remindType,
                                                 Map<String, String> unitMap) throws UnsupportedEncodingException;

    boolean ksMatchingMaterialCodeEstimateNew(List<MilepostDesignPlanDetailDto> designPlanDetailDtos, Long storageId, Long unitId);

    void saveMilepostDesignDetailMiddle(MilepostDesignPlanDetailMiddle milepostDesignPlanDetailMiddle, long batch);

    AsyncRequestResult ksMatchingMaterialCodeAsync(MilepostDesignPlanDetailDto dto, Long organizationId);

    void checkKsMaterial(MilepostDesignPlanDetailDto dto, Long organizationId);

    List<MilepostDesignPlanDetailDto> selectDetailByIds(List<Long> ids);

    List<MilepostDesignPlanDetail> selectByExample(MilepostDesignPlanDetailExample example);

    List<MilepostDesignPlanDetail> selectByParentId(Long parentId);

    void deleteByDetailIds(List<Long> detailIds);

    /**
     * 柔性项目详细设计初始化导入
     *
     * @param detailExcelVOList
     */
    void importDesignPlanDetailKUKA2022(List<ImportDesignPlanDetailKUKA2022ExcelVo> detailExcelVOList);

    /**
     * 批量查询详设扩展字段
     *
     * @param designPlanDetailList
     * @param project
     */
    void batchSetNumSum(List<MilepostDesignPlanDetailDto> designPlanDetailList, Project project);

    /**
     * 查询项目下是否存在已生成采购需求的物料
     * @param projectId
     * @return
     */
    Long getProjectDesignDetailExistGenerateRequirement(Long projectId);

}
