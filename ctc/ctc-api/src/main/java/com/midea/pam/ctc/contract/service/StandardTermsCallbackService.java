package com.midea.pam.ctc.contract.service;


public interface StandardTermsCallbackService {

    /**
     * 提交审批
     *
     * @param formInstanceId
     */
    void draftSubmit(Long formInstanceId);

    /**
     * 驳回
     *
     * @param formInstanceId
     */
    void refused(Long formInstanceId);

    /**
     * 撤回
     *
     * @param formInstanceId
     */
    void draftReturn(Long formInstanceId);

    /**
     * 通过
     *
     * @param formInstanceId
     */
    void approved(Long formInstanceId);

    /**
     * 作废
     *
     * @param formInstanceId
     */
    void abandon(Long formInstanceId);

    /**
     * 删除
     *
     * @param formInstanceId
     */
    void delete(Long formInstanceId);
}
