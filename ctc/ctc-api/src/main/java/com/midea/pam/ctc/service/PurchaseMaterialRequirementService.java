package com.midea.pam.ctc.service;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.ctc.dto.BatchEditRequirementDeliveryAddressDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.dto.RequirementDeliverAddressRepeatCheckDTO;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement;
import com.midea.pam.common.ctc.excelVo.ImportPurchaseMaterialRequirementAndCloseDetailExcelVO;
import com.midea.pam.common.ctc.excelVo.ImportPurchaseMaterialRequirementExcelVO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface PurchaseMaterialRequirementService extends BaseService<PurchaseMaterialRequirementDto> {

    List<PurchaseMaterialRequirementDto> selectListWithDetail(PurchaseMaterialRequirementDto query);

    PageInfo<PurchaseMaterialRequirementDto> selectPageWithDetail(PurchaseMaterialRequirementDto query);

    /**
     * 批量统计采购需求 未采购量/总需求量/已采购量/关闭数量
     *
     * @param ids
     * @return
     */
    List<PurchaseMaterialRequirementDto> getDetailByIds(List<Long> ids);

    /**
     * 统计采购需求 未采购量/总需求量/已采购量/关闭数量
     *
     * @param id
     * @return
     */
    PurchaseMaterialRequirementDto getDetailById(Long id);

    /**
     * 确认发布(采购)
     * 生成物料采购需求(递归)
     *
     * @param detailPlanParentId
     * @param submitRecordId
     */
    void recursiveGenerateRequirementByDetailPlanId(Long detailPlanParentId, Long submitRecordId, List<Long> requirementIdList);

    /**
     * 进度确认的模组确认
     * 生成物料采购需求(递归)
     *
     * @param detailPlanParentId 模组id
     * @return 新增的物料采购需求集合
     */
    List<PurchaseMaterialRequirementDto> recursiveGenerateRequirement(Long detailPlanParentId, List<Long> requirementIdList);


    /**
     * 获取物料采购需求(维度:一个项目下同一交付时间的一个物料采购需求)
     *
     * @param projectId
     * @param pamCode
     * @param deliveryTime
     * @return
     */
    PurchaseMaterialRequirementDto getByProjectIdAndPamCodeAndDeliveryTime(Long projectId, Date deliveryTime, String pamCode);

    /**
     * 获取物料需求对应供应商的物料下达情况(采购员)
     *
     * @param requirementIdStr
     * @param buyerBy
     * @return
     */
    List<PurchaseMaterialRequirementDto> getReleasedOrderDetailByRequirementId(String requirementIdStr, Long buyerBy);

    /**
     * 更新物料需求状态
     *
     * @param requirementId
     * @return
     */
    PurchaseMaterialRequirementDto updateStatus(Long requirementId);

    void batchUpdateRequirementStatus(List<Long> requirementIdList);

    /**
     * 更新物料需求状态
     *
     * @param requirementDto
     * @return
     */
    PurchaseMaterialRequirementDto updateStatus(PurchaseMaterialRequirementDto requirementDto);

    /**
     * 批量更新物料需求状态
     *
     * @param requirementIdList
     * @return
     */
    void batchUpdateStatus(List<Long> requirementIdList);

    /**
     * 加入待下达订单
     *
     * @param dto
     * @return
     */
    Boolean addPendingOrder(PurchaseMaterialRequirementDto dto, Long userBy);

    /**
     * 关闭或打开采购需求
     *
     * @param dto
     * @return
     */
    Boolean closeOrOpenRequirement(PurchaseMaterialRequirementDto dto, Long userBy);

    Boolean closeOrOpenRequirementWbs(PurchaseMaterialRequirementDto dto, Long userBy);

    Boolean closeOrOpenRequirementCommon(PurchaseMaterialRequirementDto dto, Long userBy);

    /**
     * 添加采购需求发布明细
     *
     * @param materialId
     * @param purchaseMaterialRequirementId
     * @param designPlanDetailSon
     * @param releaseDetailStatus
     * @param publisherId
     * @param publishNum
     */
    void addPurchaseMaterialReleaseDetail(Long materialId,
                                          Long purchaseMaterialRequirementId,
                                          MilepostDesignPlanDetailDto designPlanDetailSon,
                                          Integer releaseDetailStatus,
                                          Long publisherId,
                                          BigDecimal publishNum);

    /**
     * 添加采购需求发布明细（WBS）
     *
     * @param requirement         采购需求
     * @param receipts            详细设计单据
     * @param planDetail          详细设计
     * @param releaseDetailStatus 发布明细状态（1=提交;2=变更）
     * @param publisherId         发布人
     * @param publishNum          当前设计信息的最新发布总量
     */
    void addWbsPurchaseMaterialReleaseDetail(PurchaseMaterialRequirement requirement, ProjectWbsReceipts receipts,
                                             ProjectWbsReceipts changeReceipts, MilepostDesignPlanDetail planDetail,
                                             Integer releaseDetailStatus, Long publisherId, BigDecimal publishNum, Long createBy);


    /**
     * 提前采购
     * 生成物料采购需求(递归)
     *
     * @param detailDtoList
     */
    List<PurchaseMaterialRequirementDto> recursiveGenerateRequirementByPurchase(List<MilepostDesignPlanDetailDto> detailDtoList, List<Long> requirementIdList);

    void importPurchaseMaterialRequirement(List<ImportPurchaseMaterialRequirementExcelVO> detailExcelVOList);

    /**
     * 获取外购物料需求信息
     *
     * @param projectId 项目id
     * @return 外购物料需求信息
     */
    List<PurchaseMaterialRequirementDto> getOutRequirementInfo(Long projectId, Long headerId);

    void importPurchaseMaterialRequirementAndCloseDetail(List<ImportPurchaseMaterialRequirementAndCloseDetailExcelVO> detailExcelVOList);

    List<PurchaseMaterialRequirementDto> getChangeOrderDetailByRequirementId(String requirementIdStr, Long buyerBy);

    String addChangingOrder(PurchaseMaterialRequirementDto dto, Long userBy);

    String getRequirementRedisNumber(Long projectId, Date deliveryTime, String pamCode, BigDecimal needTotal, BigDecimal differenceNumber);


    /**
     *
     * 本类中 selectListWithDetail 的替代方法
     * @param query
     * @return
     */
    List<PurchaseMaterialRequirementDto> selectPurchaseMaterialRequirementList(PurchaseMaterialRequirementDto query);

    Boolean correctRequirement(MilepostDesignPlanDetailDto dto);

    /**
     * 校验采购需求收货地址
     * @return
     */
    String checkRequirementDeliveryAddress(RequirementDeliverAddressRepeatCheckDTO repeatCheckDTO);

    /**
     * 批量修改收货地址
     * @param dto
     * @return
     */
    Boolean batchEditRequirementDeliveryAddress(BatchEditRequirementDeliveryAddressDto dto);

    String materialRequirementFreeze(PurchaseMaterialRequirementDto dto);

    String materialRequirementThaw(PurchaseMaterialRequirementDto dto);

    String wbsTypeUpdate(PurchaseMaterialRequirementDto dto);

    String requirementTypeUpdate(PurchaseMaterialRequirementDto dto);
}
