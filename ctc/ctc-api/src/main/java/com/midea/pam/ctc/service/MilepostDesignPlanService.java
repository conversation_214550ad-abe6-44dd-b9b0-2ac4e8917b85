package com.midea.pam.ctc.service;


import com.midea.pam.common.basedata.dto.MaterialCostDto;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanSubmitRecordDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.dto.UpdateDeliveryTimeDto;
import com.midea.pam.common.ctc.dto.UpdateTickDeliveryTimeDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.entity.MrpSum;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectBudgetMaterial;
import com.midea.pam.common.ctc.entity.ProjectMilepostChangeHistory;
import com.midea.pam.common.ctc.query.DetailByProjectIdAndMilepostIdNewWbsQuery;
import com.midea.pam.common.ctc.vo.MilepostDesignPlanDetailApprovedVO;
import com.midea.pam.common.ctc.vo.ProjectBudgetChangeVO;
import com.midea.pam.common.ctc.vo.ProjectCheckExcelVo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


public interface MilepostDesignPlanService {

    /**
     * 根据项目id和里程碑id获取里程碑详细设计方案
     *
     * @param projectId
     * @param milepostId
     * @return
     */
    MilepostDesignPlanDto getDetailByProjectIdAndMilepostId(Long projectId, Long milepostId);

    MilepostDesignPlanDto getDetailByProjectIdAndProjectId(DetailByProjectIdAndMilepostIdNewWbsQuery query);

    MilepostDesignPlanDto getWbsLayerDetailByProjectId(Long projectId,Integer editQuery,String wbsRequirementCode);

    MilepostDesignPlanDto getDetailByProjectIdWithWBSEnabled(DetailByProjectIdAndMilepostIdNewWbsQuery query);

    MilepostDesignPlanDto getDetailByProjectIdAndMilepostIdNew(Long projectId, Long milepostId);

    MilepostDesignPlanDto getDetailByProjectIdAndMilepostIdNewWbs(DetailByProjectIdAndMilepostIdNewWbsQuery query);

    Map<String, Object> getMaterialStatusDetailByProjectIdAndMilepostId(Long projectId, Long milepostId);

    Map<String, Object> getMaterialStatusDetailByProjectId(Long projectId);

    List<String> getDynamicPamCodeOrErpCodeList(Long projectId, Long milepostId, boolean isSynchronizedErp, boolean isGeneratePurchase);

    /**
     * 根据提交记录获取里程碑详细设计方案(仅支持3层结构)
     *
     * @param submitId
     * @return
     */
    MilepostDesignPlanDto getDetailBySubmitId(Long submitId);

    MilepostDesignPlanDetailApprovedVO getDetailBySubmitId1(Long submitId);

    MilepostDesignPlanDetailApprovedVO getDetailByProjectId(Long projectId);

    MilepostDesignPlanDetailApprovedVO getMaterialCostForPurchase(Long submitId);

    /**
     * 根据确认记录获取里程碑详细设计方案(仅支持3层结构)
     *
     * @param confirmId
     * @return
     */
    MilepostDesignPlanDto getDetailByConfirmId(Long confirmId);

    /**
     * 查询所有最底层数据id的所有上级
     *
     * @param
     * @return
     */
    List<MilepostDesignPlanDetailDto> findParentForPlanDetail(List<MilepostDesignPlanDetailDto> bottomPlanDetailDtos);

    /**
     * 移动审批获取里程碑详细设计方案(仅支持3层结构)
     *
     * @param id
     * @return
     */
    ResponseMap getMilepostDesignPlanConfirmApp(Long id);

    /**
     * 发布里程碑详细设计方案
     *
     * @param dto
     * @param userBy
     * @return
     */
    MilepostDesignPlanSubmitRecordDto saveSubmitRecordWithDetail(MilepostDesignPlanDto dto, Long userBy);

    /**
     * 发布里程碑详细设计方案-项目立项
     *
     * @param dto
     * @param userBy
     * @return
     */
    MilepostDesignPlanSubmitRecordDto saveSubmitRecordWithDetailForProject(ProjectDto dto, Long userBy);

    /**
     * 发布里程碑详细设计方案
     *
     * @param asyncRequestResultId
     * @param dto
     * @param userBy
     * @return
     */
    void saveSubmitRecordWithDetail(Long asyncRequestResultId, MilepostDesignPlanDto dto, Long userBy);

    /**
     * 发布里程碑详细设计方案
     *
     * @param asyncRequestResultId
     * @param dto
     * @param userBy
     * @return
     */
    void saveSubmitRecordWithDetailForProject(Long asyncRequestResultId, ProjectDto dto, Long userBy);

    List<String> savePlanWithDetailAsyncBefore(MilepostDesignPlanDto dto);

    /**
     * 发布里程碑详细设计方案(异步)
     *
     * @param dto
     * @param userBy
     * @return
     */
    AsyncRequestResult saveSubmitRecordWithDetailAsync(MilepostDesignPlanDto dto, Long userBy);

    /**
     * 发布里程碑详细设计方案(异步)--项目立项
     *
     * @param dto
     * @param userBy
     * @return
     */
    AsyncRequestResult saveSubmitRecordWithDetailForProjectAsync(ProjectDto dto, Long userBy);

    /**
     * 项目物料预算转换详细方案模组
     *
     * @param projectId
     * @return
     */
    Boolean projectBudgetMaterialToMilepostDesignPlanDetail(Long projectId, Long userBy);

    /**
     * 物料预算转换详设
     *
     * @param idMap                   物料id(第一层产品id)与详细方案设计信息id
     * @param projectBudgetMaterialId 物料预算正式表id
     * @param projectBudgetMaterial   物料预算(变更副本表数据)
     * @param detailParentId          物料预算的父节点在详设表的id
     * @param projectId               项目id
     * @param milepostId              里程碑id
     * @param milepostStatus          里程碑审批状态
     * @param userBy                  操作人id
     * @return
     */
    MilepostDesignPlanDetailDto budgetMaterialTranslateDesignPlanDetail(HashMap<Long, Long> idMap, Long projectBudgetMaterialId,
                                                                        ProjectBudgetMaterial projectBudgetMaterial, Long detailParentId,
                                                                        Long projectId, Long milepostId, Integer milepostStatus, Long userBy);

    /**
     * 通知里程碑负责人确认里程碑
     *
     * @param milepostId
     */
    void notifyThePersonInChargeOfTheMilestoneToConfirm(Long milepostId);

    /**
     * 检查详细方案交付类型里程碑所有模组是否全确认
     *
     * @param milepostId
     */
    Boolean checkMilepostDesignPlanAllConfirm(Long milepostId);

    /**
     * 项目物料转换详细方案模组
     *
     * @param projectId
     * @param userBy
     * @param projectBudgetMaterialChangeHistoryList
     * @return
     */
    Boolean handleProjectBudgetMaterialChange(Long projectId, Long userBy,
                                              List<ProjectBudgetChangeVO.ChangeHistory> projectBudgetMaterialChangeHistoryList);

    List<MaterialCostDto> exportMaterialCostForSubmitRecord(Long submitRecordId);

    List<MaterialCostDto> exportMaterialCostForConfirmRecord(Long confirmRecordId);

    /**
     * 获取审批通过的物料树
     *
     * @param projectId  项目ID
     * @param milepostId 里程碑ID
     * @return 物料树
     */
    MilepostDesignPlanDetailApprovedVO findApprovedDesignPlanDetail(Long projectId, Long milepostId);

    /**
     * 获取审批中的物料树
     *
     * @param submitId 发布ID
     * @return 物料树
     */
    MilepostDesignPlanDetailApprovedVO findApprovalingDesignPlanDetail(Long submitId);

    /**
     * 移动审批获取项目详设发布(提前采购)
     *
     * @param id
     * @return
     */
    ResponseMap getMilepostDesignPlanPurchaseApp(Long id);

    /**
     * 获取提前采购移动端待办
     *
     * @param id
     * @return
     */
    ResponseMap getPurchaseInAdvanceApp(Long id);

    /**
     * 移动审批获取项目详设发布(非提前采购)
     *
     * @param id
     * @return
     */
    ResponseMap getMilepostDesignPlanNoPurchaseApp(Long id);

    MilepostDesignPlanDetailApprovedVO findPurchaseApprovalingDesignPlanDetail(Long submitId);

    /**
     * 检测详细设计下有审批中的详细设计发布和变更流程
     *
     * @param projectId
     * @param milepostId
     * @return
     */
    MilepostDesignPlanDto checkDetailByProjectIdAndMilepostId(Long projectId, Long milepostId);


    MilepostDesignPlanDto getDetailBySubmitIdForStaging(Long submitId);

    List<MilepostDesignPlanDetailDto> getMilepostPlanDesignByTicketTasksCode(Long id);

    ResponseMap setMilepostPlanDesignMRP(Long id);

    List<MrpSum> getMRP();

    void setMRP(Long id);

    ResponseMap changeDeliveryTime(UpdateDeliveryTimeDto dto);

    Boolean updateMrpByOrgAndPamCode(List<Long> projectIds, String pamCode);

    Map<Long, MilepostDesignPlanDetailDto> findMilepostDesignPlanDetails(Long projectId);

    void updateDesignPlanDetail(HashMap<Long, Long> idMap, ProjectBudgetMaterial projectBudgetMaterial, MilepostDesignPlanDetailDto planDetailDto,
                                Integer milepostStatus, Long userBy);

    List<PurchaseMaterialRequirementDto> confirmLogic(Long designPlanDetailId, List<Long> projectIdList, List<Long> requirementIdList);

    ResponseMap updateTickDeliveryTimeDto(List<UpdateTickDeliveryTimeDto> dto);

    List<MilepostDesignPlanDetailDto> packagePlanDetail(List<MilepostDesignPlanDetailDto> designPlanDetailDtos);

    ProjectWbsReceiptsDto saveSubmitHistoryWithDetail(MilepostDesignPlanDto dto, Long userBy);

    List<MilepostDesignPlanDetailDto> filterTopParent(List<MilepostDesignPlanDetailDto> dtos);

    List<MilepostDesignPlanDetailDto> buildMilepostDesignPlanDetailTree(List<MilepostDesignPlanDetailDto> deliveryInformationDtos,
                                                                        List<MilepostDesignPlanDetailDto> topParentDtos);

    List<MilepostDesignPlanDetailDto> getWbsLastLayer(Long projectId);

    Boolean checkMaterialExist(MilepostDesignPlanDto dto);

    void milepostDesignPlanJoinWbs(Long projectId);

    void updateDetailForChangeByDetailIds(List<Long> detailIds, Long updatedBy, Integer status, Integer moduleStatus);

    List<MilepostDesignPlanDetailDto> getDetailsByParentId(Long detailId,Integer editQuery,String wbsRequirementCode);

    List<String> filerMilepostDesignPlanByProjectIdAndErpCodes(MilepostDesignPlanDetailDto dto);

    void partialUpdateByChangeHistory(ProjectMilepostChangeHistory parentChange);

    ProjectCheckExcelVo checkProjectPurchaseMaterialRequirementItem(Project project);

    List<MilepostDesignPlanDetailDto> getMilepostDesignPlanDetails(Long projectId, Long milepostId, boolean isSynchronizedErp,
                                                                   boolean isGeneratePurchase);

    MilepostDesignPlanDetailDto findEntityById(MilepostDesignPlanDetailDto root, String targetId);
}
