package com.midea.pam.ctc.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.ctc.dto.WorkingHourDto;
import com.midea.pam.common.ctc.dto.WorkingHourQueryDto;
import com.midea.pam.common.ctc.dto.WorkingHourResultDto;
import com.midea.pam.common.ctc.dto.WorkingHourStatDto;
import com.midea.pam.common.ctc.dto.WorkingHourStatResultDto;
import com.midea.pam.common.ctc.entity.WorkingHour;
import com.midea.pam.common.ctc.entity.WorkingHourExample;
import com.midea.pam.common.ctc.excelVo.WorkingHourExcelVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 工时管理service
 * @date 2019-4-8
 */
public interface WorkingHourService {

    /**
     * 统计用户工时状态
     *
     * @param userId
     * @param projectId
     * @param projectName
     * @param userType
     * @param status
     * @param startDate
     * @param endDate
     * @return
     */
    JSONObject getUserStatus(Long userId, Long projectId, String projectName, String userType, Integer status, String startDate, String endDate);

    JSONObject getPendingStatusByManager(Long userId, Integer status);

    /**
     * 查询
     *
     * @param userId
     * @param projectName
     * @param userType
     * @param status
     * @param startDate
     * @param endDate
     * @return
     */
    List<WorkingHourStatResultDto> query(Long userId, String projectIds, String projectName, String managerName, String userType, Integer status,
                                         Integer businessFlag, String startDate, String endDate, String dateStr);

    /**
     * 查询工时修改
     *
     * @param userId
     * @param projectId
     * @param projectName
     * @param userType
     * @param status
     * @param startDate
     * @param endDate
     * @return
     */
    List<WorkingHourStatResultDto> queryForUpdate(Long userId, Long projectId, String projectName, String managerName, String userType,
                                                  Integer status, Integer businessFlag, String startDate, String endDate);

    /**
     * 查询填报工时
     *
     * @param userId
     * @param projectId
     * @param projectName
     * @param userType
     * @param startDate
     * @param endDate
     * @return
     */
    List<WorkingHourStatResultDto> queryApplyHour(Long userId, Long projectId, String projectName, String managerName, String userType,
                                                  Integer businessFlag, String projectStatusStr, String startDate, String endDate);

    /**
     * 工时审批列表
     *
     * @param userId
     * @param startDate
     * @param endDate
     * @return
     */
    JSONArray approveList(Long userId, String startDate, String endDate, String projectIds);

    /**
     * 分页查询工时审批列表
     *
     * @param query
     * @return
     */
    PageInfo<WorkingHourResultDto> pageForApprove(WorkingHourQueryDto query, Long userId, String projectIds, String statusStr);

    /**
     * 按月统计(数据权限)
     *
     * @param userId
     * @param startDate
     * @param endDate
     * @param projectId
     * @param userType
     * @param userName
     * @return
     */
    PageInfo<WorkingHourStatDto> statProjectAndMemberByMonth(Long userId, Date startDate, Date endDate, Long projectId, String userType, String userName, Integer pageNum, Integer pageSize);

    /**
     * 按月统计(项目经理)
     *
     * @param userId
     * @param startDate
     * @param endDate
     * @param projectId
     * @param userType
     * @param userName
     * @return
     */
    PageInfo<WorkingHourStatDto> managerProjectAndMemberByMonth(Long userId, Date startDate, Date endDate, Long projectId, String userType, String userName, Integer pageNum, Integer pageSize);

    /**
     * 统计我的工时
     *
     * @param userId
     * @param startDate
     * @param endDate
     * @param projectId
     * @return
     */
    Map<Date, List<WorkingHourResultDto>> myProjectAndMemberByMonth(Long userId, String startDate, String endDate, Long projectId);

    /**
     * 按年统计（数据权限）
     *
     * @param userId
     * @param year
     * @param projectId
     * @param userType
     * @param userName
     * @return
     */
    PageInfo<WorkingHourStatDto> statProjectAndMemberByYear(Long userId, Integer year, Long projectId, String userType, String userName, Integer pageNum, Integer pageSize);

    /**
     * 按年统计（项目经理）
     *
     * @param userId
     * @param year
     * @param projectId
     * @param userType
     * @param userName
     * @return
     */
    List<WorkingHourStatDto> managerProjectAndMemberByYear(Long userId, Integer year, Long projectId, String userType, String userName);

    /**
     * 按年统计
     *
     * @param userId
     * @param year
     * @return
     */
    PageInfo<WorkingHourStatDto> statProjectByYear(Long userId, Integer year, Integer pageNum, Integer pageSize);

    /**
     * 暂存
     *
     * @param userId
     * @param projectId
     * @param applyDate
     * @param applyWorkingHours
     * @return
     */
    Boolean save(Long workHourId, Long userId, Long projectId, String applyDate, Float applyWorkingHours, String week,
                 String wbsBudgetCode, Long laborWbsCostId, String laborWbsCostName);

    /**
     * 重置
     *
     * @param userId
     * @param projectId
     * @param applyDate
     * @param applyWorkingHours
     * @return
     */
    Boolean reset(Long workHourId, Long userId, Long projectId, String applyDate, Float applyWorkingHours, String week,
                  String wbsBudgetCode, Long laborWbsCostId, String laborWbsCostName);

    /**
     * 提交
     *
     * @param userId
     * @param unitId
     * @param workingHourDtos
     * @return
     */
    void submit(Long userId, Long unitId, WorkingHourDto[] workingHourDtos);

    /**
     * 审批通过
     *
     * @param id
     * @param actualWorkingHours
     * @param remarks
     * @return
     */
    Boolean pass(Long id, Float actualWorkingHours, String remarks);

    /**
     * 审批驳回
     *
     * @param id
     * @param remarks
     * @return
     */
    Boolean reject(Long id, String remarks);

    /**
     * 审批驳回
     *
     * @param workingHourDtoList
     * @return
     */
    Boolean reject(List<WorkingHourDto> workingHourDtoList, String rejectComment);

    /**
     * 变更
     *
     * @param id
     * @param applyWorkingHours
     * @return
     */
    Boolean modify(Long id, Long userId, Long projectId, Float applyWorkingHours);

    /**
     * 变更驳回
     *
     * @param id
     * @param remarks
     * @return
     */
    Boolean modifyReject(Long id, String remarks);

    /**
     * 变更撤回
     *
     * @param id
     * @return
     */
    Boolean modifyReturn(Long id);

    List<WorkingHour> selectByExample(WorkingHourExample example);

    /**
     * 根据ID列表批量查询工时记录
     *
     * @param ids 工时ID列表
     * @return 工时记录列表
     */
    List<WorkingHour> selectByIds(List<Long> ids);

    /**
     * 填报工时提醒
     */
    String sendSubmitRemind();

    /**
     * 填报工时提醒(区分白名单)
     */
    void sendSubmitRemindByWhiteList(Date startDate, Date endDate);

    /**
     * 审批工时提醒
     */
    String sendApproveRemind();

    void sendWorkingHourRemind();

    int batchInsert(List<WorkingHour> workingHours);

    JSONObject personalWorkinghour(String startDate, String endDate, Long userId);

    List<WorkingHourResultDto> getWorkingHourResult(Long userId, String startDate, String endDate, Long unitId, String dateStr);

    Response validImportWorkingHourDetail(List<WorkingHourExcelVo> detailImportExcelVoList, Long unitId);

    String getPositionName(Long userId);

    Map<String, Boolean> isExistMultiRoleProject();

    List<Map<String, Object>> getWbsByProjectId(Long projectId, Long userId);

    Map<String, String> budgetValid(WorkingHourDto[] workingHourDtos);

    Map<String, String> checkBudgetCanSubmit(WorkingHourDto[] workingHourDtos);

    void updateLaborCostTypeSetId(WorkingHourResultDto queryDto);

    Integer noWbsFixLaborCostTypeSetId();

    List<OperatingUnit> queryFullPayOuIdList();

    Integer initFullPayOuId();
}
