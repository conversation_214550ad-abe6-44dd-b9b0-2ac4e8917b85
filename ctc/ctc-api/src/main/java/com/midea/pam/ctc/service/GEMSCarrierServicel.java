package com.midea.pam.ctc.service;

import com.midea.pam.common.base.CarrierResponse;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetFeeItemDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.entity.ProjectBudgetChangePushEms;
import com.midea.pam.common.esb.dto.EmsPassRequest;
import com.midea.pam.common.esb.dto.PassResponse;

public interface GEMSCarrierServicel {

    PassResponse callback(EmsPassRequest request);

    EsbResponse gemsProjectNumber(final ProjectDto project, Integer projectStatus, BusinessDto businessDto);

    EsbResponse createBudgetNode(final ProjectBudgetFeeItemDto budget);

    EsbResponse createAdjustApply(final ProjectBudgetChangePushEms projectBudgetChangePushEm);

    /**
     * PAM-EMS-009 创建pam-wbs号 同步项目-wbs关联关系
     *
     * @param projectDto
     * @return
     */
    CarrierResponse saveProjWbsRel(ProjectDto projectDto);
}
