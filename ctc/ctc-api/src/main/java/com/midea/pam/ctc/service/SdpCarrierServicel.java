package com.midea.pam.ctc.service;

import com.midea.pam.common.ctc.dto.SdpTradeResultResponseEleDto;
import com.midea.sdp.dto.common.SdpResult;
import com.midea.sdp.dto.common.SdpTradeResult;

import java.util.List;
import java.util.Map;

public interface SdpCarrierServicel {

    /**
     * ERP查询类接口
     *
     * @param requestParam
     * @return
     */
    SdpTradeResult callERPQuery(Map<String, String> requestParam);

    /**
     * 查询类接口（SDP提供）
     *
     * @param targetSystemCode
     * @param topicCode
     * @param requestParam
     * @return
     */
    SdpTradeResult doSdpDataQuery(String targetSystemCode, String topicCode, Map<String, String> requestParam);

    /**
     * 交易类接口（SDP提供）
     *
     * @param targetSystemCode
     * @param topicCode
     * @param businessId
     * @param requestObj
     * @return
     */
    SdpTradeResult doSdpDataTrade(String targetSystemCode, String topicCode, String businessId, Object requestObj);

    /**
     * 数据类数据推送(SDP提供)
     * @param targetSystemCode
     * @param topicCode
     * @param businessId
     * @param dataList
     * @return
     */
    SdpResult doSdpDataImport(String targetSystemCode, String topicCode, String businessId, List<Object> dataList);


    List<SdpTradeResultResponseEleDto> callSdpMassQuery(String pifacedoe, Map<String, String> requestParam);

    SdpTradeResult callERPQuery2(Map<String, String> requestParam);

    /**
     * 交易类或查询接口（SDP提供）
     *
     * @param targetSystemCode
     * @param requestParam
     * @return
     */
    SdpTradeResult doSdpDataTrade(String targetSystemCode, String topicCode, Map<String, String> requestParam);
}
