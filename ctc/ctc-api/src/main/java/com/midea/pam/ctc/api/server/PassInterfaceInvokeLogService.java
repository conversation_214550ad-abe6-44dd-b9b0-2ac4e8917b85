package com.midea.pam.ctc.api.server;


import com.midea.pam.common.ctc.entity.PassInterfaceInvokeLog;

public interface PassInterfaceInvokeLogService {

    /**
     * 插入日志
     *
     * @param applyNo
     * @param serialNo
     * @param invokeParams
     * @param url
     * @param serviceSystem
     * @return
     */
    Long insertLog(String applyNo, String serialNo, String invokeParams, String url, String serviceSystem);

    /**
     * @param passInterfaceInvokeLog
     */
    void updateLog(PassInterfaceInvokeLog passInterfaceInvokeLog);
}
