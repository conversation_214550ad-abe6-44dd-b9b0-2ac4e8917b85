package com.midea.pam.ctc.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.ProjectBudgetChangeDTO;
import com.midea.pam.common.ctc.dto.ProjectBudgetFeeDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetMaterialDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectManagersOrfinancialsChangeDto;
import com.midea.pam.common.ctc.dto.ProjectMemberDto;
import com.midea.pam.common.ctc.dto.ProjectTerminationDTO;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.ctc.entity.ProjectHistoryHeader;
import com.midea.pam.common.ctc.entity.ProjectMember;
import com.midea.pam.common.ctc.entity.ProjectResourceRel;
import com.midea.pam.common.ctc.entity.WorkingHour;
import com.midea.pam.common.ctc.vo.BusinessVO;
import com.midea.pam.common.ctc.vo.ProjectCycleTimeVo;
import com.midea.pam.common.ctc.vo.ProjectIncomeCostPlanVO;
import com.midea.pam.common.ctc.vo.ProjectVO;
import com.midea.pam.common.enums.ProjectStatus;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 项目主业务服务类.
 * chengzy7
 */
public interface ProjectBusinessService extends ProjectService {
    /**
     * 创建或编辑项目
     *
     * @param projectInfo 项目数据
     * @return 项目id
     */
    Long persistence(final ProjectDto projectInfo);

    /**
     * 保存项目
     *
     * @param projectInfo 项目数据
     * @return ProjectVO 封装对象
     */
    ProjectVO save(final ProjectDto projectInfo);

    /**
     * 发起预立项转正
     *
     * @param projectInfo 项目数据
     * @return ProjectVO 封装对象
     */
    ProjectVO savePreview(final ProjectDto projectInfo);

    /**
     * 项目基础信息变更
     *
     * @param projectInfo 项目数据
     * @return 变更头表id
     */
    Long baseInfoChange(final ProjectDto projectInfo);

    /**
     * 项目目标成本变更
     *
     * @param projectInfo 项目数据
     * @return 变更头表id
     */
    Long budgetTargetChange(final ProjectDto projectInfo);

    /**
     * 批量变更项目经理或者财务经理
     *
     * @param projectDtos 项目数据集合
     * @return 变更头表id
     */
    Long updateManagersOrfinancials(final ProjectManagersOrfinancialsChangeDto projectDtos);

    /**
     * 项目里程碑信息变更
     *
     * @param projectInfo 项目数据
     * @return 变更头表id
     */
    Long milepostChange(final ProjectDto projectInfo);

    /**
     * 收入成本计划变更
     *
     * @param projectInfo 项目数据
     * @return 变更头表id
     */
    Long profitChange(final ProjectDto projectInfo);

    /**
     * 转正--收入成本计划变更
     *
     * @param projectInfo 项目数据
     * @return 变更头表id
     */
    Long savePreviewProfitChange(final ProjectDto projectInfo, ProjectHistoryHeader header);

    /**
     * 转正--月度成本计划变更
     *
     * @param projectInfo 项目数据
     * @return 变更头表id
     */
    Long savePreviewIncomeCostPlanChange(final ProjectDto projectInfo, ProjectHistoryHeader header);

    /**
     * 项目里程碑信息变更审批通过
     *
     * @param headId 表头id
     * @return 变更头表id
     */
    Long milepostChangePass(Long headId);

    /**
     * 项目基础信息变更审批通过
     *
     * @param headId 表头id
     * @return 变更头表id
     */
    Long baseInfoChangePass(Long headId);

    /**
     * 项目目标成本变更审批通过
     *
     * @param headId 表头id
     * @return 变更头表id
     */
    Long projectBudgetTargetChangePass(Long headId);

    void changeContractManager(Project project);


    /**
     * 基础信息变更审批通过
     *
     * @param status 状态跟新
     * @param headId 表头id
     * @return
     */
    Long changeHeadStatus(ProjectStatus status, Long headId);

    /**
     * 项目状态变更
     *
     * @param status    状态跟新
     * @param projectId 项目id
     * @return
     */
    Boolean changeStatus(ProjectStatus status, Long projectId);

    /**
     * 项目历史变更头表状态变更(新)--需统一
     *
     * @param status    状态跟新
     * @param projectId 项目id
     * @return
     */
    void changeProjectHistoryHeaderStatus(ProjectStatus status, Long projectId);

    /**
     * 创建项目
     *
     * @param projectInfo 项目数据
     * @return 项目id
     */
    Long add(final ProjectDto projectInfo);

    /**
     * 编辑项目
     *
     * @param projectInfo 项目数据
     * @return 项目id
     */
    Long update(final ProjectDto projectInfo);

    /**
     * 获取项目详情.
     *
     * @param id 项目id
     * @return 项目详情
     */
    ProjectDto findById(final Long id);

    /**
     * 获取项目转正详情.
     *
     * @param id 项目id
     * @return 项目详情
     */
    ProjectDto findPreviewInfoById(Long id);

    /**
     * 获取项目基本信息
     *
     * @param id 项目id
     * @return 项目详情
     */
    ProjectDto getBaseInfoById(final Long id, String flag);

    /**
     * 获取项目预算信息
     *
     * @param id 项目id
     * @return 项目详情
     */
    ProjectDto getBudgetInfoById(final Long id, String flag);

    /**
     * 查询项目（只获取project）
     *
     * @param id 项目id
     * @return
     */
    ProjectDto findOnlyProjectById(final Long id);

    /**
     * 获取项目成员信息
     *
     * @param id 项目id
     * @return 项目详情
     */
    ProjectDto getMemberInfoById(final Long id, String flag);

    /**
     * 获取项目收入成本
     * 场景：项目详情、发起里程碑变更
     *
     * @param id 项目id
     * @return 项目详情
     */
    ProjectDto getIncomeCostPlansById(final Long id);

    /**
     * 获取项目里程碑信息
     *
     * @param id 项目id
     * @return 项目详情
     */
    ProjectDto getMilepostInfoById(final Long id);

    /**
     * 分页查询项目列表
     *
     * @param projectDto 查询条件
     * @param pageNum    页码
     * @param pageSize   条数
     * @return 项目列表
     */
    PageInfo<ProjectDto> selectPage(final ProjectDto projectDto, final Integer pageNum, final Integer pageSize);

    /**
     * 分页查询项目列表(查询所属虚拟部门)
     *
     * @param projectDto 查询条件
     * @param pageNum    页码
     * @param pageSize   条数
     * @return 项目列表
     */
    PageInfo<ProjectDto> selectPageByUnit(final ProjectDto projectDto, final Integer pageNum, final Integer pageSize);

    /**
     * 分页查询收入成本计划
     *
     * @param project  查询条件
     * @param pageNum  页码
     * @param pageSize 条数
     * @return 项目列表
     */
    PageInfo<ProjectDto> selectFinancePage(final Project project, final Integer pageNum, final Integer pageSize);

    /**
     * 审批通过回调.
     *
     * @param formInstanceId 表单实例id-项目id
     */
    void approvedHandler(final Long formInstanceId, Long companyId);

    void approvedHandler(ProjectDto projectDto);

    void createFirstMonth(Project project, Date currentDate);

    void addLatestIncomeMonth(Project project, Date currentDate);

    /**
     * 关联合同变更审批通过回调.
     */
    void contractChangeApprovedHandler(final Long headerId, Long companyId, Long createUserId);

    /**
     * 项目类型配置为“允许立项时上传详细设计方案”为是的项目
     *
     * @param projectDto 表单实例id-项目id
     * @param status     审批状态
     */
    void updateProjectDesign(ProjectDto projectDto, Integer status);

    /**
     * 员工工作饱和度.
     *
     * @param userId    员工
     * @param projectId 当前项目
     * @return 员工工作情况
     */
    List<ProjectMemberDto> memberSaturation(final Long userId, final Long projectId);

    /**
     * 保存或修改项目成员
     *
     * @param projectMember
     * @return
     */
    int persistenceMember(ProjectMember projectMember);

    /**
     * 保存或修改项目成员
     *
     * @param projectMembers
     * @return
     */
    void persistenceMember(List<ProjectMember> projectMembers);

    /**
     * 检查人员一天只能在一个项目
     *
     * @param projectMembers
     * @return
     */
    Boolean checkMemberDistinct(List<ProjectMemberDto> projectMembers);

    /**
     * 项目列表查询
     *
     * @param status     状态
     * @param fuzzyLike  模糊查询条件
     * @param wbsEnabled 启用wbs
     * @return 项目列表
     */
    List<ProjectDto> listProjects(Integer status, String fuzzyLike, Boolean wbsEnabled);

    /**
     * 检查项目是否进行中
     *
     * @param projectId
     * @return
     */
    Boolean checkProjectIsApprovaled(Long projectId);

    /**
     * 获取当前用户的分配的所有虚拟部门
     *
     * @return List<Long>
     */
    List<Long> getUnitIds();

    /**
     * 获取项目周期时间
     *
     * @param projectTypeId 项目类型id
     * @param subcontractId 子合同id
     * @return 项目周期时间
     */
    ProjectCycleTimeVo findProjectCycleTime(Long projectTypeId, Long subcontractId);

    /**
     * 获取服务项目类型
     *
     * @param projectTypeId 项目类型ID
     * @param projectTypeId 项目ID
     * @param ouId          业务实体ID
     * @return 服务项目类型，1表示saas服务，2表示非saas服务，月度确认类型，3表示其他服务
     */
    String findServerProjectType(Long projectTypeId, Long projectId, Long ouId);

    /**
     * 计算月度成本收入计划
     *
     * @param beginTime       项目开始时间（字符串类型, yyyy-MM）
     * @param endTime         项目结束时间（字符串类型, yyyy-MM）
     * @param allIncomeAmount 主-里程碑收入总额
     * @param allCostAmount   主-里程碑预算总额
     * @return 月度成本收入计划集合
     */
    List<ProjectIncomeCostPlanVO> countIncomeCostPlan(String beginTime, String endTime, String allIncomeAmount, String allCostAmount);

    /**
     * 获取可用项目预算（不含税）
     *
     * @param projectId 项目ID
     * @param projectId 合同ID
     * @return 可用项目预算（不含税）
     */
    BigDecimal findAvailableProjectBudget(Long projectId, Long contractId);

    Map<String, Object> findAvailableProjectBudgetDetail(Long projectId, Long contractId);

    /**
     * 获取收入成本计划总天数
     *
     * @param projectId 项目id
     * @return 时间信息
     */
    ProjectCycleTimeVo findProjectIncomeCostPlanTotalTime(Long projectId);


    /**
     * 根据项目ID判断当前登录用户是否创建人,true是 false否
     *
     * @param projectId
     * @return Boolean
     */
    Boolean isCreater(Long projectId);

    /**
     * 根据项目ID判断当前登录用户是否项目经理,true是 false否
     *
     * @param projectId
     * @return Boolean
     */
    Boolean isManager(Long projectId);

    /**
     * 根据项目ID判断当前登录用户是项目成员,true是 false否
     *
     * @param projectId
     * @return Boolean
     */
    Boolean isMember(Long projectId);

    /**
     * 根据项目ID判断当前登录用户是否财务人员,true是 false否
     *
     * @param projectId
     * @return Boolean
     */
    Boolean isFinance(Long projectId);

    /**
     * 根据项目ID判断当前登录用户是否拥有数据权限,true是 false否
     *
     * @param projectId
     * @return Boolean
     */
    Boolean isDataAuthor(Long projectId);

    /**
     * 根据里程碑ID判断当前登录用户是否责任人,true是 false否
     *
     * @param milepostId
     * @return Boolean
     */
    Boolean isDeliverAuthor(Long milepostId);

    /**
     * 判断当前登录用户是否有交付按钮权限,true是 false否
     *
     * @param milepostId
     * @param projectId
     * @return Boolean
     */
    Boolean isDeliverPermission(Long milepostId, Long projectId);

    /**
     * 变更收入成本计划
     *
     * @param projectId
     * @param contractExcludingTaxAmount 合同不含税金额
     * @param contractStartDate          子合同开始日期
     * @param contractEndDate            字合同结束日期
     * @return 变更结果
     */
    Boolean changeIncomeCostPlans(Long projectId, BigDecimal contractExcludingTaxAmount, Date contractStartDate, Date contractEndDate,
                                  BigDecimal mainIncome, BigDecimal helpIncome);

    /**
     * 获取项目立项审批数据
     *
     * @param projectId 项目ID
     * @return ResponseMap
     */
    ResponseMap getProjectApp(Long projectId);

    /**
     * 获取转质保立项审批数据
     *
     * @param projectId 项目ID
     * @return ResponseMap
     */
    ResponseMap getProjectTransferQualityApp(Long projectId);

    /**
     * 获取项目转正审批数据
     *
     * @param projectId 项目ID
     * @return ResponseMap
     */
    ResponseMap getPreviewProjectApp(Long projectId);

    PageInfo<ProjectDto> page(final ProjectDto projectDto, final Integer pageNum, final Integer pageSize);

    List<ProjectDto> list(final ProjectDto projectDto);

    Map<String, Object> listExport(final ProjectDto projectDto);


    /**
     * 获取项目立项提示信息
     *
     * @param type organization_custom_dict_20项目名称命名规则提示 organization_custom_dict_22预立项项目提交提示
     * @return ResponseMap
     */
    String getProjectInfo(String type);

    OrganizationRelDto findOrganizationRel(final Long ouId);

    Boolean checkBusinessRemainCost(String businessCode, Long contractId);

    Long businessProject(ProjectDto projectDto);

    Long businessMember(ProjectMemberDto member);

    Long updateProjectStatus(ProjectDto projectDto);

    List<WorkingHour> getWorkingHour(String code);

    List<Project> getProject(String code);

    Long saveProjectTermination(ProjectTerminationDTO dto);

    Boolean checkPurchaseOrder(ProjectBudgetChangeDTO projectBudgetChangeDTO);

    Boolean checkProjectCode(String projectCode, Long unitId);


    /**
     * 项目里程碑变更
     *
     * @param status 状态跟新
     * @param headId 表头id
     * @return
     */
    Long changeMilepostHisStatus(ProjectStatus status, Long headId);

    void checkResourceDate(ProjectResourceRel projectResourceRel);

    String getResourceMemberCheck(ProjectResourceRel projectResourceRel);

    JSONObject checkResourceMember(List<ProjectResourceRel> projectResourceRels);

    /**
     * 基础信息批量变更审批中，驳回，撤回，作废，删除
     *
     * @param formInstanceId 状态跟新
     * @return
     */
    Long batchChangeHeadStatus(ProjectStatus status, Long formInstanceId);

    /**
     * 项目基础信息批量变更审批通过
     *
     * @param formInstanceId 批次
     * @return 批次
     */
    Long baseInfoBatchChangePass(Long formInstanceId);

    List<ProjectBudgetMaterialDto> getBudgetMaterial(Long id);

    Boolean wbsCallback(Long id, Boolean isSuccess, String msg);

    /**
     * 根据条件查询项目信息
     */
    List<ProjectDto> findProject(ProjectDto projectDto);

    /**
     * 按id查询项目立项/转正数据快照
     *
     * @param projectId
     * @param snapshotType
     * @return
     */
    ProjectDto findProjectSnapshot(Long projectId, Integer snapshotType);

    /**
     * 关联合同变更
     *
     * @param projectInfo
     */
    ProjectVO contractRsChange(ProjectDto projectInfo);

    /**
     * 关联合同变更通过
     *
     * @param headId
     * @return
     */
    Long projectContractChangePass(Long headId);

    /**
     * 获取项目关联合同变更详情
     *
     * @param headId
     * @return
     */
    ProjectDto findContractRsChangeInfo(Long headId);

    /**
     * 获取预立项转正变更详情
     *
     * @param projectId
     * @return
     */
    ProjectDto findPreContractBusinessSnapShot(Long projectId);

    /**
     * 判断当前商机报价是否必须用完
     *
     * @param contractIds
     * @param businessIds
     * @return
     */
    List<BusinessVO> isCostCanbeFullUsed(List<Long> contractIds, List<Long> businessIds, Long currentProjectId);

    /**
     * 保存商机报价单变更信息
     *
     * @param projectDto
     * @param header
     */
    void saveBusinessRsChangeHistory(ProjectDto projectDto, ProjectHistoryHeader header);


    void calculationContractAmount(ProjectDto info);

    /**
     * 移动端 WBS关联合同变更审批
     *
     * @param headerId 表头id
     * <AUTHOR>
     * @Date 2023-01-04, 0004 上午 10:56:24
     */
    ResponseMap projectContractRelChangeApp(Long headerId);

    Integer updateProjectByCode(ProjectDto projectDto);

    List<WorkingHour> checkProjectWorkingHourApprove(String code);

    /**
     * 更新项目关联的子合同的业务分类与业务分部
     *
     * @param project
     */
    void changeContractBusinessAndDepartment(Project project);

    /**
     * 项目变更，当涉及 业务分类、业务分布、项目经理的变更，如果项目有关联合同，回写销售合同并则插一条变更记录进主合同变更记录
     *
     * @param headId
     */
    void writeBackContractInfo(Long headId);

    Boolean checkIfHadCarryoverBill(Long projectId, String currency);

    void checkMaterialsCanDelete(Long projectId, List<ProjectBudgetMaterialDto> materials);

    void checkFeeCanDelete(Long projectId, List<ProjectBudgetFeeDto> fees);

    Long assetRsChange(ProjectDto projectInfo);

    void projectStatusCheck(Long id);

    void updateModuleStatusByProjectType(Project project);

    void milepostDesignCanSubmit(ProjectDto projectInfo);
}
