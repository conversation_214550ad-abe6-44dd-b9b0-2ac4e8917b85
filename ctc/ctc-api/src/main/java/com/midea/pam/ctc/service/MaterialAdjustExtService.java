package com.midea.pam.ctc.service;

import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;

import java.util.List;

/**
 * Description
 * Created by liuqing
 * Date 2022/7/22 16:37
 */
public interface MaterialAdjustExtService {

    DataResponse<List<MilepostDesignPlanDetailDto>> validImportDesignPlanDetailMaterial(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos,
                                                                                        Long storageId, Long unitId,
                                                                                        List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo,
                                                                                        Long markId, String wbsSummaryCode, Boolean wbsEnabled,
                                                                                        Long projectId, Boolean isChange, Long detailId,
                                                                                        Boolean skipMultiLevelCheck);

    DataResponse<List<MilepostDesignPlanDetailDto>> validBatchImportDesignPlanDetailMaterial(List<MilepostDesignPlanDetailDto> kukaDetailExcelVos,
                                                                                             Long storageId, Long unitId,
                                                                                             List<MilepostDesignPlanDetailDto> designPlanDetailDtoForWebInfo,
                                                                                             Long markId, Boolean wbsEnabled,
                                                                                             Long projectId, Boolean isChange,
                                                                                             String operateType);
}
