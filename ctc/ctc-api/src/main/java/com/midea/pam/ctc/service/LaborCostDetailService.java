package com.midea.pam.ctc.service;

import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.ctc.dto.LaborCostDetailDto;
import com.midea.pam.common.ctc.entity.LaborCostDetail;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 人工成本明细业务接口类
 *
 * <AUTHOR>
 * @since 2019-06-10 10:23
 */
public interface LaborCostDetailService {

    /**
     * 人工成本（内/外部）计算
     *
     * @param collectionDto  归集信息
     * @param collectionDate 归集日期
     * @return 人工成本明细
     */
    List<LaborCostDetail> collection(CostCollectionDto collectionDto, Date collectionDate, Map<Long, Map<String, BigDecimal>> unitMap);

    /**
     * 校正未入账的工时科目
     *
     * @param queryDto
     */
    void updateLaborCostHardWorking(LaborCostDetailDto queryDto);

    /**
     * 批量保存人工费用明细
     *
     * @param collectionId     归集ID
     * @param laborCostDetails 人工费用明细
     */
    void batchSave(Long collectionId, List<LaborCostDetail> laborCostDetails);

    /**
     * 根据归集ID和类型查询人工费用明细
     *
     * @param collectionId 归集ID
     * @param typeList     类型 1：内部 2：外部 3：自招外包
     * @return 人工费用明细
     */
    List<LaborCostDetailDto> queryDetailByCollectionIdAndType(Long collectionId, List<String> typeList);

    /**
     * 根据归集ID获取人工成本
     *
     * @param collectionId 归集ID
     * @return 人工成本
     */
    List<LaborCostDetail> queryByCollection(Long collectionId);

    /**
     * 查询已被归集且实际人力费率为0的人工成本
     *
     * @return
     */
    List<LaborCostDetail> findNeedRestartCollection();

    /**
     * 同步人工成本明细hard_working字段
     * 根据cnb_emp_basic_data的更新时间，查询发薪ou和项目ou不一致的数据，
     * 以user_id+statistic_date维度更新labor_cost_detail表的hard_working字段
     *
     * @param lastUpdateDate 最后更新时间
     */
    void syncLaborCostHardWorking(String lastUpdateDate);
}
