package com.midea.pam.ctc.service;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.FeeItemDto;
import com.midea.pam.common.ctc.dto.CheckContractDto;
import com.midea.pam.common.ctc.dto.FeeItemExpenseTypeDto;
import com.midea.pam.common.ctc.dto.ProjectTypeCheckRelDto;
import com.midea.pam.common.ctc.dto.ProjectTypeDto;
import com.midea.pam.common.ctc.entity.ProjectType;
import com.midea.pam.common.ctc.entity.ProjectTypeExample;
import com.midea.pam.common.ctc.entity.ProjectTypeRemind;
import com.midea.pam.common.ctc.vo.ProjectTypeVO;

import java.util.List;

public interface ProjectTypeService {
    long countByExample(ProjectTypeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectType record);

    int insertSelective(ProjectType record);

    List<ProjectType> selectByExample(ProjectTypeExample example);

    ProjectType selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProjectType record);

    int updateByPrimaryKey(ProjectType record);

    Long persistence(ProjectType record);

    PageInfo<ProjectTypeDto> selectByPage(final String name, final String code, final Boolean deletedFlag,
                                          final String isAll, final Integer pageNum, final Integer pageSize, Boolean auto, Long unitId);

    PageInfo<ProjectTypeDto> selectList(final String name, final String code, final Boolean deletedFlag,
                                        final String isAll, final Integer pageNum, final Integer pageSize, Boolean auto, Long unitId);

    Long save(ProjectTypeDto dto);

    List<ProjectTypeCheckRelDto> selectProjectTypeCheckRelDtoById(Long projectTypeId);

    ProjectTypeDto selectProjectTypeByContractId(Long contractId);

    int addOrDelProjectTypeCheckRel(Long projectTypeId, String addCheckItemIds, String delCheckItemIds);

    int delProjectTypeCheckRelById(Long relId);

    int isStrictlyControl(Long relId, boolean yesOrNo);

    Long checkProjectTypeByContractId(CheckContractDto dto);

    ProjectTypeVO queryById(ProjectType projectType);

    /**
     * 校验当前单位是否存在wbs类型的项目类型
     *
     * @return
     */
    boolean checkWbsProjectType();

    /**
     * 获取费用类型经济事项
     *
     * @param projectTypeId
     * @return
     */
    List<FeeItemExpenseTypeDto> selectFeeItemExpenseType(Long projectTypeId);

    /**
     * 根据id批量查询
     *
     * @param projectTypeIds 项目类型id集合
     */
    List<ProjectType> getByIds(List<Long> projectTypeIds);

    List<ProjectType> queryByTransferType();

    ProjectTypeRemind queryRemindConfig(Long projectTypeId);

    Long saveRemindConfig(ProjectTypeRemind projectTypeRemind);

    List<FeeItemDto> selectExpenseTypeSet(Long projectTypeId);

    String updeteBudgetConfig();
}