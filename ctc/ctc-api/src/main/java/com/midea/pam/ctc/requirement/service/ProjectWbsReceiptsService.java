package com.midea.pam.ctc.requirement.service;

import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailChange;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample;
import com.midea.pam.common.ctc.excelVo.ImportProjectRequirementPublishExcelVO;
import com.midea.pam.common.ctc.excelVo.ImportProjectWbsRequirementPublishExcelVO;
import com.midea.pam.ctc.service.BaseService;

import java.util.List;
import java.util.Map;

public interface ProjectWbsReceiptsService extends BaseService<ProjectWbsReceiptsDto> {

    /**
     * 查看详情
     *
     * @param id
     * @return
     */
    ProjectWbsReceiptsDto findRequirementDetail(Long id);

    /**
     * 搜索
     *
     * @param dto
     * @return
     */
    ProjectWbsReceiptsDto filterTreeSearch(ProjectWbsReceiptsDto dto);

    /**
     * 下一步 新建界面第一步
     *
     * @param dto
     * @return
     */
    ProjectWbsReceiptsDto filterTree(ProjectWbsReceiptsDto dto);

    /**
     * 保存 新建界面第二步
     *
     * @param dto
     * @return
     */
    ProjectWbsReceiptsDto saveReceipts(ProjectWbsReceiptsDto dto, Long userBy);

    ProjectWbsReceiptsDto saveReceiptsForChange(ProjectWbsReceiptsDto dto, Long userBy);

    /**
     * 提交 新建界面第三步
     *
     * @param dto
     * @return
     */
    ProjectWbsReceiptsDto submitReceipts(ProjectWbsReceiptsDto dto);

    /**
     * 退回 新建界面第三步
     *
     * @param id
     * @return
     */
    Integer reject(Long id);

    Boolean changeReceiptsReject(Long id);

    /**
     * 作废 新建界面第二步(草稿状态)
     *
     * @param id
     * @return 需求发布单据id
     */
    Integer cancel(Long id);

    /**
     * 单据详情导出
     *
     * @param id
     * @return 导出数据集
     */
    Map<String, Object> detailExport(Long id);

    /**
     * 根据需求发布单据，获取详细设计
     *
     * @param receipts
     * @return
     */
    List<MilepostDesignPlanDetail> getDesignPlanDetail(ProjectWbsReceipts receipts);

    /**
     * 新建详设发布单据
     *
     * @param dto
     * @return
     */
    ProjectWbsReceiptsDto saveReceiptsForSubmit(ProjectWbsReceiptsDto dto, Long userBy);

    ProjectWbsReceiptsDto findDetailForSubmit(Long id);

    ProjectWbsReceiptsDto findNonHierarchicalSubmitDetail(Long id);

    void saveRelForSubmit(ProjectWbsReceiptsDto dto, Long userBy, List<Long> historyIds);

    /**
     * 根据详设发布单据，获取详细设计
     *
     * @param receipts
     * @return
     */
    List<MilepostDesignPlanDetailSubmitHistory> getDesignPlanDetailForSubmit(ProjectWbsReceipts receipts);

    Integer deleteForSubmit(Long id);

    String generateRequirementCode();

    List<ProjectWbsReceipts> selectByExample(ProjectWbsReceiptsExample example);

    List<ProjectWbsReceipts> selectByExampleWithBLOBs(ProjectWbsReceiptsExample example);

    List<ProjectWbsReceiptsDto> selectByIds(List<Long> ids);

    List<ProjectWbsReceiptsDto> selectApprovingReceiptsByIds(List<Long> ids);

    ProjectWbsReceiptsDto findDetailForChange(Long id);

    ProjectWbsReceiptsDto submitChangeReceipts(ProjectWbsReceiptsDto dto);

    void projectWbsReceiptsBudgetChangeHistory(Long projectWbsChangeReceiptsId);

    Boolean changeReceiptsCancel(Long id);

    String getRequirementCodeByReceiptsId(Long receiptsId);

    List<ProjectWbsReceiptsDto> selectProcessReceiptsByDetailIds(List<Long> detailIds);

    List<ProjectWbsReceiptsDto> selectByDetailIds(List<Long> detailIds);

    void importProjectWbsRequirementPublish(List<ImportProjectWbsRequirementPublishExcelVO> detailExcelVOList);

    ResponseMap getRequirementPublishApp(Long id);

    List<MilepostDesignPlanDetailChange> getDesignPlanDetailForChange(ProjectWbsReceipts receipts);

    void updateRequirementStatus(Long id, Integer requirementStatus);

    void importProjectRequirementPublish(List<ImportProjectRequirementPublishExcelVO> detailExcelVOList);

    ProjectWbsReceipts getByRequirementCode(String requirementCode);

    List<String> getProjectWbsReceiptsBudgetErrorCode(Long startId, Long endId);

    List<ProjectWbsReceiptsDto> getByIdList(List<Long> idList);

    List<ProjectWbsReceiptsDto> getByRequirementCodeFuzzy(String fuzzyLike);

    void changeMilepostDesignPlanDetailStatusReceiptsPassed(String milepostDesignPlanDetailIdStr);

    void deleteById(Long id);

    /**
     * 查询当前状态不为作废和生效的需求发布单据数据
     *
     * @param projectId
     * @return
     */
    List<ProjectWbsReceipts> queryDesignInfoByProjectId(Long projectId);

    /**
     * 根据详设id查询对应的详设单据号
     *
     * @param designId
     * @return
     */
    String queryRequirementCodeByDesignId(Long designId);

    Map<String, Object> checkTemplateWbsChange(ProjectWbsReceiptsDto dto);

    /**
     * 新建wbs变更单据
     *
     * @param dto
     * @param userId
     * @return
     */
    ProjectWbsReceiptsDto saveWbsChangeReceipts(ProjectWbsReceiptsDto dto, Long userId);

    /**
     * 查询wbs变更单据
     *
     * @param id
     * @return
     */
    ProjectWbsReceiptsDto findWbsChangeDetail(Long id);

    Boolean commonReject(Long id);

    Boolean commonCancel(Long id);

    ProjectDto buildTemplateContent(Long projectWbsReceiptsId, String tag);

    Boolean ProjectWbsDesignPlanAutoConfirm(Long projectId,Long projectWbsReceiptsId);
}
