<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>

    <context id="Mysql" targetRuntime="MyBatis3" defaultModelType="flat">

        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="com.midea.pam.common.util.OverWriteXmlPlugin" />
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>

        <commentGenerator type="com.midea.pam.ctc.config.ApiCommentGenerator">
            <property name="suppressDate" value="false"/>
            <property name="suppressAllComments" value="false"/>
        </commentGenerator>
        <!--数据库链接地址账号密码-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*******************************************************"
                        userId="pam_sit"
                        >
            <property name="useInformationSchema" value="true" />
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!--生成Model类存放位置-->
        <javaModelGenerator targetPackage="com.midea.pam.common.ctc.entity"
                            targetProject="common-module/src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
            <!--设置所有实体类的基类。如果设置，需要使用类的全限定名称。并且如果MBG能够加载rootClass，那么MBG不会覆盖和父类中完全匹配的属性。-->
            <property name="rootClass" value="com.midea.pam.common.base.LongIdEntity"/>
        </javaModelGenerator>
        <!--生成映射文件存放位置-->
        <sqlMapGenerator targetPackage="com.midea.pam.ctc.mapper"
                         targetProject="ctc/ctc-dao/src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!--生成Dao类存放位置-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.midea.pam.ctc.mapper"
                             targetProject="ctc/ctc-dao/src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="rootInterface" value="com.meicloud.light.mapper.Mapper"/>
        </javaClientGenerator>

        <table tableName="gsc_payment_invoice" domainObjectName="GscPaymentInvoice"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                    <columnOverride column="status" javaType="Integer" />
                    <columnOverride column="tax_invoice_status" javaType="Integer" />
        </table>

    <!--    <table tableName="gsc_payment_invoice_detail" domainObjectName="GscPaymentInvoiceDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

     <!--   <table tableName="gsc_payment_invoice_isp_detail" domainObjectName="GscPaymentInvoiceIspDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>
-->

<!--                <table tableName="sdp_log" domainObjectName="SdpLog"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                    <columnOverride column="operation_type" javaType="java.lang.Integer" />
                    <columnOverride column="status" javaType="java.lang.Integer" />
                </table>-->

<!--        <table tableName="vendor_penalty_invoice_amount_change" domainObjectName="VendorPenaltyInvoiceAmountChange"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="update_invoice_amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="original_invoice_amount" javaType="java.math.BigDecimal" />-->
<!--        </table>-->

<!--        <table tableName="mif_push_log" domainObjectName="MifPushLog"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        </table>-->

<!--        <table tableName="vendor_penalty_cost_detail" domainObjectName="VendorPenaltyCostDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="project_cost" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="conversion_rate" javaType="java.math.BigDecimal" />-->
<!--        </table>-->

<!--        <table tableName="vendor_penalty_history" domainObjectName="VendorPenaltyHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="erp_status" javaType="Integer" />-->
<!--            <columnOverride column="history_type" javaType="Integer" />-->
<!--            <columnOverride column="collection_flag" javaType="Boolean" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="invoice_amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="collection_amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="conversion_rate" javaType="java.math.BigDecimal" />-->
<!--        </table>-->

<!--        <table tableName="vendor_penalty_change" domainObjectName="VendorPenaltyChange"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="vendor_penalty_detail_history" domainObjectName="VendorPenaltyDetailHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="history_type" javaType="Integer" />-->
<!--            <columnOverride column="amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="invoice_amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="project_cost" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="occur_cost" javaType="java.math.BigDecimal" />&ndash;&gt;-->
<!--            <columnOverride column="collection_amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="summary_occur_cost" javaType="java.math.BigDecimal" />-->
<!--        </table>-->

<!--        <table tableName="vendor_penalty_detail" domainObjectName="VendorPenaltyDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="collection_flag" javaType="Boolean" />-->
<!--            <columnOverride column="summary_flag" javaType="Boolean" />-->
<!--            <columnOverride column="amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="invoice_amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="project_cost" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="occur_cost" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="collection_amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="summary_occur_cost" javaType="java.math.BigDecimal" />-->
<!--        </table>-->

<!--        <table tableName="vendor_penalty" domainObjectName="VendorPenalty"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="erp_status" javaType="Integer" />-->
<!--            <columnOverride column="collection_flag" javaType="Boolean" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="invoice_amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="collection_amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="conversion_rate" javaType="java.math.BigDecimal" />-->
<!--        </table>-->

<!--        <table tableName="vendor_penalty_config" domainObjectName="VendorPenaltyConfig"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="hro_working_hour_bill_cost_detail" domainObjectName="HroWorkingHourBillCostDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="bill_cost" javaType="java.math.BigDecimal" />-->
<!--        </table>-->

<!--        <table tableName="hro_working_hour_bill" domainObjectName="HroWorkingHourBill"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="erp_sync_status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="collection_flag" javaType="Boolean" />-->
<!--            <columnOverride column="total_working_hour" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="amount" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="tax_rate" javaType="java.math.BigDecimal" />-->
<!--        </table>-->

<!--        <table tableName="hro_working_hour_bill_item" domainObjectName="HroWorkingHourBillItem"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="car_fare" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="boarding_fee" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="other_fee" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="holiday_working_hour" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="deduct_fee" javaType="java.math.BigDecimal" />-->
<!--            <columnOverride column="role_price" javaType="java.math.BigDecimal" />-->
<!--        </table>-->

<!--        <table tableName="hro_bill_working_hour_ref" domainObjectName="HroBillWorkingHourRef"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->


<!--        <table tableName="hro_working_hour_change" domainObjectName="HroWorkingHourChange"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="hro_working_hour_item_history" domainObjectName="HroWorkingHourItemHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="history_type" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="hro_working_hour_import" domainObjectName="HroWorkingHourImport"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="total_working_hour" javaType="BigDecimal" />-->
<!--        </table>-->

<!--        <table tableName="hro_working_hour_item" domainObjectName="HroWorkingHourItem"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="hro_requirement_change" domainObjectName="HroRequirementChange"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="hro_requirement_item_his" domainObjectName="HroRequirementItemHis"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="his_type" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="hro_requirement" domainObjectName="HroRequirement"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="hro_requirement_item" domainObjectName="HroRequirementItem"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="sdp_buyers" domainObjectName="SdpBuyers"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="delete_flag" javaType="Boolean" />-->
<!--        </table>-->


<!--        <table tableName="purchase_order_change_history" domainObjectName="PurchaseOrderChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="erp_order_status" javaType="Integer" />-->
<!--            <columnOverride column="order_status" javaType="Integer" />-->
<!--            <columnOverride column="sync_status" javaType="Integer" />-->
<!--            <columnOverride column="pricing_type" javaType="Integer" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="dispatch_is" javaType="Boolean" />-->
<!--        </table>-->

<!--   <table tableName="purchase_order_change_record" domainObjectName="PurchaseOrderChangeRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        <columnOverride column="status" javaType="Integer" />
       <columnOverride column="syncStatus" javaType="Integer" />
        </table>-->
<!--          <table tableName="purchase_order_detail_change_history" domainObjectName="PurchaseOrderDetailChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--              <columnOverride column="merge_rows" javaType="Integer" />-->
<!--              <columnOverride column="requirement_code_star" javaType="Integer" />-->
<!--              <columnOverride column="design_release_lot_number_star" javaType="Integer" />-->
<!--              <columnOverride column="wbs_summary_code_star" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="purchase_order_merge" domainObjectName="PurchaseOrderMerge"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="merge_rows" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="purchase_order_merge_change_history" domainObjectName="PurchaseOrderMergeChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="merge_rows" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="purchase_order" domainObjectName="PurchaseOrder"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="erp_order_status" javaType="Integer" />-->
<!--            <columnOverride column="order_status" javaType="Integer" />-->
<!--            <columnOverride column="sync_status" javaType="Integer" />-->
<!--            <columnOverride column="pricing_type" javaType="Integer" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="dispatch_is" javaType="Boolean" />-->
<!--        </table>-->
<!--        <table tableName="purchase_material_requirement" domainObjectName="PurchaseMaterialRequirement" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="approved_supplier_number" javaType="Integer" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="purchase_type" javaType="Integer" />-->
<!--            <columnOverride column="dispatch_is" javaType="Boolean" />-->
<!--            <columnOverride column="init" javaType="Boolean" />-->
<!--            <columnOverride column="requirement_type" javaType="Integer" />-->
<!--            <columnOverride column="freeze_flag" javaType="Integer" />-->
<!--        </table>-->
<!--        <table tableName="purchase_order_detail" domainObjectName="PurchaseOrderDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="merge_rows" javaType="Boolean" />-->
<!--         <columnOverride column="requirement_code_star" javaType="Integer" />-->
<!--         <columnOverride column="design_release_lot_number_star" javaType="Integer" />-->
<!--         <columnOverride column="wbs_summary_code_star" javaType="Integer" />-->
<!--            <columnOverride column="sync_delivery_info_status" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="purchase_order_title" domainObjectName="PurchaseOrderTitle"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="pricing_type" javaType="Integer" />-->
<!--            <columnOverride column="dispatch_is" javaType="Boolean" />-->
<!--        </table>-->

     <!--   <table tableName="supplier_quality_deactivation" domainObjectName="SupplierQualityDeactivation"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
<!--        <table tableName="collection_configuration" domainObjectName="CollectionConfiguration"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="enable" javaType="Boolean" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="category" javaType="Integer" />
        </table>-->
        <!--<table tableName="purchase_order_record" domainObjectName="PurchaseOrderRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
<!--        <table tableName="ticket_tasks" domainObjectName="TicketTasks"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="unit_price" javaType="BigDecimal" />-->
<!--            <columnOverride column="task_status" javaType="Integer" />-->
<!--        </table>-->
<!--        <table tableName="project_problem_file_rel" domainObjectName="ProjectProblemFileRel"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->
        <!--金税 税票-->
<!--                <table tableName="invoice_receivable_external_logs" domainObjectName="InvoiceReceivableExternalLogs"-->
<!--                       enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                    <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                    <columnOverride column="external_invoice_amount" javaType="java.math.BigDecimal" />-->
<!--                    <columnOverride column="external_invoice_tax" javaType="java.math.BigDecimal" />-->
<!--                </table>-->


        <!--<table tableName="ticket_tasks" domainObjectName="TicketTasks"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="task_status" javaType="Integer" />
        </table>-->
        <!--<table tableName="mrp_sum" domainObjectName="MrpSum"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
               <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->
        <!--生成对应表及类名-->
       <!-- <table tableName="product_tax_setting" domainObjectName="ProductTaxSetting"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Integer" />
        </table>-->
<!--
        <table tableName="project_funds_deploy" domainObjectName="ProjectFundsDeploy"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Integer" />
        </table>
        <table tableName="project_risk_setting" domainObjectName="ProjectRiskSetting"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="risk_flag" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Integer" />
        </table>-->
        <!--<table tableName="business_change_record" domainObjectName="BusinessChangeRecord"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="business_competition" domainObjectName="BusinessCompetition"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="business_contact_rel" domainObjectName="BusinessContactRel"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="business_extra" domainObjectName="BusinessExtra"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="business_follow_record" domainObjectName="BusinessFollowRecord"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="business_product" domainObjectName="BusinessProduct"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="business_product_module" domainObjectName="BusinessProductModule"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="business_product_module_quotation" domainObjectName="BusinessProductModuleQuotation"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="business_product_quotation" domainObjectName="BusinessProductQuotation"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="business_prompt" domainObjectName="BusinessPrompt"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="business_record_comment" domainObjectName="BusinessRecordComment"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="competition" domainObjectName="Competition"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="competition_extra" domainObjectName="CompetitionExtra"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--线索置顶-->
        <!--<table tableName="quotation" domainObjectName="Quotation"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="quotation_manager" domainObjectName="QuotationManager"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="resource" domainObjectName="Resource"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="resource_role" domainObjectName="ResourceRole"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="role" domainObjectName="Role"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="role_dept" domainObjectName="RoleDept"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="role_dept_user" domainObjectName="roleDeptUser"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="read_only" javaType="Integer"/>-->
        <!--</table>-->
        <!--<table tableName="business_role_dept_rel" domainObjectName="BusinessRoleDeptRel"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="product" domainObjectName="ProductDto"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="has_remove" javaType="Integer"/>-->
        <!--</table>-->
        <!--<table tableName="product_extra" domainObjectName="ProductExtra"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="product_label" domainObjectName="ProductLabel"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="product_module" domainObjectName="ProductModule"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="business_product" domainObjectName="BusinessProduct"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->

        <!--<table tableName="achievement_goal" domainObjectName="AchievementGoal"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->
        <!--<table tableName="member_achievement_goal" domainObjectName="MemberAchievementGoal"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->

        <!--<table tableName="attribution_unit" domainObjectName="AttributionUnit"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>
        <table tableName="labor_cost" domainObjectName="LaborCost"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>
        <table tableName="labor_cost_type" domainObjectName="LaborCostType"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>
        <table tableName="product" domainObjectName="Product"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>
        <table tableName="product_attribute" domainObjectName="ProductAttribute"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>

        <table tableName="tax_rate_rel" domainObjectName="TaxRateRel"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>
        <table tableName="configuration_expand" domainObjectName="ConfigurationExpand"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="is_unite" javaType="Boolean" />
            <columnOverride column="is_detail" javaType="Boolean" />
        </table>-->

<!--        <table tableName="receipt_claim" domainObjectName="ReceiptClaim"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Integer" />-->
<!--            <columnOverride column="divide_status" javaType="Integer" />-->
<!--            <columnOverride column="cash_status" javaType="Integer" />-->
<!--            <columnOverride column="source" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="ihr_attend_detail" domainObjectName="IhrAttendDetail"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        <columnOverride column="attend_week" javaType="Integer" />-->
<!--        <columnOverride column="attend_month" javaType="Integer" />-->
<!--        <columnOverride column="attend_year" javaType="Integer" />-->
<!--        </table>-->
        <!--<table tableName="receipt_claim_sale" domainObjectName="ReceiptClaimSale"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Integer" />-->
        <!--<columnOverride column="claim_status" javaType="Integer" />-->
        <!--<columnOverride column="business_type" javaType="Integer" />-->
        <!--</table>-->

<!--        <table tableName="working_hour_remind_white_list" domainObjectName="WorkingHourRemindWhiteList"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Integer" />-->
<!--        </table>-->

        <!--<table tableName="receipt_claim_non_sale" domainObjectName="ReceiptClaimNonSale"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Integer" />-->
        <!--<columnOverride column="claim_status" javaType="Integer" />-->
        <!--<columnOverride column="business_type" javaType="Integer" />-->
        <!--</table>-->

<!--        <table tableName="project_fee_collection" domainObjectName="ProjectFeeCollection"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_budget_change_push_ems" domainObjectName="ProjectBudgetChangePushEms"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!-- 注意 andCondition 方法被覆盖-->
<!--       <table tableName="project" domainObjectName="Project" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="project_source" javaType="Integer" />-->
<!--            <columnOverride column="preview_flag" javaType="Boolean" />-->
<!--            <columnOverride column="risk_status" javaType="Boolean" />-->
<!--            <columnOverride column="is_import" javaType="Boolean" />-->
<!--            <columnOverride column="confirm_record_flag" javaType="Boolean" />-->
<!--            <columnOverride column="is_objective_project" javaType="Integer" />-->
<!--            <columnOverride column="project_level" javaType="Integer" />-->
<!--            <columnOverride column="resource_status" javaType="Integer" />-->
<!--            <columnOverride column="ems_status" javaType="Integer" />-->
<!--            <columnOverride column="transfer_project_state" javaType="Integer" />-->
<!--            <columnOverride column="wbs_enabled" javaType="Boolean" />-->
<!--            <columnOverride column="reopen_status" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="project_budget_fee" domainObjectName="ProjectBudgetFee"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="preview_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="working_hour_accounting_detail_subject" domainObjectName="WorkingHourAccountingDetailSubject"
        enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="working_hour" domainObjectName="WorkingHour"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Integer"/>-->
<!--            <columnOverride column="is_import" javaType="Boolean"/>-->
<!--            <columnOverride column="rdm_flag" javaType="Integer"/>-->
<!--            <columnOverride column="source_flag" javaType="Integer"/>-->
<!--            <columnOverride column="status" javaType="Integer"/>-->
<!--            <columnOverride column="write_off_status" javaType="Integer"/>-->
<!--        </table>-->

<!--        <table tableName="project_budget_travel" domainObjectName="ProjectBudgetTravel"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="preview_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="cnaps_info" domainObjectName="CnapsInfo"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Integer" />-->
        <!--<columnOverride column="verify_mark" javaType="Integer" />-->
        <!--<columnOverride column="status" javaType="Integer" />-->
        <!--</table>-->


        <!--<table tableName="project_member" domainObjectName="ProjectMember"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--<columnOverride column="follow_project_flag" javaType="Boolean" />-->
        <!--<columnOverride column="transfer" javaType="Boolean" />-->
        <!--<columnOverride column="source" javaType="Boolean" />-->

        <!--</table>-->

<!--        <table tableName="project_milepost" domainObjectName="ProjectMilepost" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="order_num" javaType="Integer"/>-->
<!--            <columnOverride column="status" javaType="Integer"/>-->
<!--            <columnOverride column="help_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="income_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="pre_deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="transfer_project" javaType="Boolean"/>-->
<!--            <columnOverride column="fixed" javaType="Boolean"/>-->
<!--            <columnOverride column="carry_status" javaType="Boolean"/>-->
<!--            <columnOverride column="parallel_delivery_line_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_milepost_change_history" domainObjectName="ProjectMilepostChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="order_num" javaType="Integer"/>-->
<!--            <columnOverride column="change_order_num" javaType="Integer"/>-->
<!--            <columnOverride column="history_type" javaType="Integer"/>-->
<!--            <columnOverride column="status" javaType="Integer"/>-->
<!--            <columnOverride column="help_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="income_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="pre_deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="fixed" javaType="Boolean"/>-->
<!--            <columnOverride column="carry_status" javaType="Boolean"/>-->
<!--            <columnOverride column="transfer_project" javaType="Boolean"/>-->
<!--            <columnOverride column="parallel_delivery_line_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_type" domainObjectName="ProjectType" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="auto_milepost" javaType="Boolean" />-->
<!--            <columnOverride column="validate_budget_humans" javaType="Boolean" />-->
<!--            <columnOverride column="validate_budget_travels" javaType="Boolean" />-->
<!--            <columnOverride column="validate_budget_fees" javaType="Boolean" />-->
<!--            <columnOverride column="validate_budget_materials" javaType="Boolean" />-->
<!--            <columnOverride column="validate_contract" javaType="Boolean" />-->
<!--            <columnOverride column="validate_miplepost_help" javaType="Boolean" />-->
<!--            <columnOverride column="validate_miplepost_main" javaType="Boolean" />-->
<!--            <columnOverride column="validate_business" javaType="Boolean" />-->
<!--            <columnOverride column="validate_financal" javaType="Boolean" />-->
<!--            <columnOverride column="validate_year_budget" javaType="Boolean" />-->
<!--            <columnOverride column="validate_wf" javaType="Boolean" />-->
<!--            <columnOverride column="validate_amount" javaType="Boolean" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="locator_flag" javaType="Boolean" />-->
<!--            <columnOverride column="validate_preview" javaType="Boolean" />-->
<!--            <columnOverride column="validate_project_type" javaType="Boolean" />-->
<!--            <columnOverride column="validate_code" javaType="Boolean" />-->
<!--            <columnOverride column="validate_name" javaType="Boolean" />-->
<!--            <columnOverride column="validate_manager" javaType="Boolean" />-->
<!--            <columnOverride column="validate_price_type" javaType="Boolean" />-->
<!--            <columnOverride column="validate_customer" javaType="Boolean" />-->
<!--            <columnOverride column="validate_product" javaType="Boolean" />-->
<!--            <columnOverride column="validate_ou" javaType="Boolean" />-->
<!--            <columnOverride column="validate_summary" javaType="Boolean" />-->
<!--            <columnOverride column="validate_type" javaType="Boolean" />-->
<!--            <columnOverride column="main_income_flag" javaType="Boolean" />-->
<!--            <columnOverride column="help_income_flag" javaType="Boolean" />-->
<!--            <columnOverride column="milepost_design_can_submit" javaType="Boolean" />-->
<!--            <columnOverride column="milestone_base_date" javaType="Boolean" />-->
<!--            <columnOverride column="project_member_distinct" javaType="Boolean" />-->
<!--            <columnOverride column="validate_objective_project" javaType="Boolean" />-->
<!--            <columnOverride column="validate_project_level" javaType="Boolean" />-->
<!--            <columnOverride column="validate_resource_code" javaType="Boolean" />-->
<!--            <columnOverride column="validate_project_member" javaType="Boolean" />-->
<!--            <columnOverride column="budget_control_flag" javaType="Integer" />-->
<!--            <columnOverride column="validate_project_manual" javaType="Boolean" />-->
<!--            <columnOverride column="requriement_deliver_mrp" javaType="Boolean" />-->
<!--            <columnOverride column="transfer_project" javaType="Boolean" />-->
<!--            <columnOverride column="project_profit_contribution_rate" javaType="Boolean" />-->
<!--            <columnOverride column="wbs_enabled" javaType="Boolean" />-->
<!--            <columnOverride column="relate_asset" javaType="Boolean" />-->
<!--            <columnOverride column="project_progress_predict_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="project_profit_change_history" domainObjectName="ProjectProfitChangeHistory"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--<columnOverride column="follow_project_flag" javaType="Boolean" />-->
        <!--<columnOverride column="transfer" javaType="Boolean" />-->
        <!--<columnOverride column="source" javaType="Boolean" />-->
            <!--<columnOverride column="history_type" javaType="Integer" />-->


        <!--</table>-->
        <!--
                <table tableName="contract" domainObjectName="Contract"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="frame_flag" javaType="Boolean" />
                    <columnOverride column="status" javaType="Integer" />
                    <columnOverride column="classes" javaType="Integer" />
                    <columnOverride column="belong_area" javaType="Integer" />
                    <columnOverride column="public_or_private" javaType="Integer" />
                    <columnOverride column="seal_category" javaType="Integer" />
                    <columnOverride column="invoice_type" javaType="Integer" />
                    <columnOverride column="is_electronic_contract" javaType="Boolean" />
                    <columnOverride column="import_flag" javaType="Boolean" />
                    <columnOverride column="is_double_chapter_contract" javaType="Boolean" />
                    <columnOverride column="is_synchronize_legal_system_flag" javaType="Boolean" />
                    <columnOverride column="if_watermarking" javaType="Boolean" />
                    <columnOverride column="deleted_flag" javaType="Boolean"/>
                    <columnOverride column="whether_customer_confirm" javaType="Boolean"/>
                    <ignoreColumn column="legal_affairs_id" />
                </table>

                <table tableName="contract_his" domainObjectName="ContractHis"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="frame_flag" javaType="Boolean" />
                    <columnOverride column="operation_type" javaType="Integer" />
                    <columnOverride column="status" javaType="Integer" />
                    <columnOverride column="classes" javaType="Integer" />
                    <columnOverride column="belong_area" javaType="Integer" />
                    <columnOverride column="public_or_private" javaType="Integer" />
                    <columnOverride column="seal_category" javaType="Integer" />
                    <columnOverride column="invoice_type" javaType="Integer" />
                    <columnOverride column="is_electronic_contract" javaType="Boolean" />
                    <columnOverride column="is_synchronize_legal_system_flag" javaType="Boolean" />
                    <columnOverride column="if_watermarking" javaType="Boolean" />
                    <columnOverride column="deleted_flag" javaType="Boolean"/>
                    <columnOverride column="whether_customer_confirm" javaType="Boolean"/>
                </table>

                <table tableName="contract_origin" domainObjectName="ContractOrigin"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="frame_flag" javaType="Boolean" />
                    <columnOverride column="operation_type" javaType="Integer" />
                    <columnOverride column="status" javaType="Integer" />
                    <columnOverride column="classes" javaType="Integer" />
                    <columnOverride column="belong_area" javaType="Integer" />
                    <columnOverride column="public_or_private" javaType="Integer" />
                    <columnOverride column="seal_category" javaType="Integer" />
                    <columnOverride column="invoice_type" javaType="Integer" />
                    <columnOverride column="is_electronic_contract" javaType="Boolean" />
                    <columnOverride column="import_flag" javaType="Boolean" />
                    <columnOverride column="is_double_chapter_contract" javaType="Boolean" />
                    <columnOverride column="is_synchronize_legal_system_flag" javaType="Boolean" />
                    <columnOverride column="if_watermarking" javaType="Boolean" />
                    <columnOverride column="deleted_flag" javaType="Boolean"/>
                    <columnOverride column="whether_customer_confirm" javaType="Boolean"/>
                </table>
        -->
                <!--<table tableName="project_budget_material" domainObjectName="ProjectBudgetMaterial"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                    <columnOverride column="ext" javaType="Boolean" />
                </table>-->

<!--        <table tableName="document_library_new" domainObjectName="DocumentLibraryNew"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="folder_properties" javaType="Integer" />
            <columnOverride column="source" javaType="Integer" />
        </table>-->
<!--        <table tableName="file_list_info" domainObjectName="FileListInfo"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="ctc_attachment" domainObjectName="CtcAttachment" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

    <!--    <table tableName="material_estimation" domainObjectName="MaterialEstimation"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>
        <table tableName="milepost_design_plan" domainObjectName="MilepostDesignPlan"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->
<!--        <table tableName="milepost_design_plan_detail" domainObjectName="MilepostDesignPlanDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="import" javaType="Boolean"/>-->
<!--            <columnOverride column="generateRequirement" javaType="Boolean"/>-->
<!--            <columnOverride column="whetherModel" javaType="Boolean"/>-->
<!--            <columnOverride column="ext" javaType="Boolean"/>-->
<!--            <columnOverride column="generate_requirement" javaType="Boolean"/>-->
<!--            <columnOverride column="init" javaType="Boolean"/>-->
<!--            <columnOverride column="item_cost_is_null" javaType="Integer"/>-->
<!--            <columnOverride column="whether_model" javaType="Boolean"/>-->
<!--            <columnOverride column="wbs_confirm_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="wbs_last_layer" javaType="Boolean"/>-->
<!--            <columnOverride column="dispatch_is" javaType="Boolean"/>-->
<!--            <columnOverride column="ext_is" javaType="Boolean"/>-->
<!--        </table>-->
                <!--<table tableName="milepost_design_plan_detail_submit_history"
        domainObjectName="MilepostDesignPlanDetailSubmitHistory"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="whether_model" javaType="Boolean"/>
                    <columnOverride column="ext" javaType="Boolean"/>
                    <columnOverride column="generate_requirement" javaType="Boolean"/>
                    <columnOverride column="init" javaType="Boolean"/>
                    <columnOverride column="item_cost_is_null" javaType="Integer"/>
                    <columnOverride column="wbs_confirm_flag" javaType="Boolean"/>
                    <columnOverride column="dispatch_is" javaType="Boolean"/>
                    <columnOverride column="ext_is" javaType="Boolean"/>
                    <columnOverride column="history_type" javaType="Boolean"/>
                    <columnOverride column="deleted_flag" javaType="Boolean"/>
                    <columnOverride column="wbs_last_layer" javaType="Boolean"/>
                </table>-->

  <!--      <table tableName="milepost_design_plan_submit_record" domainObjectName="MilepostDesignPlanSubmitRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="purchase" javaType="Boolean" />
            <columnOverride column="project_submit" javaType="Boolean" />
            <columnOverride column="status" javaType="Integer" />
            <columnOverride column="materiel_status" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_profit" domainObjectName="ProjectProfit"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->

        <!--<table tableName="project_profit" domainObjectName="ProjectProfit"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->

        <!--<table tableName="project_budget_human" domainObjectName="ProjectBudgetHuman"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--<columnOverride column="type" javaType="Integer" />-->
        <!--</table>-->

        <!--<table tableName="project_storge_synchro" domainObjectName="ProjectStorgeSynchro"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--<columnOverride column="type" javaType="Integer" />-->
        <!--<columnOverride column="status" javaType="Integer" />-->
        <!--</table>-->


<!--        <table tableName="project_deliveries" domainObjectName="ProjectDeliveries" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="selected" javaType="Boolean" />-->
<!--            <columnOverride column="necessary" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="project_budget_synchro" domainObjectName="ProjectBudgetSynchro"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--<columnOverride column="status" javaType="Integer" />-->
        <!--<columnOverride column="type" javaType="Integer" />-->
        <!--</table>-->
        <!--<table tableName="receipt_claim_detail" domainObjectName="ReceiptClaimDetail"
        enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="business_type" javaType="Integer" />
        <columnOverride column="claim_status" javaType="Integer" />
        <columnOverride column="erp_status" javaType="Integer" />
        <columnOverride column="contract_sync_status" javaType="Integer" />
        <columnOverride column="write_off_status" javaType="Integer" />
        <columnOverride column="contract_status" javaType="Integer" />
            <columnOverride column="invoice_sync_status" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Integer" />
        </table>-->
<!--        <table tableName="receipt_claim_contract_rel" domainObjectName="ReceiptClaimContractRel" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Integer"/>-->
<!--        </table>-->
<!--        <table tableName="receipt_claim_invoice_rel" domainObjectName="ReceiptClaimInvoiceRel" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Integer"/>-->
<!--            <columnOverride column="erp_status" javaType="Integer"/>-->
<!--            <columnOverride column="invoice_wf_status" javaType="Integer"/>-->
<!--        </table>-->
        <!--<table tableName="contract" domainObjectName="Contract"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="status" javaType="Integer" />
            <columnOverride column="is_frame" javaType="Boolean" />
        </table>-->

       <!-- <table tableName="contract" domainObjectName="Contract"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="is_synchronize_legal_system" javaType="Boolean" />
            <columnOverride column="if_water_marking" javaType="Long" />
            <columnOverride column="no_watermarking_reason" javaType="Boolean" />
        </table>-->

        <!--<table tableName="invoice_plan" domainObjectName="InvoicePlan"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="invoice_plan_detail" domainObjectName="InvoicePlanDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="is_import" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="legal_contract" domainObjectName="LegalContract"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="receipt_plan" domainObjectName="ReceiptPlan"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="receipt_plan_detail" domainObjectName="ReceiptPlanDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="num" javaType="Integer" />-->
<!--        </table>-->

        <!--<table tableName="contract_product" domainObjectName="ContractProduct"
        enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>
-->
        <!--<table tableName="contract_product_cost" domainObjectName="ContractProductCost"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="payment_write_off_record" domainObjectName="PaymentWriteOffRecord"-->
<!--                enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="cancel_status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="payment_write_off_cancel_record" domainObjectName="PaymentWriteOffCancelRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="cancel_status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="receipt_invoice_relation" domainObjectName="ReceiptInvoiceRelation"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_contract_rs" domainObjectName="ProjectContractRs"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->

        <!--<table tableName="erp_code_rule" domainObjectName="ErpCodeRule"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="check_type" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="milepost_design_plan_confirm_record" domainObjectName="MilepostDesignPlanConfirmRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="project_submit" javaType="Boolean" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="deletedFlag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="milepost_design_plan_confirm_record_relation" domainObjectName="MilepostDesignPlanConfirmRecordRelation"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="milepost_design_plan_submit_record_relation" domainObjectName="MilepostDesignPlanSubmitRecordRelation"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->
        <!--<table tableName="legal_contract_history" domainObjectName="LegalContractHistory"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--采购订单-->
<!--        <table tableName="purchase_material_release_detail" domainObjectName="PurchaseMaterialReleaseDetail" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="release_detail_status" javaType="Integer" />-->
<!--        </table>-->

        <!--采购订单-->

<!--        <table tableName="material_get_header" domainObjectName="MaterialGetHeader"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Integer" />-->
<!--            <columnOverride column="is_import" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="material_get_detail" domainObjectName="MaterialGetDetail"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Integer" />-->
<!--            <columnOverride column="item_cost_is_null" javaType="Integer" />-->
<!--            <columnOverride column="is_import" javaType="Integer" />-->
<!--        </table>-->

        <!--<table tableName="storage" domainObjectName="Storage"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->
<!--        <table tableName="material_transfer_header" domainObjectName="MaterialTransferHeader"
        enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Integer" />
        <columnOverride column="workflow_flag" javaType="Integer" />
        <columnOverride column="erp_status" javaType="Integer" />
        </table>-->
      <!--  <table tableName="material_transfer_detail" domainObjectName="MaterialTransferDetail"
        enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Integer" />
            <columnOverride column="erp_status" javaType="Integer" />
        </table>-->
        <!--<table tableName="resend_execute" domainObjectName="ResendExecute"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Integer" />-->
        <!--</table>-->

        <!--<table tableName="interface_param" domainObjectName="InterfaceParam"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--<columnOverride column="sync" javaType="Boolean" />-->


<!--       <table tableName="material_return_header" domainObjectName="MaterialReturnHeader"-->
<!--              enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--           <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--           <columnOverride column="is_import" javaType="Integer" />-->
<!--      </table>-->

<!--        <table tableName="material_return_detail" domainObjectName="MaterialReturnDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="item_cost_is_null" javaType="Integer" />-->
<!--            <columnOverride column="is_import" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="purchase_contract" domainObjectName="PurchaseContract"-->
<!--                enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--             <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--             <columnOverride column="classes" javaType="Integer" />-->
<!--             <columnOverride column="belong_area" javaType="Integer" />-->
<!--             <columnOverride column="invoice_type" javaType="Integer" />-->
<!--             <columnOverride column="public_or_private" javaType="Integer" />-->
<!--             <columnOverride column="seal_category" javaType="Integer" />-->
<!--             <columnOverride column="is_electronic_contract" javaType="Boolean" />-->
<!--             <columnOverride column="if_watermarking" javaType="Boolean" />-->
<!--             <columnOverride column="carryover_flag" javaType="Boolean"/>-->
<!--         </table>-->

        <!--<table tableName="purchase_contract_detail" domainObjectName="PurchaseContractDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="purchase_contract_change_header" domainObjectName="PurchaseContractChangeHeader"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="change_type" javaType="Integer" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="seal_category" javaType="Integer" />-->
<!--            <columnOverride column="is_electronic_contract" javaType="Boolean" />-->
<!--            <columnOverride column="public_or_private" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="purchase_contract_change_history" domainObjectName="PurchaseContractChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="carryover_flag" javaType="Boolean" />-->
<!--            <columnOverride column="classes" javaType="Integer" />-->
<!--            <columnOverride column="belong_area" javaType="Integer" />-->
<!--            <columnOverride column="invoice_type" javaType="Integer" />-->
<!--            <columnOverride column="public_or_private" javaType="Integer" />-->
<!--            <columnOverride column="seal_category" javaType="Integer" />-->
<!--            <columnOverride column="is_electronic_contract" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="purchase_contract_detail_change_history" domainObjectName="PurchaseContractDetailChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="payment_plan_change_history" domainObjectName="PaymentPlanChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="advance_payment_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="payment_plan" domainObjectName="PaymentPlan"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="advance_payment_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="carryover_bill_process_schedule" domainObjectName="CarryoverBillProcessSchedule"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->

        <!--<table tableName="project_detail_sync" domainObjectName="ProjectDetailSync"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="sync_status" javaType="Integer" />
            <columnOverride column="source_type" javaType="Integer" />
        </table>-->
        <!--结转单-->
<!--        <table tableName="carryover_bill" domainObjectName="CarryoverBill"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="erpSyncStatus" javaType="Integer" />
            <columnOverride column="period_total_confirmation" javaType="Integer" />
            <columnOverride column="period_confirmed" javaType="Integer" />
            <columnOverride column="status" javaType="Integer" />
            <columnOverride column="source" javaType="Integer" />
            <columnOverride column="resource_type" javaType="Integer" />
            <columnOverride column="reverse_status" javaType="Integer" />
            <columnOverride column="seq" javaType="Integer" />
        </table>-->
        <!--

        <table tableName="carryover_bill_income_collection" domainObjectName="CarryoverBillIncomeCollection"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="carry_status" javaType="Boolean" />
        </table>-->
        <!--<table tableName="carryover_bill_cost_collection_rel" domainObjectName="CarryoverBillCostCollectionRel"
               enableUpdateByExample="false" enableDeleteByExample="false">
               <columnOverride column="type" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
       <!-- <table tableName="cost_collection" domainObjectName="CostCollection" enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="type" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="carry_status" javaType="Integer" />
            <columnOverride column="status" javaType="Integer" />
        </table>-->
        <!--<table tableName="material_actual_cost_detail" domainObjectName="MaterialActualCostDetail"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="trace_flag" javaType="Integer" />-->
        <!--</table>-->
        <!--<table tableName="purchase_progress" domainObjectName="PurchaseProgress"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--</table>-->
        <!--<table tableName="purchase_bpa_price" domainObjectName="PurchaseBpaPrice"
        enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->
        <!--<table tableName="purchase_bpa_price_record" domainObjectName="PurchaseBpaPriceRecord"
        enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->
        <!--<table tableName="fee_cost_detail" domainObjectName="FeeCostDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
        <!--<table tableName="labor_cost_detail" domainObjectName="LaborCostDetail"
            enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="status" javaType="Integer" />
            <columnOverride column="accounting_flag" javaType="Integer" />
        </table>-->
<!--        <table tableName="project_budget_cost" domainObjectName="ProjectBudgetCost"
            enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Boolean" />
        <columnOverride column="is_collection" javaType="Boolean" />
        </table>-->
    <!--    <table tableName="milepost_design_plan_change_record" domainObjectName="MilepostDesignPlanChangeRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
<!--          <table tableName="milepost_design_plan_detail_change" domainObjectName="MilepostDesignPlanDetailChange"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--              <columnOverride column="whether_model" javaType="Boolean" />-->
<!--              <columnOverride column="ext" javaType="Boolean" />-->
<!--              <columnOverride column="generate_requirement" javaType="Boolean" />-->
<!--              <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--              <columnOverride column="item_cost_is_null" javaType="Boolean" />-->
<!--              <columnOverride column="dispatch_is" javaType="Boolean" />-->
<!--              <columnOverride column="ext_is" javaType="Boolean" />-->
<!--              <columnOverride column="wbs_last_layer" javaType="Boolean" />-->
<!--        </table>-->
        <!--<table tableName="project_budget_material_change_history" domainObjectName="ProjectBudgetMaterialChangeHistory"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="ext" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_budget_material_change_summary_history" domainObjectName="ProjectBudgetMaterialChangeSummaryHistory"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="project_history_header" domainObjectName="ProjectHistoryHeader"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="change_type" javaType="Integer" />-->
<!--            <columnOverride column="budget_target_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_budget_human_change_history" domainObjectName="ProjectBudgetHumanChangeHistory"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_member_change_history" domainObjectName="ProjectMemberChangeHistory"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_budget_human_change_summary_history" domainObjectName="ProjectBudgetHumanChangeSummaryHistory"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="project_budget_travel_change_history" domainObjectName="ProjectBudgetTravelChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="preview_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="purchase_contract_penalty" domainObjectName="PurchaseContractPenalty"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="penalty_source" javaType="Integer" />-->
<!--        </table>-->

<!--            <table tableName="purchase_contract_penalty_change_history" domainObjectName="PurchaseContractPenaltyChangeHistory"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                <columnOverride column="penalty_source" javaType="Integer" />-->
<!--            </table>-->

        <!--<table tableName="project_budget_travel_change_summary_history" domainObjectName="ProjectBudgetTravelChangeSummaryHistory"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="project_budget_fee_change_history" domainObjectName="ProjectBudgetFeeChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="preview_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="project_budget_fee_change_summary_history" domainObjectName="ProjectBudgetFeeChangeSummaryHistory"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="project_budget_change_summary_history" domainObjectName="ProjectBudgetChangeSummaryHistory" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="revenue_cost_order" domainObjectName="RevenueCostOrder"
			enableUpdateByExample="false" enableDeleteByExample="false">
		    <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="status" javaType="Integer" />
            <columnOverride column="carryover_status" javaType="Boolean" />
		</table>-->
<!--        <table tableName="revenue_cost_order_detail" domainObjectName="RevenueCostOrderDetail"
			enableUpdateByExample="false" enableDeleteByExample="false">
		    <columnOverride column="deleted_flag" javaType="Boolean" />
		    <columnOverride column="is_import" javaType="Boolean" />
		</table>-->
<!--        <table tableName="working_hour_accounting" domainObjectName="WorkingHourAccounting"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="erp_status" javaType="Integer" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="write_off_status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->
 <!--       <table tableName="working_hour_accounting_detail" domainObjectName="WorkingHourAccountingDetail"
                       enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Boolean" />
    </table>
-->
<!--        <table tableName="labor_cost_detail_working_hour_accounting_res" domainObjectName="LaborCostDetailWorkingHourAccountingRes"
                      enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="accounting_flag" javaType="Integer" />
        </table>-->

<!--        <table tableName="invoice_apply_header" domainObjectName="InvoiceApplyHeader" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="merge_different_invoic" javaType="Boolean"/>-->
<!--            <columnOverride column="plan_invoice_type" javaType="Integer"/>-->
<!--            <columnOverride column="eam_status" javaType="Integer"/>-->
<!--        </table>-->

        <!-- <table tableName="invoice_apply_details" domainObjectName="InvoiceApplyDetails"
        enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--       <table tableName="invoice_receivable" domainObjectName="InvoiceReceivable"-->
<!--            enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--           <columnOverride column="deleted_flag" javaType="Integer" />-->
<!--           <columnOverride column="plan_invoice_type" javaType="Integer" />-->
<!--           <columnOverride column="source" javaType="Integer" />-->
<!--           <columnOverride column="due_date_status" javaType="Integer" />-->
<!--           <columnOverride column="send_mif_status" javaType="Integer" />-->
<!--       </table>-->

<!--               <table tableName="invoice_receivable_record" domainObjectName="InvoiceReceivableRecord"
                    enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Integer" />
                    <columnOverride column="plan_invoice_type" javaType="Integer" />
                </table>-->

        <!--<table tableName="async_request_result" domainObjectName="AsyncRequestResult"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>
        <table tableName="design_plan_detail_match_result" domainObjectName="DesignPlanDetailMatchResult"
               enableUpdateByExample="false" enableDeleteByExample="false">
        </table>-->


       <!-- <table tableName="vendor_asl" domainObjectName="VendorAslCtc"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>
-->
        <!--<table tableName="project_budget_change_push_ems" domainObjectName="ProjectBudgetChangePushEms"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="esb_result_return" domainObjectName="EsbResultReturn"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
       <!-- <table tableName="esb_result_return_record" domainObjectName="EsbResultReturnRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="material_outsourcing_contract_config" domainObjectName="MaterialOutsourcingContractConfig"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        </table>-->
<!--        <table tableName="cost_ratio_config" domainObjectName="CostRatioConfig"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        </table>-->
<!--        <table tableName="cost_ratio_config_detail" domainObjectName="CostRatioConfigDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        </table>-->

        <!-- 付款申请单 -->
        <!--<table tableName="payment_apply" domainObjectName="PaymentApply"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false" >-->
            <!--<columnOverride column="is_charge" javaType="Integer"/>-->
            <!--<columnOverride column="is_advance" javaType="Integer"/>-->
            <!--<columnOverride column="invoice_method" javaType="Integer"/>-->
            <!--<columnOverride column="deleted_flag" javaType="Integer" />-->
            <!--<columnOverride column="audit_status" javaType="Integer" />-->
            <!--<columnOverride column="write_off_status" javaType="Integer" />-->
        <!--</table>-->

        <!-- 实际付款记录 -->
       <!--<table tableName="payment_record" domainObjectName="PaymentRecord"
               enableUpdateByExample="false" enableDeleteByExample="false" >
            <columnOverride column="deleted_flag" javaType="Integer" />
        </table>-->

        <!-- 项目同步外围系统记录表 -->
<!--       <table tableName="project_synchro" domainObjectName="ProjectSynchro"
               enableUpdateByExample="false" enableDeleteByExample="false" >
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="status" javaType="Integer" />
            <columnOverride column="type" javaType="Integer" />
        </table>-->

        <!-- 付款申请发票头关联 -->
        <!--<table tableName="payment_apply_invoice_rel" domainObjectName="PaymentApplyInvoiceRel"
               enableUpdateByExample="false" enableDeleteByExample="false" >
            <columnOverride column="deleted_flag" javaType="Integer" />
        </table>-->

        <!-- 付款申请发票行关联 -->
        <!--<table tableName="payment_apply_detail_rel" domainObjectName="PaymentApplyDetailRel"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false" >-->
            <!--<columnOverride column="deleted_flag" javaType="Integer" />-->
        <!--</table>-->

        <!-- 发票头 -->
      <!--  <table tableName="payment_invoice" domainObjectName="PaymentInvoice"
               enableUpdateByExample="false" enableDeleteByExample="false" >
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="erp_status" javaType="Integer" />
            <columnOverride column="available" javaType="Integer" />
            <columnOverride column="source" javaType="Integer" />
            <columnOverride column="status" javaType="Integer" />
            <columnOverride column="erp_cancel_status" javaType="Integer" />
        </table>
-->
        <!-- 发票行 -->
<!--        <table tableName="payment_invoice_detail" domainObjectName="PaymentInvoiceDetail"
               enableUpdateByExample="false" enableDeleteByExample="false" >
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="is_manual_amendment" javaType="Boolean" />
        </table>-->

        <!-- 物料外包 -->
<!--        <table tableName="material_outsource_cost_detail" domainObjectName="MaterialOutsourceCostDetail"
              enableUpdateByExample="false" enableDeleteByExample="false" >
           <columnOverride column="deleted_flag" javaType="Integer" />
           <columnOverride column="type" javaType="Integer" />
       </table>-->


        <!-- 罚扣返利 -->
        <!--<table tableName="payment_penalty_profit" domainObjectName="PaymentPenaltyProfit"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false" >-->
            <!--<columnOverride column="deleted_flag" javaType="Integer" />-->
        <!--</table>-->


        <!-- 预付款核销记录 -->
        <!--<table tableName="payment_write_off_record" domainObjectName="PaymentWriteOffRecord"
               enableUpdateByExample="false" enableDeleteByExample="false" >
            <columnOverride column="deleted_flag" javaType="Integer" />
        </table>-->


        <!-- 预付款核销记录 -->
        <!--<table tableName="payment_write_off_record" domainObjectName="PaymentWriteOffRecord"
               enableUpdateByExample="false" enableDeleteByExample="false" >
            <columnOverride column="deleted_flag" javaType="Integer" />
        </table>-->

        <!-- 工单打印统计次数 -->
<!--        <table tableName="cloud_print_statistics" domainObjectName="CloudPrintStatistics"
               enableUpdateByExample="false" enableDeleteByExample="false" >
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!-- 结转单外包成本归集 -->
        <!--<table tableName="carryover_bill_outsourcing_cost_collection" domainObjectName="CarryoverBillOutsourcingCostCollection"
               enableUpdateByExample="false" enableDeleteByExample="false" >
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="carry_status" javaType="Integer" />
        </table>-->

        <!-- 工时分配表 -->
<!--        <table tableName="working_hour_distribute" domainObjectName="WorkingHourDistribute"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false" >-->
<!--            <columnOverride column="deleted_flag" javaType="Integer" />-->
<!--            <columnOverride column="is_open" javaType="Integer" />-->
<!--        </table>-->

        <!-- 工时分配明细表 -->
<!--        <table tableName="working_hour_distribute_detail" domainObjectName="WorkingHourDistributeDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false" >-->
<!--            <columnOverride column="deleted_flag" javaType="Integer" />-->
<!--        </table>-->

        <!--<table tableName="carryover_income_accounting" domainObjectName="CarryoverIncomeAccounting"-->
        <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--<columnOverride column="erp_status" javaType="Integer" />-->
        <!--<columnOverride column="type" javaType="Integer" />-->
        <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->
        <!--<table tableName="carryover_cost_accounting" domainObjectName="CarryoverCostAccounting"-->
            <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="erp_status" javaType="Integer" />-->
            <!--<columnOverride column="debit_credit" javaType="Integer" />-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->
        <!--<table tableName="project_income_cost_plan" domainObjectName="ProjectIncomeCostPlan"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="order_num" javaType="Integer" />
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                </table>-->

<!--        <table tableName="project_income_cost_plan_change_history" domainObjectName="ProjectIncomeCostPlanChangeHistory"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="order_num" javaType="Integer" />
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                    <columnOverride column="history_type" javaType="Integer" />
                </table>-->


       <!-- <table tableName="check_item" domainObjectName="CheckItem"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="carryover_check_flag" javaType="Boolean" />
        </table>
-->
        <!--


                        <table tableName="project_type_check_rel" domainObjectName="ProjectTypeCheckRel"
                               enableUpdateByExample="false" enableDeleteByExample="false">
                            <columnOverride column="yes_or_no" javaType="Boolean" />
                        </table>

                        <table tableName="delivery_inspection" domainObjectName="DeliveryInspection"
                               enableUpdateByExample="false" enableDeleteByExample="false">
                            <columnOverride column="yes_or_no" javaType="Boolean" />
                            <columnOverride column="status" javaType="Integer" />
                        </table>

                        <table tableName="delivery_inspection_details" domainObjectName="DeliveryInspectionDetails"
                               enableUpdateByExample="false" enableDeleteByExample="false">
                            <columnOverride column="yes_or_no" javaType="Boolean" />
                            <columnOverride column="status" javaType="Integer" />
                        </table>
         -->
        <!--<table tableName="difference_share_account" domainObjectName="DifferenceShareAccount"
                       enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="difference_share_account_detail" domainObjectName="DifferenceShareAccountDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
<!--        <table tableName="ems_pam_fee_detail" domainObjectName="EmsPamFeeDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="contract_changeway" domainObjectName="ContractChangeway"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="change_type" javaType="Integer" />
            <columnOverride column="approval_status" javaType="Integer" />
            <columnOverride column="change_reason_type_id" javaType="Integer" />
            <columnOverride column="public_or_private" javaType="Integer" />
            <columnOverride column="seal_category" javaType="Integer" />
            <columnOverride column="is_electronic_contract" javaType="Boolean" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->



        <!--<table tableName="contract_product_his" domainObjectName="ContractProductHis"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->

        <!--<table tableName="contract_product_cost_his" domainObjectName="ContractProductCostHis"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->

        <!--<table tableName="receipt_plan_his" domainObjectName="ReceiptPlanHis"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->

<!--        <table tableName="receipt_plan_detail_his" domainObjectName="ReceiptPlanDetailHis"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="invoice_plan_his" domainObjectName="InvoicePlanHis"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
            <!--<columnOverride column="type" javaType="Integer" />-->
        <!--</table>-->

<!--        <table tableName="invoice_plan_detail_his" domainObjectName="InvoicePlanDetailHis"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="is_import" javaType="Boolean" />-->
<!--            <columnOverride column="num" javaType="Integer" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--        </table>-->



       <!-- <table tableName="ds_purchase_price_difference_record" domainObjectName="PurchasePriceDifferenceRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="ds_material_update_difference_record" domainObjectName="MaterialUpdateDifferenceRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>
-->
        <!--<table tableName="difference_share_account_summary" domainObjectName="DifferenceShareAccountSummary"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->

<!--        <table tableName="receipt_invoice_relation_his" domainObjectName="ReceiptInvoiceRelationHis"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="ds_subject_balance" domainObjectName="SubjectBalance"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->


        <!--<table tableName="difference_share_project" domainObjectName="DifferenceShareProject"
        enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_profit_his" domainObjectName="ProjectProfitHis"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->

       <!-- <table tableName="ds_invoice_price_difference_record" domainObjectName="InvoicePriceDifferenceRecord"
              enableUpdateByExample="false" enableDeleteByExample="false">
           <columnOverride column="deleted_flag" javaType="Boolean" />
       </table>-->

        <!--<table tableName="difference_share_data_sync" domainObjectName="DifferenceShareDataSync"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="difference_share" domainObjectName="DifferenceShare"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="ds_subject_balance_record" domainObjectName="SubjectBalanceRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="difference_share_result_detail" domainObjectName="DifferenceShareResultDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="difference_share_result_detail" domainObjectName="DifferenceShareResultDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

       <!-- <table tableName="difference_share_data_sync_detail" domainObjectName="DifferenceShareDataSyncDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
        <!--<table tableName="PRE_PROJECT_ACTION" domainObjectName="PreProjectAction"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="resend_execute" domainObjectName="ResendExecute"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_cost_execute_record" domainObjectName="ProjectCostExecuteRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_cost_summary_record" domainObjectName="ProjectCostSummaryRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_cost_fee_summary_record" domainObjectName="ProjectCostFeeSummaryRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="project_cost_fee_detail_record" domainObjectName="ProjectCostFeeDetailRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="project_cost_revenue_order_record" domainObjectName="ProjectCostRevenueOrderRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_cost_purchase_order_record" domainObjectName="ProjectCostPurchaseOrderRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>

        <table tableName="project_cost_storage_record" domainObjectName="ProjectCostStorageRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>

        <table tableName="project_cost_getreturn_material_record" domainObjectName="ProjectCostGetreturnMaterialRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>

        <table tableName="project_cost_outsource_purchase_record" domainObjectName="ProjectCostOutsourcePurchaseRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>

        <table tableName="project_cost_difference_record" domainObjectName="ProjectCostDifferenceRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="project_change_history" domainObjectName="ProjectChangeHistory" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="is_import" javaType="Boolean" />-->
<!--            <columnOverride column="preview_flag" javaType="Boolean" />-->
<!--            <columnOverride column="risk_status" javaType="Boolean" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="project_source" javaType="Integer" />-->
<!--            <columnOverride column="confirm_record_flag" javaType="Boolean" />-->
<!--            <columnOverride column="is_objective_project" javaType="Integer" />-->
<!--            <columnOverride column="project_level" javaType="Integer" />-->
<!--            <columnOverride column="resource_status" javaType="Integer" />-->
<!--            <columnOverride column="ems_status" javaType="Integer" />-->
<!--            <columnOverride column="history_type" javaType="Integer" />-->
<!--            <columnOverride column="reopen_status" javaType="Integer" />-->
<!--            <columnOverride column="transfer_project_state" javaType="Integer" />-->
<!--            <columnOverride column="wbs_enabled" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="project_back_up" domainObjectName="ProjectBackUp" enableUpdateByExample="false"
               enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
            <columnOverride column="status" javaType="Integer"/>
            <columnOverride column="project_source" javaType="Integer"/>
            <columnOverride column="preview_flag" javaType="Boolean"/>
            <columnOverride column="risk_status" javaType="Boolean"/>
            <columnOverride column="is_import" javaType="Boolean"/>
            <columnOverride column="confirm_record_flag" javaType="Boolean"/>
            <columnOverride column="is_objective_project" javaType="Integer"/>
            <columnOverride column="project_level" javaType="Integer"/>
            <columnOverride column="resource_status" javaType="Integer"/>
        </table>-->

        <!--<table tableName="project_cost_fee_item_record" domainObjectName="ProjectCostFeeItemRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="project_cost_human_summary_record" domainObjectName="ProjectCostHumanSummaryRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_cost_human_detail_record" domainObjectName="ProjectCostHumanDetailRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="project_cost_human_item_record" domainObjectName="ProjectCostHumanItemRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="milepost_notice" domainObjectName="MilepostNotice"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--            <table tableName="eam_purchase_info" domainObjectName="EamPurchaseInfo"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                <columnOverride column="is_person" javaType="Boolean" />-->
<!--            </table>-->

<!--            <table tableName="eam_payment_apply_info" domainObjectName="EamPaymentApplyInfo"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                <columnOverride column="auditing" javaType="Integer" />-->
<!--                <columnOverride column="ems_sync_status" javaType="Integer" />-->
<!--                <columnOverride column="ems_biz_status" javaType="Integer" />-->
<!--            </table>-->

<!--            <table tableName="eam_purchase_adviser_info" domainObjectName="EamPurchaseAdviserInfo"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            </table>-->

<!--            <table tableName="eam_purchase_pay_info" domainObjectName="EamPurchasePayInfo"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                <columnOverride column="is_pay" javaType="Boolean" />-->
<!--            </table>-->

<!--                <table tableName="eam_purchase_device_info" domainObjectName="EamPurchaseDeviceInfo"-->
<!--                       enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                    <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                </table>-->

<!--            <table tableName="rdm_resource_plan" domainObjectName="RdmResourcePlan"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                <columnOverride column="pam_enabled" javaType="Boolean" />-->
<!--                <columnOverride column="people_days" javaType="Integer" />-->
<!--                <columnOverride column="is_person" javaType="Boolean" />-->
<!--            </table>-->

<!--                <table tableName="transition_rdm_resource_plan" domainObjectName="TransitionRdmResourcePlan"-->
<!--                       enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                    <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                    <columnOverride column="people_days" javaType="Integer" />-->
<!--                    <columnOverride column="is_person" javaType="Boolean" />-->
<!--                </table>-->

<!--            <table tableName="rdm_working_hour" domainObjectName="RdmWorkingHour"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            </table>-->

<!--            <table tableName="cost_collection_working_hour" domainObjectName="CostCollectionWorkingHour"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                <columnOverride column="resource_flag" javaType="Integer" />-->
<!--            </table>-->

<!--                <table tableName="transition_rdm_working_hour" domainObjectName="TransitionRdmWorkingHour"-->
<!--                       enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                    <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--                </table>-->

<!--            <table tableName="rdm_settlement_sheet" domainObjectName="RdmSettlementSheet"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            </table>-->

<!--            <table tableName="rdm_settlement_sheet_detail" domainObjectName="RdmSettlementSheetDetail"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                <columnOverride column="people_days" javaType="Integer" />-->
<!--            </table>-->
<!--        <table tableName="project_resource_rel" domainObjectName="ProjectResourceRel"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

            <!--<table tableName="contract_eampurchase_relation" domainObjectName="ContractEampurchaseRelation"-->
                   <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
                <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
            <!--</table>-->

        <!--<table tableName="carryover_bill_working_hour_rel" domainObjectName="CarryoverBillWorkingHourRel"
        enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="reverse_flag" javaType="Boolean" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="carry_status" javaType="Boolean" />
        </table>-->

<!--        <table tableName="working_hour_writeoff_laborcost_rel" domainObjectName="WorkingHourWriteoffLaborcostRel"
        enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->
<!--        <table tableName="working_hour_writeoff_budget_change_rel" domainObjectName="WorkingHourWriteoffBudgetChangeRel"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->
<!--        <table tableName="working_hour_cost_change_header" domainObjectName="WorkingHourCostChangeHeader"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="change_type" javaType="Integer" />-->
<!--            <columnOverride column="resource_flag" javaType="Integer" />-->
<!--        </table>-->
<!--        <table tableName="working_hour_cost_change_detail" domainObjectName="WorkingHourCostChangeDetail"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->
<!--
        <table tableName="project_termination_check_rel" domainObjectName="ProjectTerminationCheckRel"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="yes_or_no" javaType="Integer" />
        </table>

        <table tableName="termination_inspection" domainObjectName="TerminationInspection"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="status" javaType="Integer" />
        </table>

        <table tableName="termination_inspection_details" domainObjectName="TerminationInspectionDetails"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="yes_or_no" javaType="Boolean" />
            <columnOverride column="status" javaType="Integer" />
        </table>

        <table tableName="project_termination_type" domainObjectName="ProjectTerminationType" enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="delete_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="project_termination" domainObjectName="ProjectTermination"
        enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="status" javaType="Integer" />
        </table>-->

<!--        <table tableName="milepost_design_plan_purchase_record_relation" domainObjectName="MilepostDesignPlanPurchaseRecordRelation"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="milepost_design_plan_purchase_record" domainObjectName="MilepostDesignPlanPurchaseRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        </table>-->

        <!--<table tableName="budget_item" domainObjectName="BudgetItem"
               enableUpdateByExample="false" enableDeleteByExample="false">
               <columnOverride column="business_type" javaType="Integer" />
               <columnOverride column="deleted_flag" javaType="Integer" />
               <columnOverride column="status" javaType="Integer" />
        </table>-->

        <!--<table tableName="agency_syn_info" domainObjectName="AgencySynInfo" enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->

<!--        <table tableName="refund_apply" domainObjectName="RefundApply" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="refund_type" javaType="Integer" />-->
<!--            <columnOverride column="refund_apply_status" javaType="Integer" />-->
<!--            <columnOverride column="outer_status" javaType="Integer" />-->
<!--            <columnOverride column="return_original_way_is" javaType="Integer" />-->
<!--        </table>-->

        <!--<table tableName="refund_apply_detail" domainObjectName="RefundApplyDetail" enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->
        <!--        <table tableName="async_cost_collection_result" domainObjectName="AsyncCostCollectionResult" enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="apply_contract_unit" domainObjectName="ApplyContractUnit" enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="is_open" javaType="Integer" />
            <columnOverride column="status" javaType="Integer" />
        </table>-->

<!--        <table tableName="seal_administrator" domainObjectName="SealAdministrator" enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="project_award_department" domainObjectName="ProjectAwardDepartment" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--            <table tableName="project_initialize_info" domainObjectName="ProjectInitializeInfo" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            </table>-->

<!--        <table tableName="project_award_deduction" domainObjectName="ProjectAwardDeduction" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_award" domainObjectName="ProjectAward"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Integer" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="project_award_target_detail" domainObjectName="ProjectAwardTargetDetail" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="pause_flag" javaType="Boolean" />-->
<!--            <columnOverride column="objective_flag" javaType="Boolean" />-->
<!--            <columnOverride column="is_import" javaType="Boolean" />-->
<!--            <columnOverride column="project_status" javaType="Integer" />-->
<!--            <columnOverride column="award_status" javaType="Integer" />-->
<!--            <columnOverride column="budget_risk_rank" javaType="Integer" />-->
<!--            <columnOverride column="milepost_delay_day" javaType="Integer" />-->
<!--            <columnOverride column="milepost_delay_rank" javaType="Integer" />-->
<!--        </table>-->

<!--        <table tableName="contract_unit_seal_real" domainObjectName="ContractUnitSealReal" enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

                <!--<table tableName="receipt_claim_detail" domainObjectName="ReceiptClaimDetail" enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Integer" />
            <columnOverride column="business_type" javaType="Integer" />
            <columnOverride column="claim_status" javaType="Integer" />
            <columnOverride column="contract_status" javaType="Integer" />
            <columnOverride column="erp_status" javaType="Integer" />
            <columnOverride column="write_off_status" javaType="Integer" />
            <columnOverride column="invoice_sync_status" javaType="Integer" />
                </table>-->

<!--
        <table tableName="project_history_batch_header" domainObjectName="ProjectHistoryBatchHeader"
       enableUpdateByExample="false" enableDeleteByExample="false">
    <columnOverride column="deleted_flag" javaType="Boolean" />
    <columnOverride column="status" javaType="Integer" />
    </table>
-->

     <!--<table tableName="write_off_invoice_rel" domainObjectName="WriteOffInvoiceRel"
       enableUpdateByExample="false" enableDeleteByExample="false">
    <columnOverride column="deleted_flag" javaType="Integer" />
    <columnOverride column="invoice_wf_status" javaType="Integer" />
    <columnOverride column="erp_status" javaType="Integer" />
    </table>-->

<!--        <table tableName="business_apply_rel" domainObjectName="BusinessApplyRel"
               enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Integer" />
        </table>-->
<!--        <table tableName="eam_payment_apply_info" domainObjectName="EamPaymentApplyInfo"
          enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="auditing" javaType="Integer" />
            <columnOverride column="ems_sync_status" javaType="Integer" />
            <columnOverride column="ems_biz_status" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
       </table>-->

<!--        <table tableName="inner_swap_apply" domainObjectName="InnerSwapApply"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Integer"/>
            <columnOverride column="status" javaType="Integer"/>
        </table>-->

<!--        <table tableName="swap_apply_member" domainObjectName="SwapApplyMember"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Integer"/>
        </table>-->

<!--        <table tableName="swap_apply_product" domainObjectName="SwapApplyProduct"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Integer"/>
        </table>-->

<!--        <table tableName="swap_execute" domainObjectName="SwapExecute"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Integer"/>
            <columnOverride column="status" javaType="Integer"/>
        </table>

        <table tableName="swap_execute_member" domainObjectName="SwapExecuteMember"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Integer"/>
        </table>

        <table tableName="swap_execute_fee" domainObjectName="SwapExecuteFee"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Integer"/>
        </table>

        <table tableName="swap_execute_product" domainObjectName="SwapExecuteProduct"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Integer"/>
        </table>-->

<!--        <table tableName="project_award_target_execute_record" domainObjectName="ProjectAwardTargetExecuteRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--                <table tableName="project_budget_fee_back_up" domainObjectName="ProjectBudgetFeeBackUp"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                    <columnOverride column="preview_flag" javaType="Boolean" />
                </table>-->

<!--        <table tableName="project_budget_human_back_up" domainObjectName="ProjectBudgetHumanBackUp"
        enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Boolean" />
        <columnOverride column="type" javaType="Integer" />
        </table>-->

<!--        <table tableName="project_budget_material_back_up" domainObjectName="ProjectBudgetMaterialBackUp"
       enableUpdateByExample="false" enableDeleteByExample="false">
    <columnOverride column="deleted_flag" javaType="Boolean" />
    <columnOverride column="ext" javaType="Boolean" />
</table>-->

<!--                <table tableName="project_budget_travel_back_up" domainObjectName="ProjectBudgetTravelBackUp"
                enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                    <columnOverride column="preview_flag" javaType="Boolean" />
                </table>-->

<!--                <table tableName="project_member_back_up" domainObjectName="ProjectMemberBackUp"
       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean" />
                    <columnOverride column="follow_project_flag" javaType="Boolean" />
                    <columnOverride column="transfer" javaType="Boolean" />
                    <columnOverride column="source" javaType="Boolean" />
</table>-->

<!--        <table tableName="project_milepost_back_up" domainObjectName="ProjectMilepostBackUp"
    enableUpdateByExample="false" enableDeleteByExample="false">
    <columnOverride column="deleted_flag" javaType="Boolean" />
    <columnOverride column="order_num" javaType="Integer" />
    <columnOverride column="status" javaType="Integer" />
    <columnOverride column="help_flag" javaType="Boolean" />
    <columnOverride column="income_flag" javaType="Boolean" />
    <columnOverride column="pre_deleted_flag" javaType="Boolean" />
</table>-->

<!--        <table tableName="project_deliveries_back_up" domainObjectName="ProjectDeliveriesBackUp"
       enableUpdateByExample="false" enableDeleteByExample="false">
</table>-->

<!--        <table tableName="project_income_cost_plan_back_up" domainObjectName="ProjectIncomeCostPlanBackUp"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="order_num" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="project_profit_back_up" domainObjectName="ProjectProfitBackUp"
        enableUpdateByExample="false" enableDeleteByExample="false">
        <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="material_adjust_header" domainObjectName="MaterialAdjustHeader"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="adjust_type" javaType="Integer" />
            <columnOverride column="status" javaType="Integer" />
            <columnOverride column="resource" javaType="Integer" />
            <columnOverride column="sync_status" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="material_adjust_detail" domainObjectName="MaterialAdjustDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="sync_status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="material_change_header" domainObjectName="MaterialChangeHeader"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="adjust_type" javaType="Integer" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="resource" javaType="Integer" />-->
<!--            <columnOverride column="sync_status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="material_change_detail" domainObjectName="MaterialChangeDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="sync_status" javaType="Integer" />-->
<!--            <columnOverride column="delist_flag" javaType="Boolean" />-->
<!--            <columnOverride column="delist_flag_new" javaType="Boolean" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--        <table tableName="milepost_design_member" domainObjectName="MilepostDesignMember"
                enableUpdateByExample="false" enableDeleteByExample="false">
                <columnOverride column="deleted_flag" javaType="Boolean" />
                </table>-->

    <!--    <table tableName="ticket_tasks" domainObjectName="TicketTasks"
        enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="task_status" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>

        <table tableName="ticket_tasks_record" domainObjectName="TicketTasksRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="operation_type" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="ticket_working_hour_import" domainObjectName="TicketWorkingHourImport"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="status" javaType="Integer" />
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>

        <table tableName="ticket_working_hour_import_summary" domainObjectName="TicketWorkingHourImportSummary"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>

        <table tableName="ticket_working_hour_import_detail" domainObjectName="TicketWorkingHourImportDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--<table tableName="ticket_tasks_detail" domainObjectName="TicketTasksDetail"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean" />-->
        <!--</table>-->

<!--        <table tableName="project_contract_budget_material" domainObjectName="ProjectContractBudgetMaterial"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        <columnOverride column="status" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_problem_comment" domainObjectName="ProjectProblemComment"-->
<!--        enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--        <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_problem_operation_record" domainObjectName="ProjectProblemOperationRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--项目问题-->
<!--        <table tableName="project_problem" domainObjectName="ProjectProblem"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="once_solution_flag" javaType="Boolean" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="problem_describe" jdbcType="VARCHAR" />-->
<!--            <columnOverride column="solver_scheme" jdbcType="VARCHAR"/>-->
<!--        </table>-->
<!--
        <table tableName="cost_element" domainObjectName="CostElement"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

<!--        <table tableName="invoice_apply_receivable_ages" domainObjectName="InvoiceApplyReceivableAges"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

<!--        <table tableName="invoice_apply_detail_split" domainObjectName="InvoiceApplyDetailSplit" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

        <!--<table tableName="formal_material_get" domainObjectName="FormalMaterialGet"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

<!--      <table tableName="formal_material_get_and_return" domainObjectName="FormalMaterialGetAndReturn"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
            <columnOverride column="change_material_get_and_return" javaType="Boolean"/>
        </table>-->

        <!--
                <table tableName="temporary_material_get_and_return" domainObjectName="TemporaryMaterialGetAndReturn"
                       enableUpdateByExample="false" enableDeleteByExample="false">
                    <columnOverride column="deleted_flag" javaType="Boolean"/>
                </table>-->

<!--    <table tableName="milepost_design_plan_detail_middle" domainObjectName="MilepostDesignPlanDetailMiddle"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

<!--        <table tableName="purchase_material_close_detail" domainObjectName="PurchaseMaterialCloseDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
            <columnOverride column="close_type" javaType="Integer"/>
        </table>-->
<!--       <table tableName="contract_classification_record" domainObjectName="ContractClassificationRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->
<!--        <table tableName="contract_classification_head" domainObjectName="ContractClassificationHead"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->
<!--        <table tableName="contract_classification_detail" domainObjectName="ContractClassificationDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->
<!--        <table tableName="project_invoice_amount" domainObjectName="ProjectInvoiceAmount"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

<!--        <table tableName="project_budget_target" domainObjectName="ProjectBudgetTarget"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

<!--        <table tableName="project_budget_target_change_history" domainObjectName="ProjectBudgetTargetChangeHistory"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

<!--        <table tableName="milepost_template_stage" domainObjectName="MilepostTemplateStage"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="fixed" javaType="Boolean"/>-->
<!--            <columnOverride column="order_num" javaType="Integer"/>-->
<!--            <columnOverride column="income_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="transfer_project" javaType="Boolean"/>-->
<!--            <columnOverride column="final_acceptance" javaType="Boolean"/>-->
<!--            <columnOverride column="parallel_delivery_line_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_wbs_budget" domainObjectName="ProjectWbsBudget" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="fee_sync_ems" javaType="Boolean"/>-->
<!--        </table>-->
        <!--
        <table tableName="project_wbs_budget_dynamic" domainObjectName="ProjectWbsBudgetDynamic"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>
        -->

<!--        <table tableName="project_wbs_budget_change_history" domainObjectName="ProjectWbsBudgetChangeHistory" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="fee_sync_ems" javaType="Boolean"/>-->
<!--            <columnOverride column="show_case" javaType="Boolean"/>-->
<!--        </table>-->

        <!--
        <table tableName="project_wbs_budget_dynamic_change_history"
               domainObjectName="ProjectWbsBudgetDynamicChangeHistory"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>
        -->
        <!--
        <table tableName="milepost_template_stage_group" domainObjectName="MilepostTemplateStageGroup"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>
        -->

<!--        <table tableName="milepost_template_delivery_standards" domainObjectName="MilepostTemplateDeliveryStandards"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="need_state" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_milepost_group" domainObjectName="ProjectMilepostGroup" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="status" javaType="Integer"/>-->
<!--        </table>-->

<!--        <table tableName="project_milepost_delivery_standards" domainObjectName="ProjectMilepostDeliveryStandards"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="need_state" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_milepost_group_change_history" domainObjectName="ProjectMilepostGroupChangeHistory" enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="status" javaType="Integer"/>-->
<!--        </table>-->


        <!--<table tableName="project_contract_budget_material_change_history" domainObjectName="ProjectContractBudgetMaterialChangeHistory"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
            <columnOverride column="status" javaType="Boolean" />
        </table>-->

<!--        <table tableName="classification_carryover_bill" domainObjectName="ClassificationCarryoverBill"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="classification_invoice_receivable" domainObjectName="ClassificationInvoiceReceivable"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="classification_revenue_cost_order" domainObjectName="ClassificationRevenueCostOrder"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

        <!--
        <table tableName="wbs_template_info" domainObjectName="WbsTemplateInfo"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
            <columnOverride column="template_enabled" javaType="Boolean"/>
        </table>
        -->

        <!--
        <table tableName="wbs_customize_rule" domainObjectName="WbsCustomizeRule"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>
        -->

        <!--
        <table tableName="wbs_template_rule" domainObjectName="WbsTemplateRule"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
            <columnOverride column="strictly_control" javaType="Boolean"/>
        </table>
        -->

        <!--
        <table tableName="wbs_template_rule_detail" domainObjectName="WbsTemplateRuleDetail"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
            <columnOverride column="parent_state" javaType="Boolean"/>
        </table>
        -->

        <!--
        <table tableName="project_activity" domainObjectName="ProjectActivity"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
            <columnOverride column="parent_state" javaType="Boolean"/>
            <columnOverride column="budget_matters_state" javaType="Boolean"/>
        </table>
        -->

<!--        <table tableName="project_business_rs" domainObjectName="ProjectBusinessRs"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_business_rs_change_history" domainObjectName="ProjectBusinessRsChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_contract_rs_change_history" domainObjectName="ProjectContractRsChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="history_type" javaType="Integer"/>-->
<!--        </table>-->


<!--        <table tableName="project_baseline_batch" domainObjectName="ProjectBaselineBatch"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_baseline_batch_contract_rs" domainObjectName="ProjectBaselineBatchContractRs"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->


<!--        <table tableName="project_wbs_budget_baseline" domainObjectName="ProjectWbsBudgetBaseline"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_wbs_budget_baseline_change_history" domainObjectName="ProjectWbsBudgetBaselineChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_wbs_budget_summary" domainObjectName="ProjectWbsBudgetSummary"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="project_detail_select_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_wbs_budget_summary_change_history" domainObjectName="ProjectWbsBudgetSummaryChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="project_detail_select_flag" javaType="Boolean"/>-->
<!--        </table>-->

        <!--<table tableName="project_wbs_receipts" domainObjectName="ProjectWbsReceipts"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="project_submit" javaType="Boolean"/>-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean"/>-->
            <!--<columnOverride column="init" javaType="Boolean"/>-->
        <!--</table>-->

        <!--<table tableName="project_wbs_receipts_budget" domainObjectName="ProjectWbsReceiptsBudget" enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
            <columnOverride column="init" javaType="Boolean"/>
        </table>-->

        <!--<table tableName="project_wbs_receipts_budget_change_history"
               domainObjectName="ProjectWbsReceiptsBudgetChangeHistory" enableUpdateByExample="false"
               enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

        <!--<table tableName="project_wbs_receipts_design_plan_rel" domainObjectName="ProjectWbsReceiptsDesignPlanRel" enableUpdateByExample="false" enableDeleteByExample="false">-->
            <!--<columnOverride column="purchase" javaType="Boolean"/>-->
            <!--<columnOverride column="deleted_flag" javaType="Boolean"/>-->
            <!--<columnOverride column="init" javaType="Boolean"/>-->
        <!--</table>-->


    <!--        <table tableName="invoice_plan_email_record" domainObjectName="InvoicePlanEmailRecord"
                   enableUpdateByExample="false" enableDeleteByExample="false">
                <columnOverride column="deleted_flag" javaType="Boolean"/>
                <columnOverride column="email_type" javaType="Integer"/>
            </table>-->

<!--            <table tableName="carryover_bill_income_collection_detail" domainObjectName="CarryoverBillIncomeCollectionDetail"-->
<!--                   enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--                <columnOverride column="customer_type" javaType="Integer"/>-->
<!--            </table>-->

<!--        <table tableName="purchase_contract_progress" domainObjectName="PurchaseContractProgress"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="purchase_contract_quality_report" domainObjectName="PurchaseContractQualityReport"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="purchase_contract_budget" domainObjectName="PurchaseContractBudget"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="purchase_contract_progress_budget_rel" domainObjectName="PurchaseContractProgressBudgetRel"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="purchase_contract_budget_change_history" domainObjectName="PurchaseContractBudgetChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="customer_transfer" domainObjectName="CustomerTransfer"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="customer_transfer_reverse" domainObjectName="CustomerTransferReverse"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="customer_transfer_seq" domainObjectName="CustomerTransferSeq"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="customer_transfer_relation" domainObjectName="CustomerTransferRelation"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

            <!--<table tableName="supplier_quality_deactivation" domainObjectName="SupplierQualityDeactivation"-->
                   <!--enableUpdateByExample="false" enableDeleteByExample="false">-->
                <!--<columnOverride column="deleted_flag" javaType="Boolean"/>-->
            <!--</table>-->

<!--        <table tableName="purchase_contract_stamp" domainObjectName="PurchaseContractStamp"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="ctc_operating_record" domainObjectName="ctcOperatingRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="purchase_contract_progress_detail_subject" domainObjectName="PurchaseContractProgressDetailSubject"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_customer_satisfaction"-->
<!--               domainObjectName="ProjectCustomerSatisfaction"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_security_incident"-->
<!--               domainObjectName="ProjectSecurityIncident"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="requirement_code_record"-->
<!--               domainObjectName="RequirementCodeRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="transition_purchase_bpa_price"-->
<!--               domainObjectName="TransitionPurchaseBpaPrice"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="contract_origin" domainObjectName="ContractOrigin"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="contract_inventory" domainObjectName="ContractInventory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="contract_inventory_his" domainObjectName="ContractInventoryHis"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--            <columnOverride column="frame_flag" javaType="Boolean" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="classes" javaType="Integer" />-->
<!--            <columnOverride column="belong_area" javaType="Integer" />-->
<!--            <columnOverride column="public_or_private" javaType="Integer" />-->
<!--            <columnOverride column="seal_category" javaType="Integer" />-->
<!--            <columnOverride column="invoice_type" javaType="Integer" />-->
<!--            <columnOverride column="is_electronic_contract" javaType="Boolean" />-->
<!--            <columnOverride column="import_flag" javaType="Boolean" />-->
<!--            <columnOverride column="is_double_chapter_contract" javaType="Boolean" />-->
<!--            <columnOverride column="import_flag" javaType="Boolean" />-->
<!--            <columnOverride column="is_synchronize_legal_system_flag" javaType="Boolean" />-->
<!--            <columnOverride column="if_watermarking" javaType="Boolean" />-->
<!--        </table>-->

<!--                <table tableName="milepost_design_plan_detail_not_publish_requirement" domainObjectName="MilepostDesignPlanNotPublishRequirement"-->
<!--                       enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                    <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--                    <columnOverride column="wbs_enabled" javaType="Boolean"/>-->
<!--                </table>-->

<!--        <table tableName="payment_invoice_freeze_record" domainObjectName="PaymentInvoiceFreezeRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="busi_scene_non_sale" domainObjectName="BusiSceneNonSale"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="type" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="busi_scene_non_sale_detail" domainObjectName="BusiSceneNonSaleDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="is_sync_receipt" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="receipt_work_order" domainObjectName="ReceiptWorkOrder"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="receipt_work_order_accounting" domainObjectName="ReceiptWorkOrderAccounting"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="debit_credit" javaType="Integer" />-->
<!--            <columnOverride column="erp_status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="purchase_contract_template" domainObjectName="PurchaseContractTemplate"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="purchase_contract_template_config" domainObjectName="PurchaseContractTemplateConfig"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="vendor_proportion" domainObjectName="VendorProportionCtc"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

        <!--<table tableName="milepost_design_plan_detail_change_record" domainObjectName="MilepostDesignPlanDetailChangeRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

<!--        <table tableName="project_type_remind" domainObjectName="ProjectTypeRemind"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

        <!--<table tableName="project_wbs_receipts_requirement_change_record" domainObjectName="ProjectWbsReceiptsRequirementChangeRecord"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>

        <table tableName="project_wbs_receipts_requirement_change_record_relation" domainObjectName="ProjectWbsReceiptsRequirementChangeRecordRelation"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean"/>
        </table>-->

<!--        <table tableName="check_basic_info" domainObjectName="CheckBasicInfo"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="business_type" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="check_scene_info" domainObjectName="CheckSceneInfo"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="business_type" javaType="Integer" />-->
<!--            <columnOverride column="sub_business_type" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="check_scene_basic_rel" domainObjectName="CheckSceneBasicRel"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="check_level" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="check_result_header" domainObjectName="CheckResultHeader"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="business_type" javaType="Integer" />-->
<!--            <columnOverride column="sub_business_type" javaType="Integer" />-->
<!--            <columnOverride column="result" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="check_result_detail" domainObjectName="CheckResultDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="check_level" javaType="Integer" />-->
<!--            <columnOverride column="check_result" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="check_result_detail_excel" domainObjectName="CheckResultDetailExcel"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_contract_change_header_rel" domainObjectName="ProjectContractChangeHeaderRel"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="change_type" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="exchange_account_head" domainObjectName="ExchangeAccountHead"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="exchange_account_detail" domainObjectName="ExchangeAccountDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="exchange_account_subject" domainObjectName="ExchangeAccountSubject"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="exchange_account_deal_record" domainObjectName="ExchangeAccountDealRecord"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->


<!--        <table tableName="glegal_contract_change_header" domainObjectName="GlegalContractChangeHeader"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="business_type" javaType="Integer" />-->
<!--            <columnOverride column="approval_status" javaType="Integer" />-->
<!--            <columnOverride column="sync_legal_status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="glegal_contract_change_detail" domainObjectName="GlegalContractChangeDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="asset" domainObjectName="Asset"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="asset_deprn" domainObjectName="AssetDeprn"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_asset_rs" domainObjectName="ProjectAssetRs"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_asset_rs_change_history" domainObjectName="ProjectAssetRsChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_progress_predict" domainObjectName="ProjectProgressPredict"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_progress_predict_change_history" domainObjectName="ProjectProgressPredictChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="project_asset_deprn_cost_detail" domainObjectName="ProjectAssetDeprnCostDetail"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="accounting_flag" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="asset_deprn_accounting" domainObjectName="AssetDeprnAccounting"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="erp_status" javaType="Integer" />-->
<!--            <columnOverride column="status" javaType="Integer" />-->
<!--            <columnOverride column="write_off_status" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="asset_deprn_accounting_detail" domainObjectName="AssetDeprnAccountingDetail"-->
<!--                      enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="asset_deprn_accounting_detail_subject" domainObjectName="AssetDeprnAccountingDetailSubject"
               enableUpdateByExample="false" enableDeleteByExample="false">
            <columnOverride column="deleted_flag" javaType="Boolean" />
        </table>-->

<!--        <table tableName="project_budget_asset" domainObjectName="ProjectBudgetAsset"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="preview_flag" javaType="Boolean" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_budget_asset_change_history" domainObjectName="ProjectBudgetAssetChangeHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="preview_flag" javaType="Boolean" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="project_budget_asset_change_summary_history" domainObjectName="ProjectBudgetAssetChangeSummaryHistory"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--                <table tableName="project_wbs_change_receipt_detail" domainObjectName="ProjectWbsChangeReceiptDetail"-->
<!--                       enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                    <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                    <columnOverride column="fee_sync_ems" javaType="Boolean"/>-->
<!--                </table>-->

<!--                <table tableName="project_reopen_header" domainObjectName="ProjectReopenHeader"-->
<!--                       enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--                    <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--                </table>-->

<!--        <table tableName="purchase_material_requirement_freeze_log" domainObjectName="PurchaseMaterialRequirementFreezeLog"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="operation_type" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

<!--        <table tableName="purchase_material_requirement_purchase_type_change_log" domainObjectName="PurchaseMaterialRequirementPurchaseTypeChangeLog"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="operation_type" javaType="Integer" />-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean" />-->
<!--        </table>-->

        <!--<table tableName="standard_terms" domainObjectName="StandardTerms"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
        <!--        </table>-->

        <!--        <table tableName="standard_terms_content" domainObjectName="StandardTermsContent"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
        <!--        </table>-->


        <!--        <table tableName="purchase_order_standard_terms" domainObjectName="PurchaseOrderStandardTerms"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
        <!--        </table>-->

        <!--        <table tableName="purchase_order_standard_terms_content" domainObjectName="PurchaseOrderStandardTermsContent"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
        <!--        </table>-->

        <!--        <table tableName="standard_terms_deviation" domainObjectName="StandardTermsDeviation"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
        <!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
        <!--        </table>-->


<!--        <table tableName="purchase_contract_standard_terms" domainObjectName="PurchaseContractStandardTerms"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

<!--        <table tableName="purchase_contract_standard_terms_deviation" domainObjectName="PurchaseContractStandardTermsDeviation"-->
<!--               enableUpdateByExample="false" enableDeleteByExample="false">-->
<!--            <columnOverride column="deleted_flag" javaType="Boolean"/>-->
<!--        </table>-->

    </context>
</generatorConfiguration>