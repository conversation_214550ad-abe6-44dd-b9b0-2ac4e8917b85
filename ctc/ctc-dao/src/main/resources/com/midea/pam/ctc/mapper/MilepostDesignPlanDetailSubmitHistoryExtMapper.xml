<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.MilepostDesignPlanDetailSubmitHistoryExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_budget_material_id" jdbcType="BIGINT" property="projectBudgetMaterialId"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="num" jdbcType="VARCHAR" property="num"/>
        <result column="whether_model" jdbcType="TINYINT" property="whetherModel"/>
        <result column="module_status" jdbcType="INTEGER" property="moduleStatus"/>
        <result column="ext" jdbcType="TINYINT" property="ext"/>
        <result column="materiel_descr" jdbcType="VARCHAR" property="materielDescr"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="unit_code" jdbcType="VARCHAR" property="unitCode"/>
        <result column="number" jdbcType="DECIMAL" property="number"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="business_classification" jdbcType="VARCHAR" property="businessClassification"/>
        <result column="material_classification" jdbcType="VARCHAR" property="materialClassification"/>
        <result column="materiel_type" jdbcType="VARCHAR" property="materielType"/>
        <result column="machining_part_type" jdbcType="VARCHAR" property="machiningPartType"/>
        <result column="material" jdbcType="VARCHAR" property="material"/>
        <result column="unit_weight" jdbcType="DECIMAL" property="unitWeight"/>
        <result column="material_processing" jdbcType="VARCHAR" property="materialProcessing"/>
        <result column="budget_unit_price" jdbcType="DECIMAL" property="budgetUnitPrice"/>
        <result column="design_cost_id" jdbcType="BIGINT" property="designCostId"/>
        <result column="design_cost" jdbcType="DECIMAL" property="designCost"/>
        <result column="budget_subtotal" jdbcType="DECIMAL" property="budgetSubtotal"/>
        <result column="pam_code" jdbcType="VARCHAR" property="pamCode"/>
        <result column="erp_code" jdbcType="VARCHAR" property="erpCode"/>
        <result column="erp_code_source" jdbcType="INTEGER" property="erpCodeSource"/>
        <result column="materiel_status" jdbcType="INTEGER" property="materielStatus"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="generate_requirement" jdbcType="TINYINT" property="generateRequirement"/>
        <result column="init" jdbcType="TINYINT" property="init"/>
        <result column="item_cost_is_null" jdbcType="TINYINT" property="itemCostIsNull"/>
        <result column="material_category" jdbcType="VARCHAR" property="materialCategory"/>
        <result column="coding_middle_class" jdbcType="VARCHAR" property="codingMiddleClass"/>
        <result column="figure_number" jdbcType="VARCHAR" property="figureNumber"/>
        <result column="chart_version" jdbcType="VARCHAR" property="chartVersion"/>
        <result column="brand_material_code" jdbcType="VARCHAR" property="brandMaterialCode"/>
        <result column="or_spare_parts_mask" jdbcType="VARCHAR" property="orSparePartsMask"/>
        <result column="requirement_creat_date" jdbcType="DATE" property="requirementCreatDate"/>
        <result column="perchasing_leadtime" jdbcType="BIGINT" property="perchasingLeadtime"/>
        <result column="min_perchase_quantity" jdbcType="BIGINT" property="minPerchaseQuantity"/>
        <result column="min_package_quantity" jdbcType="BIGINT" property="minPackageQuantity"/>
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode"/>
        <result column="wbs_layer" jdbcType="VARCHAR" property="wbsLayer"/>
        <result column="wbs_confirm_flag" jdbcType="TINYINT" property="wbsConfirmFlag"/>
        <result column="dispatch_is" jdbcType="TINYINT" property="dispatchIs"/>
        <result column="ext_is" jdbcType="TINYINT" property="extIs"/>
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode"/>
        <result column="project_budget_type" jdbcType="VARCHAR" property="projectBudgetType"/>
        <result column="plan_designer" jdbcType="VARCHAR" property="planDesigner"/>
        <result column="design_release_lot_number" jdbcType="VARCHAR" property="designReleaseLotNumber"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="history_type" jdbcType="TINYINT" property="historyType"/>
        <result column="project_wbs_receipts_id" jdbcType="BIGINT" property="projectWbsReceiptsId"/>
        <result column="design_plan_detail_id" jdbcType="BIGINT" property="designPlanDetailId"/>
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, project_budget_material_id, project_id, parent_id, level, num, whether_model,
        module_status, ext, materiel_descr, unit, unit_code, number, delivery_time, name,
        model, brand, business_classification, material_classification, materiel_type, machining_part_type,
        material, unit_weight, material_processing, budget_unit_price, design_cost_id, design_cost,
        budget_subtotal, pam_code, erp_code, erp_code_source, materiel_status, source, remark,
        status, generate_requirement, init, item_cost_is_null, material_category, coding_middle_class,
        figure_number, chart_version, brand_material_code, or_spare_parts_mask, requirement_creat_date,
        perchasing_leadtime, min_perchase_quantity, min_package_quantity, wbs_summary_code,
        wbs_layer, wbs_confirm_flag, dispatch_is, ext_is, activity_code, project_budget_type,
        plan_designer, design_release_lot_number, description,
        history_type, project_wbs_receipts_id, design_plan_detail_id, deleted_flag, create_by,
        create_at, update_by, update_at
    </sql>

    <select id="selectByIds" resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailSubmitHistoryDto">
        select *
        from milepost_design_plan_detail_submit_history where deleted_flag != 1
        <if test="ids != null and ids.size>0">
            and id in
            <foreach collection="ids" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
    </select>

    <select id="selectRepeatPamWBSDeliveryTimeList" resultType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory">
        select
            mdpdsh.*
        from
            milepost_design_plan_detail_submit_history mdpdsh
            inner join pam_ctc.project_wbs_receipts pwr on
                pwr.id = mdpdsh.project_wbs_receipts_id
        where
            pwr.deleted_flag = 0
          and mdpdsh.deleted_flag = 0
          and pwr.requirement_status not in (4, 5)
          and mdpdsh.wbs_last_layer = 1
          and mdpdsh.project_id = #{projectId}
          and mdpdsh.wbs_summary_code = #{wbsSummaryCode}
    </select>

    <select id="selectByDetailIds" resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailSubmitHistoryDto">
        select *
        from milepost_design_plan_detail_submit_history where deleted_flag != 1
        <if test="ids != null and ids.size>0">
            and design_plan_detail_id in
            <foreach collection="ids" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
    </select>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" open=" " separator=" " close="">
            update milepost_design_plan_detail_submit_history
            <set>
                <if test="item.projectBudgetMaterialId != null">
                    project_budget_material_id = #{item.projectBudgetMaterialId,jdbcType=BIGINT},
                </if>
                <if test="item.projectId != null">
                    project_id = #{item.projectId,jdbcType=BIGINT},
                </if>
                <if test="item.parentId != null">
                    parent_id = #{item.parentId,jdbcType=BIGINT},
                </if>
                <if test="item.level != null">
                    level = #{item.level,jdbcType=INTEGER},
                </if>
                <if test="item.num != null">
                    num = #{item.num,jdbcType=VARCHAR},
                </if>
                <if test="item.whetherModel != null">
                    whether_model = #{item.whetherModel,jdbcType=TINYINT},
                </if>
                <if test="item.moduleStatus != null">
                    module_status = #{item.moduleStatus,jdbcType=INTEGER},
                </if>
                <if test="item.ext != null">
                    ext = #{item.ext,jdbcType=TINYINT},
                </if>
                <if test="item.materielDescr != null">
                    materiel_descr = #{item.materielDescr,jdbcType=VARCHAR},
                </if>
                <if test="item.unit != null">
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.unitCode != null">
                    unit_code = #{item.unitCode,jdbcType=VARCHAR},
                </if>
                <if test="item.number != null">
                    number = #{item.number,jdbcType=DECIMAL},
                </if>
                <if test="item.deliveryTime != null">
                    delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.name != null">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.model != null">
                    model = #{item.model,jdbcType=VARCHAR},
                </if>
                <if test="item.brand != null">
                    brand = #{item.brand,jdbcType=VARCHAR},
                </if>
                <if test="item.businessClassification != null">
                    business_classification = #{item.businessClassification,jdbcType=VARCHAR},
                </if>
                <if test="item.materialClassification != null">
                    material_classification = #{item.materialClassification,jdbcType=VARCHAR},
                </if>
                <if test="item.materielType != null">
                    materiel_type = #{item.materielType,jdbcType=VARCHAR},
                </if>
                <if test="item.machiningPartType != null">
                    machining_part_type = #{item.machiningPartType,jdbcType=VARCHAR},
                </if>
                <if test="item.material != null">
                    material = #{item.material,jdbcType=VARCHAR},
                </if>
                <if test="item.unitWeight != null">
                    unit_weight = #{item.unitWeight,jdbcType=DECIMAL},
                </if>
                <if test="item.materialProcessing != null">
                    material_processing = #{item.materialProcessing,jdbcType=VARCHAR},
                </if>
                <if test="item.budgetUnitPrice != null">
                    budget_unit_price = #{item.budgetUnitPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.designCostId != null">
                    design_cost_id = #{item.designCostId,jdbcType=BIGINT},
                </if>
                <if test="item.designCost != null">
                    design_cost = #{item.designCost,jdbcType=DECIMAL},
                </if>
                <if test="item.budgetSubtotal != null">
                    budget_subtotal = #{item.budgetSubtotal,jdbcType=DECIMAL},
                </if>
                <if test="item.pamCode != null">
                    pam_code = #{item.pamCode,jdbcType=VARCHAR},
                </if>
                <if test="item.erpCode != null">
                    erp_code = #{item.erpCode,jdbcType=VARCHAR},
                </if>
                <if test="item.erpCodeSource != null">
                    erp_code_source = #{item.erpCodeSource,jdbcType=INTEGER},
                </if>
                <if test="item.materielStatus != null">
                    materiel_status = #{item.materielStatus,jdbcType=INTEGER},
                </if>
                <if test="item.source != null">
                    source = #{item.source,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.generateRequirement != null">
                    generate_requirement = #{item.generateRequirement,jdbcType=TINYINT},
                </if>
                <if test="item.init != null">
                    init = #{item.init,jdbcType=TINYINT},
                </if>
                <if test="item.itemCostIsNull != null">
                    item_cost_is_null = #{item.itemCostIsNull,jdbcType=TINYINT},
                </if>
                <if test="item.materialCategory != null">
                    material_category = #{item.materialCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.codingMiddleClass != null">
                    coding_middle_class = #{item.codingMiddleClass,jdbcType=VARCHAR},
                </if>
                <if test="item.figureNumber != null">
                    figure_number = #{item.figureNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.chartVersion != null">
                    chart_version = #{item.chartVersion,jdbcType=VARCHAR},
                </if>
                <if test="item.brandMaterialCode != null">
                    brand_material_code = #{item.brandMaterialCode,jdbcType=VARCHAR},
                </if>
                <if test="item.orSparePartsMask != null">
                    or_spare_parts_mask = #{item.orSparePartsMask,jdbcType=VARCHAR},
                </if>
                <if test="item.requirementCreatDate != null">
                    requirement_creat_date = #{item.requirementCreatDate,jdbcType=DATE},
                </if>
                <if test="item.perchasingLeadtime != null">
                    perchasing_leadtime = #{item.perchasingLeadtime,jdbcType=BIGINT},
                </if>
                <if test="item.minPerchaseQuantity != null">
                    min_perchase_quantity = #{item.minPerchaseQuantity,jdbcType=BIGINT},
                </if>
                <if test="item.minPackageQuantity != null">
                    min_package_quantity = #{item.minPackageQuantity,jdbcType=BIGINT},
                </if>
                <if test="item.wbsSummaryCode != null">
                    wbs_summary_code = #{item.wbsSummaryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.wbsLayer != null">
                    wbs_layer = #{item.wbsLayer,jdbcType=VARCHAR},
                </if>
                <if test="item.wbsConfirmFlag != null">
                    wbs_confirm_flag = #{item.wbsConfirmFlag,jdbcType=TINYINT},
                </if>
                <if test="item.dispatchIs != null">
                    dispatch_is = #{item.dispatchIs,jdbcType=TINYINT},
                </if>
                <if test="item.extIs != null">
                    ext_is = #{item.extIs,jdbcType=TINYINT},
                </if>
                <if test="item.activityCode != null">
                    activity_code = #{item.activityCode,jdbcType=VARCHAR},
                </if>
                <if test="item.projectBudgetType != null">
                    project_budget_type = #{item.projectBudgetType,jdbcType=VARCHAR},
                </if>
                <if test="item.planDesigner != null">
                    plan_designer = #{item.planDesigner,jdbcType=VARCHAR},
                </if>
                <if test="item.designReleaseLotNumber != null">
                    design_release_lot_number = #{item.designReleaseLotNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.description != null">
                    description = #{item.description,jdbcType=VARCHAR},
                </if>
                <if test="item.historyType != null">
                    history_type = #{item.historyType,jdbcType=TINYINT},
                </if>
                <if test="item.projectWbsReceiptsId != null">
                    project_wbs_receipts_id = #{item.projectWbsReceiptsId,jdbcType=BIGINT},
                </if>
                <if test="item.designPlanDetailId != null">
                    design_plan_detail_id = #{item.designPlanDetailId,jdbcType=BIGINT},
                </if>
                <if test="item.deletedFlag != null">
                    deleted_flag = #{item.deletedFlag,jdbcType=TINYINT},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy,jdbcType=BIGINT},
                </if>
                <if test="item.createAt != null">
                    create_at = #{item.createAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy,jdbcType=BIGINT},
                </if>
                <if test="item.updateAt != null">
                    update_at = #{item.updateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.wbsLastLayer != null">
                    wbs_last_layer = #{item.wbsLastLayer,jdbcType=TINYINT},
                </if>
                <if test="item.requirementType != null">
                    requirement_type = #{item.requirementType,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT};
        </foreach>
    </update>
</mapper>