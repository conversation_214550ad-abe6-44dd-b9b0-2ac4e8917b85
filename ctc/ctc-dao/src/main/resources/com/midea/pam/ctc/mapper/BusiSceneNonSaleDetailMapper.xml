<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.BusiSceneNonSaleDetailMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.BusiSceneNonSaleDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="busi_scene_non_sale_id" jdbcType="BIGINT" property="busiSceneNonSaleId" />
    <result column="seq_code" jdbcType="BIGINT" property="seqCode" />
    <result column="module" jdbcType="VARCHAR" property="module" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="interface_code" jdbcType="VARCHAR" property="interfaceCode" />
    <result column="receivables_trx_id" jdbcType="BIGINT" property="receivablesTrxId" />
    <result column="receivables_trx_name" jdbcType="VARCHAR" property="receivablesTrxName" />
    <result column="cust_trx_type_id" jdbcType="BIGINT" property="custTrxTypeId" />
    <result column="cust_trx_type_id_name" jdbcType="VARCHAR" property="custTrxTypeIdName" />
    <result column="account_group_debit" jdbcType="VARCHAR" property="accountGroupDebit" />
    <result column="account_group_credit" jdbcType="VARCHAR" property="accountGroupCredit" />
    <result column="bank_account_id" jdbcType="BIGINT" property="bankAccountId" />
    <result column="bank_account_num" jdbcType="VARCHAR" property="bankAccountNum" />
    <result column="flag" jdbcType="VARCHAR" property="flag" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_sync_receipt" jdbcType="TINYINT" property="isSyncReceipt" />
    <result column="tax_rate_set" jdbcType="VARCHAR" property="taxRateSet" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, busi_scene_non_sale_id, seq_code, module, type, interface_code, receivables_trx_id, 
    receivables_trx_name, cust_trx_type_id, cust_trx_type_id_name, account_group_debit, 
    account_group_credit, bank_account_id, bank_account_num, flag, start_date, end_date, 
    remark, is_sync_receipt, tax_rate_set, create_by, create_at, update_by, update_at, 
    deleted_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.BusiSceneNonSaleDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from busi_scene_non_sale_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from busi_scene_non_sale_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from busi_scene_non_sale_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.BusiSceneNonSaleDetail">
    insert into busi_scene_non_sale_detail (id, busi_scene_non_sale_id, seq_code, 
      module, type, interface_code, 
      receivables_trx_id, receivables_trx_name, cust_trx_type_id, 
      cust_trx_type_id_name, account_group_debit, account_group_credit, 
      bank_account_id, bank_account_num, flag, 
      start_date, end_date, remark, 
      is_sync_receipt, tax_rate_set, create_by, 
      create_at, update_by, update_at, 
      deleted_flag)
    values (#{id,jdbcType=BIGINT}, #{busiSceneNonSaleId,jdbcType=BIGINT}, #{seqCode,jdbcType=BIGINT}, 
      #{module,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{interfaceCode,jdbcType=VARCHAR}, 
      #{receivablesTrxId,jdbcType=BIGINT}, #{receivablesTrxName,jdbcType=VARCHAR}, #{custTrxTypeId,jdbcType=BIGINT}, 
      #{custTrxTypeIdName,jdbcType=VARCHAR}, #{accountGroupDebit,jdbcType=VARCHAR}, #{accountGroupCredit,jdbcType=VARCHAR}, 
      #{bankAccountId,jdbcType=BIGINT}, #{bankAccountNum,jdbcType=VARCHAR}, #{flag,jdbcType=VARCHAR}, 
      #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{isSyncReceipt,jdbcType=TINYINT}, #{taxRateSet,jdbcType=VARCHAR}, #{createBy,jdbcType=BIGINT}, 
      #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, 
      #{deletedFlag,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.BusiSceneNonSaleDetail">
    insert into busi_scene_non_sale_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="busiSceneNonSaleId != null">
        busi_scene_non_sale_id,
      </if>
      <if test="seqCode != null">
        seq_code,
      </if>
      <if test="module != null">
        module,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="interfaceCode != null">
        interface_code,
      </if>
      <if test="receivablesTrxId != null">
        receivables_trx_id,
      </if>
      <if test="receivablesTrxName != null">
        receivables_trx_name,
      </if>
      <if test="custTrxTypeId != null">
        cust_trx_type_id,
      </if>
      <if test="custTrxTypeIdName != null">
        cust_trx_type_id_name,
      </if>
      <if test="accountGroupDebit != null">
        account_group_debit,
      </if>
      <if test="accountGroupCredit != null">
        account_group_credit,
      </if>
      <if test="bankAccountId != null">
        bank_account_id,
      </if>
      <if test="bankAccountNum != null">
        bank_account_num,
      </if>
      <if test="flag != null">
        flag,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isSyncReceipt != null">
        is_sync_receipt,
      </if>
      <if test="taxRateSet != null">
        tax_rate_set,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="busiSceneNonSaleId != null">
        #{busiSceneNonSaleId,jdbcType=BIGINT},
      </if>
      <if test="seqCode != null">
        #{seqCode,jdbcType=BIGINT},
      </if>
      <if test="module != null">
        #{module,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="interfaceCode != null">
        #{interfaceCode,jdbcType=VARCHAR},
      </if>
      <if test="receivablesTrxId != null">
        #{receivablesTrxId,jdbcType=BIGINT},
      </if>
      <if test="receivablesTrxName != null">
        #{receivablesTrxName,jdbcType=VARCHAR},
      </if>
      <if test="custTrxTypeId != null">
        #{custTrxTypeId,jdbcType=BIGINT},
      </if>
      <if test="custTrxTypeIdName != null">
        #{custTrxTypeIdName,jdbcType=VARCHAR},
      </if>
      <if test="accountGroupDebit != null">
        #{accountGroupDebit,jdbcType=VARCHAR},
      </if>
      <if test="accountGroupCredit != null">
        #{accountGroupCredit,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountId != null">
        #{bankAccountId,jdbcType=BIGINT},
      </if>
      <if test="bankAccountNum != null">
        #{bankAccountNum,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
        #{flag,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isSyncReceipt != null">
        #{isSyncReceipt,jdbcType=TINYINT},
      </if>
      <if test="taxRateSet != null">
        #{taxRateSet,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.BusiSceneNonSaleDetailExample" resultType="java.lang.Long">
    select count(*) from busi_scene_non_sale_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.BusiSceneNonSaleDetail">
    update busi_scene_non_sale_detail
    <set>
      <if test="busiSceneNonSaleId != null">
        busi_scene_non_sale_id = #{busiSceneNonSaleId,jdbcType=BIGINT},
      </if>
      <if test="seqCode != null">
        seq_code = #{seqCode,jdbcType=BIGINT},
      </if>
      <if test="module != null">
        module = #{module,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="interfaceCode != null">
        interface_code = #{interfaceCode,jdbcType=VARCHAR},
      </if>
      <if test="receivablesTrxId != null">
        receivables_trx_id = #{receivablesTrxId,jdbcType=BIGINT},
      </if>
      <if test="receivablesTrxName != null">
        receivables_trx_name = #{receivablesTrxName,jdbcType=VARCHAR},
      </if>
      <if test="custTrxTypeId != null">
        cust_trx_type_id = #{custTrxTypeId,jdbcType=BIGINT},
      </if>
      <if test="custTrxTypeIdName != null">
        cust_trx_type_id_name = #{custTrxTypeIdName,jdbcType=VARCHAR},
      </if>
      <if test="accountGroupDebit != null">
        account_group_debit = #{accountGroupDebit,jdbcType=VARCHAR},
      </if>
      <if test="accountGroupCredit != null">
        account_group_credit = #{accountGroupCredit,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountId != null">
        bank_account_id = #{bankAccountId,jdbcType=BIGINT},
      </if>
      <if test="bankAccountNum != null">
        bank_account_num = #{bankAccountNum,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
        flag = #{flag,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isSyncReceipt != null">
        is_sync_receipt = #{isSyncReceipt,jdbcType=TINYINT},
      </if>
      <if test="taxRateSet != null">
        tax_rate_set = #{taxRateSet,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.BusiSceneNonSaleDetail">
    update busi_scene_non_sale_detail
    set busi_scene_non_sale_id = #{busiSceneNonSaleId,jdbcType=BIGINT},
      seq_code = #{seqCode,jdbcType=BIGINT},
      module = #{module,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      interface_code = #{interfaceCode,jdbcType=VARCHAR},
      receivables_trx_id = #{receivablesTrxId,jdbcType=BIGINT},
      receivables_trx_name = #{receivablesTrxName,jdbcType=VARCHAR},
      cust_trx_type_id = #{custTrxTypeId,jdbcType=BIGINT},
      cust_trx_type_id_name = #{custTrxTypeIdName,jdbcType=VARCHAR},
      account_group_debit = #{accountGroupDebit,jdbcType=VARCHAR},
      account_group_credit = #{accountGroupCredit,jdbcType=VARCHAR},
      bank_account_id = #{bankAccountId,jdbcType=BIGINT},
      bank_account_num = #{bankAccountNum,jdbcType=VARCHAR},
      flag = #{flag,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      is_sync_receipt = #{isSyncReceipt,jdbcType=TINYINT},
      tax_rate_set = #{taxRateSet,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>