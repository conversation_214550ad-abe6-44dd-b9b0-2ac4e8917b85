<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PurchaseOrderMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.PurchaseOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="vendor_asl_id" jdbcType="BIGINT" property="vendorAslId" />
    <result column="vendor_name" jdbcType="VARCHAR" property="vendorName" />
    <result column="vendor_site_code" jdbcType="VARCHAR" property="vendorSiteCode" />
    <result column="buyer_id" jdbcType="BIGINT" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="sync_status" jdbcType="INTEGER" property="syncStatus" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="erp_message" jdbcType="VARCHAR" property="erpMessage" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="conversion_type" jdbcType="VARCHAR" property="conversionType" />
    <result column="conversion_date" jdbcType="TIMESTAMP" property="conversionDate" />
    <result column="conversion_rate" jdbcType="DECIMAL" property="conversionRate" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="erp_order_status" jdbcType="INTEGER" property="erpOrderStatus" />
    <result column="dispatch_is" jdbcType="BIGINT" property="dispatchIs" />
    <result column="receipts_id" jdbcType="BIGINT" property="receiptsId" />
    <result column="promised_date" jdbcType="TIMESTAMP" property="promisedDate" />
    <result column="tracking_date" jdbcType="TIMESTAMP" property="trackingDate" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="vendor_num" jdbcType="VARCHAR" property="vendorNum" />
    <result column="payment_method_id" jdbcType="BIGINT" property="paymentMethodId" />
    <result column="payment_method_name" jdbcType="VARCHAR" property="paymentMethodName" />
    <result column="payment_way" jdbcType="VARCHAR" property="paymentWay" />
    <result column="delivery_type" jdbcType="VARCHAR" property="deliveryType" />
    <result column="delivery_clause" jdbcType="VARCHAR" property="deliveryClause" />
    <result column="tax_id" jdbcType="BIGINT" property="taxId" />
    <result column="tax_rate" jdbcType="VARCHAR" property="taxRate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="pricing_type" jdbcType="TINYINT" property="pricingType" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="exchange_rate" jdbcType="DECIMAL" property="exchangeRate" />
    <result column="approve_info" jdbcType="VARCHAR" property="approveInfo" />
    <result column="erp_buyer_id" jdbcType="BIGINT" property="erpBuyerId" />
    <result column="init_flag" jdbcType="INTEGER" property="initFlag" />
    <result column="ou_id" jdbcType="BIGINT" property="ouId" />
    <result column="secondary_inventory" jdbcType="VARCHAR" property="secondaryInventory" />
    <result column="secondary_inventory_name" jdbcType="VARCHAR" property="secondaryInventoryName" />
    <result column="approval_time" jdbcType="TIMESTAMP" property="approvalTime" />
    <result column="sync_source_system" jdbcType="VARCHAR" property="syncSourceSystem" />
    <result column="contract_terms_flg" jdbcType="VARCHAR" property="contractTermsFlg" />
    <result column="sync_to_fap" jdbcType="TINYINT" property="syncToFap" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.midea.pam.common.ctc.entity.PurchaseOrder">
    <result column="contract_terms" jdbcType="LONGVARCHAR" property="contractTerms" />
    <result column="over_budget_des" jdbcType="LONGVARCHAR" property="overBudgetDes" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, num, project_id, vendor_asl_id, vendor_name, vendor_site_code, buyer_id, buyer_name, 
    order_status, sync_status, deleted_flag, erp_message, create_by, create_at, update_by, 
    update_at, currency, conversion_type, conversion_date, conversion_rate, source, status, 
    erp_order_status, dispatch_is, receipts_id, promised_date, tracking_date, org_id, 
    org_name, vendor_num, payment_method_id, payment_method_name, payment_way, delivery_type, 
    delivery_clause, tax_id, tax_rate, remark, pricing_type, currency_code, exchange_rate, 
    approve_info, erp_buyer_id, init_flag, ou_id, secondary_inventory, secondary_inventory_name, 
    approval_time, sync_source_system, contract_terms_flg, sync_to_fap
  </sql>
  <sql id="Blob_Column_List">
    contract_terms, over_budget_des
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrderExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from purchase_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrder">
    insert into purchase_order (id, num, project_id, 
      vendor_asl_id, vendor_name, vendor_site_code, 
      buyer_id, buyer_name, order_status, 
      sync_status, deleted_flag, erp_message, 
      create_by, create_at, update_by, 
      update_at, currency, conversion_type, 
      conversion_date, conversion_rate, source, 
      status, erp_order_status, dispatch_is, 
      receipts_id, promised_date, tracking_date, 
      org_id, org_name, vendor_num, 
      payment_method_id, payment_method_name, payment_way, 
      delivery_type, delivery_clause, tax_id, 
      tax_rate, remark, pricing_type, 
      currency_code, exchange_rate, approve_info, 
      erp_buyer_id, init_flag, ou_id, 
      secondary_inventory, secondary_inventory_name, 
      approval_time, sync_source_system, contract_terms_flg,
      sync_to_fap, contract_terms, over_budget_des
      )
    values (#{id,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, #{projectId,jdbcType=BIGINT}, 
      #{vendorAslId,jdbcType=BIGINT}, #{vendorName,jdbcType=VARCHAR}, #{vendorSiteCode,jdbcType=VARCHAR}, 
      #{buyerId,jdbcType=BIGINT}, #{buyerName,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER}, 
      #{syncStatus,jdbcType=INTEGER}, #{deletedFlag,jdbcType=TINYINT}, #{erpMessage,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{updateAt,jdbcType=TIMESTAMP}, #{currency,jdbcType=VARCHAR}, #{conversionType,jdbcType=VARCHAR}, 
      #{conversionDate,jdbcType=TIMESTAMP}, #{conversionRate,jdbcType=DECIMAL}, #{source,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{erpOrderStatus,jdbcType=INTEGER}, #{dispatchIs,jdbcType=BIGINT}, 
      #{receiptsId,jdbcType=BIGINT}, #{promisedDate,jdbcType=TIMESTAMP}, #{trackingDate,jdbcType=TIMESTAMP}, 
      #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, #{vendorNum,jdbcType=VARCHAR}, 
      #{paymentMethodId,jdbcType=BIGINT}, #{paymentMethodName,jdbcType=VARCHAR}, #{paymentWay,jdbcType=VARCHAR}, 
      #{deliveryType,jdbcType=VARCHAR}, #{deliveryClause,jdbcType=VARCHAR}, #{taxId,jdbcType=BIGINT}, 
      #{taxRate,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{pricingType,jdbcType=TINYINT}, 
      #{currencyCode,jdbcType=VARCHAR}, #{exchangeRate,jdbcType=DECIMAL}, #{approveInfo,jdbcType=VARCHAR}, 
      #{erpBuyerId,jdbcType=BIGINT}, #{initFlag,jdbcType=INTEGER}, #{ouId,jdbcType=BIGINT}, 
      #{secondaryInventory,jdbcType=VARCHAR}, #{secondaryInventoryName,jdbcType=VARCHAR}, 
      #{approvalTime,jdbcType=TIMESTAMP}, #{syncSourceSystem,jdbcType=VARCHAR}, #{contractTermsFlg,jdbcType=VARCHAR},
      #{syncToFap,jdbcType=TINYINT}, #{contractTerms,jdbcType=LONGVARCHAR}, #{overBudgetDes,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrder">
    insert into purchase_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="vendorAslId != null">
        vendor_asl_id,
      </if>
      <if test="vendorName != null">
        vendor_name,
      </if>
      <if test="vendorSiteCode != null">
        vendor_site_code,
      </if>
      <if test="buyerId != null">
        buyer_id,
      </if>
      <if test="buyerName != null">
        buyer_name,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="syncStatus != null">
        sync_status,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="erpMessage != null">
        erp_message,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="conversionType != null">
        conversion_type,
      </if>
      <if test="conversionDate != null">
        conversion_date,
      </if>
      <if test="conversionRate != null">
        conversion_rate,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="erpOrderStatus != null">
        erp_order_status,
      </if>
      <if test="dispatchIs != null">
        dispatch_is,
      </if>
      <if test="receiptsId != null">
        receipts_id,
      </if>
      <if test="promisedDate != null">
        promised_date,
      </if>
      <if test="trackingDate != null">
        tracking_date,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="vendorNum != null">
        vendor_num,
      </if>
      <if test="paymentMethodId != null">
        payment_method_id,
      </if>
      <if test="paymentMethodName != null">
        payment_method_name,
      </if>
      <if test="paymentWay != null">
        payment_way,
      </if>
      <if test="deliveryType != null">
        delivery_type,
      </if>
      <if test="deliveryClause != null">
        delivery_clause,
      </if>
      <if test="taxId != null">
        tax_id,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="pricingType != null">
        pricing_type,
      </if>
      <if test="currencyCode != null">
        currency_code,
      </if>
      <if test="exchangeRate != null">
        exchange_rate,
      </if>
      <if test="approveInfo != null">
        approve_info,
      </if>
      <if test="erpBuyerId != null">
        erp_buyer_id,
      </if>
      <if test="initFlag != null">
        init_flag,
      </if>
      <if test="ouId != null">
        ou_id,
      </if>
      <if test="secondaryInventory != null">
        secondary_inventory,
      </if>
      <if test="secondaryInventoryName != null">
        secondary_inventory_name,
      </if>
      <if test="approvalTime != null">
        approval_time,
      </if>
      <if test="syncSourceSystem != null">
        sync_source_system,
      </if>
      <if test="contractTermsFlg != null">
        contract_terms_flg,
      </if>
      <if test="syncToFap != null">
        sync_to_fap,
      </if>
      <if test="contractTerms != null">
        contract_terms,
      </if>
      <if test="overBudgetDes != null">
        over_budget_des,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="vendorAslId != null">
        #{vendorAslId,jdbcType=BIGINT},
      </if>
      <if test="vendorName != null">
        #{vendorName,jdbcType=VARCHAR},
      </if>
      <if test="vendorSiteCode != null">
        #{vendorSiteCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerId != null">
        #{buyerId,jdbcType=BIGINT},
      </if>
      <if test="buyerName != null">
        #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null">
        #{syncStatus,jdbcType=INTEGER},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="erpMessage != null">
        #{erpMessage,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="conversionType != null">
        #{conversionType,jdbcType=VARCHAR},
      </if>
      <if test="conversionDate != null">
        #{conversionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="conversionRate != null">
        #{conversionRate,jdbcType=DECIMAL},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="erpOrderStatus != null">
        #{erpOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="dispatchIs != null">
        #{dispatchIs,jdbcType=BIGINT},
      </if>
      <if test="receiptsId != null">
        #{receiptsId,jdbcType=BIGINT},
      </if>
      <if test="promisedDate != null">
        #{promisedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="trackingDate != null">
        #{trackingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="vendorNum != null">
        #{vendorNum,jdbcType=VARCHAR},
      </if>
      <if test="paymentMethodId != null">
        #{paymentMethodId,jdbcType=BIGINT},
      </if>
      <if test="paymentMethodName != null">
        #{paymentMethodName,jdbcType=VARCHAR},
      </if>
      <if test="paymentWay != null">
        #{paymentWay,jdbcType=VARCHAR},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=VARCHAR},
      </if>
      <if test="deliveryClause != null">
        #{deliveryClause,jdbcType=VARCHAR},
      </if>
      <if test="taxId != null">
        #{taxId,jdbcType=BIGINT},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="pricingType != null">
        #{pricingType,jdbcType=TINYINT},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="exchangeRate != null">
        #{exchangeRate,jdbcType=DECIMAL},
      </if>
      <if test="approveInfo != null">
        #{approveInfo,jdbcType=VARCHAR},
      </if>
      <if test="erpBuyerId != null">
        #{erpBuyerId,jdbcType=BIGINT},
      </if>
      <if test="initFlag != null">
        #{initFlag,jdbcType=INTEGER},
      </if>
      <if test="ouId != null">
        #{ouId,jdbcType=BIGINT},
      </if>
      <if test="secondaryInventory != null">
        #{secondaryInventory,jdbcType=VARCHAR},
      </if>
      <if test="secondaryInventoryName != null">
        #{secondaryInventoryName,jdbcType=VARCHAR},
      </if>
      <if test="approvalTime != null">
        #{approvalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncSourceSystem != null">
        #{syncSourceSystem,jdbcType=VARCHAR},
      </if>
      <if test="contractTermsFlg != null">
        #{contractTermsFlg,jdbcType=VARCHAR},
      </if>
      <if test="syncToFap != null">
        #{syncToFap,jdbcType=TINYINT},
      </if>
      <if test="contractTerms != null">
        #{contractTerms,jdbcType=LONGVARCHAR},
      </if>
      <if test="overBudgetDes != null">
        #{overBudgetDes,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrderExample" resultType="java.lang.Long">
    select count(*) from purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrder">
    update purchase_order
    <set>
      <if test="num != null">
        num = #{num,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="vendorAslId != null">
        vendor_asl_id = #{vendorAslId,jdbcType=BIGINT},
      </if>
      <if test="vendorName != null">
        vendor_name = #{vendorName,jdbcType=VARCHAR},
      </if>
      <if test="vendorSiteCode != null">
        vendor_site_code = #{vendorSiteCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerId != null">
        buyer_id = #{buyerId,jdbcType=BIGINT},
      </if>
      <if test="buyerName != null">
        buyer_name = #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null">
        sync_status = #{syncStatus,jdbcType=INTEGER},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="erpMessage != null">
        erp_message = #{erpMessage,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="conversionType != null">
        conversion_type = #{conversionType,jdbcType=VARCHAR},
      </if>
      <if test="conversionDate != null">
        conversion_date = #{conversionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="conversionRate != null">
        conversion_rate = #{conversionRate,jdbcType=DECIMAL},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="erpOrderStatus != null">
        erp_order_status = #{erpOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="dispatchIs != null">
        dispatch_is = #{dispatchIs,jdbcType=BIGINT},
      </if>
      <if test="receiptsId != null">
        receipts_id = #{receiptsId,jdbcType=BIGINT},
      </if>
      <if test="promisedDate != null">
        promised_date = #{promisedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="trackingDate != null">
        tracking_date = #{trackingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="vendorNum != null">
        vendor_num = #{vendorNum,jdbcType=VARCHAR},
      </if>
      <if test="paymentMethodId != null">
        payment_method_id = #{paymentMethodId,jdbcType=BIGINT},
      </if>
      <if test="paymentMethodName != null">
        payment_method_name = #{paymentMethodName,jdbcType=VARCHAR},
      </if>
      <if test="paymentWay != null">
        payment_way = #{paymentWay,jdbcType=VARCHAR},
      </if>
      <if test="deliveryType != null">
        delivery_type = #{deliveryType,jdbcType=VARCHAR},
      </if>
      <if test="deliveryClause != null">
        delivery_clause = #{deliveryClause,jdbcType=VARCHAR},
      </if>
      <if test="taxId != null">
        tax_id = #{taxId,jdbcType=BIGINT},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="pricingType != null">
        pricing_type = #{pricingType,jdbcType=TINYINT},
      </if>
      <if test="currencyCode != null">
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="exchangeRate != null">
        exchange_rate = #{exchangeRate,jdbcType=DECIMAL},
      </if>
      <if test="approveInfo != null">
        approve_info = #{approveInfo,jdbcType=VARCHAR},
      </if>
      <if test="erpBuyerId != null">
        erp_buyer_id = #{erpBuyerId,jdbcType=BIGINT},
      </if>
      <if test="initFlag != null">
        init_flag = #{initFlag,jdbcType=INTEGER},
      </if>
      <if test="ouId != null">
        ou_id = #{ouId,jdbcType=BIGINT},
      </if>
      <if test="secondaryInventory != null">
        secondary_inventory = #{secondaryInventory,jdbcType=VARCHAR},
      </if>
      <if test="secondaryInventoryName != null">
        secondary_inventory_name = #{secondaryInventoryName,jdbcType=VARCHAR},
      </if>
      <if test="approvalTime != null">
        approval_time = #{approvalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncSourceSystem != null">
        sync_source_system = #{syncSourceSystem,jdbcType=VARCHAR},
      </if>
      <if test="contractTermsFlg != null">
        contract_terms_flg = #{contractTermsFlg,jdbcType=VARCHAR},
      </if>
      <if test="syncToFap != null">
        sync_to_fap = #{syncToFap,jdbcType=TINYINT},
      </if>
      <if test="contractTerms != null">
        contract_terms = #{contractTerms,jdbcType=LONGVARCHAR},
      </if>
      <if test="overBudgetDes != null">
        over_budget_des = #{overBudgetDes,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrder">
    update purchase_order
    set num = #{num,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=BIGINT},
      vendor_asl_id = #{vendorAslId,jdbcType=BIGINT},
      vendor_name = #{vendorName,jdbcType=VARCHAR},
      vendor_site_code = #{vendorSiteCode,jdbcType=VARCHAR},
      buyer_id = #{buyerId,jdbcType=BIGINT},
      buyer_name = #{buyerName,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      sync_status = #{syncStatus,jdbcType=INTEGER},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      erp_message = #{erpMessage,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      currency = #{currency,jdbcType=VARCHAR},
      conversion_type = #{conversionType,jdbcType=VARCHAR},
      conversion_date = #{conversionDate,jdbcType=TIMESTAMP},
      conversion_rate = #{conversionRate,jdbcType=DECIMAL},
      source = #{source,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      erp_order_status = #{erpOrderStatus,jdbcType=INTEGER},
      dispatch_is = #{dispatchIs,jdbcType=BIGINT},
      receipts_id = #{receiptsId,jdbcType=BIGINT},
      promised_date = #{promisedDate,jdbcType=TIMESTAMP},
      tracking_date = #{trackingDate,jdbcType=TIMESTAMP},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      vendor_num = #{vendorNum,jdbcType=VARCHAR},
      payment_method_id = #{paymentMethodId,jdbcType=BIGINT},
      payment_method_name = #{paymentMethodName,jdbcType=VARCHAR},
      payment_way = #{paymentWay,jdbcType=VARCHAR},
      delivery_type = #{deliveryType,jdbcType=VARCHAR},
      delivery_clause = #{deliveryClause,jdbcType=VARCHAR},
      tax_id = #{taxId,jdbcType=BIGINT},
      tax_rate = #{taxRate,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      pricing_type = #{pricingType,jdbcType=TINYINT},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      exchange_rate = #{exchangeRate,jdbcType=DECIMAL},
      approve_info = #{approveInfo,jdbcType=VARCHAR},
      erp_buyer_id = #{erpBuyerId,jdbcType=BIGINT},
      init_flag = #{initFlag,jdbcType=INTEGER},
      ou_id = #{ouId,jdbcType=BIGINT},
      secondary_inventory = #{secondaryInventory,jdbcType=VARCHAR},
      secondary_inventory_name = #{secondaryInventoryName,jdbcType=VARCHAR},
      approval_time = #{approvalTime,jdbcType=TIMESTAMP},
      sync_source_system = #{syncSourceSystem,jdbcType=VARCHAR},
      contract_terms_flg = #{contractTermsFlg,jdbcType=VARCHAR},
      sync_to_fap = #{syncToFap,jdbcType=TINYINT},
      contract_terms = #{contractTerms,jdbcType=LONGVARCHAR},
      over_budget_des = #{overBudgetDes,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrder">
    update purchase_order
    set num = #{num,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=BIGINT},
      vendor_asl_id = #{vendorAslId,jdbcType=BIGINT},
      vendor_name = #{vendorName,jdbcType=VARCHAR},
      vendor_site_code = #{vendorSiteCode,jdbcType=VARCHAR},
      buyer_id = #{buyerId,jdbcType=BIGINT},
      buyer_name = #{buyerName,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      sync_status = #{syncStatus,jdbcType=INTEGER},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      erp_message = #{erpMessage,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      currency = #{currency,jdbcType=VARCHAR},
      conversion_type = #{conversionType,jdbcType=VARCHAR},
      conversion_date = #{conversionDate,jdbcType=TIMESTAMP},
      conversion_rate = #{conversionRate,jdbcType=DECIMAL},
      source = #{source,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      erp_order_status = #{erpOrderStatus,jdbcType=INTEGER},
      dispatch_is = #{dispatchIs,jdbcType=BIGINT},
      receipts_id = #{receiptsId,jdbcType=BIGINT},
      promised_date = #{promisedDate,jdbcType=TIMESTAMP},
      tracking_date = #{trackingDate,jdbcType=TIMESTAMP},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      vendor_num = #{vendorNum,jdbcType=VARCHAR},
      payment_method_id = #{paymentMethodId,jdbcType=BIGINT},
      payment_method_name = #{paymentMethodName,jdbcType=VARCHAR},
      payment_way = #{paymentWay,jdbcType=VARCHAR},
      delivery_type = #{deliveryType,jdbcType=VARCHAR},
      delivery_clause = #{deliveryClause,jdbcType=VARCHAR},
      tax_id = #{taxId,jdbcType=BIGINT},
      tax_rate = #{taxRate,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      pricing_type = #{pricingType,jdbcType=TINYINT},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      exchange_rate = #{exchangeRate,jdbcType=DECIMAL},
      approve_info = #{approveInfo,jdbcType=VARCHAR},
      erp_buyer_id = #{erpBuyerId,jdbcType=BIGINT},
      init_flag = #{initFlag,jdbcType=INTEGER},
      ou_id = #{ouId,jdbcType=BIGINT},
      secondary_inventory = #{secondaryInventory,jdbcType=VARCHAR},
      secondary_inventory_name = #{secondaryInventoryName,jdbcType=VARCHAR},
      approval_time = #{approvalTime,jdbcType=TIMESTAMP},
      sync_source_system = #{syncSourceSystem,jdbcType=VARCHAR},
      contract_terms_flg = #{contractTermsFlg,jdbcType=VARCHAR},
      sync_to_fap = #{syncToFap,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>