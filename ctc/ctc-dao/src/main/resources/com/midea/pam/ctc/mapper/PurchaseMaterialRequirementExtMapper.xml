<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PurchaseMaterialRequirementExtMapper">
    <sql id="queryCondition">
        <!-- 模糊物料编码-->
        <if test="fuzzyErpCode != null and fuzzyErpCode != ''">
            AND requirement.erp_code like concat('%', #{fuzzyErpCode, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 模糊物料描述-->
        <if test="fuzzyMaterielDescr != null and fuzzyMaterielDescr != ''">
            AND requirement.materiel_descr like concat('%', #{fuzzyMaterielDescr, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 模糊PAM编码-->
        <if test="fuzzyPamCode != null and fuzzyPamCode != ''">
            AND requirement.pam_code like concat('%', #{fuzzyPamCode, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 模糊项目名称-->
        <if test="fuzzyProjectName != null and fuzzyProjectName != ''">
            AND project.name like concat('%', #{fuzzyProjectName, jdbcType=CHAR}, '%')
        </if>

        <!-- 模糊项目编号-->
        <if test="fuzzyProjectNum != null and fuzzyProjectNum != ''">
            AND project.code like concat('%', #{fuzzyProjectNum, jdbcType=CHAR}, '%')
        </if>

        <!-- 项目业务实体id-->
        <if test="projectOuId != null">
            AND project.ou_id = #{projectOuId, jdbcType=BIGINT}
        </if>

        <!-- 物料需求项目id-->
        <if test="projectId != null">
            AND requirement.project_id = #{projectId, jdbcType=BIGINT}
        </if>

        <!-- 物料需求物料erp编码-->
        <if test="erpCode != null">
            AND requirement.erp_code = #{erpCode, jdbcType=VARCHAR}
        </if>

        <!-- 物料需求交付时间-->
        <if test="deliveryTime != null">
            AND requirement.delivery_time = #{deliveryTime, jdbcType=TIMESTAMP}
        </if>

        <!-- 物料需求id-->
        <if test="id != null">
            AND requirement.id = #{id, jdbcType=BIGINT}
        </if>

        <if test="status != null">
            AND requirement.status = #{status, jdbcType=BIGINT}
        </if>
        <if test="statusList != null and statusList.size &gt; 0">
            AND requirement.status in
            <foreach collection="statusList" index="index" item="statusTag" open="(" separator="," close=")">
                #{statusTag}
            </foreach>
        </if>

        <if test="requirementIdList != null and requirementIdList.size() > 0">
            and requirement.id in
            <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        </if>
    </sql>

    <sql id="havingCondition">
        <!-- 物料需求状态 0：待下达， 1：已下达-->
        <if test="status != null">
            <if test="status == 0">
                HAVING unreleasedAmount &gt; 0
            </if>
            <if test="status == 1">
                HAVING unreleasedAmount &lt;= 0
            </if>
        </if>
    </sql>

    <select id="selectIdsWithDetail" resultType="java.lang.Long">
        SELECT requirement.id FROM purchase_material_requirement requirement
        WHERE requirement.deleted_flag = 0
        <include refid="queryCondition" />
    </select>

    <select id="selectReleasedQuantityByIds" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        with t as (
        select
        ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
        pod.material_purchase_requirement_id as requirementId
        from
        pam_ctc.purchase_order_detail pod
        left join pam_ctc.purchase_order po on
        pod.purchase_order_id = po.id
        where
        pod.`status` != 3
        and (pod.purchase_order_id is null or
        (po.order_status  != 9
        and po.deleted_flag = 0))
        and pod.deleted_flag = 0
        and (pod.merge_rows = 0
        or pod.merge_rows is null)
        <if test="requirementIdList != null and requirementIdList.size() > 0">
            and pod.material_purchase_requirement_id in
            <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        </if>
        union all
        select
        ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
        pom.material_purchase_requirement_id as requirementId
        from
        pam_ctc.purchase_order_merge pom
        inner join pam_ctc.purchase_order_detail pod on
        pod.id = pom.purchase_order_id
        inner join pam_ctc.purchase_order po on
        po.id = pod.purchase_order_id
        where
        pod.deleted_flag = 0
        and pom.deleted_flag = 0
        and po.deleted_flag = 0
        and pod.merge_rows = 1
        and pod.`status` != 3
        and po.order_status  != 9
        <if test="requirementIdList != null and requirementIdList.size() > 0">
            and pom.material_purchase_requirement_id in
            <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        </if>
        union all
        select
        ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
        pod.material_purchase_requirement_id as requirementId
        from
        pam_ctc.purchase_order_detail_change_history pod
        inner join pam_ctc.purchase_order_change_record pocr on
        pocr.id = pod.record_id
        where
        pod.deleted_flag = 0
        and pocr.deleted_flag = 0
        and pod.history_type = 1
        and pod.origin_id is null
        and pocr.status = 2
        and (pod.merge_rows = 0
        or pod.merge_rows is null)
        <if test="requirementIdList != null and requirementIdList.size() > 0">
            and pod.material_purchase_requirement_id in
            <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        </if>
        union all
        select
        ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
        pom.material_purchase_requirement_id as requirementId
        from
        pam_ctc.purchase_order_merge_change_history pom
        inner join pam_ctc.purchase_order_detail_change_history pod on
        pod.id = pom.purchase_order_id
        inner join pam_ctc.purchase_order_change_record pocr on
        pocr.id = pom.record_id
        and pocr.id = pod.record_id
        where
        pod.deleted_flag = 0
        and pom.deleted_flag = 0
        and pod.history_type = 1
        and pom.history_type = 1
        and pocr.status = 2
        and pod.origin_id is null
        and pom.origin_id is null
        and pod.merge_rows = 1
        <if test="requirementIdList != null and requirementIdList.size() > 0">
            and pom.material_purchase_requirement_id in
            <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        </if>
        )
        select * from t
        group by t.requirementId
    </select>

    <select id="queryOrderNumByProjectId"
            parameterType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto"
            resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            requirement.id,
            requirement.project_id as 'projectId',
            requirement.erp_code as 'erpCode',
            requirement.pam_code as 'pamCode',
            requirement.wbs_summary_code as 'wbsSummaryCode',
            requirement.delivery_time as 'deliveryTime',
            ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity
        from
            purchase_material_requirement requirement
        inner join pam_ctc.purchase_order_detail pod on
            pod.material_purchase_requirement_id = requirement.id
        left join pam_ctc.purchase_order po on
            pod.purchase_order_id = po.id
        where
            pod.`status` != 3
            and (pod.purchase_order_id is null
                or
                    (po.order_status <![CDATA[ <> ]]> 9
                    and po.deleted_flag = 0))
            and pod.deleted_flag = 0
            and (pod.merge_rows = 0
                or pod.merge_rows is null)
            and pod.material_purchase_requirement_id is not null
            AND requirement.deleted_flag != 1
            AND requirement.delivery_time is not null
            <!-- 物料需求项目id-->
            <if test="projectId != null">
                AND requirement.project_id = #{projectId, jdbcType=BIGINT}
            </if>
    </select>

    <select id="queryOrderMergeNumByProjectId"
            parameterType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto"
            resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            requirement.id,
            requirement.project_id as 'projectId',
            requirement.erp_code as 'erpCode',
            requirement.pam_code as 'pamCode',
            requirement.wbs_summary_code as 'wbsSummaryCode',
            requirement.delivery_time as 'deliveryTime',
            ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity
        from
            purchase_material_requirement requirement
        inner join pam_ctc.purchase_order_merge pom on
            pom.material_purchase_requirement_id = requirement.id
        inner join pam_ctc.purchase_order_detail pod on
            pod.id = pom.purchase_order_id
        inner join pam_ctc.purchase_order po on
            po.id = pod.purchase_order_id
        where
            pod.deleted_flag = 0
            and pom.deleted_flag = 0
            and po.deleted_flag = 0
            and pod.merge_rows = 1
            and pod.`status` != 3
            and po.order_status <![CDATA[ <> ]]> 9
            and pom.material_purchase_requirement_id is not null
            AND requirement.deleted_flag != 1
            AND requirement.delivery_time is not null
            <!-- 物料需求项目id-->
            <if test="projectId != null">
                AND requirement.project_id = #{projectId, jdbcType=BIGINT}
            </if>
    </select>

    <select id="queryOrderHistoryNumByProjectId"
            parameterType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto"
            resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            requirement.id,
            requirement.project_id as 'projectId',
            requirement.erp_code as 'erpCode',
            requirement.pam_code as 'pamCode',
            requirement.wbs_summary_code as 'wbsSummaryCode',
            requirement.delivery_time as 'deliveryTime',
            ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity
        from
            purchase_material_requirement requirement
        inner join pam_ctc.purchase_order_detail_change_history pod on
            pod.material_purchase_requirement_id = requirement.id
        inner join pam_ctc.purchase_order_change_record pocr on
            pocr.id = pod.record_id
        where
            pod.deleted_flag = 0
            and pocr.deleted_flag = 0
            and pod.history_type = 1
            and pod.origin_id is null
            and pocr.status = 2
            and (pod.merge_rows = 0
                or pod.merge_rows is null)
            and pod.material_purchase_requirement_id is not null
            AND requirement.deleted_flag != 1
            AND requirement.delivery_time is not null
            <!-- 物料需求项目id-->
            <if test="projectId != null">
                AND requirement.project_id = #{projectId, jdbcType=BIGINT}
            </if>
    </select>

    <select id="queryOrderMergeHistoryNumByProjectId"
            parameterType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto"
            resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            requirement.id,
            requirement.project_id as 'projectId',
            requirement.erp_code as 'erpCode',
            requirement.pam_code as 'pamCode',
            requirement.wbs_summary_code as 'wbsSummaryCode',
            requirement.delivery_time as 'deliveryTime',
            ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity
        from
            purchase_material_requirement requirement
        inner join pam_ctc.purchase_order_merge_change_history pom on
            pom.material_purchase_requirement_id = requirement.id
        inner join pam_ctc.purchase_order_detail_change_history pod on
            pod.id = pom.purchase_order_id
        inner join pam_ctc.purchase_order_change_record pocr on
            pocr.id = pom.record_id
            and pocr.id = pod.record_id
        where
            pod.deleted_flag = 0
            and pom.deleted_flag = 0
            and pod.history_type = 1
            and pom.history_type = 1
            and pocr.status = 2
            and pod.origin_id is null
            and pom.origin_id is null
            and pod.merge_rows = 1
            and pom.material_purchase_requirement_id is not null
            AND requirement.deleted_flag != 1
            AND requirement.delivery_time is not null
            <!-- 物料需求项目id-->
            <if test="projectId != null">
                AND requirement.project_id = #{projectId, jdbcType=BIGINT}
            </if>
    </select>

    <select id="selectApprovedSupplierNumber"
            resultType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement">
        select
            ifnull(t.num, 0) as approvedSupplierNumber,
            requirement.id
        from
            purchase_material_requirement requirement
        inner join project_profit profit on
            requirement.project_id = profit.project_id
            and profit.deleted_flag = 0
        left join (
            select
                vendorAsl.material_code,
                COUNT(distinct vendorAsl.id) as num
            from
                pam_ctc.vendor_asl vendorAsl
            inner join pam_ctc.purchase_material_requirement re on
                re.erp_code = vendorAsl.material_code
                and re.status <![CDATA[ <> ]]> 2
                and re.deleted_flag = 0
            where
                (vendorAsl.deleted_flag = 0
                    or vendorAsl.deleted_flag is null)
                and (vendorAsl.erp_disable_flag = 'N'
                    or vendorAsl.erp_disable_flag is null)
                and (vendorAsl.asl_status = 'Approved'
                    or vendorAsl.asl_status = 'ONETIME')
                and vendorAsl.organization_code = #{organizationCode}
            group by
                vendorAsl.material_code) t on
            t.material_code = requirement.erp_code
        where
            requirement.status <![CDATA[ <> ]]> 2
            and profit.storage_code = #{organizationCode}
            and (requirement.approved_supplier_number is null or
                requirement.approved_supplier_number <![CDATA[ <> ]]> ifnull(t.num, 0))
            and requirement.deleted_flag = 0
    </select>

    <select id="queryUnreleasedNumByRequirementId" resultType="java.math.BigDecimal">
        SELECT
            ifnull(requirement.need_total, 0)- ifnull(o.releasedQuantity, 0) AS unreleasedNum
        FROM
            purchase_material_requirement requirement
        left join (
                select
                    t.material_purchase_requirement_id as purchase_requirement_id,
                    sum(t.releasedQuantity) as releasedQuantity
                from
                    (
                    select
                        ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
                        pod.material_purchase_requirement_id
                    from
                        pam_ctc.purchase_order_detail pod
                    left join pam_ctc.purchase_order po on
                        pod.purchase_order_id = po.id
                    where
                        pod.`status` != 3
                        and (pod.purchase_order_id is null or
                        (po.order_status  <![CDATA[ <> ]]> 9
                        and po.deleted_flag = 0))
                        and pod.deleted_flag = 0
                        and (pod.merge_rows = 0
                            or pod.merge_rows is null)
                        and pod.material_purchase_requirement_id = #{requirementId}
                union all
                    select
                        ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
                        pom.material_purchase_requirement_id
                    from
                        pam_ctc.purchase_order_merge pom
                    inner join pam_ctc.purchase_order_detail pod on
                        pod.id = pom.purchase_order_id
                    inner join pam_ctc.purchase_order po on
                        po.id = pod.purchase_order_id
                    where
                        pod.deleted_flag = 0
                        and pom.deleted_flag = 0
                        and po.deleted_flag = 0
                        and pod.merge_rows = 1
                        and pod.`status` != 3
                        and po.order_status  <![CDATA[ <> ]]> 9
                        and pom.material_purchase_requirement_id = #{requirementId}
                union all
                    select
                        ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
                        pod.material_purchase_requirement_id
                    from
                        pam_ctc.purchase_order_detail_change_history pod
                    inner join pam_ctc.purchase_order_change_record pocr on
                        pocr.id = pod.record_id
                    where
                        pod.deleted_flag = 0
                        and pocr.deleted_flag = 0
                        and pod.history_type = 1
                        and pod.origin_id is null
                        and pocr.status = 2
                        and (pod.merge_rows = 0
                            or pod.merge_rows is null)
                        and pod.material_purchase_requirement_id = #{requirementId}
                union all
                    select
                        ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
                        pom.material_purchase_requirement_id
                    from
                        pam_ctc.purchase_order_merge_change_history pom
                    inner join pam_ctc.purchase_order_detail_change_history pod on
                        pod.id = pom.purchase_order_id
                    inner join pam_ctc.purchase_order_change_record pocr on
                        pocr.id = pom.record_id
                        and pocr.id = pod.record_id
                    where
                        pod.deleted_flag = 0
                        and pom.deleted_flag = 0
                        and pod.history_type = 1
                        and pom.history_type = 1
                        and pocr.status = 2
                        and pod.origin_id is null
                        and pom.origin_id is null
                        and pod.merge_rows = 1
                        and pom.material_purchase_requirement_id is not null) t
                where
                    t.material_purchase_requirement_id = #{requirementId}
                group by
                    t.material_purchase_requirement_id
                ) o on o.purchase_requirement_id = requirement.id
        WHERE
            requirement.id = #{requirementId}
    </select>

    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement">
        insert into purchase_material_requirement (id, project_id, erp_code,
        pam_code, delivery_time, materiel_id,
        materiel_descr, need_total, unit_code,
        unit, approved_supplier_number, status,
        reason, deleted_flag, create_by,
        create_at, update_by, update_at,
        currency, conversion_type, conversion_date,
        conversion_rate, purchase_type, project_wbs_receipts_id,
        requirement_code, wbs_summary_code, activity_code,
        dispatch_is, closed_amount, closed_quantity,
        wbs_demand_os_cost, receipts_publish_date,
        has_amount, due_amount, design_release_lot_number,
        figure_number, chart_version, init,
        init_sequence,requirement_type)
        values
        <foreach collection="list" item="requirement" index="index" separator=",">
            (#{requirement.id,jdbcType=BIGINT}, #{requirement.projectId,jdbcType=BIGINT}, #{requirement.erpCode,jdbcType=VARCHAR},
            #{requirement.pamCode,jdbcType=VARCHAR}, #{requirement.deliveryTime,jdbcType=TIMESTAMP}, #{requirement.materielId,jdbcType=BIGINT},
            #{requirement.materielDescr,jdbcType=VARCHAR}, #{requirement.needTotal,jdbcType=DECIMAL}, #{requirement.unitCode,jdbcType=VARCHAR},
            #{requirement.unit,jdbcType=VARCHAR}, #{requirement.approvedSupplierNumber,jdbcType=INTEGER}, #{requirement.status,jdbcType=INTEGER},
            #{requirement.reason,jdbcType=VARCHAR}, #{requirement.deletedFlag,jdbcType=TINYINT}, #{requirement.createBy,jdbcType=BIGINT},
            #{requirement.createAt,jdbcType=TIMESTAMP}, #{requirement.updateBy,jdbcType=BIGINT}, #{requirement.updateAt,jdbcType=TIMESTAMP},
            #{requirement.currency,jdbcType=VARCHAR}, #{requirement.conversionType,jdbcType=VARCHAR}, #{requirement.conversionDate,jdbcType=TIMESTAMP},
            #{requirement.conversionRate,jdbcType=DECIMAL}, #{requirement.purchaseType,jdbcType=INTEGER}, #{requirement.projectWbsReceiptsId,jdbcType=BIGINT},
            #{requirement.requirementCode,jdbcType=VARCHAR}, #{requirement.wbsSummaryCode,jdbcType=VARCHAR}, #{requirement.activityCode,jdbcType=VARCHAR},
            #{requirement.dispatchIs,jdbcType=TINYINT}, #{requirement.closedAmount,jdbcType=DECIMAL}, #{requirement.closedQuantity,jdbcType=DECIMAL},
            #{requirement.wbsDemandOsCost,jdbcType=DECIMAL}, #{requirement.receiptsPublishDate,jdbcType=TIMESTAMP},
            #{requirement.hasAmount,jdbcType=INTEGER}, #{requirement.dueAmount,jdbcType=INTEGER}, #{requirement.designReleaseLotNumber,jdbcType=VARCHAR},
            #{requirement.figureNumber,jdbcType=VARCHAR}, #{requirement.chartVersion,jdbcType=VARCHAR}, #{requirement.init,jdbcType=TINYINT},
            #{requirement.initSequence,jdbcType=VARCHAR},#{requirement.requirementType,jdbcType=TINYINT})
        </foreach>
    </insert>

    <insert id="batchInsert1" parameterType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        insert into purchase_material_requirement (id, project_id, erp_code,
        pam_code, delivery_time, materiel_id,
        materiel_descr, need_total, unit_code,
        unit, approved_supplier_number, status,
        reason, deleted_flag, create_by,
        create_at, update_by, update_at,
        currency, conversion_type, conversion_date,
        conversion_rate, purchase_type, project_wbs_receipts_id,
        requirement_code, wbs_summary_code, activity_code,
        dispatch_is, closed_amount, closed_quantity,
        wbs_demand_os_cost, receipts_publish_date,
        has_amount, due_amount, design_release_lot_number,
        figure_number, chart_version, init,
        init_sequence,requirement_type)
        values
        <foreach collection="list" item="requirement" index="index" separator=",">
            (#{requirement.id,jdbcType=BIGINT}, #{requirement.projectId,jdbcType=BIGINT}, #{requirement.erpCode,jdbcType=VARCHAR},
            #{requirement.pamCode,jdbcType=VARCHAR}, #{requirement.deliveryTime,jdbcType=TIMESTAMP}, #{requirement.materielId,jdbcType=BIGINT},
            #{requirement.materielDescr,jdbcType=VARCHAR}, #{requirement.needTotal,jdbcType=DECIMAL}, #{requirement.unitCode,jdbcType=VARCHAR},
            #{requirement.unit,jdbcType=VARCHAR}, #{requirement.approvedSupplierNumber,jdbcType=INTEGER}, #{requirement.status,jdbcType=INTEGER},
            #{requirement.reason,jdbcType=VARCHAR}, #{requirement.deletedFlag,jdbcType=TINYINT}, #{requirement.createBy,jdbcType=BIGINT},
            #{requirement.createAt,jdbcType=TIMESTAMP}, #{requirement.updateBy,jdbcType=BIGINT}, #{requirement.updateAt,jdbcType=TIMESTAMP},
            #{requirement.currency,jdbcType=VARCHAR}, #{requirement.conversionType,jdbcType=VARCHAR}, #{requirement.conversionDate,jdbcType=TIMESTAMP},
            #{requirement.conversionRate,jdbcType=DECIMAL}, #{requirement.purchaseType,jdbcType=INTEGER}, #{requirement.projectWbsReceiptsId,jdbcType=BIGINT},
            #{requirement.requirementCode,jdbcType=VARCHAR}, #{requirement.wbsSummaryCode,jdbcType=VARCHAR}, #{requirement.activityCode,jdbcType=VARCHAR},
            #{requirement.dispatchIs,jdbcType=TINYINT}, #{requirement.closedAmount,jdbcType=DECIMAL}, #{requirement.closedQuantity,jdbcType=DECIMAL},
            #{requirement.wbsDemandOsCost,jdbcType=DECIMAL}, #{requirement.receiptsPublishDate,jdbcType=TIMESTAMP},
            #{requirement.hasAmount,jdbcType=INTEGER}, #{requirement.dueAmount,jdbcType=INTEGER}, #{requirement.designReleaseLotNumber,jdbcType=VARCHAR},
            #{requirement.figureNumber,jdbcType=VARCHAR}, #{requirement.chartVersion,jdbcType=VARCHAR}, #{requirement.init,jdbcType=TINYINT},
            #{requirement.initSequence,jdbcType=VARCHAR},#{requirement.requirementType,jdbcType=TINYINT})
        </foreach>
    </insert>

    <update id="updataNeedTotal">
        update pam_ctc.purchase_material_requirement
        set need_total = (select ifnull(sum(publish_num), 0)
                          from purchase_material_release_detail pmrd
                          where pmrd.deleted_flag = 0
                            and pmrd.purchase_requirement_id = #{id})
        where id = #{id}
    </update>

    <select id="selectUnreleasedAmountByIds"
            parameterType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto"
            resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        SELECT
            requirement.id,
            ifnull(o.releasedQuantity, 0) AS 'releasedQuantity',
            requirement.need_total-ifnull(o.releasedQuantity, 0) AS 'unreleasedAmount',
            requirement.delivery_address AS 'deliveryAddress',
            requirement.consignee AS 'consignee',
            requirement.contact_phone AS 'contactPhone'
        FROM
        purchase_material_requirement requirement
        left join (
            select
            t.material_purchase_requirement_id as purchase_requirement_id,
            sum(t.releasedQuantity) as releasedQuantity
            from
            (
                select
                ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
                pod.material_purchase_requirement_id
                from
                pam_ctc.purchase_order_detail pod
                left join pam_ctc.purchase_order po on
                pod.purchase_order_id = po.id
                where
                pod.`status` != 3
                and (pod.purchase_order_id is null or
                (po.order_status  <![CDATA[ <> ]]> 9
                and po.deleted_flag = 0))
                and pod.deleted_flag = 0
                and (pod.merge_rows = 0
                or pod.merge_rows is null)
                <if test="requirementIdList != null and requirementIdList.size() > 0">
                    and pod.material_purchase_requirement_id in
                    <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                        #{requirementId}
                    </foreach>
                </if>
                union all
                select
                ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
                pom.material_purchase_requirement_id
                from
                pam_ctc.purchase_order_merge pom
                inner join pam_ctc.purchase_order_detail pod on
                pod.id = pom.purchase_order_id
                inner join pam_ctc.purchase_order po on
                po.id = pod.purchase_order_id
                where
                pod.deleted_flag = 0
                and pom.deleted_flag = 0
                and po.deleted_flag = 0
                and pod.merge_rows = 1
                and pod.`status` != 3
                and po.order_status  <![CDATA[ <> ]]> 9
                <if test="requirementIdList != null and requirementIdList.size() > 0">
                    and pom.material_purchase_requirement_id in
                    <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                        #{requirementId}
                    </foreach>
                </if>
                union all
                select
                ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
                pod.material_purchase_requirement_id
                from
                pam_ctc.purchase_order_detail_change_history pod
                inner join pam_ctc.purchase_order_change_record pocr on
                pocr.id = pod.record_id
                where
                pod.deleted_flag = 0
                and pocr.deleted_flag = 0
                and pod.history_type = 1
                and pod.origin_id is null
                and pocr.status = 2
                and (pod.merge_rows = 0
                or pod.merge_rows is null)
                <if test="requirementIdList != null and requirementIdList.size() > 0">
                    and pod.material_purchase_requirement_id in
                    <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                        #{requirementId}
                    </foreach>
                </if>
                union all
                select
                ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
                pom.material_purchase_requirement_id
                from
                pam_ctc.purchase_order_merge_change_history pom
                inner join pam_ctc.purchase_order_detail_change_history pod on
                pod.id = pom.purchase_order_id
                inner join pam_ctc.purchase_order_change_record pocr on
                pocr.id = pom.record_id
                and pocr.id = pod.record_id
                where
                pod.deleted_flag = 0
                and pom.deleted_flag = 0
                and pod.history_type = 1
                and pom.history_type = 1
                and pocr.status = 2
                and pod.origin_id is null
                and pom.origin_id is null
                and pod.merge_rows = 1
                <if test="requirementIdList != null and requirementIdList.size() > 0">
                    and pom.material_purchase_requirement_id in
                    <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                        #{requirementId}
                    </foreach>
                </if>
                ) t
            group by
            t.material_purchase_requirement_id
            ) o on o.purchase_requirement_id = requirement.id
        WHERE
        1 = 1
        AND requirement.deleted_flag != 1
        <if test="requirementIdList != null and requirementIdList.size() > 0">
            and requirement.id in
            <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        </if>
    </select>
    <select id="selectOutRequirementInfo"
            resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            budget.purchase_requirement_id requirementId,
            sum( if( contract.status = 1 or contract.status = 3, 0 ,
                    if(history.local_total_price is null,
                        budget.local_total_price,
                        GREATEST(budget.local_total_price, history.local_total_price)))) contractTotalAmount,
            sum( if(history.`number` is null, budget.`number` ,
                    GREATEST(budget.`number`, history.`number`))) releasedQuantity
        from pam_ctc.purchase_contract_budget budget
        inner join pam_ctc.purchase_contract contract
            on contract.id = budget.purchase_contract_id
        left join(
            select history.origin_id,history.local_total_price,history.`number`
            from pam_ctc.purchase_contract_budget_change_history history
            inner join pam_ctc.purchase_contract_change_header header
                on header.id = history.header_id
            where history.purchase_requirement_id in
                <foreach collection="requirementIds" item="requirementId" open="(" separator="," close=")">
                    #{requirementId}
                </foreach>
            and history.history_type = 1
            and history.deleted_flag = 0
            and header.change_type = 2
            and header.status = 2
        ) history
            on history.origin_id = budget.id
        where budget.purchase_requirement_id in
            <foreach collection="requirementIds" item="requirementId" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        and budget.deleted_flag = 0
        and contract.status in (1,2,3,5,10)
        group by budget.purchase_requirement_id
    </select>

    <select id="selectLatestPublishTime" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialReleaseDetailDto">
        select
            purchase_requirement_id as purchaseRequirementId,
            max(publish_time) as publishTime
        from purchase_material_release_detail
        where deleted_flag = 0
        <if test="requirementIdList != null and requirementIdList.size() > 0">
            and purchase_requirement_id in
            <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        </if>
        group by purchase_requirement_id
    </select>

    <select id="selectApprovedSupplierNumberFromMaterialDetail" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            vendorAsl.material_code as erpCode,
            vendorAsl.organization_code as organizationCode,
            count(distinct vendorAsl.id) as approvedSupplierNumber
        from
            pam_ctc.vendor_asl vendorAsl
        inner join pam_ctc.milepost_design_plan_material_detail detail on
            detail.erp_code = vendorAsl.material_code
            and detail.deleted_flag = 0
        where
            (vendorAsl.deleted_flag = 0
                or vendorAsl.deleted_flag is null)
            and (vendorAsl.erp_disable_flag = 'N'
                or vendorAsl.erp_disable_flag is null)
            and (vendorAsl.asl_status = 'Approved'
                or vendorAsl.asl_status = 'ONETIME')
        group by
            vendorAsl.material_code,
            vendorAsl.organization_code
    </select>

    <update id="batchUpdateByPrimaryKey" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update purchase_material_requirement
            <set>
                <if test="item.projectId != null">
                    project_id = #{item.projectId,jdbcType=BIGINT},
                </if>
                <if test="item.erpCode != null">
                    erp_code = #{item.erpCode,jdbcType=VARCHAR},
                </if>
                <if test="item.pamCode != null">
                    pam_code = #{item.pamCode,jdbcType=VARCHAR},
                </if>
                <if test="item.deliveryTime != null">
                    delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.materielId != null">
                    materiel_id = #{item.materielId,jdbcType=BIGINT},
                </if>
                <if test="item.materielDescr != null">
                    materiel_descr = #{item.materielDescr,jdbcType=VARCHAR},
                </if>
                <if test="item.needTotal != null">
                    need_total = #{item.needTotal,jdbcType=DECIMAL},
                </if>
                <if test="item.unitCode != null">
                    unit_code = #{item.unitCode,jdbcType=VARCHAR},
                </if>
                <if test="item.unit != null">
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.approvedSupplierNumber != null">
                    approved_supplier_number = #{item.approvedSupplierNumber,jdbcType=INTEGER},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.reason != null">
                    reason = #{item.reason,jdbcType=VARCHAR},
                </if>
                <if test="item.deletedFlag != null">
                    deleted_flag = #{item.deletedFlag,jdbcType=TINYINT},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy,jdbcType=BIGINT},
                </if>
                <if test="item.createAt != null">
                    create_at = #{item.createAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy,jdbcType=BIGINT},
                </if>
                <if test="item.updateAt != null">
                    update_at = #{item.updateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.currency != null">
                    currency = #{item.currency,jdbcType=VARCHAR},
                </if>
                <if test="item.conversionType != null">
                    conversion_type = #{item.conversionType,jdbcType=VARCHAR},
                </if>
                <if test="item.conversionDate != null">
                    conversion_date = #{item.conversionDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.conversionRate != null">
                    conversion_rate = #{item.conversionRate,jdbcType=DECIMAL},
                </if>
                <if test="item.purchaseType != null">
                    purchase_type = #{item.purchaseType,jdbcType=INTEGER},
                </if>
                <if test="item.projectWbsReceiptsId != null">
                    project_wbs_receipts_id = #{item.projectWbsReceiptsId,jdbcType=BIGINT},
                </if>
                <if test="item.requirementCode != null">
                    requirement_code = #{item.requirementCode,jdbcType=VARCHAR},
                </if>
                <if test="item.wbsSummaryCode != null">
                    wbs_summary_code = #{item.wbsSummaryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.activityCode != null">
                    activity_code = #{item.activityCode,jdbcType=VARCHAR},
                </if>
                <if test="item.dispatchIs != null">
                    dispatch_is = #{item.dispatchIs,jdbcType=TINYINT},
                </if>
                <if test="item.closedAmount != null">
                    closed_amount = #{item.closedAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.closedQuantity != null">
                    closed_quantity = #{item.closedQuantity,jdbcType=DECIMAL},
                </if>
                <if test="item.wbsDemandOsCost != null">
                    wbs_demand_os_cost = #{item.wbsDemandOsCost,jdbcType=DECIMAL},
                </if>
                <if test="item.receiptsPublishDate != null">
                    receipts_publish_date = #{item.receiptsPublishDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.hasAmount != null">
                    has_amount = #{item.hasAmount,jdbcType=INTEGER},
                </if>
                <if test="item.dueAmount != null">
                    due_amount = #{item.dueAmount,jdbcType=INTEGER},
                </if>
                <if test="item.designReleaseLotNumber != null">
                    design_release_lot_number = #{item.designReleaseLotNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.figureNumber != null">
                    figure_number = #{item.figureNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.chartVersion != null">
                    chart_version = #{item.chartVersion,jdbcType=VARCHAR},
                </if>
                <if test="item.init != null">
                    init = #{item.init,jdbcType=TINYINT},
                </if>
                <if test="item.initSequence != null">
                    init_sequence = #{item.initSequence,jdbcType=VARCHAR},
                </if>
                <if test="item.requirementType != null">
                    requirement_type = #{item.requirementType,jdbcType=TINYINT},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectPurchaseMaterialRequirementList" parameterType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto"
            resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        SELECT
        requirement.id,
        requirement.project_id AS 'projectId',
        requirement.erp_code AS 'erpCode',
        requirement.pam_code AS 'pamCode',
        requirement.delivery_time AS 'deliveryTime',
        requirement.materiel_id AS 'materielId',
        requirement.materiel_descr AS 'materielDescr',
        requirement.need_total AS 'needTotal',
        requirement.unit_code AS 'unitCode',
        requirement.unit AS 'unit',
        requirement.STATUS AS 'status',
        requirement.reason AS 'reason',
        requirement.purchase_type AS 'purchaseType',
        requirement.deleted_flag AS 'deletedFlag',
        requirement.create_by AS 'createBy',
        requirement.create_at AS 'createAt',
        requirement.update_by AS 'updateBy',
        requirement.update_at AS 'updateAt',
        requirement.delivery_Address AS 'deliveryAddress',
        requirement.consignee AS 'consignee',
        requirement.contact_phone AS 'contactPhone',
        <!-- 关闭数量 -->
        IFNULL(o3.closedQuantity,0) as closedQuantity,
        project.NAME AS 'projectName',
        project.`code` AS 'projectNum',
        project.`STATUS` AS 'projectStatus',
        IFNULL(requirement.approved_supplier_number,0) AS 'approvedSupplierNumber',
        project.ou_id as 'projectOuId',
        projectProfit.storage_id as 'projectOrganizationId',
        projectProfit.storage_code AS 'projectOrganizationCode'
        FROM
        purchase_material_requirement requirement
        LEFT JOIN project project ON requirement.project_id = project.id
        LEFT JOIN project_profit projectProfit ON projectProfit.project_id = project.id
        LEFT JOIN (
        select
        IFNULL(sum(if(close_type = 1, close_num, -1 * close_num)), 0)as closedQuantity,
        purchase_requirement_id
        from
        pam_ctc.purchase_material_close_detail
        where
        deleted_flag = 0
        group by purchase_requirement_id ) o3 on
        o3.purchase_requirement_id = requirement.id
        WHERE
        requirement.deleted_flag = 0
        <include refid="queryCondition" />
        ORDER BY
        approvedSupplierNumber desc,
        project.`code` asc,
        requirement.erp_code asc
    </select>

    <select id="calculateMaterialRequirementNumber" parameterType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto"
            resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            requirement.id as id,
            requirement.materiel_id as materielId,
            requirement.project_id as projectId,
            ifnull(sum(releaseDetail.publish_num), 0) as needTotal,
            releaseDetail.design_plan_detail_id as designPlanDetailId,
            releaseDetail.purchase_requirement_id as purchaseRequirementId
        from
            pam_ctc.purchase_material_requirement requirement
        left join pam_ctc.purchase_material_release_detail releaseDetail on
            releaseDetail.purchase_requirement_id = requirement.id and releaseDetail.deleted_flag = 0
            <if test="id != null">
                and releaseDetail.design_plan_detail_id = #{id}
            </if>
        where
            (releaseDetail.deleted_flag = 0
                or releaseDetail.deleted_flag is null)
            <if test="wbsSummaryCode != null">
                and requirement.wbs_summary_code = #{wbsSummaryCode}
            </if>
            <if test="pamCode != null">
                and requirement.pam_code = #{pamCode}
            </if>
            <if test="deliveryTime != null">
                and requirement.delivery_time = #{deliveryTime}
            </if>
        group by
            releaseDetail.design_plan_detail_id,purchase_requirement_id
    </select>

    <select id="selectPurchaseMaterialReleasedQuantityList" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialQuantityDto">
        select
            t.material_purchase_requirement_id as requirementId,
            sum(t.releasedQuantity) as quantity
        from (
            select
                ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
                pod.material_purchase_requirement_id
            from
            pam_ctc.purchase_order_detail pod
            left join pam_ctc.purchase_order po on pod.purchase_order_id = po.id
            where
                pod.`status` != 3
                and (pod.purchase_order_id is null or
                (po.order_status  <![CDATA[ <> ]]> 9
                and po.deleted_flag = 0))
                and pod.deleted_flag = 0
                and (pod.merge_rows = 0
                or pod.merge_rows is null)
                and pod.material_purchase_requirement_id is not null
            union all
            select
                ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
                pom.material_purchase_requirement_id
            from
                pam_ctc.purchase_order_merge pom
                inner join pam_ctc.purchase_order_detail pod on pod.id = pom.purchase_order_id
                inner join pam_ctc.purchase_order po on po.id = pod.purchase_order_id
            where
                pod.deleted_flag = 0
                and pom.deleted_flag = 0
                and po.deleted_flag = 0
                and pod.merge_rows = 1
                and pod.`status` != 3
                and po.order_status  <![CDATA[ <> ]]> 9
                and pom.material_purchase_requirement_id is not null
            union all
            select
                ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
                pod.material_purchase_requirement_id
            from
                pam_ctc.purchase_order_detail_change_history pod
                inner join pam_ctc.purchase_order_change_record pocr on pocr.id = pod.record_id
            where
                pod.deleted_flag = 0
                and pocr.deleted_flag = 0
                and pod.history_type = 1
                and pod.origin_id is null
                and pocr.status = 2
                and (pod.merge_rows = 0
                or pod.merge_rows is null)
                and pod.material_purchase_requirement_id is not null
            union all
            select
                ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
                pom.material_purchase_requirement_id
            from
                pam_ctc.purchase_order_merge_change_history pom
                inner join pam_ctc.purchase_order_detail_change_history pod on
                pod.id = pom.purchase_order_id
                inner join pam_ctc.purchase_order_change_record pocr on
                pocr.id = pom.record_id
                and pocr.id = pod.record_id
            where
                pod.deleted_flag = 0
                and pom.deleted_flag = 0
                and pod.history_type = 1
                and pom.history_type = 1
                and pocr.status = 2
                and pod.origin_id is null
                and pom.origin_id is null
                and pod.merge_rows = 1
                and pom.material_purchase_requirement_id is not null
            <!-- 非柔性统计逻辑 -->
            union all
            select
                ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
                pod.material_purchase_requirement_id
            from
                pam_ctc.purchase_order_detail_change_history pod
            where
                pod.deleted_flag = 0
                and pod.history_type = 1
                and pod.status = 1
                and pod.change_status = 0
                and pod.origin_id is null
                and pod.material_purchase_requirement_id is not null
            ) t
            where 1=1
                <if test="requirementIdList != null and requirementIdList.size() > 0">
                    and t.material_purchase_requirement_id in
                    <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                        #{requirementId}
                    </foreach>
                </if>
            group by
            t.material_purchase_requirement_id

    </select>

    <select id="selectPurchaseMaterialOrderQuantityList" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialQuantityDto">
        select
            t.material_purchase_requirement_id as requirementId,
            sum(t.releasedQuantity) as quantity
        from
        (
            select
                ifnull(pod.order_num, 0) - ifnull(pod.cancel_num, 0) as releasedQuantity,
                pod.material_purchase_requirement_id
            from
                pam_ctc.purchase_order_detail pod
                inner join pam_ctc.purchase_order po on
                pod.purchase_order_id = po.id
            where
                pod.`status` in (2, 4)
                and po.order_status  <![CDATA[ <> ]]> 9
                and po.deleted_flag = 0
                and pod.deleted_flag = 0
                and (pod.merge_rows = 0
                or pod.merge_rows is null)
                and pod.material_purchase_requirement_id is not null
            union all
            select
                ifnull(pom.order_num, 0) - ifnull(pom.cancel_num, 0) as releasedQuantity,
                pom.material_purchase_requirement_id
            from
                pam_ctc.purchase_order_merge pom
                inner join pam_ctc.purchase_order_detail pod on
                pod.id = pom.purchase_order_id
                inner join pam_ctc.purchase_order po on
                po.id = pod.purchase_order_id
            where
                pod.deleted_flag = 0
                and pom.deleted_flag = 0
                and po.deleted_flag = 0
                and pod.merge_rows = 1
                and pod.`status` in (2, 4)
                and po.order_status  <![CDATA[ <> ]]> 9
                and pom.material_purchase_requirement_id is not null
            ) t
        where 1=1
        <if test="requirementIdList != null and requirementIdList.size() > 0">
            and t.material_purchase_requirement_id in
            <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                #{requirementId}
            </foreach>
        </if>
        group by
        t.material_purchase_requirement_id

    </select>

    <select id="listAddressesByDeliveryCondition"
            resultType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement">
        select
            project_id,
            pam_code,
            delivery_time,
            delivery_address,
            consignee,
            contact_phone
        from
            pam_ctc.purchase_material_requirement
        where
            deleted_flag = 0
            <if test="projectIds !=null and projectIds.size() >0">
                and project_id in
                <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
                    #{projectId}
                </foreach>
            </if>
            <if test="pamCodes !=null and pamCodes.size() >0">
                and pam_code in
                <foreach collection="pamCodes" item="pamCode" index="index" open="(" separator="," close=")">
                    #{pamCode}
                </foreach>
            </if>
            <if test="deliveryTimes !=null and deliveryTimes.size() >0">
                and DATE(delivery_time) in
                <foreach collection="deliveryTimes" item="deliveryTime" index="index" open="(" separator="," close=")">
                    #{deliveryTime}
                </foreach>
            </if>
    </select>
    <select id="selectWbsReleasedQuantity" resultType="java.math.BigDecimal">
        SELECT
        IFNULL(SUM(t.releasedQuantity), 0) AS releasedQuantity
        FROM (
        SELECT
        IFNULL(pod.order_num, 0) - IFNULL(pod.cancel_num, 0) AS releasedQuantity
        FROM
        pam_ctc.purchase_order_detail pod
        INNER JOIN pam_ctc.purchase_order po ON
        pod.purchase_order_id = po.id
        WHERE
        pod.`status` != 3
        AND po.order_status <![CDATA[ <> ]]> 9
        AND po.deleted_flag = 0
        AND pod.deleted_flag = 0
        AND (pod.merge_rows = 0 OR pod.merge_rows IS NULL)
        AND pod.material_purchase_requirement_id = #{requirementId}

        UNION ALL

        SELECT
        IFNULL(pom.order_num, 0) - IFNULL(pom.cancel_num, 0) AS releasedQuantity
        FROM
        pam_ctc.purchase_order_merge pom
        INNER JOIN pam_ctc.purchase_order_detail pod ON
        pod.id = pom.purchase_order_id
        INNER JOIN pam_ctc.purchase_order po ON
        po.id = pod.purchase_order_id
        WHERE
        pod.deleted_flag = 0
        AND pom.deleted_flag = 0
        AND po.deleted_flag = 0
        AND pod.merge_rows = 1
        AND pod.`status` != 3
        AND po.order_status <![CDATA[ <> ]]> 9
        AND pom.material_purchase_requirement_id = #{requirementId}

        UNION ALL

        SELECT
        IFNULL(pod.order_num, 0) - IFNULL(pod.cancel_num, 0) AS releasedQuantity
        FROM
        pam_ctc.purchase_order_detail_change_history pod
        INNER JOIN pam_ctc.purchase_order_change_record pocr ON
        pocr.id = pod.record_id
        WHERE
        pod.deleted_flag = 0
        AND pocr.deleted_flag = 0
        AND pod.history_type = 1
        AND pod.origin_id IS NULL
        AND pocr.status = 2
        AND (pod.merge_rows = 0 OR pod.merge_rows IS NULL)
        AND pod.material_purchase_requirement_id = #{requirementId}

        UNION ALL

        SELECT
        IFNULL(pom.order_num, 0) - IFNULL(pom.cancel_num, 0) AS releasedQuantity
        FROM
        pam_ctc.purchase_order_merge_change_history pom
        INNER JOIN pam_ctc.purchase_order_detail_change_history pod ON
        pod.id = pom.purchase_order_id
        INNER JOIN pam_ctc.purchase_order_change_record pocr ON
        pocr.id = pom.record_id
        AND pocr.id = pod.record_id
        WHERE
        pod.deleted_flag = 0
        AND pom.deleted_flag = 0
        AND pod.history_type = 1
        AND pom.history_type = 1
        AND pocr.status = 2
        AND pod.origin_id IS NULL
        AND pom.origin_id IS NULL
        AND pod.merge_rows = 1
        AND pom.material_purchase_requirement_id = #{requirementId}
        ) t
    </select>

    <select id="selectReleasedQuantity" resultType="java.math.BigDecimal">
        select
        ifnull(sum(GREATEST(ifnull(pcb.number, 0), ifnull(changes.number, 0))), 0) as releasedQuantity
        from pam_ctc.purchase_contract_budget pcb
        left join (
        select
        budget_change.origin_id,
        budget_change.total_price,
        budget_change.number
        from
        pam_ctc.purchase_contract_change_header header,
        pam_ctc.purchase_contract_budget_change_history budget_change
        where header.id = budget_change .header_id
        and header.status = 2
        and budget_change.history_type = 1 ) as changes
        on pcb.id = changes.origin_id
        left join pam_ctc.purchase_contract contract on pcb.purchase_contract_id = contract.id
        where pcb.deleted_flag = 0
        and contract.status <![CDATA[ <> ]]> 9
        and contract.deleted_flag = 0
        and pcb.purchase_requirement_id = #{requirementId}
        group by pcb.purchase_requirement_id
    </select>
</mapper>