<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PurchaseContractStandardTermsDeviationMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="association_purchase_terms_id" jdbcType="BIGINT" property="associationPurchaseTermsId" />
    <result column="deviation_info" jdbcType="VARCHAR" property="deviationInfo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, association_purchase_terms_id, deviation_info, remark, sort, create_by, create_at, 
    update_by, update_at, version, deleted_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from purchase_contract_standard_terms_deviation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_contract_standard_terms_deviation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_contract_standard_terms_deviation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviation">
    insert into purchase_contract_standard_terms_deviation (id, association_purchase_terms_id, deviation_info, 
      remark, sort, create_by, 
      create_at, update_by, update_at, 
      version, deleted_flag)
    values (#{id,jdbcType=BIGINT}, #{associationPurchaseTermsId,jdbcType=BIGINT}, #{deviationInfo,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, 
      #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, 
      #{version,jdbcType=BIGINT}, #{deletedFlag,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviation">
    insert into purchase_contract_standard_terms_deviation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="associationPurchaseTermsId != null">
        association_purchase_terms_id,
      </if>
      <if test="deviationInfo != null">
        deviation_info,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="associationPurchaseTermsId != null">
        #{associationPurchaseTermsId,jdbcType=BIGINT},
      </if>
      <if test="deviationInfo != null">
        #{deviationInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviationExample" resultType="java.lang.Long">
    select count(*) from purchase_contract_standard_terms_deviation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviation">
    update purchase_contract_standard_terms_deviation
    <set>
      <if test="associationPurchaseTermsId != null">
        association_purchase_terms_id = #{associationPurchaseTermsId,jdbcType=BIGINT},
      </if>
      <if test="deviationInfo != null">
        deviation_info = #{deviationInfo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviation">
    update purchase_contract_standard_terms_deviation
    set association_purchase_terms_id = #{associationPurchaseTermsId,jdbcType=BIGINT},
      deviation_info = #{deviationInfo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=BIGINT},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>