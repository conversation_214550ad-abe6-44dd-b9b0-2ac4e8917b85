<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.MilepostDesignPlanDetailSubmitHistoryMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_budget_material_id" jdbcType="BIGINT" property="projectBudgetMaterialId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="whether_model" jdbcType="TINYINT" property="whetherModel" />
    <result column="module_status" jdbcType="INTEGER" property="moduleStatus" />
    <result column="ext" jdbcType="TINYINT" property="ext" />
    <result column="materiel_descr" jdbcType="VARCHAR" property="materielDescr" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="unit_code" jdbcType="VARCHAR" property="unitCode" />
    <result column="number" jdbcType="DECIMAL" property="number" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="business_classification" jdbcType="VARCHAR" property="businessClassification" />
    <result column="material_classification" jdbcType="VARCHAR" property="materialClassification" />
    <result column="materiel_type" jdbcType="VARCHAR" property="materielType" />
    <result column="machining_part_type" jdbcType="VARCHAR" property="machiningPartType" />
    <result column="material" jdbcType="VARCHAR" property="material" />
    <result column="unit_weight" jdbcType="DECIMAL" property="unitWeight" />
    <result column="material_processing" jdbcType="VARCHAR" property="materialProcessing" />
    <result column="budget_unit_price" jdbcType="DECIMAL" property="budgetUnitPrice" />
    <result column="design_cost_id" jdbcType="BIGINT" property="designCostId" />
    <result column="design_cost" jdbcType="DECIMAL" property="designCost" />
    <result column="budget_subtotal" jdbcType="DECIMAL" property="budgetSubtotal" />
    <result column="pam_code" jdbcType="VARCHAR" property="pamCode" />
    <result column="erp_code" jdbcType="VARCHAR" property="erpCode" />
    <result column="erp_code_source" jdbcType="INTEGER" property="erpCodeSource" />
    <result column="materiel_status" jdbcType="INTEGER" property="materielStatus" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="index_num" jdbcType="VARCHAR" property="indexNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="generate_requirement" jdbcType="TINYINT" property="generateRequirement" />
    <result column="init" jdbcType="TINYINT" property="init" />
    <result column="item_cost_is_null" jdbcType="TINYINT" property="itemCostIsNull" />
    <result column="material_category" jdbcType="VARCHAR" property="materialCategory" />
    <result column="coding_middle_class" jdbcType="VARCHAR" property="codingMiddleClass" />
    <result column="figure_number" jdbcType="VARCHAR" property="figureNumber" />
    <result column="chart_version" jdbcType="VARCHAR" property="chartVersion" />
    <result column="brand_material_code" jdbcType="VARCHAR" property="brandMaterialCode" />
    <result column="or_spare_parts_mask" jdbcType="VARCHAR" property="orSparePartsMask" />
    <result column="requirement_creat_date" jdbcType="DATE" property="requirementCreatDate" />
    <result column="perchasing_leadtime" jdbcType="BIGINT" property="perchasingLeadtime" />
    <result column="min_perchase_quantity" jdbcType="BIGINT" property="minPerchaseQuantity" />
    <result column="min_package_quantity" jdbcType="BIGINT" property="minPackageQuantity" />
    <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
    <result column="wbs_layer" jdbcType="VARCHAR" property="wbsLayer" />
    <result column="wbs_confirm_flag" jdbcType="TINYINT" property="wbsConfirmFlag" />
    <result column="dispatch_is" jdbcType="TINYINT" property="dispatchIs" />
    <result column="ext_is" jdbcType="TINYINT" property="extIs" />
    <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
    <result column="project_budget_type" jdbcType="VARCHAR" property="projectBudgetType" />
    <result column="plan_designer" jdbcType="VARCHAR" property="planDesigner" />
    <result column="design_release_lot_number" jdbcType="VARCHAR" property="designReleaseLotNumber" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="history_type" jdbcType="TINYINT" property="historyType" />
    <result column="project_wbs_receipts_id" jdbcType="BIGINT" property="projectWbsReceiptsId" />
    <result column="design_plan_detail_id" jdbcType="BIGINT" property="designPlanDetailId" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="wbs_last_layer" jdbcType="TINYINT" property="wbsLastLayer" />
    <result column="requirement_type" jdbcType="INTEGER" property="requirementType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_budget_material_id, project_id, parent_id, level, num, whether_model, 
    module_status, ext, materiel_descr, unit, unit_code, number, delivery_time, name, 
    model, brand, business_classification, material_classification, materiel_type, machining_part_type, 
    material, unit_weight, material_processing, budget_unit_price, design_cost_id, design_cost, 
    budget_subtotal, pam_code, erp_code, erp_code_source, materiel_status, source, remark, 
    index_num, status, generate_requirement, init, item_cost_is_null, material_category, 
    coding_middle_class, figure_number, chart_version, brand_material_code, or_spare_parts_mask, 
    requirement_creat_date, perchasing_leadtime, min_perchase_quantity, min_package_quantity, 
    wbs_summary_code, wbs_layer, wbs_confirm_flag, dispatch_is, ext_is, activity_code, 
    project_budget_type, plan_designer, design_release_lot_number, description, history_type, 
    project_wbs_receipts_id, design_plan_detail_id, deleted_flag, create_by, create_at, 
    update_by, update_at, wbs_last_layer, requirement_type
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from milepost_design_plan_detail_submit_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from milepost_design_plan_detail_submit_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from milepost_design_plan_detail_submit_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory">
    insert into milepost_design_plan_detail_submit_history (id, project_budget_material_id, project_id, 
      parent_id, level, num, 
      whether_model, module_status, ext, 
      materiel_descr, unit, unit_code, 
      number, delivery_time, name, 
      model, brand, business_classification, 
      material_classification, materiel_type, machining_part_type, 
      material, unit_weight, material_processing, 
      budget_unit_price, design_cost_id, design_cost, 
      budget_subtotal, pam_code, erp_code, 
      erp_code_source, materiel_status, source, 
      remark, index_num, status, 
      generate_requirement, init, item_cost_is_null, 
      material_category, coding_middle_class, figure_number, 
      chart_version, brand_material_code, or_spare_parts_mask, 
      requirement_creat_date, perchasing_leadtime, min_perchase_quantity, 
      min_package_quantity, wbs_summary_code, wbs_layer, 
      wbs_confirm_flag, dispatch_is, ext_is, 
      activity_code, project_budget_type, plan_designer, 
      design_release_lot_number, description, history_type, 
      project_wbs_receipts_id, design_plan_detail_id, 
      deleted_flag, create_by, create_at, 
      update_by, update_at, wbs_last_layer, 
      requirement_type)
    values (#{id,jdbcType=BIGINT}, #{projectBudgetMaterialId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, 
      #{parentId,jdbcType=BIGINT}, #{level,jdbcType=INTEGER}, #{num,jdbcType=VARCHAR}, 
      #{whetherModel,jdbcType=TINYINT}, #{moduleStatus,jdbcType=INTEGER}, #{ext,jdbcType=TINYINT}, 
      #{materielDescr,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{unitCode,jdbcType=VARCHAR}, 
      #{number,jdbcType=DECIMAL}, #{deliveryTime,jdbcType=TIMESTAMP}, #{name,jdbcType=VARCHAR}, 
      #{model,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{businessClassification,jdbcType=VARCHAR}, 
      #{materialClassification,jdbcType=VARCHAR}, #{materielType,jdbcType=VARCHAR}, #{machiningPartType,jdbcType=VARCHAR}, 
      #{material,jdbcType=VARCHAR}, #{unitWeight,jdbcType=DECIMAL}, #{materialProcessing,jdbcType=VARCHAR}, 
      #{budgetUnitPrice,jdbcType=DECIMAL}, #{designCostId,jdbcType=BIGINT}, #{designCost,jdbcType=DECIMAL}, 
      #{budgetSubtotal,jdbcType=DECIMAL}, #{pamCode,jdbcType=VARCHAR}, #{erpCode,jdbcType=VARCHAR}, 
      #{erpCodeSource,jdbcType=INTEGER}, #{materielStatus,jdbcType=INTEGER}, #{source,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{indexNum,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{generateRequirement,jdbcType=TINYINT}, #{init,jdbcType=TINYINT}, #{itemCostIsNull,jdbcType=TINYINT}, 
      #{materialCategory,jdbcType=VARCHAR}, #{codingMiddleClass,jdbcType=VARCHAR}, #{figureNumber,jdbcType=VARCHAR}, 
      #{chartVersion,jdbcType=VARCHAR}, #{brandMaterialCode,jdbcType=VARCHAR}, #{orSparePartsMask,jdbcType=VARCHAR}, 
      #{requirementCreatDate,jdbcType=DATE}, #{perchasingLeadtime,jdbcType=BIGINT}, #{minPerchaseQuantity,jdbcType=BIGINT}, 
      #{minPackageQuantity,jdbcType=BIGINT}, #{wbsSummaryCode,jdbcType=VARCHAR}, #{wbsLayer,jdbcType=VARCHAR}, 
      #{wbsConfirmFlag,jdbcType=TINYINT}, #{dispatchIs,jdbcType=TINYINT}, #{extIs,jdbcType=TINYINT}, 
      #{activityCode,jdbcType=VARCHAR}, #{projectBudgetType,jdbcType=VARCHAR}, #{planDesigner,jdbcType=VARCHAR}, 
      #{designReleaseLotNumber,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{historyType,jdbcType=TINYINT}, 
      #{projectWbsReceiptsId,jdbcType=BIGINT}, #{designPlanDetailId,jdbcType=BIGINT}, 
      #{deletedFlag,jdbcType=TINYINT}, #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{wbsLastLayer,jdbcType=TINYINT}, 
      #{requirementType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory">
    insert into milepost_design_plan_detail_submit_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectBudgetMaterialId != null">
        project_budget_material_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="whetherModel != null">
        whether_model,
      </if>
      <if test="moduleStatus != null">
        module_status,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="materielDescr != null">
        materiel_descr,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="unitCode != null">
        unit_code,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="businessClassification != null">
        business_classification,
      </if>
      <if test="materialClassification != null">
        material_classification,
      </if>
      <if test="materielType != null">
        materiel_type,
      </if>
      <if test="machiningPartType != null">
        machining_part_type,
      </if>
      <if test="material != null">
        material,
      </if>
      <if test="unitWeight != null">
        unit_weight,
      </if>
      <if test="materialProcessing != null">
        material_processing,
      </if>
      <if test="budgetUnitPrice != null">
        budget_unit_price,
      </if>
      <if test="designCostId != null">
        design_cost_id,
      </if>
      <if test="designCost != null">
        design_cost,
      </if>
      <if test="budgetSubtotal != null">
        budget_subtotal,
      </if>
      <if test="pamCode != null">
        pam_code,
      </if>
      <if test="erpCode != null">
        erp_code,
      </if>
      <if test="erpCodeSource != null">
        erp_code_source,
      </if>
      <if test="materielStatus != null">
        materiel_status,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="indexNum != null">
        index_num,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="generateRequirement != null">
        generate_requirement,
      </if>
      <if test="init != null">
        init,
      </if>
      <if test="itemCostIsNull != null">
        item_cost_is_null,
      </if>
      <if test="materialCategory != null">
        material_category,
      </if>
      <if test="codingMiddleClass != null">
        coding_middle_class,
      </if>
      <if test="figureNumber != null">
        figure_number,
      </if>
      <if test="chartVersion != null">
        chart_version,
      </if>
      <if test="brandMaterialCode != null">
        brand_material_code,
      </if>
      <if test="orSparePartsMask != null">
        or_spare_parts_mask,
      </if>
      <if test="requirementCreatDate != null">
        requirement_creat_date,
      </if>
      <if test="perchasingLeadtime != null">
        perchasing_leadtime,
      </if>
      <if test="minPerchaseQuantity != null">
        min_perchase_quantity,
      </if>
      <if test="minPackageQuantity != null">
        min_package_quantity,
      </if>
      <if test="wbsSummaryCode != null">
        wbs_summary_code,
      </if>
      <if test="wbsLayer != null">
        wbs_layer,
      </if>
      <if test="wbsConfirmFlag != null">
        wbs_confirm_flag,
      </if>
      <if test="dispatchIs != null">
        dispatch_is,
      </if>
      <if test="extIs != null">
        ext_is,
      </if>
      <if test="activityCode != null">
        activity_code,
      </if>
      <if test="projectBudgetType != null">
        project_budget_type,
      </if>
      <if test="planDesigner != null">
        plan_designer,
      </if>
      <if test="designReleaseLotNumber != null">
        design_release_lot_number,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="historyType != null">
        history_type,
      </if>
      <if test="projectWbsReceiptsId != null">
        project_wbs_receipts_id,
      </if>
      <if test="designPlanDetailId != null">
        design_plan_detail_id,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="wbsLastLayer != null">
        wbs_last_layer,
      </if>
      <if test="requirementType != null">
        requirement_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectBudgetMaterialId != null">
        #{projectBudgetMaterialId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="whetherModel != null">
        #{whetherModel,jdbcType=TINYINT},
      </if>
      <if test="moduleStatus != null">
        #{moduleStatus,jdbcType=INTEGER},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=TINYINT},
      </if>
      <if test="materielDescr != null">
        #{materielDescr,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=DECIMAL},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="businessClassification != null">
        #{businessClassification,jdbcType=VARCHAR},
      </if>
      <if test="materialClassification != null">
        #{materialClassification,jdbcType=VARCHAR},
      </if>
      <if test="materielType != null">
        #{materielType,jdbcType=VARCHAR},
      </if>
      <if test="machiningPartType != null">
        #{machiningPartType,jdbcType=VARCHAR},
      </if>
      <if test="material != null">
        #{material,jdbcType=VARCHAR},
      </if>
      <if test="unitWeight != null">
        #{unitWeight,jdbcType=DECIMAL},
      </if>
      <if test="materialProcessing != null">
        #{materialProcessing,jdbcType=VARCHAR},
      </if>
      <if test="budgetUnitPrice != null">
        #{budgetUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="designCostId != null">
        #{designCostId,jdbcType=BIGINT},
      </if>
      <if test="designCost != null">
        #{designCost,jdbcType=DECIMAL},
      </if>
      <if test="budgetSubtotal != null">
        #{budgetSubtotal,jdbcType=DECIMAL},
      </if>
      <if test="pamCode != null">
        #{pamCode,jdbcType=VARCHAR},
      </if>
      <if test="erpCode != null">
        #{erpCode,jdbcType=VARCHAR},
      </if>
      <if test="erpCodeSource != null">
        #{erpCodeSource,jdbcType=INTEGER},
      </if>
      <if test="materielStatus != null">
        #{materielStatus,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="indexNum != null">
        #{indexNum,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="generateRequirement != null">
        #{generateRequirement,jdbcType=TINYINT},
      </if>
      <if test="init != null">
        #{init,jdbcType=TINYINT},
      </if>
      <if test="itemCostIsNull != null">
        #{itemCostIsNull,jdbcType=TINYINT},
      </if>
      <if test="materialCategory != null">
        #{materialCategory,jdbcType=VARCHAR},
      </if>
      <if test="codingMiddleClass != null">
        #{codingMiddleClass,jdbcType=VARCHAR},
      </if>
      <if test="figureNumber != null">
        #{figureNumber,jdbcType=VARCHAR},
      </if>
      <if test="chartVersion != null">
        #{chartVersion,jdbcType=VARCHAR},
      </if>
      <if test="brandMaterialCode != null">
        #{brandMaterialCode,jdbcType=VARCHAR},
      </if>
      <if test="orSparePartsMask != null">
        #{orSparePartsMask,jdbcType=VARCHAR},
      </if>
      <if test="requirementCreatDate != null">
        #{requirementCreatDate,jdbcType=DATE},
      </if>
      <if test="perchasingLeadtime != null">
        #{perchasingLeadtime,jdbcType=BIGINT},
      </if>
      <if test="minPerchaseQuantity != null">
        #{minPerchaseQuantity,jdbcType=BIGINT},
      </if>
      <if test="minPackageQuantity != null">
        #{minPackageQuantity,jdbcType=BIGINT},
      </if>
      <if test="wbsSummaryCode != null">
        #{wbsSummaryCode,jdbcType=VARCHAR},
      </if>
      <if test="wbsLayer != null">
        #{wbsLayer,jdbcType=VARCHAR},
      </if>
      <if test="wbsConfirmFlag != null">
        #{wbsConfirmFlag,jdbcType=TINYINT},
      </if>
      <if test="dispatchIs != null">
        #{dispatchIs,jdbcType=TINYINT},
      </if>
      <if test="extIs != null">
        #{extIs,jdbcType=TINYINT},
      </if>
      <if test="activityCode != null">
        #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="projectBudgetType != null">
        #{projectBudgetType,jdbcType=VARCHAR},
      </if>
      <if test="planDesigner != null">
        #{planDesigner,jdbcType=VARCHAR},
      </if>
      <if test="designReleaseLotNumber != null">
        #{designReleaseLotNumber,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="historyType != null">
        #{historyType,jdbcType=TINYINT},
      </if>
      <if test="projectWbsReceiptsId != null">
        #{projectWbsReceiptsId,jdbcType=BIGINT},
      </if>
      <if test="designPlanDetailId != null">
        #{designPlanDetailId,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="wbsLastLayer != null">
        #{wbsLastLayer,jdbcType=TINYINT},
      </if>
      <if test="requirementType != null">
        #{requirementType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistoryExample" resultType="java.lang.Long">
    select count(*) from milepost_design_plan_detail_submit_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory">
    update milepost_design_plan_detail_submit_history
    <set>
      <if test="projectBudgetMaterialId != null">
        project_budget_material_id = #{projectBudgetMaterialId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=VARCHAR},
      </if>
      <if test="whetherModel != null">
        whether_model = #{whetherModel,jdbcType=TINYINT},
      </if>
      <if test="moduleStatus != null">
        module_status = #{moduleStatus,jdbcType=INTEGER},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=TINYINT},
      </if>
      <if test="materielDescr != null">
        materiel_descr = #{materielDescr,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        unit_code = #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=DECIMAL},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="businessClassification != null">
        business_classification = #{businessClassification,jdbcType=VARCHAR},
      </if>
      <if test="materialClassification != null">
        material_classification = #{materialClassification,jdbcType=VARCHAR},
      </if>
      <if test="materielType != null">
        materiel_type = #{materielType,jdbcType=VARCHAR},
      </if>
      <if test="machiningPartType != null">
        machining_part_type = #{machiningPartType,jdbcType=VARCHAR},
      </if>
      <if test="material != null">
        material = #{material,jdbcType=VARCHAR},
      </if>
      <if test="unitWeight != null">
        unit_weight = #{unitWeight,jdbcType=DECIMAL},
      </if>
      <if test="materialProcessing != null">
        material_processing = #{materialProcessing,jdbcType=VARCHAR},
      </if>
      <if test="budgetUnitPrice != null">
        budget_unit_price = #{budgetUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="designCostId != null">
        design_cost_id = #{designCostId,jdbcType=BIGINT},
      </if>
      <if test="designCost != null">
        design_cost = #{designCost,jdbcType=DECIMAL},
      </if>
      <if test="budgetSubtotal != null">
        budget_subtotal = #{budgetSubtotal,jdbcType=DECIMAL},
      </if>
      <if test="pamCode != null">
        pam_code = #{pamCode,jdbcType=VARCHAR},
      </if>
      <if test="erpCode != null">
        erp_code = #{erpCode,jdbcType=VARCHAR},
      </if>
      <if test="erpCodeSource != null">
        erp_code_source = #{erpCodeSource,jdbcType=INTEGER},
      </if>
      <if test="materielStatus != null">
        materiel_status = #{materielStatus,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="indexNum != null">
        index_num = #{indexNum,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="generateRequirement != null">
        generate_requirement = #{generateRequirement,jdbcType=TINYINT},
      </if>
      <if test="init != null">
        init = #{init,jdbcType=TINYINT},
      </if>
      <if test="itemCostIsNull != null">
        item_cost_is_null = #{itemCostIsNull,jdbcType=TINYINT},
      </if>
      <if test="materialCategory != null">
        material_category = #{materialCategory,jdbcType=VARCHAR},
      </if>
      <if test="codingMiddleClass != null">
        coding_middle_class = #{codingMiddleClass,jdbcType=VARCHAR},
      </if>
      <if test="figureNumber != null">
        figure_number = #{figureNumber,jdbcType=VARCHAR},
      </if>
      <if test="chartVersion != null">
        chart_version = #{chartVersion,jdbcType=VARCHAR},
      </if>
      <if test="brandMaterialCode != null">
        brand_material_code = #{brandMaterialCode,jdbcType=VARCHAR},
      </if>
      <if test="orSparePartsMask != null">
        or_spare_parts_mask = #{orSparePartsMask,jdbcType=VARCHAR},
      </if>
      <if test="requirementCreatDate != null">
        requirement_creat_date = #{requirementCreatDate,jdbcType=DATE},
      </if>
      <if test="perchasingLeadtime != null">
        perchasing_leadtime = #{perchasingLeadtime,jdbcType=BIGINT},
      </if>
      <if test="minPerchaseQuantity != null">
        min_perchase_quantity = #{minPerchaseQuantity,jdbcType=BIGINT},
      </if>
      <if test="minPackageQuantity != null">
        min_package_quantity = #{minPackageQuantity,jdbcType=BIGINT},
      </if>
      <if test="wbsSummaryCode != null">
        wbs_summary_code = #{wbsSummaryCode,jdbcType=VARCHAR},
      </if>
      <if test="wbsLayer != null">
        wbs_layer = #{wbsLayer,jdbcType=VARCHAR},
      </if>
      <if test="wbsConfirmFlag != null">
        wbs_confirm_flag = #{wbsConfirmFlag,jdbcType=TINYINT},
      </if>
      <if test="dispatchIs != null">
        dispatch_is = #{dispatchIs,jdbcType=TINYINT},
      </if>
      <if test="extIs != null">
        ext_is = #{extIs,jdbcType=TINYINT},
      </if>
      <if test="activityCode != null">
        activity_code = #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="projectBudgetType != null">
        project_budget_type = #{projectBudgetType,jdbcType=VARCHAR},
      </if>
      <if test="planDesigner != null">
        plan_designer = #{planDesigner,jdbcType=VARCHAR},
      </if>
      <if test="designReleaseLotNumber != null">
        design_release_lot_number = #{designReleaseLotNumber,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="historyType != null">
        history_type = #{historyType,jdbcType=TINYINT},
      </if>
      <if test="projectWbsReceiptsId != null">
        project_wbs_receipts_id = #{projectWbsReceiptsId,jdbcType=BIGINT},
      </if>
      <if test="designPlanDetailId != null">
        design_plan_detail_id = #{designPlanDetailId,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="wbsLastLayer != null">
        wbs_last_layer = #{wbsLastLayer,jdbcType=TINYINT},
      </if>
      <if test="requirementType != null">
        requirement_type = #{requirementType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory">
    update milepost_design_plan_detail_submit_history
    set project_budget_material_id = #{projectBudgetMaterialId,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=BIGINT},
      parent_id = #{parentId,jdbcType=BIGINT},
      level = #{level,jdbcType=INTEGER},
      num = #{num,jdbcType=VARCHAR},
      whether_model = #{whetherModel,jdbcType=TINYINT},
      module_status = #{moduleStatus,jdbcType=INTEGER},
      ext = #{ext,jdbcType=TINYINT},
      materiel_descr = #{materielDescr,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      unit_code = #{unitCode,jdbcType=VARCHAR},
      number = #{number,jdbcType=DECIMAL},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      name = #{name,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      business_classification = #{businessClassification,jdbcType=VARCHAR},
      material_classification = #{materialClassification,jdbcType=VARCHAR},
      materiel_type = #{materielType,jdbcType=VARCHAR},
      machining_part_type = #{machiningPartType,jdbcType=VARCHAR},
      material = #{material,jdbcType=VARCHAR},
      unit_weight = #{unitWeight,jdbcType=DECIMAL},
      material_processing = #{materialProcessing,jdbcType=VARCHAR},
      budget_unit_price = #{budgetUnitPrice,jdbcType=DECIMAL},
      design_cost_id = #{designCostId,jdbcType=BIGINT},
      design_cost = #{designCost,jdbcType=DECIMAL},
      budget_subtotal = #{budgetSubtotal,jdbcType=DECIMAL},
      pam_code = #{pamCode,jdbcType=VARCHAR},
      erp_code = #{erpCode,jdbcType=VARCHAR},
      erp_code_source = #{erpCodeSource,jdbcType=INTEGER},
      materiel_status = #{materielStatus,jdbcType=INTEGER},
      source = #{source,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      index_num = #{indexNum,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      generate_requirement = #{generateRequirement,jdbcType=TINYINT},
      init = #{init,jdbcType=TINYINT},
      item_cost_is_null = #{itemCostIsNull,jdbcType=TINYINT},
      material_category = #{materialCategory,jdbcType=VARCHAR},
      coding_middle_class = #{codingMiddleClass,jdbcType=VARCHAR},
      figure_number = #{figureNumber,jdbcType=VARCHAR},
      chart_version = #{chartVersion,jdbcType=VARCHAR},
      brand_material_code = #{brandMaterialCode,jdbcType=VARCHAR},
      or_spare_parts_mask = #{orSparePartsMask,jdbcType=VARCHAR},
      requirement_creat_date = #{requirementCreatDate,jdbcType=DATE},
      perchasing_leadtime = #{perchasingLeadtime,jdbcType=BIGINT},
      min_perchase_quantity = #{minPerchaseQuantity,jdbcType=BIGINT},
      min_package_quantity = #{minPackageQuantity,jdbcType=BIGINT},
      wbs_summary_code = #{wbsSummaryCode,jdbcType=VARCHAR},
      wbs_layer = #{wbsLayer,jdbcType=VARCHAR},
      wbs_confirm_flag = #{wbsConfirmFlag,jdbcType=TINYINT},
      dispatch_is = #{dispatchIs,jdbcType=TINYINT},
      ext_is = #{extIs,jdbcType=TINYINT},
      activity_code = #{activityCode,jdbcType=VARCHAR},
      project_budget_type = #{projectBudgetType,jdbcType=VARCHAR},
      plan_designer = #{planDesigner,jdbcType=VARCHAR},
      design_release_lot_number = #{designReleaseLotNumber,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      history_type = #{historyType,jdbcType=TINYINT},
      project_wbs_receipts_id = #{projectWbsReceiptsId,jdbcType=BIGINT},
      design_plan_detail_id = #{designPlanDetailId,jdbcType=BIGINT},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      wbs_last_layer = #{wbsLastLayer,jdbcType=TINYINT},
      requirement_type = #{requirementType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>