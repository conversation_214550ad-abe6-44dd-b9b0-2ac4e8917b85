<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.WbsTemplateInfoExtMapper">
    <resultMap id="BaseResultDtoMap" type="com.midea.pam.common.ctc.dto.WbsTemplateInfoDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="template_name" jdbcType="VARCHAR" property="templateName"/>
        <result column="template_enabled" jdbcType="TINYINT" property="templateEnabled"/>
        <result column="template_description" jdbcType="VARCHAR" property="templateDescription"/>
        <result column="unit_id" jdbcType="BIGINT" property="unitId"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag"/>
        <result column="version" jdbcType="BIGINT" property="version"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, template_name, template_enabled, template_description, unit_id, create_by, create_at,
        update_by, update_at, deleted_flag, version , describe_display
    </sql>

    <!-- 根据id查询wbs模板信息 -->
    <select id="findById" resultMap="BaseResultDtoMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wbs_template_info t
        WHERE t.deleted_flag = 0
        AND t.id = #{id}
    </select>

    <!-- 查询wbs模板信息列表 -->
    <select id="selectList" parameterType="com.midea.pam.common.ctc.dto.WbsTemplateInfoDto" resultMap="BaseResultDtoMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wbs_template_info t
        <where>
            <if test="templateName != null and templateName != ''">
                AND t.template_name like concat('%', #{templateName}, '%')
            </if>
            <if test="deletedFlag != null">
                AND t.deleted_flag = #{deletedFlag,jdbcType=TINYINT}
            </if>
            <if test="unitIds != null and unitIds.size > 0">
                AND t.unit_id in
                <foreach collection="unitIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY t.create_at DESC
    </select>

    <!-- 费用类型下拉查询（WBS方式） -->
    <select id="feeItemJoinQuery" parameterType="com.midea.pam.common.ctc.vo.FeeItemFeeSettingModeDropdownBoxVO" resultType="com.midea.pam.common.ctc.vo.FeeItemFeeSettingModeDropdownBoxVO">
        select
            t3.code,
            t1.template_name as name,
            t1.template_name as wbsTemplateName,
            t1.id as wbsTemplateInfoId,
            t2.id as wbsTemplateRuleId,
            t3.description
        from
            pam_ctc.wbs_template_rule_detail t3,
            pam_ctc.wbs_template_rule t2,
            pam_ctc.wbs_template_info t1
        where
            t1.deleted_flag = 0
            and t2.deleted_flag = 0
            and t3.deleted_flag = 0
            and t3.wbs_template_rule_id = t2.id
            and t2.wbs_template_info_id = t1.id
            and t3.level = '明细'
            and t1.template_enabled = 1
            and (t3.start_time is null or t3.start_time &lt;= now())
            and (t3.end_time is null or t3.end_time &gt;= now())
        <if test="unitId != null">
            and t1.unit_id = #{unitId,jdbcType=BIGINT}
        </if>
        <if test="name != null and name != ''">
            and t1.template_name like concat('%', #{name,jdbcType=VARCHAR}, '%')
        </if>
        <if test="code != null and code != ''">
            and t3.code like concat('%', #{code,jdbcType=VARCHAR}, '%')
        </if>
        <if test="wbsTemplateRuleId != null">
            and t2.id = #{wbsTemplateRuleId}
        </if>
    </select>
</mapper>