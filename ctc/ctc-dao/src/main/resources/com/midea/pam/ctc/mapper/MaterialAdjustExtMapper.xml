<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.MaterialAdjustExtMapper">

    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.MaterialAdjustHeader">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
        <result column="organization_id" jdbcType="BIGINT" property="organizationId" />
        <result column="ou_id" jdbcType="BIGINT" property="ouId" />
        <result column="adjust_type" jdbcType="TINYINT" property="adjustType" />
        <result column="apply_by" jdbcType="BIGINT" property="applyBy" />
        <result column="apply_by_name" jdbcType="VARCHAR" property="applyByName" />
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="resource" jdbcType="TINYINT" property="resource" />
        <result column="sync_status" jdbcType="TINYINT" property="syncStatus" />
        <result column="sync_mes" jdbcType="VARCHAR" property="syncMes" />
        <result column="adjust_reason_type" jdbcType="VARCHAR" property="adjustReasonType" />
        <result column="adjust_reason_des" jdbcType="VARCHAR" property="adjustReasonDes" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="annex" jdbcType="VARCHAR" property="annex" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="milepost_id" jdbcType="BIGINT" property="milepostId" />
        <result column="attribute1" jdbcType="VARCHAR" property="attribute1" />
        <result column="attribute2" jdbcType="VARCHAR" property="attribute2" />
        <result column="attribute3" jdbcType="VARCHAR" property="attribute3" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="handle_by" jdbcType="BIGINT" property="handleBy" />
        <result column="handle_by_name" jdbcType="VARCHAR" property="handleByName" />
        <result column="handle_at" jdbcType="TIMESTAMP" property="handleAt" />
        <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
        <result column="reject_at" jdbcType="TIMESTAMP" property="rejectAt" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Example_Where_Clause_Modified">
        <foreach collection="example.oredCriteria" item="criteria" separator="or">
            <if test="criteria.valid">
                <trim prefix="(" prefixOverrides="and" suffix=")">
                    <foreach collection="criteria.criteria" item="criterion">
                        <choose>
                            <when test="criterion.noValue">
                                and ${criterion.condition}
                            </when>
                            <when test="criterion.singleValue">
                                and ${criterion.condition} #{criterion.value}
                            </when>
                            <when test="criterion.betweenValue">
                                and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                            </when>
                            <when test="criterion.listValue">
                                and ${criterion.condition}
                                <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                    #{listItem}
                                </foreach>
                            </when>
                        </choose>
                    </foreach>
                </trim>
            </if>
        </foreach>
    </sql>

    <sql id="Base_Column_List">
        id, adjust_code, organization_id, ou_id, adjust_type, apply_by, apply_by_name, apply_time,
        status, resource, sync_status, sync_mes, adjust_reason_type, adjust_reason_des, remark,
        annex, project_id, milepost_id, attribute1, attribute2, attribute3, create_by, create_at,
        update_by, update_at, deleted_flag, handle_by, handle_by_name, handle_at, reject_reason,
        reject_at
    </sql>

    <delete id="deleteMaterialAdjustDetailByHeaderId" parameterType="java.lang.Long">
        delete from material_adjust_detail
        where header_id = #{headerId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteMaterialChangeDetailByHeaderId" parameterType="java.lang.Long">
        delete from material_change_detail
        where header_id = #{headerId,jdbcType=BIGINT}
    </delete>

    <update id="deleteHeaderDraft" parameterType="java.lang.Long">
        update material_adjust_header set deleted_flag = 1
        where id = #{id} and deleted_flag = 0 and status = 0
    </update>

    <update id="deleteDetailDraft" parameterType="java.lang.Long">
        update material_adjust_detail set deleted_flag = 1
        where header_id = #{id} and deleted_flag = 0
    </update>

    <update id="updateDetailSyncInfo" parameterType="com.midea.pam.common.ctc.entity.MaterialAdjustDetail">
        update material_adjust_detail set sync_status = #{syncStatus}, sync_mes = #{syncMes}
        where deleted_flag = 0 and pam_code = #{pamCode} and erp_code = #{erpCode}
    </update>

    <select id="listHeaderNeedUpdateSyncInfo" resultType="com.midea.pam.common.ctc.entity.MaterialAdjustHeader">
        select
        header_id as id,
        sum(sync_result) as syncStatus
        from (
        select
        header_id,
        case when sync_status = 1 then 1 else 0 end as sync_result
        from pam_ctc.material_adjust_detail
        where erp_code is not null and deleted_flag = 0
        and pam_code in
        <foreach collection="pamCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and erp_code in
        <foreach collection="erpCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        ) r group by header_id
    </select>

    <select id="selectMaterialDetail" resultType="com.midea.pam.common.ctc.vo.MaterialChangeTypeVo">
        select ifnull(mmh.actual_amount, 0) as pickedMaterial,mdpd4.pam_code as module,mm.* from (
        select
        distinct
        m.id as materialId,
        mmr.id as designId,
        mmr.parent_id as parentId,
        mmr.pam_code as pamCode,
        mmr.erp_code as erpCode,
        mmr.materiel_descr as materielDescr,
        mmr.unit as unit,
        mmr.material_category as materialCategory,
        mmr.coding_middle_class as codingMiddleClass,
        mmr.materiel_type as materielType,
        p.code as projectName,
        pmrd.requirementNum,
        ifnull(mmr.number,0) as number,
        case
        when mmr.material_category = '装配件'
        or mmr.material_category = '虚拟件' then tt.ticket_task_code
        when mmr.material_category = '外购物料'
        or mmr.material_category = '看板物料' then ttt.ticket_task_code
        else null
        end as ticketTaskCode,
        ROUND(ifnull(mc.item_cost, 0), 6) as price,
        ROUND((ifnull(mmr.number,0) * ifnull(mc.item_cost, 0)), 6) as totalPrice
        from
        (with recursive mdpd as (
        select
        *
        from
        pam_ctc.milepost_design_plan_detail
        where
        id in (
        select
        mdpd2.id
        from
        pam_ctc.milepost_design_plan_detail mdpd2
        where mdpd2.pam_code = #{pamCode}
        <if test="idList != null and idList.size>0">
            and mdpd2.project_id in
            <foreach collection="idList" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        )
        union
        select
        a.*
        from
        pam_ctc.milepost_design_plan_detail a
        join mdpd
        where
        a.parent_id = mdpd.id)
        select
        mdpd.*
        from
        mdpd where mdpd.deleted_flag != 1 and (mdpd.status not in (-2,-1,0) or mdpd.status is null or mdpd.status = ''))
        mmr
        left join pam_ctc.project p on
        mmr.project_id = p.id
        left join pam_basedata.material_cost mc on
        mmr.design_cost_id = mc.id
        left join (
        select
        sum(publish_num) as requirementNum ,
        design_plan_detail_id
        from
        pam_ctc.purchase_material_release_detail
        group by
        design_plan_detail_id ) pmrd on
        mmr.id = pmrd.design_plan_detail_id
        left join pam_ctc.ticket_tasks tt on
        mmr.id = tt.milepost_design_detail_id
        left join (
        select
        tt2.ticket_task_code ,
        ttd.milepost_design_detail_id
        from
        pam_ctc.ticket_tasks_detail ttd
        left join pam_ctc.ticket_tasks tt2 on
        ttd.ticket_tasks_id = tt2.id)ttt on
        mmr.id = ttt.milepost_design_detail_id
        left join (select id,pam_code from pam_basedata.material where organization_id = #{organizationId}) m on
        mmr.pam_code = m.pam_code
        )mm left join (
        select
        mgd.actual_amount,
        mgd.material_id
        from
        pam_ctc.material_get_detail mgd
        left join pam_ctc.material_get_header mah on
        mah.id = mgd.header_id) mmh on
        mm.materialId = mmh.material_id
        left join (with recursive mdpd as (
        select
        *
        from
        pam_ctc.milepost_design_plan_detail
        where
        id in (select
        mdpd5.id
        from
        pam_ctc.milepost_design_plan_detail mdpd5
        left join pam_ctc.project p on
        mdpd5.project_id = p.id
        where mdpd5.pam_code = #{pamCode}
        <if test="idList != null and idList.size>0">
            and mdpd5.project_id in
            <foreach collection="idList" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        )
        union
        select a.*
        from pam_ctc.milepost_design_plan_detail a
        join mdpd
        where a.id = mdpd.parent_id )
        select mdpd.*
        from mdpd
        where mdpd.whether_model = 1 and mdpd.project_budget_material_id is not null and mdpd.deleted_flag != 1 and
        (mdpd.status not in (-2,-1,0) or mdpd.status is null or mdpd.status = '')) mdpd4 on
        1 = 1 group by mm.designId
    </select>

    <select id="judgeUniqueCheckType2" resultType="java.lang.String">
        select
        h.adjust_code
        from material_adjust_detail d left join material_adjust_header h on d.header_id = h.id
        where d.deleted_flag = false and h.deleted_flag = false
        and d.brand = #{brand}
        and d.model = #{model}
        and d.brand_material_code = #{brandMaterialCode}
        and h.organization_id = #{organizationId}
    </select>

    <select id="judgeUniqueCheckType3" resultType="java.lang.String">
        select
        h.adjust_code
        from material_adjust_detail d left join material_adjust_header h on d.header_id = h.id
        where d.deleted_flag = false and h.deleted_flag = false
        and d.figure_number = #{figureNumber}
        and d.chart_version = #{chartVersion}
        and h.organization_id = #{organizationId}
    </select>

    <select id="selectPriceBySon" resultType="java.math.BigDecimal">
        select ROUND(sum(cnmd.number * ifnull(mc.item_cost, 0)), 6) as itemCost
        from (
        with recursive mdpd as (
        select *
        from pam_ctc.milepost_design_plan_detail
        where id = #{designId}
        union
        select a.*
        from pam_ctc.milepost_design_plan_detail a
        join mdpd
        where a.parent_id = mdpd.id)
        select mdpd.*
        from mdpd
        where mdpd.id != #{designId}) cnmd
        left join pam_basedata.material_cost mc on
        cnmd.design_cost_id = mc.id
    </select>

    <select id="selectStatusByOrgAndPamCode" resultType="com.midea.pam.common.ctc.entity.MaterialAdjustHeader">
        select mah.*
        from pam_ctc.material_adjust_header mah
        left join pam_ctc.material_adjust_detail mad on
        mah.id = mad.header_id
        where mah.organization_id = #{organizationId}
        and mad.pam_code = #{pamCode}
        and mah.status = 1
        order by mah.create_at desc limit 1
    </select>

    <select id="checkMaterial1" resultType="java.lang.String">
        select
        h.adjust_code
        from material_adjust_detail d left join material_adjust_header h on d.header_id = h.id
        where d.deleted_flag = false and h.deleted_flag = false
        and d.material_middle_class = #{materialMiddleClass}
        and d.brand = #{brand}
        and d.figure_number = #{figureNumber}
        and h.organization_id = #{organizationId}
    </select>

    <select id="checkMaterial3" resultType="java.lang.String">
        select
        h.adjust_code
        from material_adjust_detail d left join material_adjust_header h on d.header_id = h.id
        where d.deleted_flag = false and h.deleted_flag = false
        and d.material_middle_class = #{materialMiddleClass}
        and d.brand = #{brand}
        and d.brand_material_code = #{brandMaterialCode}
        and h.organization_id = #{organizationId}
    </select>

    <select id="checkMaterial2" resultType="java.lang.String">
        select
        h.adjust_code
        from material_adjust_detail d left join material_adjust_header h on d.header_id = h.id
        where d.deleted_flag = false and h.deleted_flag = false
        and d.material_middle_class = #{materialMiddleClass}
        and d.brand = #{brand}
        and d.model = #{model}
        and h.organization_id = #{organizationId}
    </select>

    <select id="getAdjustCodeForKukaCodeRule" resultType="com.midea.pam.common.basedata.dto.MaterialDto">
        select
        h.adjust_code adjustCode,
        d.pam_code pamCode,
        d.erp_code as itemCode,
        d.item_des as itemInfo,
        d.unit,
        d.unit_code unitCode,
        d.material_class_id as materialClassificationId,
        d.material_class as materialClassification,
        d.material_middle_class_id as codingMiddleclassId,
        d.material_middle_class codingMiddleclass,
        d.material_small_class_id as materialTypeId,
        d.material_small_class as materialType,
        d.name,
        d.model,
        d.brand,
        d.machining_part_type as machiningPartType,
        d.material,
        d.unit_weight unitWeight,
        ifnull(d.surface_handle_new,d.surface_handle) as materialProcessing,
        d.figure_number figureNumber,
        h.organization_id organizationId,
        d.chart_version chartVersion,
        d.material_type as materialCategory,
        h.status as materialAdjustHeaderStatus
        from material_adjust_detail d left join material_adjust_header h on d.header_id = h.id
        where d.deleted_flag = false and h.deleted_flag = false and h.status != 2 and h.status != 3
        <if test="figureNumber != null and figureNumber != ''">
            and d.figure_number = #{figureNumber}
        </if>
        <if test="brand != null and brand != ''">
            and d.brand = #{brand}
        </if>
        <if test="model != null and model != ''">
            and d.model = #{model}
        </if>
        <if test="organizationIds != null and organizationIds.size>0">
            and h.organization_id in
            <foreach collection="organizationIds" item="organizationId" index="index" open="(" separator="," close=")">
                #{organizationId}
            </foreach>
        </if>
        <if test="adjustHeaderId != null and adjustHeaderId != ''">
            and h.id != #{adjustHeaderId}
        </if>
    </select>

    <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.MaterialAdjustHeaderExample" resultMap="BaseResultMap">
        with t as (
        select
        <include refid="Base_Column_List" />
        from material_adjust_header
        union all
        select
        <include refid="Base_Column_List" />
        from material_change_header
        ) select * from t
        <where>
            <if test="example != null">
                <include refid="Example_Where_Clause_Modified" />
            </if>
        </where>
        <if test="example != null and example.orderByClause != null">
            order by ${example.orderByClause}
        </if>
    </select>

    <select id="checkMaterialDelistRepeatSubmit" resultType="com.midea.pam.common.ctc.dto.MaterialChangeDetailDTO">
        select
            d.pam_code as pamCode,
            h.adjust_code as adjustCode
        from pam_ctc.material_change_detail d
        left join pam_ctc.material_change_header h on d.header_id = h.id
        where h.status not in (2,3)
        and h.adjust_type = 103
        <if test="id != null">
            and h.id != #{id}
        </if>
        <if test="organizationId != null">
            and h.organization_id = #{organizationId}
        </if>
        <if test="pamCodeList != null and pamCodeList.size > 0">
            and d.pam_code in
            <foreach collection="pamCodeList" item="pamCode" index="index" open="(" separator="," close=")">
                #{pamCode}
            </foreach>
        </if>
    </select>

    <insert id="batchInsertChangeDetail" parameterType="com.midea.pam.common.ctc.entity.MaterialChangeDetail">
        insert into material_change_detail (id, header_id, pam_code,
        erp_code, name, item_des,
        unit, unit_code, model,
        material_class_id, material_class, material_middle_class_id,
        material_middle_class, material_small_class_id,
        material_small_class, material_type, material_type_new,
        material_id, figure_number, chart_version,
        chart_version_new, unit_weight, unit_weight_new,
        machining_part_type, machining_part_type_new,
        surface_handle, surface_handle_new, material,
        material_new, or_spare_parts_mask, or_spare_parts_mask_new,
        delist_flag, delist_flag_new, brand,
        brand_material_code, perchasing_leadtime, min_perchase_quantity,
        min_package_quantity, sync_status, sync_mes,
        activity_code, material_attribute, inventory_type,
        attribute1, attribute2, attribute3,
        create_by, create_at, update_by,
        update_at, deleted_flag)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.headerId,jdbcType=BIGINT}, #{item.pamCode,jdbcType=VARCHAR},
            #{item.erpCode,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.itemDes,jdbcType=VARCHAR},
            #{item.unit,jdbcType=VARCHAR}, #{item.unitCode,jdbcType=VARCHAR}, #{item.model,jdbcType=VARCHAR},
            #{item.materialClassId,jdbcType=BIGINT}, #{item.materialClass,jdbcType=VARCHAR}, #{item.materialMiddleClassId,jdbcType=BIGINT},
            #{item.materialMiddleClass,jdbcType=VARCHAR}, #{item.materialSmallClassId,jdbcType=BIGINT},
            #{item.materialSmallClass,jdbcType=VARCHAR}, #{item.materialType,jdbcType=VARCHAR}, #{item.materialTypeNew,jdbcType=VARCHAR},
            #{item.materialId,jdbcType=BIGINT}, #{item.figureNumber,jdbcType=VARCHAR}, #{item.chartVersion,jdbcType=VARCHAR},
            #{item.chartVersionNew,jdbcType=VARCHAR}, #{item.unitWeight,jdbcType=DECIMAL}, #{item.unitWeightNew,jdbcType=DECIMAL},
            #{item.machiningPartType,jdbcType=VARCHAR}, #{item.machiningPartTypeNew,jdbcType=VARCHAR},
            #{item.surfaceHandle,jdbcType=VARCHAR}, #{item.surfaceHandleNew,jdbcType=VARCHAR}, #{item.material,jdbcType=VARCHAR},
            #{item.materialNew,jdbcType=VARCHAR}, #{item.orSparePartsMask,jdbcType=VARCHAR}, #{item.orSparePartsMaskNew,jdbcType=VARCHAR},
            #{item.delistFlag,jdbcType=TINYINT}, #{item.delistFlagNew,jdbcType=TINYINT}, #{item.brand,jdbcType=VARCHAR},
            #{item.brandMaterialCode,jdbcType=VARCHAR}, #{item.perchasingLeadtime,jdbcType=BIGINT}, #{item.minPerchaseQuantity,jdbcType=BIGINT},
            #{item.minPackageQuantity,jdbcType=BIGINT}, #{item.syncStatus,jdbcType=TINYINT}, #{item.syncMes,jdbcType=VARCHAR},
            #{item.activityCode,jdbcType=VARCHAR}, #{item.materialAttribute,jdbcType=VARCHAR}, #{item.inventoryType,jdbcType=VARCHAR},
            #{item.attribute1,jdbcType=VARCHAR}, #{item.attribute2,jdbcType=VARCHAR}, #{item.attribute3,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=BIGINT}, #{item.createAt,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT},
            #{item.updateAt,jdbcType=TIMESTAMP}, #{item.deletedFlag,jdbcType=TINYINT})
        </foreach>
    </insert>

    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.MaterialAdjustDetail">
        insert into material_adjust_detail (id, header_id, pam_code,
        erp_code, name, item_des,
        unit, unit_code, model,
        material_class_id, material_class, material_middle_class_id,
        material_middle_class, material_small_class_id,
        material_small_class, material_type, material_type_new, figure_number,
        chart_version, chart_version_new, unit_weight,
        unit_weight_new, machining_part_type, machining_part_type_new,
        surface_handle, surface_handle_new, material,
        material_new, or_spare_parts_mask, or_spare_parts_mask_new,
        brand, brand_material_code, perchasing_leadtime,
        min_perchase_quantity, min_package_quantity,
        remark, sync_status, sync_mes, attribute1,
        attribute2, attribute3, create_by,
        create_at, update_by, update_at,
        deleted_flag,material_id,inventory_type, material_attribute)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.headerId,jdbcType=BIGINT}, #{item.pamCode,jdbcType=VARCHAR},
            #{item.erpCode,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.itemDes,jdbcType=VARCHAR},
            #{item.unit,jdbcType=VARCHAR}, #{item.unitCode,jdbcType=VARCHAR}, #{item.model,jdbcType=VARCHAR},
            #{item.materialClassId,jdbcType=BIGINT}, #{item.materialClass,jdbcType=VARCHAR}, #{item.materialMiddleClassId,jdbcType=BIGINT},
            #{item.materialMiddleClass,jdbcType=VARCHAR}, #{item.materialSmallClassId,jdbcType=BIGINT},
            #{item.materialSmallClass,jdbcType=VARCHAR}, #{item.materialType,jdbcType=VARCHAR}, #{item.materialTypeNew,jdbcType=VARCHAR}, #{item.figureNumber,jdbcType=VARCHAR},
            #{item.chartVersion,jdbcType=VARCHAR}, #{item.chartVersionNew,jdbcType=VARCHAR}, #{item.unitWeight,jdbcType=DECIMAL},
            #{item.unitWeightNew,jdbcType=DECIMAL}, #{item.machiningPartType,jdbcType=VARCHAR}, #{item.machiningPartTypeNew,jdbcType=VARCHAR},
            #{item.surfaceHandle,jdbcType=VARCHAR}, #{item.surfaceHandleNew,jdbcType=VARCHAR}, #{item.material,jdbcType=VARCHAR},
            #{item.materialNew,jdbcType=VARCHAR}, #{item.orSparePartsMask,jdbcType=VARCHAR}, #{item.orSparePartsMaskNew,jdbcType=VARCHAR},
            #{item.brand,jdbcType=VARCHAR}, #{item.brandMaterialCode,jdbcType=VARCHAR}, #{item.perchasingLeadtime,jdbcType=BIGINT},
            #{item.minPerchaseQuantity,jdbcType=BIGINT}, #{item.minPackageQuantity,jdbcType=BIGINT},
            #{item.remark,jdbcType=VARCHAR}, #{item.syncStatus,jdbcType=TINYINT}, #{item.syncMes,jdbcType=VARCHAR}, #{item.attribute1,jdbcType=VARCHAR},
            #{item.attribute2,jdbcType=VARCHAR}, #{item.attribute3,jdbcType=VARCHAR}, #{item.createBy,jdbcType=BIGINT},
            #{item.createAt,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT}, #{item.updateAt,jdbcType=TIMESTAMP},
            #{item.deletedFlag,jdbcType=TINYINT}, #{item.materialId,jdbcType=BIGINT}, #{item.inventoryType,jdbcType=VARCHAR}, #{item.materialAttribute,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="selectByDto" parameterType="com.midea.pam.common.ctc.dto.MaterialAdjustHeaderDTO" resultMap="BaseResultMap">
        with t as (
        select
        <include refid="Base_Column_List" />
        from material_adjust_header
        union all
        select
        <include refid="Base_Column_List" />
        from material_change_header
        ) select * from t
        <where>
            deleted_flag = 0

            <if test="dto.adjustCode != null and dto.adjustCode != ''">
                AND adjust_code LIKE CONCAT('%', #{dto.adjustCode}, '%')
            </if>

            <!-- 变更类型列表查询 -->
            <if test="dto.adjustTypeStr != null and dto.adjustTypeStr != ''">
                AND adjust_type IN
                <foreach collection="dto.adjustTypeStr.split(',')" item="item" open="(" separator="," close=")">
                    #{item, jdbcType=INTEGER}
                </foreach>
            </if>

            <!-- 申请人模糊查询 -->
            <if test="dto.applyByName != null and dto.applyByName != ''">
                AND apply_by_name LIKE CONCAT('%', #{dto.applyByName}, '%')
            </if>

            <!-- 处理人模糊查询 -->
            <if test="dto.handleByName != null and dto.handleByName != ''">
                AND handle_by_name LIKE CONCAT('%', #{dto.handleByName}, '%')
            </if>

            <!-- 审批状态列表查询 -->
            <if test="dto.statusStr != null and dto.statusStr != ''">
                AND status IN
                <foreach collection="dto.statusStr.split(',')" item="item" open="(" separator="," close=")">
                    #{item, jdbcType=INTEGER}
                </foreach>
            </if>

            <!-- 库存组织列表查询 -->
            <if test="dto.organizationIdStr != null and dto.organizationIdStr != ''">
                AND organization_id IN
                <foreach collection="dto.organizationIdStr.split(',')" item="item" open="(" separator="," close=")">
                    #{item, jdbcType=BIGINT}
                </foreach>
            </if>

            <!-- 创建时间范围查询 -->
            <if test="dto.createAtBegin != null">
                AND create_at &gt;= #{dto.createAtBegin}
            </if>
            <if test="dto.createAtEnd != null">
                AND create_at &lt;= #{dto.createAtEnd}
            </if>

            <!-- 数据来源列表查询 -->
            <if test="dto.resourceStr != null and dto.resourceStr != ''">
                AND resource IN
                <foreach collection="dto.resourceStr.split(',')" item="item" open="(" separator="," close=")">
                    #{item, jdbcType=INTEGER}
                </foreach>
            </if>

            <!-- 同步状态列表查询 -->
            <if test="dto.syncStatusStr != null and dto.syncStatusStr != ''">
                AND sync_status IN
                <foreach collection="dto.syncStatusStr.split(',')" item="item" open="(" separator="," close=")">
                    #{item, jdbcType=INTEGER}
                </foreach>
            </if>

            <!-- 使用单位过滤条件 -->
            <if test="dto.ouIdList != null and dto.ouIdList.size() > 0">
                AND ou_id IN
                <foreach collection="dto.ouIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 查询当前用户创建或处理的物料标识 -->
            <if test="dto.me != null and dto.me == true">
                AND (apply_by = #{dto.currentUserId} OR handle_by = #{dto.currentUserId})
            </if>
        </where>
        ORDER BY create_at DESC
    </select>

</mapper>