<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ReceiptClaimMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ReceiptClaim">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source_system_code" jdbcType="VARCHAR" property="sourceSystemCode" />
    <result column="cash_receipt_code" jdbcType="VARCHAR" property="cashReceiptCode" />
    <result column="cash_status" jdbcType="TINYINT" property="cashStatus" />
    <result column="divide_status" jdbcType="TINYINT" property="divideStatus" />
    <result column="pay_name" jdbcType="VARCHAR" property="payName" />
    <result column="pay_bank_code" jdbcType="VARCHAR" property="payBankCode" />
    <result column="pay_bank_name" jdbcType="VARCHAR" property="payBankName" />
    <result column="bill_code" jdbcType="VARCHAR" property="billCode" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="bill_type" jdbcType="VARCHAR" property="billType" />
    <result column="settle_way" jdbcType="VARCHAR" property="settleWay" />
    <result column="rec_method" jdbcType="VARCHAR" property="recMethod" />
    <result column="rec_bank_id" jdbcType="BIGINT" property="recBankId" />
    <result column="rec_bank_code" jdbcType="VARCHAR" property="recBankCode" />
    <result column="rec_account_no" jdbcType="VARCHAR" property="recAccountNo" />
    <result column="rec_org_name" jdbcType="VARCHAR" property="recOrgName" />
    <result column="rec_org_code" jdbcType="VARCHAR" property="recOrgCode" />
    <result column="budget_item_code" jdbcType="VARCHAR" property="budgetItemCode" />
    <result column="ou_id" jdbcType="BIGINT" property="ouId" />
    <result column="pay_date" jdbcType="TIMESTAMP" property="payDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="conntrans_code" jdbcType="VARCHAR" property="conntransCode" />
    <result column="transfer_code" jdbcType="VARCHAR" property="transferCode" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="crm_customer_code" jdbcType="VARCHAR" property="crmCustomerCode" />
    <result column="crm_customer_name" jdbcType="VARCHAR" property="crmCustomerName" />
    <result column="attribute1" jdbcType="VARCHAR" property="attribute1" />
    <result column="attribute2" jdbcType="VARCHAR" property="attribute2" />
    <result column="attribute3" jdbcType="VARCHAR" property="attribute3" />
    <result column="attribute4" jdbcType="VARCHAR" property="attribute4" />
    <result column="attribute5" jdbcType="VARCHAR" property="attribute5" />
    <result column="attribute6" jdbcType="VARCHAR" property="attribute6" />
    <result column="is_import" jdbcType="TINYINT" property="isImport" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="conversion_type" jdbcType="VARCHAR" property="conversionType" />
    <result column="conversion_date" jdbcType="TIMESTAMP" property="conversionDate" />
    <result column="conversion_rate" jdbcType="DECIMAL" property="conversionRate" />
    <result column="customer_transfer_id" jdbcType="BIGINT" property="customerTransferId" />
    <result column="source" jdbcType="TINYINT" property="source" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, source_system_code, cash_receipt_code, cash_status, divide_status, pay_name, 
    pay_bank_code, pay_bank_name, bill_code, pay_amount, currency_code, bill_type, settle_way, 
    rec_method, rec_bank_id, rec_bank_code, rec_account_no, rec_org_name, rec_org_code, 
    budget_item_code, ou_id, pay_date, remark, conntrans_code, transfer_code, serial_number, 
    crm_customer_code, crm_customer_name, attribute1, attribute2, attribute3, attribute4, 
    attribute5, attribute6, is_import, create_by, create_at, update_by, update_at, deleted_flag, 
    conversion_type, conversion_date, conversion_rate, customer_transfer_id, source
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.ReceiptClaimExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from receipt_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from receipt_claim
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from receipt_claim
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.ReceiptClaim">
    insert into receipt_claim (id, source_system_code, cash_receipt_code, 
      cash_status, divide_status, pay_name, 
      pay_bank_code, pay_bank_name, bill_code, 
      pay_amount, currency_code, bill_type, 
      settle_way, rec_method, rec_bank_id, 
      rec_bank_code, rec_account_no, rec_org_name, 
      rec_org_code, budget_item_code, ou_id, 
      pay_date, remark, conntrans_code, 
      transfer_code, serial_number, crm_customer_code, 
      crm_customer_name, attribute1, attribute2, 
      attribute3, attribute4, attribute5, 
      attribute6, is_import, create_by, 
      create_at, update_by, update_at, 
      deleted_flag, conversion_type, conversion_date, 
      conversion_rate, customer_transfer_id, source
      )
    values (#{id,jdbcType=BIGINT}, #{sourceSystemCode,jdbcType=VARCHAR}, #{cashReceiptCode,jdbcType=VARCHAR}, 
      #{cashStatus,jdbcType=TINYINT}, #{divideStatus,jdbcType=TINYINT}, #{payName,jdbcType=VARCHAR}, 
      #{payBankCode,jdbcType=VARCHAR}, #{payBankName,jdbcType=VARCHAR}, #{billCode,jdbcType=VARCHAR}, 
      #{payAmount,jdbcType=DECIMAL}, #{currencyCode,jdbcType=VARCHAR}, #{billType,jdbcType=VARCHAR}, 
      #{settleWay,jdbcType=VARCHAR}, #{recMethod,jdbcType=VARCHAR}, #{recBankId,jdbcType=BIGINT}, 
      #{recBankCode,jdbcType=VARCHAR}, #{recAccountNo,jdbcType=VARCHAR}, #{recOrgName,jdbcType=VARCHAR}, 
      #{recOrgCode,jdbcType=VARCHAR}, #{budgetItemCode,jdbcType=VARCHAR}, #{ouId,jdbcType=BIGINT}, 
      #{payDate,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{conntransCode,jdbcType=VARCHAR}, 
      #{transferCode,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR}, #{crmCustomerCode,jdbcType=VARCHAR}, 
      #{crmCustomerName,jdbcType=VARCHAR}, #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, 
      #{attribute3,jdbcType=VARCHAR}, #{attribute4,jdbcType=VARCHAR}, #{attribute5,jdbcType=VARCHAR}, 
      #{attribute6,jdbcType=VARCHAR}, #{isImport,jdbcType=TINYINT}, #{createBy,jdbcType=BIGINT}, 
      #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, 
      #{deletedFlag,jdbcType=TINYINT}, #{conversionType,jdbcType=VARCHAR}, #{conversionDate,jdbcType=TIMESTAMP}, 
      #{conversionRate,jdbcType=DECIMAL}, #{customerTransferId,jdbcType=BIGINT}, #{source,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.ReceiptClaim">
    insert into receipt_claim
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sourceSystemCode != null">
        source_system_code,
      </if>
      <if test="cashReceiptCode != null">
        cash_receipt_code,
      </if>
      <if test="cashStatus != null">
        cash_status,
      </if>
      <if test="divideStatus != null">
        divide_status,
      </if>
      <if test="payName != null">
        pay_name,
      </if>
      <if test="payBankCode != null">
        pay_bank_code,
      </if>
      <if test="payBankName != null">
        pay_bank_name,
      </if>
      <if test="billCode != null">
        bill_code,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="currencyCode != null">
        currency_code,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="settleWay != null">
        settle_way,
      </if>
      <if test="recMethod != null">
        rec_method,
      </if>
      <if test="recBankId != null">
        rec_bank_id,
      </if>
      <if test="recBankCode != null">
        rec_bank_code,
      </if>
      <if test="recAccountNo != null">
        rec_account_no,
      </if>
      <if test="recOrgName != null">
        rec_org_name,
      </if>
      <if test="recOrgCode != null">
        rec_org_code,
      </if>
      <if test="budgetItemCode != null">
        budget_item_code,
      </if>
      <if test="ouId != null">
        ou_id,
      </if>
      <if test="payDate != null">
        pay_date,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="conntransCode != null">
        conntrans_code,
      </if>
      <if test="transferCode != null">
        transfer_code,
      </if>
      <if test="serialNumber != null">
        serial_number,
      </if>
      <if test="crmCustomerCode != null">
        crm_customer_code,
      </if>
      <if test="crmCustomerName != null">
        crm_customer_name,
      </if>
      <if test="attribute1 != null">
        attribute1,
      </if>
      <if test="attribute2 != null">
        attribute2,
      </if>
      <if test="attribute3 != null">
        attribute3,
      </if>
      <if test="attribute4 != null">
        attribute4,
      </if>
      <if test="attribute5 != null">
        attribute5,
      </if>
      <if test="attribute6 != null">
        attribute6,
      </if>
      <if test="isImport != null">
        is_import,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="conversionType != null">
        conversion_type,
      </if>
      <if test="conversionDate != null">
        conversion_date,
      </if>
      <if test="conversionRate != null">
        conversion_rate,
      </if>
      <if test="customerTransferId != null">
        customer_transfer_id,
      </if>
      <if test="source != null">
        source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sourceSystemCode != null">
        #{sourceSystemCode,jdbcType=VARCHAR},
      </if>
      <if test="cashReceiptCode != null">
        #{cashReceiptCode,jdbcType=VARCHAR},
      </if>
      <if test="cashStatus != null">
        #{cashStatus,jdbcType=TINYINT},
      </if>
      <if test="divideStatus != null">
        #{divideStatus,jdbcType=TINYINT},
      </if>
      <if test="payName != null">
        #{payName,jdbcType=VARCHAR},
      </if>
      <if test="payBankCode != null">
        #{payBankCode,jdbcType=VARCHAR},
      </if>
      <if test="payBankName != null">
        #{payBankName,jdbcType=VARCHAR},
      </if>
      <if test="billCode != null">
        #{billCode,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=VARCHAR},
      </if>
      <if test="settleWay != null">
        #{settleWay,jdbcType=VARCHAR},
      </if>
      <if test="recMethod != null">
        #{recMethod,jdbcType=VARCHAR},
      </if>
      <if test="recBankId != null">
        #{recBankId,jdbcType=BIGINT},
      </if>
      <if test="recBankCode != null">
        #{recBankCode,jdbcType=VARCHAR},
      </if>
      <if test="recAccountNo != null">
        #{recAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="recOrgName != null">
        #{recOrgName,jdbcType=VARCHAR},
      </if>
      <if test="recOrgCode != null">
        #{recOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="budgetItemCode != null">
        #{budgetItemCode,jdbcType=VARCHAR},
      </if>
      <if test="ouId != null">
        #{ouId,jdbcType=BIGINT},
      </if>
      <if test="payDate != null">
        #{payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="conntransCode != null">
        #{conntransCode,jdbcType=VARCHAR},
      </if>
      <if test="transferCode != null">
        #{transferCode,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="crmCustomerCode != null">
        #{crmCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="crmCustomerName != null">
        #{crmCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null">
        #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null">
        #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null">
        #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null">
        #{attribute5,jdbcType=VARCHAR},
      </if>
      <if test="attribute6 != null">
        #{attribute6,jdbcType=VARCHAR},
      </if>
      <if test="isImport != null">
        #{isImport,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="conversionType != null">
        #{conversionType,jdbcType=VARCHAR},
      </if>
      <if test="conversionDate != null">
        #{conversionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="conversionRate != null">
        #{conversionRate,jdbcType=DECIMAL},
      </if>
      <if test="customerTransferId != null">
        #{customerTransferId,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.ReceiptClaimExample" resultType="java.lang.Long">
    select count(*) from receipt_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.ReceiptClaim">
    update receipt_claim
    <set>
      <if test="sourceSystemCode != null">
        source_system_code = #{sourceSystemCode,jdbcType=VARCHAR},
      </if>
      <if test="cashReceiptCode != null">
        cash_receipt_code = #{cashReceiptCode,jdbcType=VARCHAR},
      </if>
      <if test="cashStatus != null">
        cash_status = #{cashStatus,jdbcType=TINYINT},
      </if>
      <if test="divideStatus != null">
        divide_status = #{divideStatus,jdbcType=TINYINT},
      </if>
      <if test="payName != null">
        pay_name = #{payName,jdbcType=VARCHAR},
      </if>
      <if test="payBankCode != null">
        pay_bank_code = #{payBankCode,jdbcType=VARCHAR},
      </if>
      <if test="payBankName != null">
        pay_bank_name = #{payBankName,jdbcType=VARCHAR},
      </if>
      <if test="billCode != null">
        bill_code = #{billCode,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="currencyCode != null">
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=VARCHAR},
      </if>
      <if test="settleWay != null">
        settle_way = #{settleWay,jdbcType=VARCHAR},
      </if>
      <if test="recMethod != null">
        rec_method = #{recMethod,jdbcType=VARCHAR},
      </if>
      <if test="recBankId != null">
        rec_bank_id = #{recBankId,jdbcType=BIGINT},
      </if>
      <if test="recBankCode != null">
        rec_bank_code = #{recBankCode,jdbcType=VARCHAR},
      </if>
      <if test="recAccountNo != null">
        rec_account_no = #{recAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="recOrgName != null">
        rec_org_name = #{recOrgName,jdbcType=VARCHAR},
      </if>
      <if test="recOrgCode != null">
        rec_org_code = #{recOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="budgetItemCode != null">
        budget_item_code = #{budgetItemCode,jdbcType=VARCHAR},
      </if>
      <if test="ouId != null">
        ou_id = #{ouId,jdbcType=BIGINT},
      </if>
      <if test="payDate != null">
        pay_date = #{payDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="conntransCode != null">
        conntrans_code = #{conntransCode,jdbcType=VARCHAR},
      </if>
      <if test="transferCode != null">
        transfer_code = #{transferCode,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        serial_number = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="crmCustomerCode != null">
        crm_customer_code = #{crmCustomerCode,jdbcType=VARCHAR},
      </if>
      <if test="crmCustomerName != null">
        crm_customer_name = #{crmCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null">
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        attribute2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null">
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null">
        attribute4 = #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null">
        attribute5 = #{attribute5,jdbcType=VARCHAR},
      </if>
      <if test="attribute6 != null">
        attribute6 = #{attribute6,jdbcType=VARCHAR},
      </if>
      <if test="isImport != null">
        is_import = #{isImport,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="conversionType != null">
        conversion_type = #{conversionType,jdbcType=VARCHAR},
      </if>
      <if test="conversionDate != null">
        conversion_date = #{conversionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="conversionRate != null">
        conversion_rate = #{conversionRate,jdbcType=DECIMAL},
      </if>
      <if test="customerTransferId != null">
        customer_transfer_id = #{customerTransferId,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.ReceiptClaim">
    update receipt_claim
    set source_system_code = #{sourceSystemCode,jdbcType=VARCHAR},
      cash_receipt_code = #{cashReceiptCode,jdbcType=VARCHAR},
      cash_status = #{cashStatus,jdbcType=TINYINT},
      divide_status = #{divideStatus,jdbcType=TINYINT},
      pay_name = #{payName,jdbcType=VARCHAR},
      pay_bank_code = #{payBankCode,jdbcType=VARCHAR},
      pay_bank_name = #{payBankName,jdbcType=VARCHAR},
      bill_code = #{billCode,jdbcType=VARCHAR},
      pay_amount = #{payAmount,jdbcType=DECIMAL},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      bill_type = #{billType,jdbcType=VARCHAR},
      settle_way = #{settleWay,jdbcType=VARCHAR},
      rec_method = #{recMethod,jdbcType=VARCHAR},
      rec_bank_id = #{recBankId,jdbcType=BIGINT},
      rec_bank_code = #{recBankCode,jdbcType=VARCHAR},
      rec_account_no = #{recAccountNo,jdbcType=VARCHAR},
      rec_org_name = #{recOrgName,jdbcType=VARCHAR},
      rec_org_code = #{recOrgCode,jdbcType=VARCHAR},
      budget_item_code = #{budgetItemCode,jdbcType=VARCHAR},
      ou_id = #{ouId,jdbcType=BIGINT},
      pay_date = #{payDate,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      conntrans_code = #{conntransCode,jdbcType=VARCHAR},
      transfer_code = #{transferCode,jdbcType=VARCHAR},
      serial_number = #{serialNumber,jdbcType=VARCHAR},
      crm_customer_code = #{crmCustomerCode,jdbcType=VARCHAR},
      crm_customer_name = #{crmCustomerName,jdbcType=VARCHAR},
      attribute1 = #{attribute1,jdbcType=VARCHAR},
      attribute2 = #{attribute2,jdbcType=VARCHAR},
      attribute3 = #{attribute3,jdbcType=VARCHAR},
      attribute4 = #{attribute4,jdbcType=VARCHAR},
      attribute5 = #{attribute5,jdbcType=VARCHAR},
      attribute6 = #{attribute6,jdbcType=VARCHAR},
      is_import = #{isImport,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      conversion_type = #{conversionType,jdbcType=VARCHAR},
      conversion_date = #{conversionDate,jdbcType=TIMESTAMP},
      conversion_rate = #{conversionRate,jdbcType=DECIMAL},
      customer_transfer_id = #{customerTransferId,jdbcType=BIGINT},
      source = #{source,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>