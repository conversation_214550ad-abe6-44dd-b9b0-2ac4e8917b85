<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectWbsReceiptsExtMapper">
    <resultMap id="BaseResultDtoMap" type="com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="requirement_code" jdbcType="VARCHAR" property="requirementCode" />
        <result column="requirement_status" jdbcType="INTEGER" property="requirementStatus" />
        <result column="requirement_type" jdbcType="INTEGER" property="requirementType" />
        <result column="confirm_mode" jdbcType="INTEGER" property="confirmMode" />
        <result column="handle_by" jdbcType="BIGINT" property="handleBy" />
        <result column="handle_name" jdbcType="VARCHAR" property="handleName" />
        <result column="reason" jdbcType="VARCHAR" property="reason" />
        <result column="handle_at" jdbcType="TIMESTAMP" property="handleAt" />
        <result column="design_release_lot_number" jdbcType="VARCHAR" property="designReleaseLotNumber" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="producer_id" jdbcType="BIGINT" property="producerId" />
        <result column="producer_name" jdbcType="VARCHAR" property="producerName" />
        <result column="process_name" jdbcType="VARCHAR" property="processName" />
        <result column="project_submit" jdbcType="TINYINT" property="projectSubmit" />
        <result column="unit_id" jdbcType="BIGINT" property="unitId" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="version" jdbcType="BIGINT" property="version" />
        <result column="renson_remark" jdbcType="VARCHAR" property="rensonRemark" />
        <result column="submit_reason" jdbcType="VARCHAR" property="submitReason" />
        <result column="rel_receipts_id" jdbcType="BIGINT" property="relReceiptsId" />
        <result column="init" jdbcType="TINYINT" property="init" />
        <result column="init_sequence" jdbcType="VARCHAR" property="initSequence" />
        <result column="design_plan_detail_id" jdbcType="BIGINT" property="designPlanDetailId" />
    </resultMap>
    <resultMap extends="BaseResultDtoMap" id="ResultDtoMapWithBLOBs" type="com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto">
        <result column="batch_upload_paths" jdbcType="LONGVARCHAR" property="batchUploadPaths" />
        <result column="batch_upload_path_ids" jdbcType="LONGVARCHAR" property="batchUploadPathIds" />
    </resultMap>

    <update id="updateReceiptsDel">
        UPDATE
        project_wbs_receipts_design_plan_rel rd
        SET
        rd.deleted_flag = 1
        WHERE rd.`project_wbs_receipts_id` = #{id}
    </update>

    <update id="updateWbsRequirementBudgetDel">
        UPDATE
        project_wbs_receipts_budget rd
        SET
        rd.deleted_flag = 1
        WHERE rd.`project_wbs_receipts_id` = #{id}
    </update>

    <select id="selectByIds" resultMap="ResultDtoMapWithBLOBs">
        select *
        from project_wbs_receipts where (deleted_flag = 0 or deleted_flag is null)
        <if test="ids != null and ids.size>0">
            and id in
            <foreach collection="ids" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
    </select>

    <select id="selectApprovingReceiptsByIds" resultMap="ResultDtoMapWithBLOBs">
        select *
        from project_wbs_receipts where (deleted_flag = 0 or deleted_flag is null) and requirement_status != 4 and requirement_status != 5
        <if test="ids != null and ids.size>0">
            and id in
            <foreach collection="ids" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
    </select>

    <select id="selectProcessReceiptsByDetailIds" resultMap="ResultDtoMapWithBLOBs">
        select pwr.*,pwrdpr.design_plan_detail_id
        from project_wbs_receipts pwr left join project_wbs_receipts_design_plan_rel pwrdpr on pwr.id =pwrdpr.project_wbs_receipts_id
        where (pwr.deleted_flag = 0 or pwr.deleted_flag is null) and (pwrdpr.deleted_flag = 0 or pwrdpr.deleted_flag is null)
        and pwr.requirement_status = 4 and pwr.requirement_type =3
        <if test="detailIds != null and detailIds.size>0">
            and pwrdpr.design_plan_detail_id in
            <foreach collection="detailIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectByDtos" resultMap="ResultDtoMapWithBLOBs">
        select pwr.*,pwrdpr.design_plan_detail_id
        from project_wbs_receipts pwr left join project_wbs_receipts_design_plan_rel pwrdpr on pwr.id =pwrdpr.project_wbs_receipts_id
        where (pwr.deleted_flag = 0 or pwr.deleted_flag is null) and (pwrdpr.deleted_flag = 0 or pwrdpr.deleted_flag is null)
        and pwr.requirement_status != 5 and pwr.requirement_type =3
        <if test="list != null and list.size>0">
            and pwrdpr.design_plan_detail_id in
            <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </if>
    </select>

    <select id="selectByDetailIds" resultMap="ResultDtoMapWithBLOBs">
        select pwr.*,pwrdpr.design_plan_detail_id
        from project_wbs_receipts pwr left join project_wbs_receipts_design_plan_rel pwrdpr on pwr.id =pwrdpr.project_wbs_receipts_id
        where (pwr.deleted_flag = 0 or pwr.deleted_flag is null) and (pwrdpr.deleted_flag = 0 or pwrdpr.deleted_flag is null)
        and pwr.requirement_status != 5 and pwr.requirement_type =3
        <if test="detailIds != null and detailIds.size>0">
            and pwrdpr.design_plan_detail_id in
            <foreach collection="detailIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectDto" resultMap="BaseResultDtoMap">
        select
            pwr.id,
            pwr.project_id,
            pwr.project_code,
            pwr.project_name,
            pwr.requirement_status,
            pwr.requirement_type,
            pwr.requirement_code
        from pam_ctc.project_wbs_receipts pwr
        left join pam_ctc.project_wbs_receipts_design_plan_rel pwrdpr on pwr.id =pwrdpr.project_wbs_receipts_id
        where (pwr.deleted_flag = 0 or pwr.deleted_flag is null) and (pwrdpr.deleted_flag = 0 or pwrdpr.deleted_flag is null)
        and pwr.requirement_type =3
        and pwr.requirement_status != 5
        and pwr.requirement_status != 4
        and pwrdpr.design_plan_detail_id in(
            select pwr.id from pam_ctc.milepost_design_plan_detail pwr where (pwr.deleted_flag = 0 or pwr.deleted_flag is null)
            <if test="projectId != null and projectId != ''">
                and pwr.project_id = #{projectId}
            </if>
            <if test="pamCode != null and pamCode != ''">
                and pwr.pam_code = #{pamCode}
            </if>
        )
        limit 1
    </select>

    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsReceipts">
        insert into project_wbs_receipts (id, project_id, project_code,
        project_name, requirement_code, requirement_status,
        requirement_type, confirm_mode, handle_by,
        handle_name, batch_upload_paths, batch_upload_path_ids,
        reason, handle_at, design_release_lot_number,
        remark, producer_id, producer_name,
        process_name, project_submit, unit_id,
        create_by, create_at, update_by,
        update_at, deleted_flag, version,
        renson_remark, submit_reason, rel_receipts_id,
        init, init_sequence)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.projectId,jdbcType=BIGINT}, #{item.projectCode,jdbcType=VARCHAR},
            #{item.projectName,jdbcType=VARCHAR}, #{item.requirementCode,jdbcType=VARCHAR}, #{item.requirementStatus,jdbcType=INTEGER},
            #{item.requirementType,jdbcType=INTEGER}, #{item.confirmMode,jdbcType=INTEGER}, #{item.handleBy,jdbcType=BIGINT},
            #{item.handleName,jdbcType=VARCHAR}, #{item.batchUploadPaths,jdbcType=VARCHAR}, #{item.batchUploadPathIds,jdbcType=VARCHAR},
            #{item.reason,jdbcType=VARCHAR}, #{item.handleAt,jdbcType=TIMESTAMP}, #{item.designReleaseLotNumber,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.producerId,jdbcType=BIGINT}, #{item.producerName,jdbcType=VARCHAR},
            #{item.processName,jdbcType=VARCHAR}, #{item.projectSubmit,jdbcType=TINYINT}, #{item.unitId,jdbcType=BIGINT},
            #{item.createBy,jdbcType=BIGINT}, #{item.createAt,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT},
            #{item.updateAt,jdbcType=TIMESTAMP}, #{item.deletedFlag,jdbcType=TINYINT}, #{item.version,jdbcType=BIGINT},
            #{item.rensonRemark,jdbcType=VARCHAR}, #{item.submitReason,jdbcType=VARCHAR}, #{item.relReceiptsId,jdbcType=BIGINT},
            #{item.init,jdbcType=TINYINT}, #{item.initSequence,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="queryDesignInfoByProjectId" parameterType="java.lang.Long" resultType="com.midea.pam.common.ctc.entity.ProjectWbsReceipts">
        select id, requirement_code from  project_wbs_receipts where project_id = #{projectId} and requirement_status not in (4,5)
    </select>

    <select id="queryRequirementCodeByDesignId" parameterType="java.lang.Long" resultType="java.lang.String">
        select requirement_code from project_wbs_receipts p inner join project_wbs_receipts_design_plan_rel r on p.id = project_wbs_receipts_id
        where r.design_plan_detail_id = #{designId} and r.deleted_flag = 0 and p.requirement_status not in (4,5)
    </select>

</mapper>