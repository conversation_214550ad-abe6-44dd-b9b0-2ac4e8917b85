<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectReopenHeaderMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ProjectReopenHeader">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, deleted_flag, create_at, create_by, update_at, update_by, status, project_id, 
    remark
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.ProjectReopenHeaderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_reopen_header
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_reopen_header
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_reopen_header
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.ProjectReopenHeader">
    insert into project_reopen_header (id, deleted_flag, create_at, 
      create_by, update_at, update_by, 
      status, project_id, remark
      )
    values (#{id,jdbcType=BIGINT}, #{deletedFlag,jdbcType=TINYINT}, #{createAt,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{status,jdbcType=INTEGER}, #{projectId,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.ProjectReopenHeader">
    insert into project_reopen_header
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.ProjectReopenHeaderExample" resultType="java.lang.Long">
    select count(*) from project_reopen_header
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.ProjectReopenHeader">
    update project_reopen_header
    <set>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.ProjectReopenHeader">
    update project_reopen_header
    set deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      project_id = #{projectId,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>