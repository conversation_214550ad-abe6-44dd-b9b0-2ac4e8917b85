<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.midea.pam.ctc.mapper.ProjectExtMapper">
    <sql id="conditionForDisplay">
        <if test="userIds != null">
            and wh.user_id in
            <foreach collection="userIds" item="userId" index="index" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="projectIds != null">
            and wh.project_id in
            <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
    </sql>
    <select id="selectProjectForUser" resultType="com.midea.pam.common.ctc.dto.ProjectDto">
        SELECT
            project.id,
            project.code,
            project.name,
            project.type,
            project.manager_name as managerName,
            project.start_date as startDate,
            project.end_date as endDate,
            project.status,
            projectType.`name` as typeName
        FROM
            project_member member
        LEFT JOIN project project ON member.project_id = project.id
        LEFT JOIN project_type projectType ON projectType.id = project.type
        WHERE
            project.id IS NOT NULL
        <if test="userBy != null and userBy != ''">
            and member.user_id = #{userBy}
        </if>
        <if test="projectStatus != null and projectStatus != ''">
            and project.`status` = #{projectStatus}
        </if>
    </select>

    <select id="countForDisplay" parameterType="com.midea.pam.common.ctc.dto.WorkingHourQueryDto"
            resultType="java.lang.Long">
        select
        count(*)
        from working_hour wh
        left join project p on p.id = wh.project_id
        where 1=1
        and p.status >= 4 and p.status <![CDATA[ <> ]]> 12
        <include refid="conditionForDisplay"></include>
    </select>

    <select id="filterLastMilepostAndApproved" resultType="java.lang.Long" parameterType="list">
        SELECT
            milepost.project_id
        FROM project_milepost milepost
        where milepost.id =
        (
        SELECT pm.id
        FROM project_milepost pm
        WHERE pm.help_flag = 0 and pm.deleted_flag = 0
        <if test="projectIds != null">
            and pm.project_id in
            <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        and pm.project_id = milepost.project_id
        order by pm.order_num desc
        limit 1
        )
        and milepost.`status` = 2
    </select>

    <select id="list" resultType="com.midea.pam.common.ctc.dto.ProjectDto">
        select
            p.id,
            p.code,
            p.name,
            p.business_id as businessId,
            p.business_name as businessName,
            p.customer_id as customerId,
            p.customer_name as customerName,
            p.budget_cost as budgetCost,
            p.status,
            p.end_date as endDate,
            p.start_date as startDate,
            p.type,
            p.amount,
            p.manager_name as managerName,
            p.manager_id as managerId,
            p.preview_flag as previewFlag,
            p.price_type as priceType,
            p.unit_id as unitId,
            p.ou_id as ouId,
            pcr.contract_id as contractId,
            c.name as contractName,
            c.code as contractCode,
            pt.name as typeName,
            pt.cost_method as costMethod,
            pt.milestone_base_date as milestoneBaseDate,
            p.financial as financial,
            p.create_at as createAt,
            p.create_by as createBy,
            p.is_objective_project as isObjectiveProject,
            p.project_level as projectLevel,
            p.budget_material_import_flag as budgetMaterialImportFlag,
            (select rdur.product_unit_id
            from pam_statistics.report_department_unit_rel rdur
            left join pam_basedata.product_unit up on rdur.product_unit_id = up.id AND up.deleted_flag = 0
            WHERE rdur.deleted_flag = 0
            and rdur.unit_id = p.unit_id
            limit 1)              as productUnitId,
            (select up.name
            from pam_statistics.report_department_unit_rel rdur
            left join pam_basedata.product_unit up on rdur.product_unit_id = up.id AND up.deleted_flag = 0
            WHERE rdur.deleted_flag = 0
            and rdur.unit_id = p.unit_id
            limit 1)              as productUnitName
        from
            project p
        left join project_contract_rs pcr on
            pcr.project_id = p.id
            and pcr.deleted_flag = 0
        left join contract c on
            pcr.contract_id = c.id
        left join project_type pt on pt.id = p.`type`
        where p.deleted_flag = 0
        <if test="isObjectiveProject != null and isObjectiveProject != ''">
            and p.is_objective_project = #{isObjectiveProject}
        </if>
        <if test="projectLevel != null and projectLevel != ''">
            and p.project_level = #{projectLevel}
        </if>
        <if test="code != null and code != ''">
            and p.code like concat('%', #{code}, '%')
        </if>

        <if test="name != null and name != ''">
            and p.name like concat('%', #{name}, '%')
        </if>

        <if test="customerName != null and customerName != ''">
            and p.customer_name like concat('%', #{customerName}, '%')
        </if>

        <if test="managerName != null and managerName != ''">
            and p.manager_name like concat('%', #{managerName}, '%')
        </if>

        <if test="status != null">
            and p.status = #{status}
        </if>

        <if test="priceType != null">
            and p.price_type = #{priceType}
        </if>

        <if test="type != null">
            and p.type = #{type}
        </if>

        <if test="ouId != null">
            and p.ou_id = #{ouId}
        </if>

        <if test="previewFlag != null">
            and p.preview_flag = #{previewFlag}
        </if>

        <if test="customerIds != null">
            and p.customer_id in
            <foreach collection="customerIds" item="customerId" index="index" open="(" separator="," close=")">
                #{customerId}
            </foreach>
        </if>

        <if test="businessId != null and businessId != ''">
            and p.business_id like concat('%', #{businessId}, '%')
        </if>

        <!--<if test="businessIds != null">
            and p.business_id in
            <foreach collection="businessIds" item="businessId" index="index" open="(" separator="," close=")">
                #{businessId}
            </foreach>
        </if>-->

        <if test="contractCode != null and contractCode != ''">
            and c.code like concat('%', #{contractCode}, '%')
        </if>

        <if test="ouIds != null">
            and p.ou_id in
            <foreach collection="ouIds" item="ouId" index="index" open="(" separator="," close=")">
                #{ouId}
            </foreach>
        </if>

        <if test="types != null">
            and p.type in
            <foreach collection="types" item="type" index="index" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>

        <!-- 项目属性 -->
        <if test="priceTypes != null">
            and p.price_type in
            <foreach collection="priceTypes" item="priceType" index="index" open="(" separator="," close=")">
                #{priceType}
            </foreach>
        </if>

        <if test="statuses != null">
            and p.status in
            <foreach collection="statuses" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        <if test="previewFlags != null">
            and p.preview_flag in
            <foreach collection="previewFlags" item="previewFlag" index="index" open="(" separator="," close=")">
                #{previewFlag}
            </foreach>
        </if>

        <if test="unitIds != null">
            and p.unit_id in
            <foreach collection="unitIds" item="unitId" index="index" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>

        <!-- 我的项目 数据权限 -->
        <if test="me">
            and (p.id in (
                select
                pm.project_id
                from
                project_member pm
                where
                pm.user_id = #{userId}
                and pm.deleted_flag = 0)
            or p.manager_id = #{userId}
            or p.create_by = #{userId}
            or p.financial = #{userId} )
        </if>

        <if test="fuzzyLike != null and fuzzyLike != ''">
            and p.code LIKE concat('%', #{fuzzyLike}, '%')
            or p.name LIKE concat('%', #{fuzzyLike}, '%')
        </if>
        group by p.id
        order by p.create_at desc
    </select>

    <select id="listProjectMilepostsByProjectIds" resultType="com.midea.pam.common.ctc.vo.ProjectMilepostExcelVO" parameterType="list">
        select
            p.name as projectName,
            p.code as projectCode,
            pm.order_num as orderNum,
            pm.name as name,
            pm.status as status,
            case pm.`status` when 0 then '进行中' when 1 then '评审中' when 2 then '评审通过' when 3 then '驳回' when 4 then '废弃' else '' end as statusName,
            pm.base_start_time as baseStartTime,
            pm.base_end_time as baseEndTime,
            pm.start_time as startTime,
            pm.end_time as endTime,
            pm.actual_start_time as actualStartTime,
            pm.actual_end_time as actualEndTime,
            pm.annex,
            pm.annex_type as annexType,
            pm.responsible as responsible,
            pr.name as notice,
            pm.help_flag as helpFlag
        from project_milepost pm
        inner join project p on pm.project_id = p.id
        left join project_role pr on pr.id = pm.notice
        where
            pm.deleted_flag = 0
            and p.deleted_flag = 0
            and p.id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        order by p.create_at desc, pm.help_flag asc, pm.order_num asc
    </select>

    <select id="listProjectMembersByProjectIds" resultType="com.midea.pam.common.ctc.vo.ProjectMemberExcelVO" parameterType="list">
        select
            p.name as projectName,
            p.code as projectCode,
            pm.user_id as userId,
            pm.user_name as userName,
            pm.telphone as telphone,
            pr.name as projectMemberRoleName,
            pm.start_date as startDate,
            pm.end_date as endDate
        from project_member pm
        inner join project p on pm.project_id = p.id
        left join project_role pr on pr.id = pm.project_member_role
        where pm.deleted_flag = 0
        and p.deleted_flag = 0
        and p.id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        order by p.create_at desc
    </select>

    <select id="getProjectMaterialAmounts" resultType="com.midea.pam.common.ctc.dto.ProjectDto" parameterType="list">
        select
            project_id as id,
            truncate(sum(ifnull(price_total, 0)), 2) as materialBudgetAmount
        from project_budget_material
        where deleted_flag = 0
        and parent_id is null
        and project_id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by project_id
    </select>

    <select id="getProjectHumanAmounts" resultType="com.midea.pam.common.ctc.dto.ProjectDto" parameterType="list">
        select
            project_id as id,
            truncate(sum(ifnull(price, 0) * ifnull(number, 0) * ifnull(days, 0)), 2) as humanBudgetAmount
        from project_budget_human
        where deleted_flag = 0
        and project_id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by project_id
    </select>

    <select id="getProjectTravelAmounts" resultType="com.midea.pam.common.ctc.dto.ProjectDto" parameterType="list">
        select
            project_id as id,
            truncate(sum(ifnull(come_back_traffic, 0) + ifnull(other, 0) + (ifnull(traffic, 0)+ifnull(hotel, 0)+ifnull(subsidy, 0))*ifnull(number, 0)), 2) as travelBudgetAmount
        from project_budget_travel
        where deleted_flag = 0
        and project_id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by project_id
    </select>

    <select id="getProjectFeeAmounts" resultType="com.midea.pam.common.ctc.dto.ProjectDto" parameterType="list">
        select
        project_id as id,
        truncate(sum(ifnull(amount, 0)), 2) as feeBudgetAmount
        from project_budget_fee
        where deleted_flag = 0
        and project_id in
        <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        group by project_id
    </select>

    <select id="getProjectByBusinessId" resultType="java.lang.Long">
        select id from project where business_id = #{businessCode} and deleted_flag = 0 and status in (3, 4, 9, 10, 7)
        <if test="currentProjectId != null">
            and <![CDATA[ id <> #{currentProjectId} ]]>
        </if>
        union
        select p.id
        from contract c
                 left join project_contract_rs pcr on c.id = pcr.contract_id
                 left join project p on pcr.project_id = p.id
        where c.deleted_flag = 0
          and pcr.deleted_flag = 0
          and p.deleted_flag = 0
          and c.status in (5, 10)
          and p.status in (3, 4, 9, 10, 7, -3)
          and c.business_id = #{businessId}
        <if test="currentProjectId != null">
            and <![CDATA[ p.id <> #{currentProjectId} ]]>
        </if>
    </select>

    <select id="getSummaryBudgetMaterial" resultType="java.math.BigDecimal">
        select  ifnull(sum(price_total),0)
        from project_budget_material
        where deleted_flag = 0
          and parent_id is null
          and project_id in
            <foreach collection="projectList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="getSummaryBudgetHuman" resultType="java.math.BigDecimal">
        select ifnull(sum(days*number*price),0)
        from project_budget_human
        where deleted_flag = 0
        and project_id in
        <foreach collection="projectList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getSummaryBudgetTravel" resultType="java.math.BigDecimal">
        select ifnull(sum((number*(hotel+subsidy+traffic)+other+come_back_traffic)),0)
        from project_budget_travel
        where deleted_flag = 0
        and project_id in
        <foreach collection="projectList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getSummaryBudgetfee" resultType="java.math.BigDecimal">
        select ifnull(sum(amount),0)
        from project_budget_fee
        where deleted_flag = 0
        and project_id in
        <foreach collection="projectList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectRemainContract" resultType="com.midea.pam.common.ctc.entity.Contract">
        select c.*
        from contract c
                 inner join contract cp on c.parent_id = cp.id
                 left join project_contract_rs pcr on pcr.contract_id = c.id
                 left join project p on p.id = pcr.project_id
        where c.deleted_flag = 0
          and cp.deleted_flag = 0
          and (pcr.id is null or pcr.deleted_flag = 0)
          and (p.id is null or p.deleted_flag = 0)
          and cp.status in (4, 5, 8, 10)
          and cp.business_id = #{businessId}
          and (p.id is null or p.status not in (3, 4, 9, 10, -3, 7))
    </select>

    <select id="selectRemainContractNew" resultType="java.lang.Long">
        select
            c.id
        from contract c
        left join project_contract_rs pcr on pcr.contract_id = c.id and pcr.deleted_flag = 0
        left join project p on p.id = pcr.project_id and p.deleted_flag = 0
        where c.deleted_flag = 0
        and (
            <!-- 项目状态：审批中/项目进行中/项目变更中/预立项转正审批中/预立项转正驳回/结项/项目终止审批中/项目终止/结项项目重新打开审批中/终止项目重新打开审批中 -->
            p.id is null or p.status not in (3, 4, 9, -3, 7, 10, 15, 16, 18, 19)
            <if test="currentProjectId != null">
                or p.id = #{currentProjectId}
            </if>
        )
        and c.id in (
            select
               <!-- 如果合同有子合同则取所有未关联商机的子合同，否则取自身(自身便是子合同) -->
               coalesce(c.id, cp.id)
            from contract cp
            left join contract c on c.parent_id = cp.id
            where cp.status in (4, 5, 8, 10)
            and cp.deleted_flag = 0
            and cp.business_id = #{businessId}
            and c.business_id is null
        )

    </select>

    <select id="getDetailBudgetByBusinessId"
            resultType="com.midea.pam.common.crm.dto.ProjectSummaryBudget">
        select p.id as id, p.code as code, p.name as name, p.manager_name as managerName,
               (select  ifnull(sum(price_total),0)
                from project_budget_material
                where deleted_flag = 0
                  and parent_id is null and project_id = p.id) as materialCost,
               (select ifnull(sum(days*number*price),0)
                from project_budget_human
                where deleted_flag = 0
                  and project_id = p.id and type = 2) as outerCost,
               (select ifnull(sum(days*number*price),0)
                from project_budget_human
                where deleted_flag = 0
                  and project_id = p.id and type = 1) as innerCost,
               (select ifnull(sum((number*(hotel+subsidy+traffic)+other+come_back_traffic)),0)
                from project_budget_travel
                where deleted_flag = 0
                  and project_id = p.id) as travelCost,
               (select ifnull(sum(amount),0)
                from project_budget_fee
                where deleted_flag = 0
                  and project_id = p.id) as feeCost
        from project p
        where p.deleted_flag = 0
          and p.business_id = #{businessCode}
          and p.status in (3, 4, 9, 10, -3, 7)
        <if test="currentProjectId != null">
            and <![CDATA[ p.id <> #{currentProjectId} ]]>
        </if>
    </select>
    <select id="checkPurchaseOrder" resultType="java.lang.Long">
        select
        count(0)
        from
        milepost_design_plan_detail pd
        left join purchase_material_release_detail rd
        ON pd.id = rd.`design_plan_detail_id`
        AND rd.`deleted_flag` = 0
        left join purchase_material_requirement pmr
        on pmr.id = rd.`purchase_requirement_id`
        and pmr.`deleted_flag` = 0
        left join purchase_order_detail pod
        on pod.`material_purchase_requirement_id` = pmr.`id`
        and pod.`deleted_flag` = 0
        where pd.`deleted_flag` = 0
        and pd.`whether_model` is null
        and (
        pmr.`status` != 2
        or pod.`status` not in (3, 4)
        )
        and pd.parent_id in
        (select
        id
        from
        milepost_design_plan_detail pd
        where pd.`deleted_flag` = 0
        and pd.`project_budget_material_id` in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        )
    </select>
    <select id="selectRdmProjectByApplyDate" resultType="com.midea.pam.common.ctc.entity.Project">
        select distinct p.*
        from project p
                 inner join project_type pt on p.type = pt.id
                 left join project_member pm on p.id = pm.project_id
        where p.deleted_flag = 0
          and pm.deleted_flag = 0 and pm.user_id = #{userId}
          and <![CDATA[ pm.start_date <= #{applyDate} and pm.end_date >= #{applyDate} ]]>
          and p.status in (4,9,10,-3,7)
          and pt.name = '人力外包（RDM）';
    </select>

    <select id="selectProjectByMember" resultType="com.midea.pam.common.ctc.dto.ProjectDto">
        select distinct p.id, p.name, pm.start_date as startTime, pm.end_date as endTime
        from project p
                 inner join project_type pt on p.type = pt.id
                 left join project_member pm on p.id = pm.project_id
        where p.deleted_flag = 0
          and pm.deleted_flag = 0 and pm.user_id = #{userId}
          and <![CDATA[ pm.start_date <= #{endDate} and pm.end_date >= #{startDate} ]]>
          and p.status in (4,9,10,-3,7)
          <if test="projectId != null">
              and <![CDATA[ p.id <> #{projectId} ]]>
          </if>
          and pt.project_member_distinct = 1
          <if test="unitId != null" >
              and pt.unit_id = #{unitId}
          </if>
    </select>

    <select id="getPCInvRecMilestoneInf" resultType="com.midea.pam.common.ctc.dto.PCInvRecDTO">
        select c.id contractId,
               c.code contractCode,
                 pcr.project_id projectId,
               ipd.id invoicePlanDetailId,
               ipd.milestone_id invMilestoneId,
               mts.milepost_stage invMilepostStage,
               rpd.id as receiptPlanDetailId,
               rpd.milestone_id recMilestoneId,
               mts1.milepost_stage recMilepostStage
               from contract c
        left join project_contract_rs pcr on c.id = pcr.contract_id and pcr.deleted_flag = 0
        left join invoice_plan_detail ipd on ipd.contract_id = c.id and ipd.deleted_flag = 0
        left join milepost_template_stage mts on mts.id = ipd.milestone_id and mts.deleted_flag = 0
        left join receipt_plan_detail rpd on rpd.contract_id = c.id and rpd.deleted_flag = 0
        left join milepost_template_stage mts1 on mts1.id = rpd.milestone_id and mts1.deleted_flag = 0
        where 1=1 and pcr.project_id = #{projectId}
    </select>

    <select id="getSecondUnits" resultType="long">
        select
        id
        from  pam_basedata.unit
        where 1 = 1
        <if test="unitId !=null and unitId !=''">
            and parent_id = #{unitId}
        </if>
    </select>

    <select id="getUnitIdByProjectId" resultType="java.lang.Long">
        select unit_id from project_type
        where id = (select type from project where id = #{projectId});
    </select>
    <select id="findByTechnologyLeaderIdOrManagerId" resultType="com.midea.pam.common.ctc.entity.Project">
        select p.* from project p
        where p.deleted_flag = false
        <if test="projectId != null">
            and p.id = #{projectId,jdbcType=BIGINT}
        </if>
        <if test="userId != null">
            and p.technology_leader_id = #{userId,jdbcType=BIGINT}
        </if>
        union select p1.* from project p1
        where p1.deleted_flag = false
        <if test="projectId != null">
            and p1.id = #{projectId,jdbcType=BIGINT}
        </if>
        <if test="userId != null">
            and p1.manager_id = #{userId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="queryRequirementDeliverMrp" resultType="java.lang.Boolean">
        select requriement_deliver_mrp
        from project_type where id = (
            select type from project where id = #{projectId}
        )
    </select>

    <select id="getProjectDetail" resultType="java.lang.Long">
        select distinct id
        from project
        where ou_id = #{operatingUnitId}
          and status not in (10, 12, 16)
    </select>

    <select id="queryHumanChangeSummaryHistory"
            resultType="com.midea.pam.common.ctc.entity.ProjectBudgetHumanChangeSummaryHistory">
        select
            p.id as projectId,
            t2.header_id as headerId,
            ifnull(t1.price_total,0) as beforeAmount,
            ifnull(t2.price_total,0) as afterAmount,
            ifnull(t2.price_total,0) - ifnull(t1.price_total,0) as changeAmount,
            ifnull(t1.inner_total,0) as beforeInnerAmount,
            ifnull(t2.inner_total,0) as afterInnerAmount,
            ifnull(t2.inner_total,0) - ifnull(t1.inner_total,0) as changeInnerAmount,
            ifnull(t1.outer_total,0) as beforeOutsideAmount,
            ifnull(t2.outer_total,0) as afterOutsideAmount,
            ifnull(t2.outer_total,0) - ifnull(t1.outer_total,0) as changeOutsideAmount
        from
            project p
        left join (
                select
                    project_id,
                    ifnull(sum(if(type = 1, ifnull(days,0)*ifnull(number,0)*ifnull(price,0), 0)),0) as inner_total,
                    ifnull(sum(if(type = 2, ifnull(days,0)*ifnull(number,0)*ifnull(price,0), 0)),0) as outer_total,
                    ifnull(sum(ifnull(days,0)*ifnull(number,0)*ifnull(price,0)),0) as price_total
                from project_budget_human
                where deleted_flag = 0
                and project_id = #{projectId}) t1 on
            t1.project_id = p.id
        left join (
            select
                project_id,
                header_id,
                ifnull(sum(if(type = 1, ifnull(days,0)*ifnull(number,0)*ifnull(price,0), 0)),0) as inner_total,
                ifnull(sum(if(type = 2, ifnull(days,0)*ifnull(number,0)*ifnull(price,0), 0)),0) as outer_total,
                ifnull(sum(ifnull(days,0)*ifnull(number,0)*ifnull(price,0)),0) as price_total
            from project_budget_human_change_history
            where deleted_flag = 0
              and history_type = 1
              and project_id = #{projectId}
              and header_id = #{headerId}) t2 on
            t2.project_id = p.id
        where
            p.id = #{projectId}
    </select>

    <select id="queryMaterialChangeSummaryHistory"
            resultType="com.midea.pam.common.ctc.entity.ProjectBudgetMaterialChangeSummaryHistory">
        select
            p.id as projectId,
            t2.header_id as headerId,
            ifnull(t1.price_total,0) as beforeAmount,
            ifnull(t2.price_total,0) as afterAmount,
            ifnull(t2.price_total,0) - ifnull(t1.price_total,0) as change_amount
        from
            project p
                left join (
                select
                    project_id,
                    ifnull(sum(ifnull(price_total,0)), 0) as price_total
                from
                    project_budget_material
                where
                    deleted_flag = 0
                  and parent_id is null
                  and project_id = #{projectId}) t1 on
                t1.project_id = p.id
                left join (
                select
                    project_id,
                    header_id,
                    ifnull(sum(ifnull(price_total,0)), 0) as price_total
                from
                    project_budget_material_change_history
                where
                    deleted_flag = 0
                  and parent_id is null
                  and history_type = 1
                  and project_id = #{projectId}
                  and header_id = #{headerId}) t2 on
                t2.project_id = p.id
        where
            p.id = #{projectId}
    </select>

    <select id="queryTravelChangeSummaryHistory"
            resultType="com.midea.pam.common.ctc.entity.ProjectBudgetTravelChangeSummaryHistory">
        select
            p.id as projectId,
            t2.header_id as headerId,
            ifnull(t1.price_total,0) as beforeAmount,
            ifnull(t2.price_total,0) as afterAmount,
            ifnull(t2.price_total,0) - ifnull(t1.price_total,0) as change_amount
        from
            project p
        left join (
            select
                project_id,
                sum((ifnull(number, 0) * (ifnull(hotel, 0) + ifnull(subsidy, 0) + ifnull(traffic, 0)) + ifnull(other, 0) + ifnull(come_back_traffic, 0))) as price_total
            from project_budget_travel
            where deleted_flag = 0
            and project_id = #{projectId}) t1 on
            t1.project_id = p.id
        left join (
        select
            project_id,
            header_id,
            sum((ifnull(number, 0) * (ifnull(hotel, 0) + ifnull(subsidy, 0) + ifnull(traffic, 0)) + ifnull(other, 0) + ifnull(come_back_traffic, 0))) as price_total
        from project_budget_travel_change_history
        where deleted_flag = 0
          and history_type = 1
          and project_id = #{projectId}
          and header_id = #{headerId}) t2 on
            t2.project_id = p.id
        where
            p.id = #{projectId}
    </select>

    <select id="queryFeeChangeSummaryHistory"
            resultType="com.midea.pam.common.ctc.entity.ProjectBudgetFeeChangeSummaryHistory">
        select
            p.id as projectId,
            t2.header_id as headerId,
            ifnull(t1.price_total,0) as beforeAmount,
            ifnull(t2.price_total,0) as afterAmount,
            ifnull(t2.price_total,0) - ifnull(t1.price_total,0) as change_amount
        from
            project p
                left join (
                select
                    project_id,
                    ifnull(sum(ifnull(amount,0)),0) as price_total
                from project_budget_fee
                where deleted_flag = 0
                  and project_id = #{projectId}) t1 on
                t1.project_id = p.id
                left join (
                select
                    project_id,
                    header_id,
                    ifnull(sum(ifnull(amount,0)),0) as price_total
                from project_budget_fee_change_history
                where deleted_flag = 0
                  and history_type = 1
                  and project_id = #{projectId}
                  and header_id = #{headerId}) t2 on
                t2.project_id = p.id
        where
            p.id = #{projectId}
    </select>

    <select id="queryAssetChangeSummaryHistory"
            resultType="com.midea.pam.common.ctc.entity.ProjectBudgetAssetChangeSummaryHistory">
        select
            p.id as projectId,
            t2.header_id as headerId,
            ifnull(t1.price_total, 0) as beforeAmount,
            ifnull(t2.price_total, 0) as afterAmount,
            ifnull(t2.price_total, 0) - ifnull(t1.price_total, 0) as change_amount
        from
            project p
        left join (
            select
                project_id,
                ifnull(sum(ifnull(amount, 0)), 0) as price_total
            from
                project_budget_asset
            where
                deleted_flag = 0
                and project_id = #{projectId}
            ) t1 on t1.project_id = p.id
        left join (
            select
                project_id,
                header_id,
                ifnull(sum(ifnull(amount, 0)), 0) as price_total
            from
                project_budget_asset_change_history
            where
                deleted_flag = 0
                and history_type = 1
                and project_id = #{projectId}
                and header_id = #{headerId}
            ) t2 on t2.project_id = p.id
        where
            p.id = #{projectId}
    </select>

    <select id="queryChangeSummaryHistory"
            resultType="com.midea.pam.common.ctc.entity.ProjectBudgetChangeSummaryHistory">
        select p.id        as projectId,
               #{headerId} as headerId,
               ifnull(t1.before_amount,0)+ifnull(t2.before_amount,0)+ifnull(t3.before_amount,0)+ifnull(t4.before_amount,0)+ifnull(t5.before_amount,0) as beforeAmount,
               ifnull(t1.after_amount,0)+ifnull(t2.after_amount,0)+ifnull(t3.after_amount,0)+ifnull(t4.after_amount,0)+ifnull(t5.after_amount,0) as afterAmount,
               ifnull(t1.after_amount,0)+ifnull(t2.after_amount,0)+ifnull(t3.after_amount,0)+ifnull(t4.after_amount,0)+ifnull(t5.after_amount,0) -
               (ifnull(t1.before_amount,0)+ifnull(t2.before_amount,0)+ifnull(t3.before_amount,0)+ifnull(t4.before_amount,0)+ifnull(t5.before_amount,0)) as change_amount
        from project p
             left join project_budget_fee_change_summary_history t1
                on t1.project_id = p.id
                  and t1.header_id = #{headerId} and t1.deleted_flag = 0
             left join project_budget_human_change_summary_history t2
                on t2.project_id = p.id
                  and t2.header_id = #{headerId} and t2.deleted_flag = 0
             left join project_budget_material_change_summary_history t3
                on t3.project_id = p.id
                  and t3.header_id = #{headerId} and t3.deleted_flag = 0
             left join project_budget_travel_change_summary_history t4
                on t4.project_id = p.id
                  and t4.header_id = #{headerId} and t4.deleted_flag = 0
             left join project_budget_asset_change_summary_history t5
                on t5.project_id = p.id
                  and t5.header_id = #{headerId} and t5.deleted_flag = 0
        where p.id = #{projectId}
    </select>

    <select id="selectByIds" resultType="com.midea.pam.common.ctc.entity.Project">
        select
        p.id,
        p.name,
        p.code,
        p.status,
        p.type,
        p.ou_id as ouId,
        p.wbs_enabled as wbsEnabled,
        p.price_type as priceType,
        p.manager_id as managerId,
        p.project_source as projectSource
        from project p
        where p.id in
        <foreach collection="projectIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectByCodes" resultType="com.midea.pam.common.ctc.entity.Project">
        select id, code , name
        from project
        where code in
        <foreach collection="projectCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="selectByRequirementQuery" resultType="com.midea.pam.common.ctc.dto.ProjectDto">
        select
        p.id,
        p.code,
        p.name,
        p.manager_name as managerName,
        p.type,
        pt.name as typeName,
        p.unit_id as unitId,
        unit.unit_name as unitName,
        p.wbs_enabled as wbsEnabled,
        p.manager_id as managerId
        from
        pam_ctc.project p
        left join pam_ctc.project_type pt on
        pt.id = p.`type`
        left join pam_basedata.unit unit on
        unit.id = p.unit_id
        where
        p.deleted_flag = 0
    </select>

    <select id="countIdsProjectBudgetIncrementData" resultType="java.lang.Integer">
        select count(distinct p.id)
        from pam_ctc.project_fee_collection c
        left join pam_ctc.project p on c.project_id = p.id
        where p.deleted_flag = 0
        and c.deleted_flag = 0
        and c.ems_budget_id is not null
        and p.status in (-3, 4, 7, 9, 10, 12, 14, 15, 16, 18, 19)
        <if test="ouId != null">
            and p.ou_id = #{ouId}
        </if>
        <if test="lastUpdateStart != null">
            and (p.create_at <![CDATA[ >= ]]> #{lastUpdateStart} or p.update_at <![CDATA[ >= ]]> #{lastUpdateStart}
            or c.create_at <![CDATA[ >= ]]> #{lastUpdateStart} or c.update_at <![CDATA[ >= ]]> #{lastUpdateStart})
        </if>
        <if test="lastUpdateEnd != null">
            and (p.create_at <![CDATA[ <= ]]> #{lastUpdateEnd} or p.update_at <![CDATA[ <= ]]> #{lastUpdateEnd}
            or c.create_at <![CDATA[ <= ]]> #{lastUpdateEnd} or c.update_at <![CDATA[ <= ]]> #{lastUpdateEnd})
        </if>
    </select>

    <select id="selectIdsProjectBudgetIncrementData" resultType="java.lang.Long">
        select distinct p.id
        from pam_ctc.project_fee_collection c
        left join pam_ctc.project p on c.project_id = p.id
        where c.ems_budget_id is not null
        and p.status in (-3, 4, 7, 9, 10, 12, 14, 15, 16, 18, 19)
        <if test="ouId != null">
            and p.ou_id = #{ouId}
        </if>
        <if test="lastUpdateStart != null">
            and (p.create_at <![CDATA[ >= ]]> #{lastUpdateStart} or p.update_at <![CDATA[ >= ]]> #{lastUpdateStart}
            or c.create_at <![CDATA[ >= ]]> #{lastUpdateStart} or c.update_at <![CDATA[ >= ]]> #{lastUpdateStart})
        </if>
        <if test="lastUpdateEnd != null">
            and (p.create_at <![CDATA[ <= ]]> #{lastUpdateEnd} or p.update_at <![CDATA[ <= ]]> #{lastUpdateEnd}
            or c.create_at <![CDATA[ <= ]]> #{lastUpdateEnd} or c.update_at <![CDATA[ <= ]]> #{lastUpdateEnd})
        </if>
        limit #{offset}, #{limit}
    </select>

    <select id="selectSdpVoByIds" resultType="com.midea.pam.common.sdp.vo.ProjectSdpVo">
        select
            id as projectId,
            ou_id as ouId,
            code as projectNumber,
            name as projectName,
            deleted_flag as deletedFlag,
            case
            when status in (10, 18) then 10
            when status in (12, 15, 16, 19) then 16
            else 4
            end as status,
            manager_id as managerId,
            create_by as createBy,
            create_at as createAt,
            update_at as updateAt,
            financial
        from project
        where id in
        <foreach collection="projectIdList" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        and status in (-3, 4, 7, 9, 10, 12, 14, 15, 16, 18, 19)
        <if test="ouId != null">
            and ou_id = #{ouId}
        </if>
    </select>

    <select id="selectEmsBudgetSdpVoByIds" resultType="com.midea.pam.common.sdp.vo.ProjectFeeCollectionSdpVo">
        select
            id as projectFeeCollectionId,
            project_id as projectId,
            ems_budget_id as emsBudgetId,
            fee_item_id as feeItemId,
            fee_item_name as feeItemName,
            fee_type_id as feeTypeId,
            fee_type_name as feeTypeName,
            push_date as pushDate,
            wbs_code as wbsCode,
            wbs_name as wbsName,
            deleted_flag as deletedFlag
        from project_fee_collection
        where ems_budget_id is not null
        and project_id in
        <foreach collection="projectIdList" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </select>

    <select id="getWbsByProjectId" resultType="map">
        select
            olct.org_name as orgName,
            p.manager_name as managerName,
            wbs.rule_name as ruleName,
            wbs.constraints
        from
        pam_basedata.org_labor_cost_type_set olct
        inner join pam_basedata.ltc_user_info u on u.labor_cost_type_set_id = olct.id
        inner join pam_basedata.wbs_constraint wbs on wbs.org_labor_id = olct.id
        inner join pam_ctc.project p on p.wbs_template_info_id = wbs.template_id
        where olct.deleted_flag = 0
        and u.id = #{userId}
        and p.id = #{projectId}
        and wbs.deleted_flag = 0
    </select>

    <select id="getProjectContractStandardAmount" resultType="java.math.BigDecimal">
        select
            ifnull(sum(ifnull(c.excluding_tax_amount, 0) * ifnull(p.conversion_rate, 1)), 0)
        from pam_ctc.project_contract_rs rs
        left join pam_ctc.contract c on rs.contract_id = c.id
        left join pam_ctc.contract p on c.parent_id = p.id
        where rs.deleted_flag = 0
        and c.deleted_flag = 0
        and p.deleted_flag = 0
        and rs.project_id = #{projectId}
    </select>
</mapper>