<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PurchaseMaterialRequirementMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="erp_code" jdbcType="VARCHAR" property="erpCode" />
    <result column="pam_code" jdbcType="VARCHAR" property="pamCode" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="materiel_id" jdbcType="BIGINT" property="materielId" />
    <result column="materiel_descr" jdbcType="VARCHAR" property="materielDescr" />
    <result column="need_total" jdbcType="DECIMAL" property="needTotal" />
    <result column="unit_code" jdbcType="VARCHAR" property="unitCode" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="approved_supplier_number" jdbcType="INTEGER" property="approvedSupplierNumber" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="conversion_type" jdbcType="VARCHAR" property="conversionType" />
    <result column="conversion_date" jdbcType="TIMESTAMP" property="conversionDate" />
    <result column="conversion_rate" jdbcType="DECIMAL" property="conversionRate" />
    <result column="purchase_type" jdbcType="INTEGER" property="purchaseType" />
    <result column="project_wbs_receipts_id" jdbcType="BIGINT" property="projectWbsReceiptsId" />
    <result column="requirement_code" jdbcType="VARCHAR" property="requirementCode" />
    <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
    <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
    <result column="dispatch_is" jdbcType="TINYINT" property="dispatchIs" />
    <result column="closed_amount" jdbcType="DECIMAL" property="closedAmount" />
    <result column="closed_quantity" jdbcType="DECIMAL" property="closedQuantity" />
    <result column="wbs_demand_os_cost" jdbcType="DECIMAL" property="wbsDemandOsCost" />
    <result column="figure_number" jdbcType="VARCHAR" property="figureNumber" />
    <result column="receipts_publish_date" jdbcType="TIMESTAMP" property="receiptsPublishDate" />
    <result column="has_amount" jdbcType="INTEGER" property="hasAmount" />
    <result column="due_amount" jdbcType="INTEGER" property="dueAmount" />
    <result column="chart_version" jdbcType="VARCHAR" property="chartVersion" />
    <result column="design_release_lot_number" jdbcType="VARCHAR" property="designReleaseLotNumber" />
    <result column="init" jdbcType="TINYINT" property="init" />
    <result column="init_sequence" jdbcType="VARCHAR" property="initSequence" />
    <result column="requirement_type" jdbcType="TINYINT" property="requirementType" />
    <result column="delivery_address" jdbcType="VARCHAR" property="deliveryAddress" />
    <result column="consignee" jdbcType="VARCHAR" property="consignee" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="freeze_flag" jdbcType="TINYINT" property="freezeFlag" />
    <result column="freeze_reason" jdbcType="VARCHAR" property="freezeReason" />
    <result column="freeze_by_name" jdbcType="VARCHAR" property="freezeByName" />
    <result column="freeze_at" jdbcType="TIMESTAMP" property="freezeAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, erp_code, pam_code, delivery_time, materiel_id, materiel_descr, need_total, 
    unit_code, unit, approved_supplier_number, status, reason, deleted_flag, create_by, 
    create_at, update_by, update_at, currency, conversion_type, conversion_date, conversion_rate, 
    purchase_type, project_wbs_receipts_id, requirement_code, activity_code, wbs_summary_code,
    dispatch_is, closed_amount, closed_quantity, wbs_demand_os_cost, figure_number, receipts_publish_date,
    has_amount, due_amount, chart_version, design_release_lot_number, init, init_sequence,
    requirement_type, delivery_address, consignee, contact_phone, freeze_flag, freeze_reason,
    freeze_by_name, freeze_at
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from purchase_material_requirement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_material_requirement
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_material_requirement
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement">
    insert into purchase_material_requirement (id, project_id, erp_code, 
      pam_code, delivery_time, materiel_id, 
      materiel_descr, need_total, unit_code, 
      unit, approved_supplier_number, status, 
      reason, deleted_flag, create_by, 
      create_at, update_by, update_at, 
      currency, conversion_type, conversion_date, 
      conversion_rate, purchase_type, project_wbs_receipts_id, 
      requirement_code, activity_code, wbs_summary_code,
      dispatch_is, closed_amount, closed_quantity, 
      wbs_demand_os_cost, figure_number, receipts_publish_date,
      has_amount, due_amount, chart_version,
      design_release_lot_number, init, init_sequence,
      requirement_type, delivery_address, consignee,
      contact_phone, freeze_flag, freeze_reason,
      freeze_by_name, freeze_at)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{erpCode,jdbcType=VARCHAR}, 
      #{pamCode,jdbcType=VARCHAR}, #{deliveryTime,jdbcType=TIMESTAMP}, #{materielId,jdbcType=BIGINT}, 
      #{materielDescr,jdbcType=VARCHAR}, #{needTotal,jdbcType=DECIMAL}, #{unitCode,jdbcType=VARCHAR}, 
      #{unit,jdbcType=VARCHAR}, #{approvedSupplierNumber,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{reason,jdbcType=VARCHAR}, #{deletedFlag,jdbcType=TINYINT}, #{createBy,jdbcType=BIGINT}, 
      #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, 
      #{currency,jdbcType=VARCHAR}, #{conversionType,jdbcType=VARCHAR}, #{conversionDate,jdbcType=TIMESTAMP}, 
      #{conversionRate,jdbcType=DECIMAL}, #{purchaseType,jdbcType=INTEGER}, #{projectWbsReceiptsId,jdbcType=BIGINT}, 
      #{requirementCode,jdbcType=VARCHAR}, #{activityCode,jdbcType=VARCHAR}, #{wbsSummaryCode,jdbcType=VARCHAR},
      #{dispatchIs,jdbcType=TINYINT}, #{closedAmount,jdbcType=DECIMAL}, #{closedQuantity,jdbcType=DECIMAL}, 
      #{wbsDemandOsCost,jdbcType=DECIMAL}, #{figureNumber,jdbcType=VARCHAR}, #{receiptsPublishDate,jdbcType=TIMESTAMP},
      #{hasAmount,jdbcType=INTEGER}, #{dueAmount,jdbcType=INTEGER}, #{chartVersion,jdbcType=VARCHAR},
      #{designReleaseLotNumber,jdbcType=VARCHAR}, #{init,jdbcType=TINYINT}, #{initSequence,jdbcType=VARCHAR},
      #{requirementType,jdbcType=TINYINT}, #{deliveryAddress,jdbcType=VARCHAR}, #{consignee,jdbcType=VARCHAR},
      #{contactPhone,jdbcType=VARCHAR}, #{freezeFlag,jdbcType=TINYINT}, #{freezeReason,jdbcType=VARCHAR},
      #{freezeByName,jdbcType=VARCHAR}, #{freezeAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement">
    insert into purchase_material_requirement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="erpCode != null">
        erp_code,
      </if>
      <if test="pamCode != null">
        pam_code,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="materielId != null">
        materiel_id,
      </if>
      <if test="materielDescr != null">
        materiel_descr,
      </if>
      <if test="needTotal != null">
        need_total,
      </if>
      <if test="unitCode != null">
        unit_code,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="approvedSupplierNumber != null">
        approved_supplier_number,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="conversionType != null">
        conversion_type,
      </if>
      <if test="conversionDate != null">
        conversion_date,
      </if>
      <if test="conversionRate != null">
        conversion_rate,
      </if>
      <if test="purchaseType != null">
        purchase_type,
      </if>
      <if test="projectWbsReceiptsId != null">
        project_wbs_receipts_id,
      </if>
      <if test="requirementCode != null">
        requirement_code,
      </if>
      <if test="activityCode != null">
        activity_code,
      </if>
      <if test="wbsSummaryCode != null">
        wbs_summary_code,
      </if>
      <if test="dispatchIs != null">
        dispatch_is,
      </if>
      <if test="closedAmount != null">
        closed_amount,
      </if>
      <if test="closedQuantity != null">
        closed_quantity,
      </if>
      <if test="wbsDemandOsCost != null">
        wbs_demand_os_cost,
      </if>
      <if test="figureNumber != null">
        figure_number,
      </if>
      <if test="receiptsPublishDate != null">
        receipts_publish_date,
      </if>
      <if test="hasAmount != null">
        has_amount,
      </if>
      <if test="dueAmount != null">
        due_amount,
      </if>
      <if test="chartVersion != null">
        chart_version,
      </if>
      <if test="designReleaseLotNumber != null">
        design_release_lot_number,
      </if>
      <if test="init != null">
        init,
      </if>
      <if test="initSequence != null">
        init_sequence,
      </if>
      <if test="requirementType != null">
        requirement_type,
      </if>
      <if test="deliveryAddress != null">
        delivery_address,
      </if>
      <if test="consignee != null">
        consignee,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="freezeFlag != null">
        freeze_flag,
      </if>
      <if test="freezeReason != null">
        freeze_reason,
      </if>
      <if test="freezeByName != null">
        freeze_by_name,
      </if>
      <if test="freezeAt != null">
        freeze_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="erpCode != null">
        #{erpCode,jdbcType=VARCHAR},
      </if>
      <if test="pamCode != null">
        #{pamCode,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="materielId != null">
        #{materielId,jdbcType=BIGINT},
      </if>
      <if test="materielDescr != null">
        #{materielDescr,jdbcType=VARCHAR},
      </if>
      <if test="needTotal != null">
        #{needTotal,jdbcType=DECIMAL},
      </if>
      <if test="unitCode != null">
        #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="approvedSupplierNumber != null">
        #{approvedSupplierNumber,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="conversionType != null">
        #{conversionType,jdbcType=VARCHAR},
      </if>
      <if test="conversionDate != null">
        #{conversionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="conversionRate != null">
        #{conversionRate,jdbcType=DECIMAL},
      </if>
      <if test="purchaseType != null">
        #{purchaseType,jdbcType=INTEGER},
      </if>
      <if test="projectWbsReceiptsId != null">
        #{projectWbsReceiptsId,jdbcType=BIGINT},
      </if>
      <if test="requirementCode != null">
        #{requirementCode,jdbcType=VARCHAR},
      </if>
      <if test="activityCode != null">
        #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="wbsSummaryCode != null">
        #{wbsSummaryCode,jdbcType=VARCHAR},
      </if>
      <if test="dispatchIs != null">
        #{dispatchIs,jdbcType=TINYINT},
      </if>
      <if test="closedAmount != null">
        #{closedAmount,jdbcType=DECIMAL},
      </if>
      <if test="closedQuantity != null">
        #{closedQuantity,jdbcType=DECIMAL},
      </if>
      <if test="wbsDemandOsCost != null">
        #{wbsDemandOsCost,jdbcType=DECIMAL},
      </if>
      <if test="figureNumber != null">
        #{figureNumber,jdbcType=VARCHAR},
      </if>
      <if test="receiptsPublishDate != null">
        #{receiptsPublishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="hasAmount != null">
        #{hasAmount,jdbcType=INTEGER},
      </if>
      <if test="dueAmount != null">
        #{dueAmount,jdbcType=INTEGER},
      </if>
      <if test="chartVersion != null">
        #{chartVersion,jdbcType=VARCHAR},
      </if>
      <if test="designReleaseLotNumber != null">
        #{designReleaseLotNumber,jdbcType=VARCHAR},
      </if>
      <if test="init != null">
        #{init,jdbcType=TINYINT},
      </if>
      <if test="initSequence != null">
        #{initSequence,jdbcType=VARCHAR},
      </if>
      <if test="requirementType != null">
        #{requirementType,jdbcType=TINYINT},
      </if>
      <if test="deliveryAddress != null">
        #{deliveryAddress,jdbcType=VARCHAR},
      </if>
      <if test="consignee != null">
        #{consignee,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="freezeFlag != null">
        #{freezeFlag,jdbcType=TINYINT},
      </if>
      <if test="freezeReason != null">
        #{freezeReason,jdbcType=VARCHAR},
      </if>
      <if test="freezeByName != null">
        #{freezeByName,jdbcType=VARCHAR},
      </if>
      <if test="freezeAt != null">
        #{freezeAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementExample" resultType="java.lang.Long">
    select count(*) from purchase_material_requirement
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement">
    update purchase_material_requirement
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="erpCode != null">
        erp_code = #{erpCode,jdbcType=VARCHAR},
      </if>
      <if test="pamCode != null">
        pam_code = #{pamCode,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="materielId != null">
        materiel_id = #{materielId,jdbcType=BIGINT},
      </if>
      <if test="materielDescr != null">
        materiel_descr = #{materielDescr,jdbcType=VARCHAR},
      </if>
      <if test="needTotal != null">
        need_total = #{needTotal,jdbcType=DECIMAL},
      </if>
      <if test="unitCode != null">
        unit_code = #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="approvedSupplierNumber != null">
        approved_supplier_number = #{approvedSupplierNumber,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="conversionType != null">
        conversion_type = #{conversionType,jdbcType=VARCHAR},
      </if>
      <if test="conversionDate != null">
        conversion_date = #{conversionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="conversionRate != null">
        conversion_rate = #{conversionRate,jdbcType=DECIMAL},
      </if>
      <if test="purchaseType != null">
        purchase_type = #{purchaseType,jdbcType=INTEGER},
      </if>
      <if test="projectWbsReceiptsId != null">
        project_wbs_receipts_id = #{projectWbsReceiptsId,jdbcType=BIGINT},
      </if>
      <if test="requirementCode != null">
        requirement_code = #{requirementCode,jdbcType=VARCHAR},
      </if>
      <if test="activityCode != null">
        activity_code = #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="wbsSummaryCode != null">
        wbs_summary_code = #{wbsSummaryCode,jdbcType=VARCHAR},
      </if>
      <if test="dispatchIs != null">
        dispatch_is = #{dispatchIs,jdbcType=TINYINT},
      </if>
      <if test="closedAmount != null">
        closed_amount = #{closedAmount,jdbcType=DECIMAL},
      </if>
      <if test="closedQuantity != null">
        closed_quantity = #{closedQuantity,jdbcType=DECIMAL},
      </if>
      <if test="wbsDemandOsCost != null">
        wbs_demand_os_cost = #{wbsDemandOsCost,jdbcType=DECIMAL},
      </if>
      <if test="figureNumber != null">
        figure_number = #{figureNumber,jdbcType=VARCHAR},
      </if>
      <if test="receiptsPublishDate != null">
        receipts_publish_date = #{receiptsPublishDate,jdbcType=TIMESTAMP},
      </if>
      <if test="hasAmount != null">
        has_amount = #{hasAmount,jdbcType=INTEGER},
      </if>
      <if test="dueAmount != null">
        due_amount = #{dueAmount,jdbcType=INTEGER},
      </if>
      <if test="chartVersion != null">
        chart_version = #{chartVersion,jdbcType=VARCHAR},
      </if>
      <if test="designReleaseLotNumber != null">
        design_release_lot_number = #{designReleaseLotNumber,jdbcType=VARCHAR},
      </if>
      <if test="init != null">
        init = #{init,jdbcType=TINYINT},
      </if>
      <if test="initSequence != null">
        init_sequence = #{initSequence,jdbcType=VARCHAR},
      </if>
      <if test="requirementType != null">
        requirement_type = #{requirementType,jdbcType=TINYINT},
      </if>
      <if test="deliveryAddress != null">
        delivery_address = #{deliveryAddress,jdbcType=VARCHAR},
      </if>
      <if test="consignee != null">
        consignee = #{consignee,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="freezeFlag != null">
        freeze_flag = #{freezeFlag,jdbcType=TINYINT},
      </if>
      <if test="freezeReason != null">
        freeze_reason = #{freezeReason,jdbcType=VARCHAR},
      </if>
      <if test="freezeByName != null">
        freeze_by_name = #{freezeByName,jdbcType=VARCHAR},
      </if>
      <if test="freezeAt != null">
        freeze_at = #{freezeAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement">
    update purchase_material_requirement
    set project_id = #{projectId,jdbcType=BIGINT},
      erp_code = #{erpCode,jdbcType=VARCHAR},
      pam_code = #{pamCode,jdbcType=VARCHAR},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      materiel_id = #{materielId,jdbcType=BIGINT},
      materiel_descr = #{materielDescr,jdbcType=VARCHAR},
      need_total = #{needTotal,jdbcType=DECIMAL},
      unit_code = #{unitCode,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      approved_supplier_number = #{approvedSupplierNumber,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      currency = #{currency,jdbcType=VARCHAR},
      conversion_type = #{conversionType,jdbcType=VARCHAR},
      conversion_date = #{conversionDate,jdbcType=TIMESTAMP},
      conversion_rate = #{conversionRate,jdbcType=DECIMAL},
      purchase_type = #{purchaseType,jdbcType=INTEGER},
      project_wbs_receipts_id = #{projectWbsReceiptsId,jdbcType=BIGINT},
      requirement_code = #{requirementCode,jdbcType=VARCHAR},
      activity_code = #{activityCode,jdbcType=VARCHAR},
      wbs_summary_code = #{wbsSummaryCode,jdbcType=VARCHAR},
      dispatch_is = #{dispatchIs,jdbcType=TINYINT},
      closed_amount = #{closedAmount,jdbcType=DECIMAL},
      closed_quantity = #{closedQuantity,jdbcType=DECIMAL},
      wbs_demand_os_cost = #{wbsDemandOsCost,jdbcType=DECIMAL},
      figure_number = #{figureNumber,jdbcType=VARCHAR},
      receipts_publish_date = #{receiptsPublishDate,jdbcType=TIMESTAMP},
      has_amount = #{hasAmount,jdbcType=INTEGER},
      due_amount = #{dueAmount,jdbcType=INTEGER},
      chart_version = #{chartVersion,jdbcType=VARCHAR},
      design_release_lot_number = #{designReleaseLotNumber,jdbcType=VARCHAR},
      init = #{init,jdbcType=TINYINT},
      init_sequence = #{initSequence,jdbcType=VARCHAR},
      requirement_type = #{requirementType,jdbcType=TINYINT},
      delivery_address = #{deliveryAddress,jdbcType=VARCHAR},
      consignee = #{consignee,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      freeze_flag = #{freezeFlag,jdbcType=TINYINT},
      freeze_reason = #{freezeReason,jdbcType=VARCHAR},
      freeze_by_name = #{freezeByName,jdbcType=VARCHAR},
      freeze_at = #{freezeAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>