<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PurchaseOrderStandardTermsMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="purchase_order_id" jdbcType="BIGINT" property="purchaseOrderId" />
    <result column="association_terms_id" jdbcType="BIGINT" property="associationTermsId" />
    <result column="terms_code" jdbcType="VARCHAR" property="termsCode" />
    <result column="terms_name" jdbcType="VARCHAR" property="termsName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_by_name" jdbcType="VARCHAR" property="createByName" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_by_name" jdbcType="VARCHAR" property="updateByName" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms">
    <result column="terms_display_content" jdbcType="LONGVARCHAR" property="termsDisplayContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, purchase_order_id, association_terms_id, terms_code, terms_name, remark, create_by, 
    create_by_name, create_at, update_by, update_by_name, update_at, version, deleted_flag
  </sql>
  <sql id="Blob_Column_List">
    terms_display_content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrderStandardTermsExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from purchase_order_standard_terms
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrderStandardTermsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from purchase_order_standard_terms
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from purchase_order_standard_terms
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_order_standard_terms
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms">
    insert into purchase_order_standard_terms (id, purchase_order_id, association_terms_id, 
      terms_code, terms_name, remark, 
      create_by, create_by_name, create_at, 
      update_by, update_by_name, update_at, 
      version, deleted_flag, terms_display_content
      )
    values (#{id,jdbcType=BIGINT}, #{purchaseOrderId,jdbcType=BIGINT}, #{associationTermsId,jdbcType=BIGINT}, 
      #{termsCode,jdbcType=VARCHAR}, #{termsName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=BIGINT}, #{createByName,jdbcType=VARCHAR}, #{createAt,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{updateByName,jdbcType=VARCHAR}, #{updateAt,jdbcType=TIMESTAMP}, 
      #{version,jdbcType=BIGINT}, #{deletedFlag,jdbcType=TINYINT}, #{termsDisplayContent,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms">
    insert into purchase_order_standard_terms
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id,
      </if>
      <if test="associationTermsId != null">
        association_terms_id,
      </if>
      <if test="termsCode != null">
        terms_code,
      </if>
      <if test="termsName != null">
        terms_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createByName != null">
        create_by_name,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateByName != null">
        update_by_name,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="termsDisplayContent != null">
        terms_display_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="purchaseOrderId != null">
        #{purchaseOrderId,jdbcType=BIGINT},
      </if>
      <if test="associationTermsId != null">
        #{associationTermsId,jdbcType=BIGINT},
      </if>
      <if test="termsCode != null">
        #{termsCode,jdbcType=VARCHAR},
      </if>
      <if test="termsName != null">
        #{termsName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createByName != null">
        #{createByName,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateByName != null">
        #{updateByName,jdbcType=VARCHAR},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="termsDisplayContent != null">
        #{termsDisplayContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrderStandardTermsExample" resultType="java.lang.Long">
    select count(*) from purchase_order_standard_terms
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms">
    update purchase_order_standard_terms
    <set>
      <if test="purchaseOrderId != null">
        purchase_order_id = #{purchaseOrderId,jdbcType=BIGINT},
      </if>
      <if test="associationTermsId != null">
        association_terms_id = #{associationTermsId,jdbcType=BIGINT},
      </if>
      <if test="termsCode != null">
        terms_code = #{termsCode,jdbcType=VARCHAR},
      </if>
      <if test="termsName != null">
        terms_name = #{termsName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createByName != null">
        create_by_name = #{createByName,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateByName != null">
        update_by_name = #{updateByName,jdbcType=VARCHAR},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="termsDisplayContent != null">
        terms_display_content = #{termsDisplayContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms">
    update purchase_order_standard_terms
    set purchase_order_id = #{purchaseOrderId,jdbcType=BIGINT},
      association_terms_id = #{associationTermsId,jdbcType=BIGINT},
      terms_code = #{termsCode,jdbcType=VARCHAR},
      terms_name = #{termsName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_by_name = #{createByName,jdbcType=VARCHAR},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_by_name = #{updateByName,jdbcType=VARCHAR},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=BIGINT},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      terms_display_content = #{termsDisplayContent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms">
    update purchase_order_standard_terms
    set purchase_order_id = #{purchaseOrderId,jdbcType=BIGINT},
      association_terms_id = #{associationTermsId,jdbcType=BIGINT},
      terms_code = #{termsCode,jdbcType=VARCHAR},
      terms_name = #{termsName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_by_name = #{createByName,jdbcType=VARCHAR},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_by_name = #{updateByName,jdbcType=VARCHAR},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=BIGINT},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>