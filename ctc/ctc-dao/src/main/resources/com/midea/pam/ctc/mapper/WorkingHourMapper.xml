<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.WorkingHourMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.WorkingHour">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="week" jdbcType="VARCHAR" property="week" />
    <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate" />
    <result column="apply_working_hours" jdbcType="DECIMAL" property="applyWorkingHours" />
    <result column="ihr_attend_hours" jdbcType="DECIMAL" property="ihrAttendHours" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_mip" jdbcType="VARCHAR" property="userMip" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="apply_org" jdbcType="VARCHAR" property="applyOrg" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="process_id" jdbcType="BIGINT" property="processId" />
    <result column="invoice_apply_header_id" jdbcType="BIGINT" property="invoiceApplyHeaderId" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="labor_cost_type" jdbcType="VARCHAR" property="laborCostType" />
    <result column="labor_cost_type_set_id" jdbcType="BIGINT" property="laborCostTypeSetId" />
    <result column="cost_money" jdbcType="DECIMAL" property="costMoney" />
    <result column="actual_cost_money" jdbcType="DECIMAL" property="actualCostMoney" />
    <result column="actual_working_hours" jdbcType="DECIMAL" property="actualWorkingHours" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="approve_user_id" jdbcType="BIGINT" property="approveUserId" />
    <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime" />
    <result column="approve_user_name" jdbcType="VARCHAR" property="approveUserName" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="biz_unit_id" jdbcType="BIGINT" property="bizUnitId" />
    <result column="labor_cost_source_unit_id" jdbcType="BIGINT" property="laborCostSourceUnitId" />
    <result column="is_import" jdbcType="TINYINT" property="isImport" />
    <result column="rdm_flag" jdbcType="TINYINT" property="rdmFlag" />
    <result column="source_flag" jdbcType="TINYINT" property="sourceFlag" />
    <result column="write_off_status" jdbcType="TINYINT" property="writeOffStatus" />
    <result column="stay_approve_user_id" jdbcType="BIGINT" property="stayApproveUserId" />
    <result column="stay_approve_user_mip" jdbcType="VARCHAR" property="stayApproveUserMip" />
    <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="wbs_budget_code" jdbcType="VARCHAR" property="wbsBudgetCode" />
    <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
    <result column="labor_wbs_cost_id" jdbcType="BIGINT" property="laborWbsCostId" />
    <result column="labor_wbs_cost_name" jdbcType="VARCHAR" property="laborWbsCostName" />
    <result column="project_activity_code" jdbcType="VARCHAR" property="projectActivityCode" />
    <result column="ou_id" jdbcType="BIGINT" property="ouId" />
    <result column="full_pay_ou_id" jdbcType="BIGINT" property="fullPayOuId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, week, apply_date, apply_working_hours, ihr_attend_hours, status, user_id, user_mip, 
    org_id, org_name, apply_org, project_id, project_code, process_id, invoice_apply_header_id, 
    level, labor_cost_type, labor_cost_type_set_id, cost_money, actual_cost_money, actual_working_hours, 
    remarks, delete_flag, create_by, create_at, update_by, update_at, approve_user_id, 
    approve_time, approve_user_name, user_type, biz_unit_id, labor_cost_source_unit_id, 
    is_import, rdm_flag, source_flag, write_off_status, stay_approve_user_id, stay_approve_user_mip, 
    vendor_code, role_name, wbs_budget_code, wbs_summary_code, labor_wbs_cost_id, labor_wbs_cost_name, 
    project_activity_code, ou_id, full_pay_ou_id
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.WorkingHourExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from working_hour
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from working_hour
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from working_hour
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.WorkingHour">
    insert into working_hour (id, week, apply_date, 
      apply_working_hours, ihr_attend_hours, status, 
      user_id, user_mip, org_id, 
      org_name, apply_org, project_id, 
      project_code, process_id, invoice_apply_header_id, 
      level, labor_cost_type, labor_cost_type_set_id, 
      cost_money, actual_cost_money, actual_working_hours, 
      remarks, delete_flag, create_by, 
      create_at, update_by, update_at, 
      approve_user_id, approve_time, approve_user_name, 
      user_type, biz_unit_id, labor_cost_source_unit_id, 
      is_import, rdm_flag, source_flag, 
      write_off_status, stay_approve_user_id, stay_approve_user_mip, 
      vendor_code, role_name, wbs_budget_code, 
      wbs_summary_code, labor_wbs_cost_id, labor_wbs_cost_name, 
      project_activity_code, ou_id, full_pay_ou_id
      )
    values (#{id,jdbcType=BIGINT}, #{week,jdbcType=VARCHAR}, #{applyDate,jdbcType=TIMESTAMP}, 
      #{applyWorkingHours,jdbcType=DECIMAL}, #{ihrAttendHours,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, 
      #{userId,jdbcType=BIGINT}, #{userMip,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT}, 
      #{orgName,jdbcType=VARCHAR}, #{applyOrg,jdbcType=VARCHAR}, #{projectId,jdbcType=BIGINT}, 
      #{projectCode,jdbcType=VARCHAR}, #{processId,jdbcType=BIGINT}, #{invoiceApplyHeaderId,jdbcType=BIGINT}, 
      #{level,jdbcType=VARCHAR}, #{laborCostType,jdbcType=VARCHAR}, #{laborCostTypeSetId,jdbcType=BIGINT}, 
      #{costMoney,jdbcType=DECIMAL}, #{actualCostMoney,jdbcType=DECIMAL}, #{actualWorkingHours,jdbcType=DECIMAL}, 
      #{remarks,jdbcType=VARCHAR}, #{deleteFlag,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, 
      #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, 
      #{approveUserId,jdbcType=BIGINT}, #{approveTime,jdbcType=TIMESTAMP}, #{approveUserName,jdbcType=VARCHAR}, 
      #{userType,jdbcType=VARCHAR}, #{bizUnitId,jdbcType=BIGINT}, #{laborCostSourceUnitId,jdbcType=BIGINT}, 
      #{isImport,jdbcType=TINYINT}, #{rdmFlag,jdbcType=TINYINT}, #{sourceFlag,jdbcType=TINYINT}, 
      #{writeOffStatus,jdbcType=TINYINT}, #{stayApproveUserId,jdbcType=BIGINT}, #{stayApproveUserMip,jdbcType=VARCHAR}, 
      #{vendorCode,jdbcType=VARCHAR}, #{roleName,jdbcType=VARCHAR}, #{wbsBudgetCode,jdbcType=VARCHAR}, 
      #{wbsSummaryCode,jdbcType=VARCHAR}, #{laborWbsCostId,jdbcType=BIGINT}, #{laborWbsCostName,jdbcType=VARCHAR}, 
      #{projectActivityCode,jdbcType=VARCHAR}, #{ouId,jdbcType=BIGINT}, #{fullPayOuId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.WorkingHour">
    insert into working_hour
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="week != null">
        week,
      </if>
      <if test="applyDate != null">
        apply_date,
      </if>
      <if test="applyWorkingHours != null">
        apply_working_hours,
      </if>
      <if test="ihrAttendHours != null">
        ihr_attend_hours,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userMip != null">
        user_mip,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="applyOrg != null">
        apply_org,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectCode != null">
        project_code,
      </if>
      <if test="processId != null">
        process_id,
      </if>
      <if test="invoiceApplyHeaderId != null">
        invoice_apply_header_id,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="laborCostType != null">
        labor_cost_type,
      </if>
      <if test="laborCostTypeSetId != null">
        labor_cost_type_set_id,
      </if>
      <if test="costMoney != null">
        cost_money,
      </if>
      <if test="actualCostMoney != null">
        actual_cost_money,
      </if>
      <if test="actualWorkingHours != null">
        actual_working_hours,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="approveUserId != null">
        approve_user_id,
      </if>
      <if test="approveTime != null">
        approve_time,
      </if>
      <if test="approveUserName != null">
        approve_user_name,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="bizUnitId != null">
        biz_unit_id,
      </if>
      <if test="laborCostSourceUnitId != null">
        labor_cost_source_unit_id,
      </if>
      <if test="isImport != null">
        is_import,
      </if>
      <if test="rdmFlag != null">
        rdm_flag,
      </if>
      <if test="sourceFlag != null">
        source_flag,
      </if>
      <if test="writeOffStatus != null">
        write_off_status,
      </if>
      <if test="stayApproveUserId != null">
        stay_approve_user_id,
      </if>
      <if test="stayApproveUserMip != null">
        stay_approve_user_mip,
      </if>
      <if test="vendorCode != null">
        vendor_code,
      </if>
      <if test="roleName != null">
        role_name,
      </if>
      <if test="wbsBudgetCode != null">
        wbs_budget_code,
      </if>
      <if test="wbsSummaryCode != null">
        wbs_summary_code,
      </if>
      <if test="laborWbsCostId != null">
        labor_wbs_cost_id,
      </if>
      <if test="laborWbsCostName != null">
        labor_wbs_cost_name,
      </if>
      <if test="projectActivityCode != null">
        project_activity_code,
      </if>
      <if test="ouId != null">
        ou_id,
      </if>
      <if test="fullPayOuId != null">
        full_pay_ou_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="week != null">
        #{week,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        #{applyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="applyWorkingHours != null">
        #{applyWorkingHours,jdbcType=DECIMAL},
      </if>
      <if test="ihrAttendHours != null">
        #{ihrAttendHours,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userMip != null">
        #{userMip,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="applyOrg != null">
        #{applyOrg,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="processId != null">
        #{processId,jdbcType=BIGINT},
      </if>
      <if test="invoiceApplyHeaderId != null">
        #{invoiceApplyHeaderId,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="laborCostType != null">
        #{laborCostType,jdbcType=VARCHAR},
      </if>
      <if test="laborCostTypeSetId != null">
        #{laborCostTypeSetId,jdbcType=BIGINT},
      </if>
      <if test="costMoney != null">
        #{costMoney,jdbcType=DECIMAL},
      </if>
      <if test="actualCostMoney != null">
        #{actualCostMoney,jdbcType=DECIMAL},
      </if>
      <if test="actualWorkingHours != null">
        #{actualWorkingHours,jdbcType=DECIMAL},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="approveUserId != null">
        #{approveUserId,jdbcType=BIGINT},
      </if>
      <if test="approveTime != null">
        #{approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approveUserName != null">
        #{approveUserName,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="bizUnitId != null">
        #{bizUnitId,jdbcType=BIGINT},
      </if>
      <if test="laborCostSourceUnitId != null">
        #{laborCostSourceUnitId,jdbcType=BIGINT},
      </if>
      <if test="isImport != null">
        #{isImport,jdbcType=TINYINT},
      </if>
      <if test="rdmFlag != null">
        #{rdmFlag,jdbcType=TINYINT},
      </if>
      <if test="sourceFlag != null">
        #{sourceFlag,jdbcType=TINYINT},
      </if>
      <if test="writeOffStatus != null">
        #{writeOffStatus,jdbcType=TINYINT},
      </if>
      <if test="stayApproveUserId != null">
        #{stayApproveUserId,jdbcType=BIGINT},
      </if>
      <if test="stayApproveUserMip != null">
        #{stayApproveUserMip,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="roleName != null">
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="wbsBudgetCode != null">
        #{wbsBudgetCode,jdbcType=VARCHAR},
      </if>
      <if test="wbsSummaryCode != null">
        #{wbsSummaryCode,jdbcType=VARCHAR},
      </if>
      <if test="laborWbsCostId != null">
        #{laborWbsCostId,jdbcType=BIGINT},
      </if>
      <if test="laborWbsCostName != null">
        #{laborWbsCostName,jdbcType=VARCHAR},
      </if>
      <if test="projectActivityCode != null">
        #{projectActivityCode,jdbcType=VARCHAR},
      </if>
      <if test="ouId != null">
        #{ouId,jdbcType=BIGINT},
      </if>
      <if test="fullPayOuId != null">
        #{fullPayOuId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.WorkingHourExample" resultType="java.lang.Long">
    select count(*) from working_hour
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.WorkingHour">
    update working_hour
    <set>
      <if test="week != null">
        week = #{week,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        apply_date = #{applyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="applyWorkingHours != null">
        apply_working_hours = #{applyWorkingHours,jdbcType=DECIMAL},
      </if>
      <if test="ihrAttendHours != null">
        ihr_attend_hours = #{ihrAttendHours,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userMip != null">
        user_mip = #{userMip,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="applyOrg != null">
        apply_org = #{applyOrg,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        project_code = #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="processId != null">
        process_id = #{processId,jdbcType=BIGINT},
      </if>
      <if test="invoiceApplyHeaderId != null">
        invoice_apply_header_id = #{invoiceApplyHeaderId,jdbcType=BIGINT},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=VARCHAR},
      </if>
      <if test="laborCostType != null">
        labor_cost_type = #{laborCostType,jdbcType=VARCHAR},
      </if>
      <if test="laborCostTypeSetId != null">
        labor_cost_type_set_id = #{laborCostTypeSetId,jdbcType=BIGINT},
      </if>
      <if test="costMoney != null">
        cost_money = #{costMoney,jdbcType=DECIMAL},
      </if>
      <if test="actualCostMoney != null">
        actual_cost_money = #{actualCostMoney,jdbcType=DECIMAL},
      </if>
      <if test="actualWorkingHours != null">
        actual_working_hours = #{actualWorkingHours,jdbcType=DECIMAL},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="approveUserId != null">
        approve_user_id = #{approveUserId,jdbcType=BIGINT},
      </if>
      <if test="approveTime != null">
        approve_time = #{approveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approveUserName != null">
        approve_user_name = #{approveUserName,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="bizUnitId != null">
        biz_unit_id = #{bizUnitId,jdbcType=BIGINT},
      </if>
      <if test="laborCostSourceUnitId != null">
        labor_cost_source_unit_id = #{laborCostSourceUnitId,jdbcType=BIGINT},
      </if>
      <if test="isImport != null">
        is_import = #{isImport,jdbcType=TINYINT},
      </if>
      <if test="rdmFlag != null">
        rdm_flag = #{rdmFlag,jdbcType=TINYINT},
      </if>
      <if test="sourceFlag != null">
        source_flag = #{sourceFlag,jdbcType=TINYINT},
      </if>
      <if test="writeOffStatus != null">
        write_off_status = #{writeOffStatus,jdbcType=TINYINT},
      </if>
      <if test="stayApproveUserId != null">
        stay_approve_user_id = #{stayApproveUserId,jdbcType=BIGINT},
      </if>
      <if test="stayApproveUserMip != null">
        stay_approve_user_mip = #{stayApproveUserMip,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        vendor_code = #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="roleName != null">
        role_name = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="wbsBudgetCode != null">
        wbs_budget_code = #{wbsBudgetCode,jdbcType=VARCHAR},
      </if>
      <if test="wbsSummaryCode != null">
        wbs_summary_code = #{wbsSummaryCode,jdbcType=VARCHAR},
      </if>
      <if test="laborWbsCostId != null">
        labor_wbs_cost_id = #{laborWbsCostId,jdbcType=BIGINT},
      </if>
      <if test="laborWbsCostName != null">
        labor_wbs_cost_name = #{laborWbsCostName,jdbcType=VARCHAR},
      </if>
      <if test="projectActivityCode != null">
        project_activity_code = #{projectActivityCode,jdbcType=VARCHAR},
      </if>
      <if test="ouId != null">
        ou_id = #{ouId,jdbcType=BIGINT},
      </if>
      <if test="fullPayOuId != null">
        full_pay_ou_id = #{fullPayOuId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.WorkingHour">
    update working_hour
    set week = #{week,jdbcType=VARCHAR},
      apply_date = #{applyDate,jdbcType=TIMESTAMP},
      apply_working_hours = #{applyWorkingHours,jdbcType=DECIMAL},
      ihr_attend_hours = #{ihrAttendHours,jdbcType=DECIMAL},
      status = #{status,jdbcType=TINYINT},
      user_id = #{userId,jdbcType=BIGINT},
      user_mip = #{userMip,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      apply_org = #{applyOrg,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=BIGINT},
      project_code = #{projectCode,jdbcType=VARCHAR},
      process_id = #{processId,jdbcType=BIGINT},
      invoice_apply_header_id = #{invoiceApplyHeaderId,jdbcType=BIGINT},
      level = #{level,jdbcType=VARCHAR},
      labor_cost_type = #{laborCostType,jdbcType=VARCHAR},
      labor_cost_type_set_id = #{laborCostTypeSetId,jdbcType=BIGINT},
      cost_money = #{costMoney,jdbcType=DECIMAL},
      actual_cost_money = #{actualCostMoney,jdbcType=DECIMAL},
      actual_working_hours = #{actualWorkingHours,jdbcType=DECIMAL},
      remarks = #{remarks,jdbcType=VARCHAR},
      delete_flag = #{deleteFlag,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      approve_user_id = #{approveUserId,jdbcType=BIGINT},
      approve_time = #{approveTime,jdbcType=TIMESTAMP},
      approve_user_name = #{approveUserName,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=VARCHAR},
      biz_unit_id = #{bizUnitId,jdbcType=BIGINT},
      labor_cost_source_unit_id = #{laborCostSourceUnitId,jdbcType=BIGINT},
      is_import = #{isImport,jdbcType=TINYINT},
      rdm_flag = #{rdmFlag,jdbcType=TINYINT},
      source_flag = #{sourceFlag,jdbcType=TINYINT},
      write_off_status = #{writeOffStatus,jdbcType=TINYINT},
      stay_approve_user_id = #{stayApproveUserId,jdbcType=BIGINT},
      stay_approve_user_mip = #{stayApproveUserMip,jdbcType=VARCHAR},
      vendor_code = #{vendorCode,jdbcType=VARCHAR},
      role_name = #{roleName,jdbcType=VARCHAR},
      wbs_budget_code = #{wbsBudgetCode,jdbcType=VARCHAR},
      wbs_summary_code = #{wbsSummaryCode,jdbcType=VARCHAR},
      labor_wbs_cost_id = #{laborWbsCostId,jdbcType=BIGINT},
      labor_wbs_cost_name = #{laborWbsCostName,jdbcType=VARCHAR},
      project_activity_code = #{projectActivityCode,jdbcType=VARCHAR},
      ou_id = #{ouId,jdbcType=BIGINT},
      full_pay_ou_id = #{fullPayOuId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>