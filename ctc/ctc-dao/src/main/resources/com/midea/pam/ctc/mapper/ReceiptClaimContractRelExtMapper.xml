<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ReceiptClaimContractRelExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="receipt_claim_detail_id" jdbcType="BIGINT" property="receiptClaimDetailId"/>
        <result column="contract_id" jdbcType="BIGINT" property="contractId"/>
        <result column="allocated_amount" jdbcType="DECIMAL" property="allocatedAmount"/>
        <result column="local_allocated_amount" jdbcType="DECIMAL" property="localAllocatedAmount" />
        <result column="allocated_date" jdbcType="TIMESTAMP" property="allocatedDate"/>
        <result column="allocated_by" jdbcType="DECIMAL" property="allocatedBy"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag"/>
        <result column="receipt_code" jdbcType="VARCHAR" property="receiptCode"/>
        <result column="contract_name" jdbcType="VARCHAR" property="contractName"/>
        <result column="contract_code" jdbcType="VARCHAR" property="contractCode"/>
        <result column="contract_amount" jdbcType="DECIMAL" property="contractAmount"/>
        <result column="currency_code" jdbcType="VARCHAR" property="currencyCode"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="invoice_plan_detail_code" jdbcType="VARCHAR" property="invoicePlanDetailCode"/>
        <result column="invoice_plan_detail_amount" jdbcType="DECIMAL" property="invoicePlanDetailAmount"/>
        <result column="invoice_plan_detail_id" jdbcType="BIGINT" property="invoicePlanDetailId"/>
        <result column="milestone_id" jdbcType="BIGINT" property="milestoneId"/>
        <result column="requirement" jdbcType="VARCHAR" property="requirement"/>
        <result column="tax_included_price" jdbcType="DECIMAL" property="taxIncludedPrice"/>
        <result column="invoice_plan_id" jdbcType="BIGINT" property="invoicePlanId"/>
        <result column="invoice_type" jdbcType="VARCHAR" property="invoiceType"/>
        <result column="manager_name" jdbcType="VARCHAR" property="managerName"/>
        <result column="sales_manager_name" jdbcType="VARCHAR" property="salesManagerName"/>
        <result column="manager" jdbcType="VARCHAR" property="managerId"/>
        <result column="sales_manager" jdbcType="VARCHAR" property="salesManagerId"/>
        <result column="allocatable_amount" jdbcType="DECIMAL" property="allocatableAmount" />
    </resultMap>

    <sql id="Base_Column_List">
    rel.id,
    rel.receipt_claim_detail_id,
    rel.contract_id,
    rel.allocated_date,
    rel.allocated_by,
    rel.create_by,
    rel.create_at,
    rel.update_by,
    rel.update_at,
    rel.deleted_flag,
    rel.invoice_plan_detail_id,
    pd.milestone_id,
    ip.id as invoice_plan_id,
	d.receipt_code,
    c.code as contract_code,
	c.name as contract_name,
    pd.code as invoice_plan_detail_code,
	c.currency as currency_code,
    IFNULL(pd.amount, 0) as invoice_plan_detail_amount,
    pd.tax_included_price,
    rel.allocated_amount,
    rel.local_allocated_amount,
    rel.remark,
    pd.requirement,
    pd.invoice_type,
    c.amount as contract_amount,
	p.code as project_code,
	p.name as project_name,
    p.manager_name,
    c.manager,
    c.sales_manager
    </sql>

    <update id="updateDetailStatus">
        update
            receipt_claim_detail rcd
        left join ( (
            select
                case
                    when IFNULL(t.amount,0) > 0 and IFNULL(t.amount,0) <![CDATA[<]]> rcd.claim_amount then 2
                    when IFNULL(t.amount,0) = rcd.claim_amount then 3
                    else 1 end as STATUS,
                    rcd.id as rid
                from
                    receipt_claim_detail rcd
                left join (
                    select
                        IFNULL(sum(ir.receipt_write_off_amount),0) amount,
                        ir.receipt_claim_detail_id
                    from
                        receipt_claim_invoice_rel ir
                    where
                        ir.deleted_flag = 0
                        and ir.cancel_by is null
                    group by
                        ir.receipt_claim_detail_id) t on
                    rcd.id = t.receipt_claim_detail_id
                where
                    rcd.`deleted_flag` = 0) ) t2 on
            t2.rid = rcd.id
        set
            rcd.write_off_status = t2.STATUS
        where
            1 = 1
        <if test="receiptClaimDetailId != null">
            and rcd.id = #{receiptClaimDetailId}
        </if>
        <if test="receiptClaimDetailIdList != null and receiptClaimDetailIdList.size() > 0">
            and rcd.id in
            <foreach collection="receiptClaimDetailIdList" index="index" item="receiptClaimDetailId" open="("
                     separator="," close=")">
                #{receiptClaimDetailId}
            </foreach>
        </if>
    </update>

    <select id="getContractedList" parameterType="com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        receipt_claim_contract_rel rel
        LEFT JOIN contract c ON rel.contract_id = c.id
        LEFT JOIN project_contract_rs rs ON rs.contract_id = c.id and rs.deleted_flag = 0
        LEFT JOIN project p ON rs.project_id = p.id
        LEFT JOIN receipt_claim_detail d ON rel.receipt_claim_detail_id = d.id
        LEFT JOIN invoice_plan_detail pd on rel.invoice_plan_detail_id = pd.id
        LEFT JOIN invoice_plan ip on pd.plan_id = ip.id and ip.deleted_flag = 0
        where 1=1
        and rel.receipt_claim_detail_id = #{receiptClaimDetailId}
        and rel.deleted_flag = 0
    </select>

    <select id="getReceiptClaimContractRelByContract" parameterType="long"
            resultMap="BaseResultMap">
        select
            c.code as contract_code,
            c.name as contract_name,
            c.currency as currency_code,
            rel.receipt_claim_detail_id,
            rel.contract_id,
            sum(rel.allocated_amount) as allocated_amount,
            sum(rel.local_allocated_amount) as local_allocated_amount,
            c.amount as contract_amount,
            p.code as project_code,
            p.name as project_name
        from
            receipt_claim_contract_rel rel
            left join contract c on
            rel.contract_id = c.id
            left join project_contract_rs rs on
            rs.contract_id = c.id
            and rs.deleted_flag = 0
            left join project p on
            rs.project_id = p.id
        where
            rel.receipt_claim_detail_id = #{receiptClaimDetailId}
            and rel.deleted_flag = 0
            group by rel.contract_id
    </select>

    <select id="getTotalContractedAmount" parameterType="long" resultType="java.math.BigDecimal">
    SELECT
	  SUM(allocated_amount)
	from receipt_claim_contract_rel
	where 1=1
	and receipt_claim_detail_id = #{receiptClaimDetailId}
	and deleted_flag = 0
  </select>
    <select id="findCanAutoData" resultType="com.midea.pam.common.ctc.dto.ReceiptClaimInvoiceRelDto">
        SELECT
        rcd.id AS receiptClaimDetailId,
        ir.id AS invoiceId,
        IFNULL(ir.`tax_included_price`,0) - IFNULL(t1.amount,0) + IFNULL(t2.has_amount,0) AS remainingPrice,
        IFNULL(t2.has_amount,0) AS hasAmount
        FROM
        receipt_claim_detail rcd
        INNER JOIN receipt_claim_contract_rel rcrel
        ON rcd.id = rcrel.receipt_claim_detail_id
        AND rcrel.deleted_flag = 0
        INNER JOIN
        (SELECT
        SUM(IFNULL(rcrel.allocated_amount, 0)) AS allocated_amount,
        rcrel.`receipt_claim_detail_id`
        FROM
        receipt_claim_contract_rel rcrel
        WHERE rcrel.`deleted_flag` = 0
        GROUP BY rcrel.`receipt_claim_detail_id`) t
        ON rcd.id = t.`receipt_claim_detail_id`
        AND rcd.`claim_amount` >= t.allocated_amount
        INNER JOIN
            (SELECT
              ir.id AS id,
              ir.contract_id AS contract_id,
              iad.plan_detail_id AS plan_detail_id
            FROM
              pam_ctc.invoice_apply_details iad
              LEFT JOIN pam_ctc.invoice_receivable ir
                ON iad.id = ir.apply_detail_id
                AND ir.deleted_flag = 0
            WHERE iad.deleted_flag = 0) tt
            ON rcrel.contract_id = tt.contract_id
            AND tt.plan_detail_id = rcrel.invoice_plan_detail_id
        INNER JOIN invoice_receivable ir
            ON ir.id = tt.id
            AND ir.`deleted_flag` = 0
        LEFT JOIN
        (SELECT
        a.id as invoice_id,
        SUM(IFNULL(b.write_off_amount, 0)) AS amount
        FROM
        pam_ctc.`invoice_receivable` a
        LEFT JOIN pam_ctc.`receipt_claim_invoice_rel` b
        ON a.id = b.invoice_id
        AND b.deleted_flag = 0
        and b.erp_status != 6
        WHERE a.deleted_flag = 0
        GROUP BY a.id) t1
        ON t1.invoice_id = ir.id
        LEFT JOIN receipt_claim rc
        ON rc.id = rcd.`receipt_claim_id`
        AND rc.`deleted_flag` = 0
        LEFT JOIN
        (SELECT
          SUM(b.tax_included_price) AS has_amount,
          a.id
        FROM
          invoice_receivable a
          INNER JOIN invoice_receivable b
            ON a.invoice_code = b.old_invoice_code
            AND b.deleted_flag = 0
        WHERE a.deleted_flag = 0
        GROUP BY a.id) t2 ON t2.id = ir.`id`
        WHERE rcd.`deleted_flag` = 0
        AND rcd.`contract_status` != 1
        AND rcd.`claim_status` = 2
        AND rcd.`erp_status` = 3
        AND (
        t1.amount <![CDATA[<]]> ir.`tax_included_price`
        OR t1.amount IS NULL
        )
        AND ir.`erp_status` = 1
        AND ir.`ou_id` = rc.`ou_id`
        AND rcd.`customer_code` = ir.`customer_code`
        AND rcrel.receipt_claim_detail_id = #{receiptClaimDetailId}
        AND rcrel.`invoice_plan_detail_id` = #{invoicePlanDetailId}
        ORDER BY ir.`id`,rcd.`id`
    </select>
    <select id="findCanAutoReceiptClaim" resultType="com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto">
        select
            rcd.id as receiptClaimDetailId,
            rcrel.contract_id as contractId,
            c.ou_id as ouId,
            rc.is_import as isImport ,
            rcd.claim_amount as claimAmount ,
            rcrel.allocated_amount as allocatedamount,
            IFNULL(rcrel.allocated_amount, 0) - IFNULL(rcir.write_off_amount, 0) as remainingContractAmount
        from
            pam_ctc.receipt_claim_detail rcd
            inner join pam_ctc.receipt_claim rc on
            rcd.receipt_claim_id = rc.id
            and rc.deleted_flag = 0
            left join (
                select
                    receipt_claim_detail_id,
                    contract_id,
                    SUM(allocated_amount) as allocated_amount
                from
                    receipt_claim_contract_rel
                where
                    deleted_flag = 0
                group by
                receipt_claim_detail_id, contract_id
            ) rcrel on
            rcd.id = rcrel.receipt_claim_detail_id
            and rcd.claim_amount >= rcrel.allocated_amount
            inner join pam_ctc.contract c on
            rcrel.contract_id = c.id
            and c.deleted_flag = 0
            left join (
                select
                    receipt_claim_detail_id,
                    ir.contract_id,
                    SUM(write_off_amount) as write_off_amount
                from
                    pam_ctc.receipt_claim_invoice_rel rcir
                    left join pam_ctc.invoice_receivable ir on
                    rcir.invoice_id = ir.id
                    and ir.deleted_flag = 0
                where
                    rcir.erp_status != 6
                    and rcir.deleted_flag = 0
                group by rcir.receipt_claim_detail_id, ir.contract_id
            ) rcir on
            rcd.id = rcir.receipt_claim_detail_id
            and rcrel.contract_id = rcir.contract_id
        where
            rcd.contract_status in (2, 3)
            and rcd.claim_status = 2
            and rcd.erp_status = 3
            and IFNULL(rcd.deleted_flag, 0) = 0
            and rcrel.contract_id is not null
            and rc.currency_code = c.currency
            and rc.ou_id = c.ou_id
            and rcrel.allocated_amount <![CDATA[>]]> IFNULL(rcir.write_off_amount, 0)
        order by rcd.accounting_date asc
    </select>
    <select id="getDataGroupByContracted" resultType="com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto">
        select
        p.code AS projectCode,
        p.name aS projectName,
        p.manager_name,
        p.sales_manager_name,
        c.customer_id as customerId,
        c.customer_name as customerName,
        round(sum(rel.allocated_amount),2) as thisAmount,
        round(t.allocated_amount,2) as totalAmount,
        round((rp.amount - t.allocated_amount),2) as remainingContractAmount,
        c.profit_department_id as profitDepartmentId,
        c.id as contractId,
        c.code as contractCode,
        c.name as contractName,
        rc.pay_date as payDate,
        c.sales_manager as salesManagerId,
        c.manager as managerId,
        cc.name as parentContractName
        from
        contract c
        left join contract cc
        on c.parent_id = cc.id
        and cc.deleted_flag = 0
        LEFT JOIN project_contract_rs rs
        ON rs.contract_id = c.id
        and rs.deleted_flag = 0
        LEFT JOIN project p
        ON rs.project_id = p.id
        left join receipt_claim_contract_rel rel
        on c.id = rel.contract_id
        and rel.deleted_flag = 0
        left join receipt_plan rp
        on c.id = rp.contract_id
        and rp.deleted_flag = 0
        left join receipt_claim_detail rcd
        on rel.receipt_claim_detail_id = rcd.id
        and rcd.deleted_flag = 0
        left join receipt_claim rc
        on rc.id = rcd.receipt_claim_id
        and rc.deleted_flag = 0
        left join
        (select
        sum(cr.allocated_amount) as allocated_amount,
        cr.contract_id
        from
        receipt_claim_contract_rel cr
        where cr.deleted_flag = 0
        group by cr.contract_id) t
        on t.contract_id = c.id
        WHERE c.deleted_flag = 0
        <if test="ids != null">
            and rel.id in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        group by c.id
    </select>
    <select id="getDeletedRelData" resultType="com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto">
        select
        p.code AS projectCode,
        p.name aS projectName,
        c.customer_id as customerId,
        c.customer_name as customerName,
        round(sum(rel.allocated_amount), 2) as thisAmount,
        c.profit_department_id as profitDepartmentId,
        c.id as contractId,
        c.code as contractCode,
        c.name as contractName,
        rc.pay_date as payDate,
        c.sales_manager as salesManagerId,
        c.manager as managerId,
        cc.name as parentContractName
        from
        contract c
        left join contract cc
        on c.parent_id = cc.id
        and cc.deleted_flag = 0
        LEFT JOIN project_contract_rs rs
        ON rs.contract_id = c.id
        and rs.deleted_flag = 0
        LEFT JOIN project p
        ON rs.project_id = p.id
        inner join receipt_claim_contract_rel rel
        on c.id = rel.contract_id
        and rel.deleted_flag = 1
        left join receipt_plan rp
        on c.id = rp.contract_id
        and rp.deleted_flag = 0
        left join receipt_claim_detail rcd
        on rel.receipt_claim_detail_id = rcd.id
        and rcd.deleted_flag = 0
        left join receipt_claim rc
        on rc.id = rcd.receipt_claim_id
        and rc.deleted_flag = 0
        WHERE c.deleted_flag = 0
        <if test="ids != null">
            and rel.id in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        group by c.id
    </select>

    <select id="getTotalInvoicePlanDetailAmount" parameterType="long" resultType="java.math.BigDecimal">
    SELECT
	  SUM(allocated_amount)
	from receipt_claim_contract_rel
	where 1=1
	and invoice_plan_detail_id = #{invoicePlanDetailId}
	and deleted_flag = 0
  </select>

    <select id="getContractList" resultType="com.midea.pam.common.ctc.dto.ReceiptClaimContractRelDto">
        select
            con.id as contractId,
            con.code as contractCode,
            con.name as contractName,
            ipd.code as invoicePlanDetailCode,
            con.currency as currencyCode,
            IFNULL(ipd.amount, 0) as invoicePlanDetailAmount,
            ipd.tax_included_price as taxIncludedPrice,
            IFNULL(ipd.amount, 0)-IFNULL(ipd.actual_receipt_amount, 0) as allocatableAmount,
            ipd.milestone_id as milestoneId,
            ipd.requirement,
            ipd.invoice_type as invoiceType,
            con.amount as contractAmount,
            p.code as projectCode,
            p.name as projectName,
            ip.id as invoicePlanId,
            ipd.id as invoicePlanDetailId,
            ipd.num as invoicePlanDetailNum
        from
            contract con
            left join invoice_plan ip on
            con.id = ip.contract_id
            and ip.deleted_flag = 0
            left join invoice_plan_detail ipd on
            ip.id = ipd.plan_id
            and ipd.deleted_flag = 0
            left join project_contract_rs rs on
            rs.contract_id = con.id
            and rs.deleted_flag = 0
            left join project p on
            rs.project_id = p.id
        <where>
            <if test="customerId != null">
                AND con.customer_id = #{customerId}
            </if>
            <if test="contractCode != null and contractCode != ''">
                AND con.code like concat('%', #{contractCode}, '%')
            </if>
            <if test="contractName != null and contractName != ''">
                AND con.name like concat('%', #{contractName}, '%')
            </if>
            <if test="invoicePlanDetailCode != null and invoicePlanDetailCode != ''">
                AND ipd.code like concat('%', #{invoicePlanDetailCode}, '%')
            </if>
            <if test="invoicePlanDetailAmount != null">
                AND ipd.amount = #{invoicePlanDetailAmount}
            </if>
            <if test="contractAmount != null">
                AND con.amount = #{contractAmount}
            </if>
            <if test="projectCode != null and projectCode != ''">
                AND p.code like concat('%', #{projectCode}, '%')
            </if>
            <if test="projectName != null and projectName != ''">
                AND p.name like concat('%', #{projectName}, '%')
            </if>
            <if test="ouId != null">
                AND con.ou_id = #{ouId}
            </if>
            <if test="allocatableAmount != null">
                AND IFNULL(ipd.amount, 0)-IFNULL(ipd.actual_receipt_amount, 0) = #{allocatableAmount}
            </if>
            <if test="allocatableTagList != null and allocatableTagList.size != 3">
                and
                <foreach collection="allocatableTagList" item="allocatableTag" index="index" open="(" separator="or" close=")">
                    <choose>
                        <when test="allocatableTag == 2">
                            <![CDATA[ IFNULL(ipd.amount, 0)-IFNULL(ipd.actual_receipt_amount, 0) > 0 ]]>
                        </when>
                        <when test="allocatableTag == 1">
                            <![CDATA[ IFNULL(ipd.amount, 0)-IFNULL(ipd.actual_receipt_amount, 0) = 0 ]]>
                        </when>
                        <otherwise>
                            <![CDATA[ IFNULL(ipd.amount, 0)-IFNULL(ipd.actual_receipt_amount, 0) < 0 ]]>
                        </otherwise>
                    </choose>
                </foreach>
            </if>
            and con.status in (5, 11, 12)
            and con.parent_id is not null
            and con.deleted_flag = 0
        </where>
    </select>

</mapper>