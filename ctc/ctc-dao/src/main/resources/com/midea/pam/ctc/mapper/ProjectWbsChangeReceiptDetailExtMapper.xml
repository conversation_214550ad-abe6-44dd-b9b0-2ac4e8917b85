<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectWbsChangeReceiptDetailExtMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ProjectWbsChangeReceiptDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_wbs_receipts_id" jdbcType="BIGINT" property="projectWbsReceiptsId" />
    <result column="project_wbs_budget_id" jdbcType="BIGINT" property="projectWbsBudgetId" />
    <result column="tag" jdbcType="VARCHAR" property="tag" />
    <result column="after_change_price" jdbcType="DECIMAL" property="afterChangePrice" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="activity_order_no" jdbcType="VARCHAR" property="activityOrderNo" />
    <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="activity_type" jdbcType="VARCHAR" property="activityType" />
    <result column="wbs_full_code" jdbcType="VARCHAR" property="wbsFullCode" />
    <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
    <result column="wbs_last_code" jdbcType="VARCHAR" property="wbsLastCode" />
    <result column="dynamic_wbs_template_rule_ids" jdbcType="VARCHAR" property="dynamicWbsTemplateRuleIds" />
    <result column="dynamic_fields" jdbcType="VARCHAR" property="dynamicFields" />
    <result column="dynamic_values" jdbcType="VARCHAR" property="dynamicValues" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="baseline_cost" jdbcType="DECIMAL" property="baselineCost" />
    <result column="demand_cost" jdbcType="DECIMAL" property="demandCost" />
    <result column="on_the_way_cost" jdbcType="DECIMAL" property="onTheWayCost" />
    <result column="incurred_cost" jdbcType="DECIMAL" property="incurredCost" />
    <result column="remaining_cost" jdbcType="DECIMAL" property="remainingCost" />
    <result column="change_accumulate_cost" jdbcType="DECIMAL" property="changeAccumulateCost" />
    <result column="parent_wbs_id" jdbcType="BIGINT" property="parentWbsId" />
    <result column="parent_activity_id" jdbcType="BIGINT" property="parentActivityId" />
    <result column="fee_type_id" jdbcType="BIGINT" property="feeTypeId" />
    <result column="fee_type_name" jdbcType="VARCHAR" property="feeTypeName" />
    <result column="fee_sync_ems" jdbcType="TINYINT" property="feeSyncEms" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>

  <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsChangeReceiptDetail">
    insert into project_wbs_change_receipt_detail (id, project_wbs_receipts_id, project_wbs_budget_id,
      tag, after_change_price, remark,
      project_id, project_code, description,
      activity_order_no, activity_code, activity_name,
      activity_type, wbs_full_code, wbs_summary_code,
      wbs_last_code, dynamic_wbs_template_rule_ids,
      dynamic_fields, dynamic_values, price,
      baseline_cost, demand_cost, on_the_way_cost,
      incurred_cost, remaining_cost, change_accumulate_cost,
      parent_wbs_id, parent_activity_id, fee_type_id,
      fee_type_name, fee_sync_ems, create_by,
      create_at, update_by, update_at,
      deleted_flag, version)
    values
    <foreach collection="list" item="obj" separator=",">
      (#{obj.id,jdbcType=BIGINT}, #{obj.projectWbsReceiptsId,jdbcType=BIGINT}, #{obj.projectWbsBudgetId,jdbcType=BIGINT},
      #{obj.tag,jdbcType=VARCHAR}, #{obj.afterChangePrice,jdbcType=DECIMAL}, #{obj.remark,jdbcType=VARCHAR},
      #{obj.projectId,jdbcType=BIGINT}, #{obj.projectCode,jdbcType=VARCHAR}, #{obj.description,jdbcType=VARCHAR},
      #{obj.activityOrderNo,jdbcType=VARCHAR}, #{obj.activityCode,jdbcType=VARCHAR}, #{obj.activityName,jdbcType=VARCHAR},
      #{obj.activityType,jdbcType=VARCHAR}, #{obj.wbsFullCode,jdbcType=VARCHAR}, #{obj.wbsSummaryCode,jdbcType=VARCHAR},
      #{obj.wbsLastCode,jdbcType=VARCHAR}, #{obj.dynamicWbsTemplateRuleIds,jdbcType=VARCHAR},
      #{obj.dynamicFields,jdbcType=VARCHAR}, #{obj.dynamicValues,jdbcType=VARCHAR}, #{obj.price,jdbcType=DECIMAL},
      #{obj.baselineCost,jdbcType=DECIMAL}, #{obj.demandCost,jdbcType=DECIMAL}, #{obj.onTheWayCost,jdbcType=DECIMAL},
      #{obj.incurredCost,jdbcType=DECIMAL}, #{obj.remainingCost,jdbcType=DECIMAL}, #{obj.changeAccumulateCost,jdbcType=DECIMAL},
      #{obj.parentWbsId,jdbcType=BIGINT}, #{obj.parentActivityId,jdbcType=BIGINT}, #{obj.feeTypeId,jdbcType=BIGINT},
      #{obj.feeTypeName,jdbcType=VARCHAR}, #{obj.feeSyncEms,jdbcType=TINYINT}, #{obj.createBy,jdbcType=BIGINT},
      #{obj.createAt,jdbcType=TIMESTAMP}, #{obj.updateBy,jdbcType=BIGINT}, #{obj.updateAt,jdbcType=TIMESTAMP},
      #{obj.deletedFlag,jdbcType=TINYINT}, #{obj.version,jdbcType=BIGINT})
    </foreach>
  </insert>

  <select id="queryByProjectWbsReceiptsId" resultMap="BaseResultMap">
    select
        project_wbs_budget_id as id,
        project_wbs_receipts_id,
        project_wbs_budget_id,
        tag,
        after_change_price,
        remark,
        project_id,
        project_code,
        description,
        activity_order_no,
        activity_code,
        activity_name,
        activity_type,
        wbs_full_code,
        wbs_summary_code,
        wbs_last_code,
        dynamic_wbs_template_rule_ids,
        dynamic_fields,
        dynamic_values,
        price,
        baseline_cost,
        demand_cost,
        on_the_way_cost,
        incurred_cost,
        remaining_cost,
        change_accumulate_cost,
        parent_wbs_id,
        parent_activity_id,
        fee_type_id,
        fee_type_name,
        fee_sync_ems,
        create_by,
        create_at,
        update_by,
        update_at,
        deleted_flag,
        version
    from
        project_wbs_change_receipt_detail
    where
        project_wbs_receipts_id = #{projectWbsReceiptsId,jdbcType=BIGINT}
    order by tag desc, wbs_full_code
  </select>

  <delete id="deleteByProjectWbsReceiptsId" parameterType="java.lang.Long">
    delete
      from project_wbs_change_receipt_detail
    where
      project_wbs_receipts_id = #{projectWbsReceiptsId,jdbcType=BIGINT}
  </delete>


</mapper>