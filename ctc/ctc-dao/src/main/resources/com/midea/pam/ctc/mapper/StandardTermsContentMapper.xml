<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.StandardTermsContentMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.StandardTermsContent">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="association_terms_id" jdbcType="BIGINT" property="associationTermsId" />
    <result column="text_box_key" jdbcType="VARCHAR" property="textBoxKey" />
    <result column="text_box_title" jdbcType="VARCHAR" property="textBoxTitle" />
    <result column="default_value" jdbcType="VARCHAR" property="defaultValue" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, association_terms_id, text_box_key, text_box_title, default_value, remark, sort, 
    create_by, create_at, update_by, update_at, version, deleted_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.StandardTermsContentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from standard_terms_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from standard_terms_content
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from standard_terms_content
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.StandardTermsContent">
    insert into standard_terms_content (id, association_terms_id, text_box_key, 
      text_box_title, default_value, remark, 
      sort, create_by, create_at, 
      update_by, update_at, version, 
      deleted_flag)
    values (#{id,jdbcType=BIGINT}, #{associationTermsId,jdbcType=BIGINT}, #{textBoxKey,jdbcType=VARCHAR}, 
      #{textBoxTitle,jdbcType=VARCHAR}, #{defaultValue,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{version,jdbcType=BIGINT}, 
      #{deletedFlag,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.StandardTermsContent">
    insert into standard_terms_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="associationTermsId != null">
        association_terms_id,
      </if>
      <if test="textBoxKey != null">
        text_box_key,
      </if>
      <if test="textBoxTitle != null">
        text_box_title,
      </if>
      <if test="defaultValue != null">
        default_value,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="associationTermsId != null">
        #{associationTermsId,jdbcType=BIGINT},
      </if>
      <if test="textBoxKey != null">
        #{textBoxKey,jdbcType=VARCHAR},
      </if>
      <if test="textBoxTitle != null">
        #{textBoxTitle,jdbcType=VARCHAR},
      </if>
      <if test="defaultValue != null">
        #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.StandardTermsContentExample" resultType="java.lang.Long">
    select count(*) from standard_terms_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.StandardTermsContent">
    update standard_terms_content
    <set>
      <if test="associationTermsId != null">
        association_terms_id = #{associationTermsId,jdbcType=BIGINT},
      </if>
      <if test="textBoxKey != null">
        text_box_key = #{textBoxKey,jdbcType=VARCHAR},
      </if>
      <if test="textBoxTitle != null">
        text_box_title = #{textBoxTitle,jdbcType=VARCHAR},
      </if>
      <if test="defaultValue != null">
        default_value = #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.StandardTermsContent">
    update standard_terms_content
    set association_terms_id = #{associationTermsId,jdbcType=BIGINT},
      text_box_key = #{textBoxKey,jdbcType=VARCHAR},
      text_box_title = #{textBoxTitle,jdbcType=VARCHAR},
      default_value = #{defaultValue,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=BIGINT},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>