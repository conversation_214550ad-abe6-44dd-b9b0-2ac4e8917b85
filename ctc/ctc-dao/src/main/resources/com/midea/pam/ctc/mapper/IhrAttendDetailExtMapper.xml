<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.IhrAttendDetailExtMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.IhrAttendDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="max_card_record" jdbcType="TIMESTAMP" property="maxCardRecord" />
    <result column="min_card_record" jdbcType="TIMESTAMP" property="minCardRecord" />
    <result column="attend_date" jdbcType="TIMESTAMP" property="attendDate" />
    <result column="actual_hours" jdbcType="DECIMAL" property="actualHours" />
    <result column="attend_week" jdbcType="INTEGER" property="attendWeek" />
    <result column="attend_month" jdbcType="INTEGER" property="attendMonth" />
    <result column="attend_year" jdbcType="INTEGER" property="attendYear" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_mip" jdbcType="VARCHAR" property="userMip" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_number" jdbcType="VARCHAR" property="userNumber" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="labor_cost_id" jdbcType="BIGINT" property="laborCostId" />
    <result column="parent_unit_id" jdbcType="BIGINT" property="parentUnitId" />
    <result column="unit_id" jdbcType="BIGINT" property="unitId" />
    <result column="delete_flag" jdbcType="BIT" property="deleteFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, max_card_record, min_card_record, attend_date, actual_hours, attend_week, attend_month, 
    attend_year, user_id, user_mip, user_name, user_number, delete_flag, create_by, create_at, 
    update_by, update_at
  </sql>
  <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.IhrAttendDetail">
    insert into ihr_attend_detail (id, max_card_record, min_card_record, 
      attend_date, actual_hours, attend_week, 
      attend_month, attend_year, user_id, 
      user_mip, user_name, user_number, org_id, labor_cost_id, parent_unit_id, unit_id,
      delete_flag, create_by, create_at, 
      update_by, update_at)
    values
    <foreach collection="list" item="attendDetail" index="index" separator=",">
           (#{attendDetail.id,jdbcType=BIGINT}, #{attendDetail.maxCardRecord,jdbcType=TIMESTAMP}, #{attendDetail.minCardRecord,jdbcType=TIMESTAMP},
      #{attendDetail.attendDate,jdbcType=TIMESTAMP}, #{attendDetail.actualHours,jdbcType=DECIMAL}, #{attendDetail.attendWeek,jdbcType=INTEGER},
      #{attendDetail.attendMonth,jdbcType=INTEGER}, #{attendDetail.attendYear,jdbcType=INTEGER}, #{attendDetail.userId,jdbcType=BIGINT},
      #{attendDetail.userMip,jdbcType=VARCHAR}, #{attendDetail.userName,jdbcType=VARCHAR}, #{attendDetail.userNumber,jdbcType=VARCHAR},#{attendDetail.orgId,jdbcType=BIGINT},
      #{attendDetail.laborCostId,jdbcType=BIGINT},#{attendDetail.parentUnitId,jdbcType=BIGINT},#{attendDetail.unitId,jdbcType=BIGINT},
      #{attendDetail.deleteFlag,jdbcType=BIT}, #{attendDetail.createBy,jdbcType=BIGINT}, #{attendDetail.createAt,jdbcType=TIMESTAMP},
      #{attendDetail.updateBy,jdbcType=BIGINT}, #{attendDetail.updateAt,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";">
      update ihr_attend_detail
      <set>
        <if test="item.maxCardRecord != null">
          max_card_record = #{item.maxCardRecord,jdbcType=TIMESTAMP},
        </if>
        <if test="item.minCardRecord != null">
          min_card_record = #{item.minCardRecord,jdbcType=TIMESTAMP},
        </if>
        <if test="item.attendDate != null">
          attend_date = #{item.attendDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.actualHours != null">
          actual_hours = #{item.actualHours,jdbcType=DECIMAL},
        </if>
        <if test="item.attendWeek != null">
          attend_week = #{item.attendWeek,jdbcType=INTEGER},
        </if>
        <if test="item.attendMonth != null">
          attend_month = #{item.attendMonth,jdbcType=INTEGER},
        </if>
        <if test="item.attendYear != null">
          attend_year = #{item.attendYear,jdbcType=INTEGER},
        </if>
        <if test="item.userId != null">
          user_id = #{item.userId,jdbcType=BIGINT},
        </if>
        <if test="item.userMip != null">
          user_mip = #{item.userMip,jdbcType=VARCHAR},
        </if>
        <if test="item.userName != null">
          user_name = #{item.userName,jdbcType=VARCHAR},
        </if>
        <if test="item.userNumber != null">
          user_number = #{item.userNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.orgId != null">
          org_id = #{item.orgId,jdbcType=BIGINT},
        </if>
        <if test="item.laborCostId != null">
          labor_cost_id = #{item.laborCostId,jdbcType=BIGINT},
        </if>
        <if test="item.parentUnitId != null">
          parent_unit_id = #{item.parentUnitId,jdbcType=BIGINT},
        </if>
        <if test="item.unitId != null">
          unit_id = #{item.unitId,jdbcType=BIGINT},
        </if>
        <if test="item.deleteFlag != null">
          delete_flag = #{item.deleteFlag,jdbcType=BIT},
        </if>
        <if test="item.createBy != null">
          create_by = #{item.createBy,jdbcType=BIGINT},
        </if>
        <if test="item.createAt != null">
          create_at = #{item.createAt,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateBy != null">
          update_by = #{item.updateBy,jdbcType=BIGINT},
        </if>
        <if test="item.updateAt != null">
          update_at = #{item.updateAt,jdbcType=TIMESTAMP},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="batchUpdateFromTransition">
    update
      transition_ihr_attend_detail tiad,
      ihr_attend_detail iad
    set
      iad.max_card_record = ifnull(tiad.max_card_record,iad.max_card_record),
      iad.min_card_record = ifnull(tiad.min_card_record,iad.min_card_record),
      iad.actual_hours = ifnull(tiad.actual_hours,iad.actual_hours),
      iad.update_at = now()
    where
      iad.user_id = tiad.user_id
      and iad.attend_date = tiad.attend_date
      and (ifnull(iad.max_card_record,0) != ifnull(tiad.max_card_record,0)
        or ifnull(iad.min_card_record,0) != ifnull(tiad.min_card_record,0)
        or ifnull(iad.actual_hours,0) != ifnull(tiad.actual_hours,0))
      <if test="attendBeginDate != null ">
        and iad.attend_date <![CDATA[ >= ]]> #{attendBeginDate}
        and tiad.attend_date <![CDATA[ >= ]]> #{attendBeginDate}
      </if>
      <if test="attendEndDate != null ">
        and iad.attend_date <![CDATA[ <= ]]> #{attendEndDate}
        and tiad.attend_date <![CDATA[ <= ]]> #{attendEndDate}
      </if>
  </update>

  <delete id="batchDelete">
    delete from ihr_attend_detail
    where id in
    <foreach collection="list" item="id" index="index" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <select id="selectRepeatData" resultType="java.lang.Long">
    select
          iad.id
      from
          pam_ctc.ihr_attend_detail iad
      where
          delete_flag = 0
          and attend_date <![CDATA[ >= ]]> #{attendBeginDate}
          and attend_date <![CDATA[ <= ]]> #{attendEndDate}
      group by
          user_id,attend_date
      having
          count(id) > 1
  </select>

  <select id="selectFromTransition"  resultMap="BaseResultMap">
    select
      tiad.max_card_record,
      tiad.min_card_record,
      tiad.attend_date,
      tiad.actual_hours,
      tiad.attend_week,
      tiad.attend_month,
      tiad.attend_year,
      tiad.user_id,
      tiad.user_mip,
      tiad.user_name,
      tiad.user_number,
      t.org_id,
      t.company_id as parent_unit_id,
      t.labor_cost_id,
      t.unit_id,
      tiad.delete_flag,
      tiad.create_at,
      tiad.create_by,
      tiad.update_at,
      tiad.update_by
    from pam_ctc.transition_ihr_attend_detail tiad
    left join pam_ctc.ihr_attend_detail iad on tiad.user_id = iad.user_id and tiad.attend_date = iad.attend_date
    left join
    (
      select
        lou.user_id,
        lou.org_id,
        olcts.company_id,
        olcts.labor_cost_id,
        olcts.unit_id
      from pam_basedata.ltc_org_user lou
      left join pam_basedata.org_labor_cost_type_set olcts on lou.org_id = olcts.org_id and olcts.labor_cost_type_code is not null
      where lou.status = "Y" and lou.`type` = 1
      group by lou.user_id
    ) t on tiad.user_id = t.user_id
    where iad.id is null
  </select>

  <select id="selectIhrAttendDetailUnitInfoNeedUpdate" resultType="com.midea.pam.common.ctc.entity.IhrAttendDetail">
    select
      iad.id,
      olcts.labor_cost_id as laborCostId,
      olcts.company_id as parentUnitId,
      olcts.unit_id as unitId,
      now() as updateAt
    from pam_ctc.ihr_attend_detail iad
    inner join pam_basedata.org_labor_cost_type_set olcts
    on iad.org_id = olcts.org_id
    and olcts.labor_cost_type_code is not null
    and olcts.company_id is not null
    and olcts.deleted_flag = 0
    where iad.delete_flag = 0
    and iad.org_id is not null
    and iad.parent_unit_id is null
    and iad.attend_year > 2023
  </select>

</mapper>