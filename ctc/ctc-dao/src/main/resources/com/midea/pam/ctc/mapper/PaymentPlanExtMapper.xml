<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PaymentPlanExtMapper">

    <sql id="Base_Column_List">
        plan.id as id,
        plan.contract_id as contractId,
        pur.code as purchaseContractCode,
        pur.name as purchaseContractName,
        case when pur.legal_affairs_code IS NOT NULL then pur.legal_affairs_code ELSE pur.legal_contract_num end as legalAffairsCode,
        pur.`status` as purchaseContractStatus,
        pur.ou_id as ouId,
        pur.currency as currency,
        pur.conversion_type as conversionType,
        pur.conversion_date as conversionDate,
        pur.conversion_rate as conversionRate,
        pur.erp_vendor_site_id as erpVendorSiteId,
        pur.vendor_site_code as vendorSiteCode,
        pur.type_name contractTypeName,
        p.id as projectId,
        p.code as projectCode,
        p.name as projectName,
        pur.vendor_id  as vendorId,
        pur.vendor_name as vendorName,
        pur.gle_sign_flag AS isGleSign,
        plan.proportion as proportion,
        plan.amount as amount,
        plan.date as date,
        plan.requirement as requirement,
        plan.payment_method_id as paymentMethodId ,
        plan.payment_method_name as paymentMethodName,
        plan.advance_payment_flag as advancePaymentFlag,
        plan.milestone_id as milestoneId,
        t.actualAmount as actualAmount ,
        plan.code as code,
        plan.num as num,
        plan.status as status,
        plan.deleted_flag as deletedFlag,
        plan.create_by as createBy,
        plan.create_at as createAt,
        plan.update_by as updateBy,
        plan.update_at as updateAt,
	    pm.`status` as milestoneStatus,
	    pm.name as milestoneName,
        plan.allocation_punishment_amount_with_tax,
        plan.prepayment_flag,
        plan.payment_apply_source as paymentApplySource
    </sql>
    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.PaymentPlan">
        insert into payment_plan (
            id,contract_id,bill_id,proportion,amount,date,requirement,payment_method_id,
            payment_method_name,advance_payment_flag,milestone_id,actual_amount,
            code,num,status,deleted_flag,create_by,create_at,update_by,update_at,prepayment_flag
        ) values
        <foreach collection="list" item="e" separator=",">
            (
                #{e.id},#{e.contractId},#{e.billId},#{e.proportion},#{e.amount},#{e.date},#{e.requirement},#{e.paymentMethodId},
                #{e.paymentMethodName},#{e.advancePaymentFlag},#{e.milestoneId},#{e.actualAmount},
                #{e.code},#{e.num},#{e.status},#{e.deletedFlag},#{e.createBy},#{e.createAt},#{e.updateBy},#{e.updateAt},#{e.prepaymentFlag}
            )
        </foreach>
    </insert>
    <update id="deleteExcludeIds">
        update payment_plan
        set deleted_flag = 1
        where bill_id = #{billId}
        <if test="ids!=null and ids.size>0">
            and id not in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <select id="selectPage" parameterType="com.midea.pam.common.ctc.dto.PaymentPlanDTO"
            resultType="com.midea.pam.common.ctc.dto.PaymentPlanDTO">
        select
        <include refid="Base_Column_List"/>
        from
        payment_plan plan
        INNER JOIN purchase_contract pur on pur.id= plan.contract_id
        INNER JOIN project p on pur.project_id = p.id
        LEFT JOIN project_milepost pm on pm.id= plan.milestone_id
        left join (SELECT
        SUM(
        IFNULL(really_pay_included_price, 0)
        ) AS actualAmount,
        pa.payment_plan_id
        FROM
        pam_ctc.payment_apply pa
        WHERE (
        pa.erp_status = 1
        OR pa.gceb_status = 1
        )
        AND (
        pa.deleted_flag = 0
        OR pa.deleted_flag IS NULL
        ) and pa.audit_status in (1,2,3,4)
        GROUP BY pa.payment_plan_id) t
        on plan.id = t.payment_plan_id
        where 1 = 1
        and (plan.deleted_flag is null or plan.deleted_flag = 0)
        <include refid="queryCondition"/>
    </select>

    <sql id="queryCondition">

        <!-- id-->
        <if test="id != null">
            AND plan.id = #{id}
        </if>

        <!-- 付款计划编号-->
        <if test="code != null and code != ''">
            AND plan.code like concat('%', #{code}, '%')
        </if>

        <!-- 计划付款开始日期-->
        <if test="startDate != null">
            AND plan.date <![CDATA[ >= ]]> #{startDate}
        </if>

        <!-- endDate-->
        <if test="endDate != null">
            AND plan.date <![CDATA[ <= ]]> #{endDate}
        </if>

        <!-- purchaseContractName-->
        <if test="purchaseContractName != null and purchaseContractName != ''">
            AND pur.name like concat('%', #{purchaseContractName}, '%')
        </if>

        <!-- purchaseContractStatusStr-->
        <if test="allContractStatus != null and allContractStatus != ''">
            AND pur.status in(4,5,10)
        </if>


        <!-- purchaseContractStatus-->
        <if test="purchaseContractStatus != null and purchaseContractStatus != ''">
            AND pur.status = #{purchaseContractStatus}
        </if>

        <!-- vendorName-->
        <if test="vendorName != null and vendorName != ''">
            AND pur.vendor_name like concat('%', #{vendorName}, '%')
        </if>

        <!-- projectName-->
        <if test="projectName != null and projectName != ''">
            AND p.name like concat('%', #{projectName}, '%')
        </if>

        <!-- projectCode-->
        <if test="projectCode != null and projectCode != ''">
            AND p.code like concat('%', #{projectCode}, '%')
        </if>

        <!-- projectCode-->
        <if test="milestoneStatus != null">
            AND p.id in (select project_id from project_milepost where `status` = #{milestoneStatus})
        </if>

        <!-- 里程碑状态-->
        <if test="milestoneStatuses != null and milestoneStatuses.size &gt; 0">
            AND p.id in (
            select project_id from project_milepost where `status` in
            <foreach collection="milestoneStatuses" item="milestoneStatusItem" index="index" open="(" separator="," close=")">
                #{milestoneStatusItem}
            </foreach>
            )
        </if>
    </sql>

    <select id="getActualPaymentAmountByContractId" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        select sum(ifnull(actual_amount,0))
        from payment_plan
        where (deleted_flag is null or deleted_flag = 0)
        and contract_id = #{contractId}
    </select>

    <select id="getActualPaymentAmountByContractIds" resultType="com.midea.pam.common.ctc.dto.PaymentPlanDTO">
        select
            contract_id contractId,
            sum(ifnull(actual_amount,0)) totalActualAmount
        from payment_plan
        where contract_id in
        <foreach collection="contractIds" item="contractId" open="(" separator="," close=")">
            #{contractId}
        </foreach>
        and (deleted_flag is null or deleted_flag = 0)
        group by contract_id
    </select>

    <select id="selectByCodes" resultType="com.midea.pam.common.ctc.entity.PaymentPlan">
        select id, code, num, date, amount, create_by as createBy
        from payment_plan
        where code in
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <update id="updatePaymentPlan" parameterType="com.midea.pam.common.ctc.entity.PaymentPlan">
        update payment_plan
        <set>
            <if test="contractId != null">
                contract_id = #{contractId,jdbcType=BIGINT},
            </if>
            <if test="proportion != null">
                proportion = #{proportion,jdbcType=DECIMAL},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="requirement != null">
                requirement = #{requirement,jdbcType=VARCHAR},
            </if>
            <if test="paymentMethodId != null">
                payment_method_id = #{paymentMethodId,jdbcType=BIGINT},
            </if>
            <if test="paymentMethodName != null">
                payment_method_name = #{paymentMethodName,jdbcType=VARCHAR},
            </if>
            <if test="advancePaymentFlag != null">
                advance_payment_flag = #{advancePaymentFlag,jdbcType=BIT},
            </if>
            <if test="milestoneId != null">
                milestone_id = #{milestoneId,jdbcType=BIGINT},
            </if>
            <if test="actualAmount != null">
                actual_amount = #{actualAmount,jdbcType=DECIMAL},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="deletedFlag != null">
                deleted_flag = #{deletedFlag,jdbcType=BIT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createAt != null">
                create_at = #{createAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateAt != null">
                update_at = #{updateAt,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateActualAmount">
        update payment_plan p
        inner join (
            select pp.id, ifnull(sum(pr.amount),0) as actual_amount
            from pam_ctc.payment_plan pp
            left join pam_ctc.payment_apply pa on pp.id =pa.payment_plan_id
            left join pam_ctc.payment_record pr on pa.id = pr.payment_apply_id and (pr.deleted_flag = 0 or pr.deleted_flag is null)
            where pp.code in
            <foreach collection="codes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
            group by pp.id
        ) t on t.id = p.id
        set p.actual_amount = t.actual_amount,
        p.update_by = #{updateBy},
        p.update_at = #{updateAt}
    </update>

    <select id="calculateActualAmount" resultType="java.math.BigDecimal">
        select
            IFNULL(SUM(really_pay_included_price), 0)
        from
            pam_ctc.payment_apply
        where
            (erp_status = 1 or gceb_status = 1)
            and (deleted_flag = 0 or deleted_flag is null)
            and audit_status in (1, 2, 3, 4)
            and payment_plan_id = #{paymentPlanId}
    </select>
    <select id="selectByBillId" resultType="com.midea.pam.common.ctc.dto.PaymentPlanDTO">
        select
            plan.id,
            plan.bill_id billId,
            plan.code,
            plan.proportion,
            plan.amount,
            plan.`date`,
            plan.requirement,
            plan.payment_method_id paymentMethodId,
            plan.payment_method_name paymentMethodName,
            plan.actual_amount actualAmount,
            sum(ifnull(apply.tax_plan_included_price,0)) totalApplyAmount
        from pam_ctc.Payment_plan plan
        left join pam_ctc.payment_apply apply
            on apply.payment_plan_id = plan.id
            and apply.audit_status != 5
            and apply.deleted_flag = 0
        where plan.bill_id  = #{billId}
        and plan.deleted_flag = 0
        group by plan.id
    </select>

    <select id="selectByContractId" resultType="com.midea.pam.common.ctc.dto.PaymentPlanDTO">
        select
            plan.id,
            plan.proportion,
            plan.amount,
            plan.date,
            plan.requirement,
            plan.payment_method_id paymentMethodId,
            plan.payment_method_name paymentMethodName,
            plan.code,
            plan.num,
            plan.status,
            plan.bill_id billId,
            bill.code billCode,
            pc.status contractStatus,
            p.status projectStatus
        from payment_plan plan
        inner join purchase_contract pc on pc.id = plan.contract_id
        inner join project p on p.id = pc.project_id
        left join hro_working_hour_bill bill on bill.id = plan.bill_id
        where pc.id = #{contractId}
        and plan.deleted_flag = 0
    </select>

    <select id="calculateSurplusAmount" resultType="com.midea.pam.common.ctc.dto.PaymentPlanDTO">
        select
            plan.id,
            plan.amount,
            IFNULL(plan.allocation_punishment_amount_with_tax,0) as allocationPunishmentAmountWithTax,
            @actualAmount := (
                select
                IFNULL(SUM(really_pay_included_price), 0)
                from
                pam_ctc.payment_apply
                where
                (erp_status = 1 or gceb_status = 1)
                and ( deleted_flag = 0 or deleted_flag is null )
                and audit_status in (1, 2, 3, 4)
                and payment_plan_id = plan.id ) as actualAmount,
            @totalOnTheWayAmount := (
                select
                IFNULL(SUM(tax_pay_included_price - ifnull(total_cancel_amount, 0) - ifnull(really_pay_included_price, 0)), 0)
                from
                pam_ctc.payment_apply
                where
                (audit_status != 5
                and (deleted_flag = 0 or deleted_flag is null)
                and payment_plan_id = plan.id) ) as totalOnTheWayAmount,
            @totalpenaltyAmount := (
                select
                IFNULL(SUM(penalty_amount), 0)
                from
                pam_ctc.payment_apply
                where
                (audit_status in (1,2,3,4) and (erp_status!='2' and gceb_status!='2')
                and (deleted_flag = 0 or deleted_flag is null)
                and payment_plan_id = plan.id) ) as totalpenaltyAmount,
                ROUND(plan.amount - @actualAmount - @totalOnTheWayAmount - @totalpenaltyAmount, 2) as surplusAmount
        from
        pam_ctc.payment_plan plan
        where 1=1
        <if test="paymentPlanId != null">
            and plan.id = #{paymentPlanId}
        </if>
    </select>

    <update id="batchUpdate" parameterType="com.midea.pam.common.ctc.entity.PaymentPlan">
        <foreach collection="list" item="item" separator=";">
            update payment_plan
            <set>
                <if test="item.contractId != null">
                    contract_id = #{item.contractId,jdbcType=BIGINT},
                </if>
                <if test="item.proportion != null">
                    proportion = #{item.proportion,jdbcType=DECIMAL},
                </if>
                <if test="item.amount != null">
                    amount = #{item.amount,jdbcType=DECIMAL},
                </if>
                <if test="item.date != null">
                    date = #{item.date,jdbcType=DATE},
                </if>
                <if test="item.requirement != null">
                    requirement = #{item.requirement,jdbcType=VARCHAR},
                </if>
                <if test="item.paymentMethodId != null">
                    payment_method_id = #{item.paymentMethodId,jdbcType=BIGINT},
                </if>
                <if test="item.paymentMethodName != null">
                    payment_method_name = #{item.paymentMethodName,jdbcType=VARCHAR},
                </if>
                <if test="item.advancePaymentFlag != null">
                    advance_payment_flag = #{item.advancePaymentFlag,jdbcType=TINYINT},
                </if>
                <if test="item.milestoneId != null">
                    milestone_id = #{item.milestoneId,jdbcType=BIGINT},
                </if>
                <if test="item.actualAmount != null">
                    actual_amount = #{item.actualAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.code != null">
                    code = #{item.code,jdbcType=VARCHAR},
                </if>
                <if test="item.num != null">
                    num = #{item.num,jdbcType=INTEGER},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.deletedFlag != null">
                    deleted_flag = #{item.deletedFlag,jdbcType=TINYINT},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy,jdbcType=BIGINT},
                </if>
                <if test="item.createAt != null">
                    create_at = #{item.createAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy,jdbcType=BIGINT},
                </if>
                <if test="item.updateAt != null">
                    update_at = #{item.updateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.billId != null">
                    bill_id = #{item.billId,jdbcType=BIGINT},
                </if>
                <if test="item.prepaymentFlag != null">
                    prepayment_flag = #{item.prepaymentFlag,jdbcType=TINYINT},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateActualAmountById">
        update payment_plan
            set actual_amount = (
                select sum(pr.amount)
                from payment_apply pa
                inner join payment_record pr on pr.payment_apply_id = pa.id
                where pa.payment_plan_id = #{planId}
                and pa.deleted_flag = 0
                and pr.deleted_flag = 0
            )
        where id = #{planId}
    </update>

    <update id="updatePaymentApplySource">
        update payment_plan
        set payment_apply_source = #{source}
        where id = #{id}
    </update>

</mapper>