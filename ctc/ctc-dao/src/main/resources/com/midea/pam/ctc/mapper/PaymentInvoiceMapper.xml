<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PaymentInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.PaymentInvoice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="payment_apply_id" jdbcType="BIGINT" property="paymentApplyId" />
    <result column="invoice_date" jdbcType="TIMESTAMP" property="invoiceDate" />
    <result column="payment_apply_code" jdbcType="VARCHAR" property="paymentApplyCode" />
    <result column="payment_plan_code" jdbcType="VARCHAR" property="paymentPlanCode" />
    <result column="payment_plan_id" jdbcType="BIGINT" property="paymentPlanId" />
    <result column="purchase_contract_id" jdbcType="BIGINT" property="purchaseContractId" />
    <result column="purchase_contract_code" jdbcType="VARCHAR" property="purchaseContractCode" />
    <result column="ou_id" jdbcType="BIGINT" property="ouId" />
    <result column="ap_invoice_code" jdbcType="VARCHAR" property="apInvoiceCode" />
    <result column="erp_invoice_code" jdbcType="VARCHAR" property="erpInvoiceCode" />
    <result column="total_invoice_included_price" jdbcType="DECIMAL" property="totalInvoiceIncludedPrice" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="total_pay_included_price" jdbcType="DECIMAL" property="totalPayIncludedPrice" />
    <result column="surplus_amount" jdbcType="DECIMAL" property="surplusAmount" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="tax_code" jdbcType="VARCHAR" property="taxCode" />
    <result column="erp_status" jdbcType="TINYINT" property="erpStatus" />
    <result column="erp_msg" jdbcType="VARCHAR" property="erpMsg" />
    <result column="available" jdbcType="TINYINT" property="available" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="audit_date" jdbcType="TIMESTAMP" property="auditDate" />
    <result column="collection_status" jdbcType="INTEGER" property="collectionStatus" />
    <result column="annex" jdbcType="VARCHAR" property="annex" />
    <result column="erp_cancel_status" jdbcType="TINYINT" property="erpCancelStatus" />
    <result column="erp_cancel_msg" jdbcType="VARCHAR" property="erpCancelMsg" />
    <result column="erp_cancel_by" jdbcType="BIGINT" property="erpCancelBy" />
    <result column="erp_cancel_date" jdbcType="TIMESTAMP" property="erpCancelDate" />
    <result column="erp_cancel_cause" jdbcType="VARCHAR" property="erpCancelCause" />
    <result column="due_date" jdbcType="DATE" property="dueDate" />
    <result column="gl_date" jdbcType="TIMESTAMP" property="glDate" />
    <result column="freeze_status" jdbcType="INTEGER" property="freezeStatus" />
    <result column="invoice_vou_num" jdbcType="VARCHAR" property="invoiceVouNum" />
    <result column="Invoice_entry_explain" jdbcType="VARCHAR" property="invoiceEntryExplain" />
    <result column="account_entry_type" jdbcType="INTEGER" property="accountEntryType" />
    <result column="gsc_invoice_number" jdbcType="VARCHAR" property="gscInvoiceNumber" />
    <result column="item_number" jdbcType="VARCHAR" property="itemNumber" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, payment_apply_id, invoice_date, payment_apply_code, payment_plan_code, payment_plan_id, 
    purchase_contract_id, purchase_contract_code, ou_id, ap_invoice_code, erp_invoice_code, 
    total_invoice_included_price, amount, total_amount, total_pay_included_price, surplus_amount, 
    tax_amount, tax_code, erp_status, erp_msg, available, create_by, create_at, update_by, 
    update_at, deleted_flag, version, source, status, audit_date, collection_status, 
    annex, erp_cancel_status, erp_cancel_msg, erp_cancel_by, erp_cancel_date, erp_cancel_cause, 
    due_date, gl_date, freeze_status, invoice_vou_num, Invoice_entry_explain, account_entry_type,
    gsc_invoice_number, item_number
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.PaymentInvoiceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from payment_invoice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from payment_invoice
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from payment_invoice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.PaymentInvoice">
    insert into payment_invoice (id, payment_apply_id, invoice_date, 
      payment_apply_code, payment_plan_code, payment_plan_id, 
      purchase_contract_id, purchase_contract_code, 
      ou_id, ap_invoice_code, erp_invoice_code, 
      total_invoice_included_price, amount, total_amount, 
      total_pay_included_price, surplus_amount, tax_amount, 
      tax_code, erp_status, erp_msg, 
      available, create_by, create_at, 
      update_by, update_at, deleted_flag, 
      version, source, status, 
      audit_date, collection_status, annex, 
      erp_cancel_status, erp_cancel_msg, erp_cancel_by, 
      erp_cancel_date, erp_cancel_cause, due_date, 
      gl_date, freeze_status, invoice_vou_num, 
      Invoice_entry_explain, account_entry_type,
      gsc_invoice_number,item_number)
    values (#{id,jdbcType=BIGINT}, #{paymentApplyId,jdbcType=BIGINT}, #{invoiceDate,jdbcType=TIMESTAMP}, 
      #{paymentApplyCode,jdbcType=VARCHAR}, #{paymentPlanCode,jdbcType=VARCHAR}, #{paymentPlanId,jdbcType=BIGINT}, 
      #{purchaseContractId,jdbcType=BIGINT}, #{purchaseContractCode,jdbcType=VARCHAR}, 
      #{ouId,jdbcType=BIGINT}, #{apInvoiceCode,jdbcType=VARCHAR}, #{erpInvoiceCode,jdbcType=VARCHAR}, 
      #{totalInvoiceIncludedPrice,jdbcType=DECIMAL}, #{amount,jdbcType=DECIMAL}, #{totalAmount,jdbcType=DECIMAL}, 
      #{totalPayIncludedPrice,jdbcType=DECIMAL}, #{surplusAmount,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, 
      #{taxCode,jdbcType=VARCHAR}, #{erpStatus,jdbcType=TINYINT}, #{erpMsg,jdbcType=VARCHAR},
      #{available,jdbcType=TINYINT}, #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{deletedFlag,jdbcType=TINYINT}, 
      #{version,jdbcType=BIGINT}, #{source,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{auditDate,jdbcType=TIMESTAMP}, #{collectionStatus,jdbcType=INTEGER}, #{annex,jdbcType=VARCHAR}, 
      #{erpCancelStatus,jdbcType=TINYINT}, #{erpCancelMsg,jdbcType=VARCHAR}, #{erpCancelBy,jdbcType=BIGINT}, 
      #{erpCancelDate,jdbcType=TIMESTAMP}, #{erpCancelCause,jdbcType=VARCHAR}, #{dueDate,jdbcType=DATE}, 
      #{glDate,jdbcType=TIMESTAMP}, #{freezeStatus,jdbcType=INTEGER}, #{invoiceVouNum,jdbcType=VARCHAR}, 
      #{invoiceEntryExplain,jdbcType=VARCHAR}, #{accountEntryType,jdbcType=INTEGER},
      #{gscInvoiceNumber,jdbcType=VARCHAR}, #{itemNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.PaymentInvoice">
    insert into payment_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="paymentApplyId != null">
        payment_apply_id,
      </if>
      <if test="invoiceDate != null">
        invoice_date,
      </if>
      <if test="paymentApplyCode != null">
        payment_apply_code,
      </if>
      <if test="paymentPlanCode != null">
        payment_plan_code,
      </if>
      <if test="paymentPlanId != null">
        payment_plan_id,
      </if>
      <if test="purchaseContractId != null">
        purchase_contract_id,
      </if>
      <if test="purchaseContractCode != null">
        purchase_contract_code,
      </if>
      <if test="ouId != null">
        ou_id,
      </if>
      <if test="apInvoiceCode != null">
        ap_invoice_code,
      </if>
      <if test="erpInvoiceCode != null">
        erp_invoice_code,
      </if>
      <if test="totalInvoiceIncludedPrice != null">
        total_invoice_included_price,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="totalPayIncludedPrice != null">
        total_pay_included_price,
      </if>
      <if test="surplusAmount != null">
        surplus_amount,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="taxCode != null">
        tax_code,
      </if>
      <if test="erpStatus != null">
        erp_status,
      </if>
      <if test="erpMsg != null">
        erp_msg,
      </if>
      <if test="available != null">
        available,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="auditDate != null">
        audit_date,
      </if>
      <if test="collectionStatus != null">
        collection_status,
      </if>
      <if test="annex != null">
        annex,
      </if>
      <if test="erpCancelStatus != null">
        erp_cancel_status,
      </if>
      <if test="erpCancelMsg != null">
        erp_cancel_msg,
      </if>
      <if test="erpCancelBy != null">
        erp_cancel_by,
      </if>
      <if test="erpCancelDate != null">
        erp_cancel_date,
      </if>
      <if test="erpCancelCause != null">
        erp_cancel_cause,
      </if>
      <if test="dueDate != null">
        due_date,
      </if>
      <if test="glDate != null">
        gl_date,
      </if>
      <if test="freezeStatus != null">
        freeze_status,
      </if>
      <if test="invoiceVouNum != null">
        invoice_vou_num,
      </if>
      <if test="invoiceEntryExplain != null">
        Invoice_entry_explain,
      </if>
      <if test="accountEntryType != null">
        account_entry_type,
      </if>
      <if test="gscInvoiceNumber != null">
        gsc_invoice_number,
      </if>
      <if test="itemNumber != null">
        item_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="paymentApplyId != null">
        #{paymentApplyId,jdbcType=BIGINT},
      </if>
      <if test="invoiceDate != null">
        #{invoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentApplyCode != null">
        #{paymentApplyCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentPlanCode != null">
        #{paymentPlanCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentPlanId != null">
        #{paymentPlanId,jdbcType=BIGINT},
      </if>
      <if test="purchaseContractId != null">
        #{purchaseContractId,jdbcType=BIGINT},
      </if>
      <if test="purchaseContractCode != null">
        #{purchaseContractCode,jdbcType=VARCHAR},
      </if>
      <if test="ouId != null">
        #{ouId,jdbcType=BIGINT},
      </if>
      <if test="apInvoiceCode != null">
        #{apInvoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="erpInvoiceCode != null">
        #{erpInvoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="totalInvoiceIncludedPrice != null">
        #{totalInvoiceIncludedPrice,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalPayIncludedPrice != null">
        #{totalPayIncludedPrice,jdbcType=DECIMAL},
      </if>
      <if test="surplusAmount != null">
        #{surplusAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxCode != null">
        #{taxCode,jdbcType=VARCHAR},
      </if>
      <if test="erpStatus != null">
        #{erpStatus,jdbcType=TINYINT},
      </if>
      <if test="erpMsg != null">
        #{erpMsg,jdbcType=VARCHAR},
      </if>
      <if test="available != null">
        #{available,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="auditDate != null">
        #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="collectionStatus != null">
        #{collectionStatus,jdbcType=INTEGER},
      </if>
      <if test="annex != null">
        #{annex,jdbcType=VARCHAR},
      </if>
      <if test="erpCancelStatus != null">
        #{erpCancelStatus,jdbcType=TINYINT},
      </if>
      <if test="erpCancelMsg != null">
        #{erpCancelMsg,jdbcType=VARCHAR},
      </if>
      <if test="erpCancelBy != null">
        #{erpCancelBy,jdbcType=BIGINT},
      </if>
      <if test="erpCancelDate != null">
        #{erpCancelDate,jdbcType=TIMESTAMP},
      </if>
      <if test="erpCancelCause != null">
        #{erpCancelCause,jdbcType=VARCHAR},
      </if>
      <if test="dueDate != null">
        #{dueDate,jdbcType=DATE},
      </if>
      <if test="glDate != null">
        #{glDate,jdbcType=TIMESTAMP},
      </if>
      <if test="freezeStatus != null">
        #{freezeStatus,jdbcType=INTEGER},
      </if>
      <if test="invoiceVouNum != null">
        #{invoiceVouNum,jdbcType=VARCHAR},
      </if>
      <if test="invoiceEntryExplain != null">
        #{invoiceEntryExplain,jdbcType=VARCHAR},
      </if>
      <if test="accountEntryType != null">
        #{accountEntryType,jdbcType=INTEGER},
      </if>
      <if test="gscInvoiceNumber != null">
        #{gscInvoiceNumber,jdbcType=VARCHAR},
      </if>
      <if test="itemNumber != null">
        #{itemNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.PaymentInvoiceExample" resultType="java.lang.Long">
    select count(*) from payment_invoice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.PaymentInvoice">
    update payment_invoice
    <set>
      <if test="paymentApplyId != null">
        payment_apply_id = #{paymentApplyId,jdbcType=BIGINT},
      </if>
      <if test="invoiceDate != null">
        invoice_date = #{invoiceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentApplyCode != null">
        payment_apply_code = #{paymentApplyCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentPlanCode != null">
        payment_plan_code = #{paymentPlanCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentPlanId != null">
        payment_plan_id = #{paymentPlanId,jdbcType=BIGINT},
      </if>
      <if test="purchaseContractId != null">
        purchase_contract_id = #{purchaseContractId,jdbcType=BIGINT},
      </if>
      <if test="purchaseContractCode != null">
        purchase_contract_code = #{purchaseContractCode,jdbcType=VARCHAR},
      </if>
      <if test="ouId != null">
        ou_id = #{ouId,jdbcType=BIGINT},
      </if>
      <if test="apInvoiceCode != null">
        ap_invoice_code = #{apInvoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="erpInvoiceCode != null">
        erp_invoice_code = #{erpInvoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="totalInvoiceIncludedPrice != null">
        total_invoice_included_price = #{totalInvoiceIncludedPrice,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalPayIncludedPrice != null">
        total_pay_included_price = #{totalPayIncludedPrice,jdbcType=DECIMAL},
      </if>
      <if test="surplusAmount != null">
        surplus_amount = #{surplusAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxCode != null">
        tax_code = #{taxCode,jdbcType=VARCHAR},
      </if>
      <if test="erpStatus != null">
        erp_status = #{erpStatus,jdbcType=TINYINT},
      </if>
      <if test="erpMsg != null">
        erp_msg = #{erpMsg,jdbcType=VARCHAR},
      </if>
      <if test="available != null">
        available = #{available,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="auditDate != null">
        audit_date = #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="collectionStatus != null">
        collection_status = #{collectionStatus,jdbcType=INTEGER},
      </if>
      <if test="annex != null">
        annex = #{annex,jdbcType=VARCHAR},
      </if>
      <if test="erpCancelStatus != null">
        erp_cancel_status = #{erpCancelStatus,jdbcType=TINYINT},
      </if>
      <if test="erpCancelMsg != null">
        erp_cancel_msg = #{erpCancelMsg,jdbcType=VARCHAR},
      </if>
      <if test="erpCancelBy != null">
        erp_cancel_by = #{erpCancelBy,jdbcType=BIGINT},
      </if>
      <if test="erpCancelDate != null">
        erp_cancel_date = #{erpCancelDate,jdbcType=TIMESTAMP},
      </if>
      <if test="erpCancelCause != null">
        erp_cancel_cause = #{erpCancelCause,jdbcType=VARCHAR},
      </if>
      <if test="dueDate != null">
        due_date = #{dueDate,jdbcType=DATE},
      </if>
      <if test="glDate != null">
        gl_date = #{glDate,jdbcType=TIMESTAMP},
      </if>
      <if test="freezeStatus != null">
        freeze_status = #{freezeStatus,jdbcType=INTEGER},
      </if>
      <if test="invoiceVouNum != null">
        invoice_vou_num = #{invoiceVouNum,jdbcType=VARCHAR},
      </if>
      <if test="invoiceEntryExplain != null">
        Invoice_entry_explain = #{invoiceEntryExplain,jdbcType=VARCHAR},
      </if>
      <if test="accountEntryType != null">
        account_entry_type = #{accountEntryType,jdbcType=INTEGER},
      </if>
      <if test="gscInvoiceNumber != null">
        gsc_invoice_number = #{gscInvoiceNumber,jdbcType=VARCHAR},
      </if>
      <if test="itemNumber != null">
        item_number = #{itemNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.PaymentInvoice">
    update payment_invoice
    set payment_apply_id = #{paymentApplyId,jdbcType=BIGINT},
      invoice_date = #{invoiceDate,jdbcType=TIMESTAMP},
      payment_apply_code = #{paymentApplyCode,jdbcType=VARCHAR},
      payment_plan_code = #{paymentPlanCode,jdbcType=VARCHAR},
      payment_plan_id = #{paymentPlanId,jdbcType=BIGINT},
      purchase_contract_id = #{purchaseContractId,jdbcType=BIGINT},
      purchase_contract_code = #{purchaseContractCode,jdbcType=VARCHAR},
      ou_id = #{ouId,jdbcType=BIGINT},
      ap_invoice_code = #{apInvoiceCode,jdbcType=VARCHAR},
      erp_invoice_code = #{erpInvoiceCode,jdbcType=VARCHAR},
      total_invoice_included_price = #{totalInvoiceIncludedPrice,jdbcType=DECIMAL},
      amount = #{amount,jdbcType=DECIMAL},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      total_pay_included_price = #{totalPayIncludedPrice,jdbcType=DECIMAL},
      surplus_amount = #{surplusAmount,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      tax_code = #{taxCode,jdbcType=VARCHAR},
      erp_status = #{erpStatus,jdbcType=TINYINT},
      erp_msg = #{erpMsg,jdbcType=VARCHAR},
      available = #{available,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      version = #{version,jdbcType=BIGINT},
      source = #{source,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      audit_date = #{auditDate,jdbcType=TIMESTAMP},
      collection_status = #{collectionStatus,jdbcType=INTEGER},
      annex = #{annex,jdbcType=VARCHAR},
      erp_cancel_status = #{erpCancelStatus,jdbcType=TINYINT},
      erp_cancel_msg = #{erpCancelMsg,jdbcType=VARCHAR},
      erp_cancel_by = #{erpCancelBy,jdbcType=BIGINT},
      erp_cancel_date = #{erpCancelDate,jdbcType=TIMESTAMP},
      erp_cancel_cause = #{erpCancelCause,jdbcType=VARCHAR},
      due_date = #{dueDate,jdbcType=DATE},
      gl_date = #{glDate,jdbcType=TIMESTAMP},
      freeze_status = #{freezeStatus,jdbcType=INTEGER},
      invoice_vou_num = #{invoiceVouNum,jdbcType=VARCHAR},
      Invoice_entry_explain = #{invoiceEntryExplain,jdbcType=VARCHAR},
      account_entry_type = #{accountEntryType,jdbcType=INTEGER},
      gsc_invoice_number = #{gscInvoiceNumber,jdbcType=VARCHAR},
      item_number = #{itemNumber,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>