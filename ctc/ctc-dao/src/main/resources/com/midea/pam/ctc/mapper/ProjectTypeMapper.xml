<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectTypeMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ProjectType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="cost_method" jdbcType="VARCHAR" property="costMethod" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="budget_dep_Id" jdbcType="BIGINT" property="budgetDepId" />
    <result column="budget_dep_name" jdbcType="VARCHAR" property="budgetDepName" />
    <result column="auto_milepost" jdbcType="TINYINT" property="autoMilepost" />
    <result column="validate_budget_humans" jdbcType="TINYINT" property="validateBudgetHumans" />
    <result column="validate_budget_travels" jdbcType="TINYINT" property="validateBudgetTravels" />
    <result column="validate_budget_fees" jdbcType="TINYINT" property="validateBudgetFees" />
    <result column="validate_budget_materials" jdbcType="TINYINT" property="validateBudgetMaterials" />
    <result column="validate_contract" jdbcType="TINYINT" property="validateContract" />
    <result column="validate_miplepost_help" jdbcType="TINYINT" property="validateMiplepostHelp" />
    <result column="validate_miplepost_main" jdbcType="TINYINT" property="validateMiplepostMain" />
    <result column="validate_business" jdbcType="TINYINT" property="validateBusiness" />
    <result column="validate_financal" jdbcType="TINYINT" property="validateFinancal" />
    <result column="validate_year_budget" jdbcType="TINYINT" property="validateYearBudget" />
    <result column="validate_wf" jdbcType="TINYINT" property="validateWf" />
    <result column="validate_amount" jdbcType="TINYINT" property="validateAmount" />
    <result column="income_method" jdbcType="VARCHAR" property="incomeMethod" />
    <result column="income_point" jdbcType="VARCHAR" property="incomePoint" />
    <result column="milepost_template_id" jdbcType="BIGINT" property="milepostTemplateId" />
    <result column="unit_id" jdbcType="BIGINT" property="unitId" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="category_tag" jdbcType="VARCHAR" property="categoryTag" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="locator_flag" jdbcType="TINYINT" property="locatorFlag" />
    <result column="validate_preview" jdbcType="TINYINT" property="validatePreview" />
    <result column="validate_project_type" jdbcType="TINYINT" property="validateProjectType" />
    <result column="validate_code" jdbcType="TINYINT" property="validateCode" />
    <result column="validate_name" jdbcType="TINYINT" property="validateName" />
    <result column="validate_manager" jdbcType="TINYINT" property="validateManager" />
    <result column="validate_price_type" jdbcType="TINYINT" property="validatePriceType" />
    <result column="validate_customer" jdbcType="TINYINT" property="validateCustomer" />
    <result column="validate_product" jdbcType="TINYINT" property="validateProduct" />
    <result column="validate_ou" jdbcType="TINYINT" property="validateOu" />
    <result column="validate_summary" jdbcType="TINYINT" property="validateSummary" />
    <result column="validate_type" jdbcType="TINYINT" property="validateType" />
    <result column="main_income_flag" jdbcType="TINYINT" property="mainIncomeFlag" />
    <result column="help_income_flag" jdbcType="TINYINT" property="helpIncomeFlag" />
    <result column="main_income_subject" jdbcType="VARCHAR" property="mainIncomeSubject" />
    <result column="help_income_subject" jdbcType="VARCHAR" property="helpIncomeSubject" />
    <result column="milepost_design_can_submit" jdbcType="TINYINT" property="milepostDesignCanSubmit" />
    <result column="milestone_base_date" jdbcType="TINYINT" property="milestoneBaseDate" />
    <result column="project_member_distinct" jdbcType="TINYINT" property="projectMemberDistinct" />
    <result column="validate_objective_project" jdbcType="TINYINT" property="validateObjectiveProject" />
    <result column="validate_project_level" jdbcType="TINYINT" property="validateProjectLevel" />
    <result column="validate_resource_code" jdbcType="TINYINT" property="validateResourceCode" />
    <result column="validate_project_member" jdbcType="TINYINT" property="validateProjectMember" />
    <result column="budget_control_flag" jdbcType="TINYINT" property="budgetControlFlag" />
    <result column="budget_control_ratio" jdbcType="DECIMAL" property="budgetControlRatio" />
    <result column="validate_project_manual" jdbcType="TINYINT" property="validateProjectManual" />
    <result column="requriement_deliver_mrp" jdbcType="TINYINT" property="requriementDeliverMrp" />
    <result column="transfer_project" jdbcType="TINYINT" property="transferProject" />
    <result column="transfer_project_type_id" jdbcType="BIGINT" property="transferProjectTypeId" />
    <result column="project_profit_contribution_rate" jdbcType="TINYINT" property="projectProfitContributionRate" />
    <result column="purchase_contract_type" jdbcType="VARCHAR" property="purchaseContractType" />
    <result column="wbs_enabled" jdbcType="TINYINT" property="wbsEnabled" />
    <result column="wbs_template_info_id" jdbcType="BIGINT" property="wbsTemplateInfoId" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="business_department" jdbcType="VARCHAR" property="businessDepartment" />
    <result column="detailed_address" jdbcType="VARCHAR" property="detailedAddress" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="relate_asset" jdbcType="TINYINT" property="relateAsset" />
    <result column="project_progress_predict_flag" jdbcType="TINYINT" property="projectProgressPredictFlag" />
    <result column="design_plan_module_status_default" jdbcType="INTEGER" property="designPlanModuleStatusDefault" />
    <result column="wbs_design_plan_auto_confirm" jdbcType="INTEGER" property="wbsDesignPlanAutoConfirm" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.midea.pam.common.ctc.entity.ProjectType">
    <result column="field_config" jdbcType="LONGVARCHAR" property="fieldConfig" />
    <result column="budget_config" jdbcType="LONGVARCHAR" property="budgetConfig" />
    <result column="code_rule_config" jdbcType="LONGVARCHAR" property="codeRuleConfig" />
    <result column="work_order_task_config" jdbcType="LONGVARCHAR" property="workOrderTaskConfig" />
    <result column="accounting_config" jdbcType="LONGVARCHAR" property="accountingConfig" />
    <result column="subject_config" jdbcType="LONGVARCHAR" property="subjectConfig" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, code, cost_method, order_num, description, budget_dep_Id, budget_dep_name, 
    auto_milepost, validate_budget_humans, validate_budget_travels, validate_budget_fees, 
    validate_budget_materials, validate_contract, validate_miplepost_help, validate_miplepost_main, 
    validate_business, validate_financal, validate_year_budget, validate_wf, validate_amount, 
    income_method, income_point, milepost_template_id, unit_id, tax_rate, category_name, 
    category_tag, deleted_flag, create_by, create_at, update_by, update_at, locator_flag, 
    validate_preview, validate_project_type, validate_code, validate_name, validate_manager, 
    validate_price_type, validate_customer, validate_product, validate_ou, validate_summary, 
    validate_type, main_income_flag, help_income_flag, main_income_subject, help_income_subject, 
    milepost_design_can_submit, milestone_base_date, project_member_distinct, validate_objective_project, 
    validate_project_level, validate_resource_code, validate_project_member, budget_control_flag, 
    budget_control_ratio, validate_project_manual, requriement_deliver_mrp, transfer_project, 
    transfer_project_type_id, project_profit_contribution_rate, purchase_contract_type, 
    wbs_enabled, wbs_template_info_id, area, business_department, detailed_address, city, 
    relate_asset, project_progress_predict_flag, design_plan_module_status_default, wbs_design_plan_auto_confirm
  </sql>
  <sql id="Blob_Column_List">
    field_config, budget_config, code_rule_config, work_order_task_config, accounting_config, 
    subject_config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.ProjectTypeExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.ProjectTypeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project_type
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_type
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.ProjectType">
    insert into project_type (id, name, code, 
      cost_method, order_num, description, 
      budget_dep_Id, budget_dep_name, auto_milepost, 
      validate_budget_humans, validate_budget_travels, 
      validate_budget_fees, validate_budget_materials, 
      validate_contract, validate_miplepost_help, 
      validate_miplepost_main, validate_business, 
      validate_financal, validate_year_budget, validate_wf, 
      validate_amount, income_method, income_point, 
      milepost_template_id, unit_id, tax_rate, 
      category_name, category_tag, deleted_flag, 
      create_by, create_at, update_by, 
      update_at, locator_flag, validate_preview, 
      validate_project_type, validate_code, validate_name, 
      validate_manager, validate_price_type, validate_customer, 
      validate_product, validate_ou, validate_summary, 
      validate_type, main_income_flag, help_income_flag, 
      main_income_subject, help_income_subject, milepost_design_can_submit, 
      milestone_base_date, project_member_distinct, 
      validate_objective_project, validate_project_level, 
      validate_resource_code, validate_project_member, 
      budget_control_flag, budget_control_ratio, 
      validate_project_manual, requriement_deliver_mrp, 
      transfer_project, transfer_project_type_id, 
      project_profit_contribution_rate, purchase_contract_type, 
      wbs_enabled, wbs_template_info_id, area, 
      business_department, detailed_address, city, 
      relate_asset, project_progress_predict_flag, 
      design_plan_module_status_default, wbs_design_plan_auto_confirm, 
      field_config, budget_config, code_rule_config, 
      work_order_task_config, accounting_config, 
      subject_config)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{costMethod,jdbcType=VARCHAR}, #{orderNum,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR}, 
      #{budgetDepId,jdbcType=BIGINT}, #{budgetDepName,jdbcType=VARCHAR}, #{autoMilepost,jdbcType=TINYINT}, 
      #{validateBudgetHumans,jdbcType=TINYINT}, #{validateBudgetTravels,jdbcType=TINYINT}, 
      #{validateBudgetFees,jdbcType=TINYINT}, #{validateBudgetMaterials,jdbcType=TINYINT}, 
      #{validateContract,jdbcType=TINYINT}, #{validateMiplepostHelp,jdbcType=TINYINT}, 
      #{validateMiplepostMain,jdbcType=TINYINT}, #{validateBusiness,jdbcType=TINYINT}, 
      #{validateFinancal,jdbcType=TINYINT}, #{validateYearBudget,jdbcType=TINYINT}, #{validateWf,jdbcType=TINYINT}, 
      #{validateAmount,jdbcType=TINYINT}, #{incomeMethod,jdbcType=VARCHAR}, #{incomePoint,jdbcType=VARCHAR}, 
      #{milepostTemplateId,jdbcType=BIGINT}, #{unitId,jdbcType=BIGINT}, #{taxRate,jdbcType=DECIMAL}, 
      #{categoryName,jdbcType=VARCHAR}, #{categoryTag,jdbcType=VARCHAR}, #{deletedFlag,jdbcType=TINYINT}, 
      #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{updateAt,jdbcType=TIMESTAMP}, #{locatorFlag,jdbcType=TINYINT}, #{validatePreview,jdbcType=TINYINT}, 
      #{validateProjectType,jdbcType=TINYINT}, #{validateCode,jdbcType=TINYINT}, #{validateName,jdbcType=TINYINT}, 
      #{validateManager,jdbcType=TINYINT}, #{validatePriceType,jdbcType=TINYINT}, #{validateCustomer,jdbcType=TINYINT}, 
      #{validateProduct,jdbcType=TINYINT}, #{validateOu,jdbcType=TINYINT}, #{validateSummary,jdbcType=TINYINT}, 
      #{validateType,jdbcType=TINYINT}, #{mainIncomeFlag,jdbcType=TINYINT}, #{helpIncomeFlag,jdbcType=TINYINT}, 
      #{mainIncomeSubject,jdbcType=VARCHAR}, #{helpIncomeSubject,jdbcType=VARCHAR}, #{milepostDesignCanSubmit,jdbcType=TINYINT}, 
      #{milestoneBaseDate,jdbcType=TINYINT}, #{projectMemberDistinct,jdbcType=TINYINT}, 
      #{validateObjectiveProject,jdbcType=TINYINT}, #{validateProjectLevel,jdbcType=TINYINT}, 
      #{validateResourceCode,jdbcType=TINYINT}, #{validateProjectMember,jdbcType=TINYINT}, 
      #{budgetControlFlag,jdbcType=TINYINT}, #{budgetControlRatio,jdbcType=DECIMAL}, 
      #{validateProjectManual,jdbcType=TINYINT}, #{requriementDeliverMrp,jdbcType=TINYINT}, 
      #{transferProject,jdbcType=TINYINT}, #{transferProjectTypeId,jdbcType=BIGINT}, 
      #{projectProfitContributionRate,jdbcType=TINYINT}, #{purchaseContractType,jdbcType=VARCHAR}, 
      #{wbsEnabled,jdbcType=TINYINT}, #{wbsTemplateInfoId,jdbcType=BIGINT}, #{area,jdbcType=VARCHAR}, 
      #{businessDepartment,jdbcType=VARCHAR}, #{detailedAddress,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{relateAsset,jdbcType=TINYINT}, #{projectProgressPredictFlag,jdbcType=TINYINT}, 
      #{designPlanModuleStatusDefault,jdbcType=INTEGER}, #{wbsDesignPlanAutoConfirm,jdbcType=INTEGER}, 
      #{fieldConfig,jdbcType=LONGVARCHAR}, #{budgetConfig,jdbcType=LONGVARCHAR}, #{codeRuleConfig,jdbcType=LONGVARCHAR}, 
      #{workOrderTaskConfig,jdbcType=LONGVARCHAR}, #{accountingConfig,jdbcType=LONGVARCHAR}, 
      #{subjectConfig,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.ProjectType">
    insert into project_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="costMethod != null">
        cost_method,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="budgetDepId != null">
        budget_dep_Id,
      </if>
      <if test="budgetDepName != null">
        budget_dep_name,
      </if>
      <if test="autoMilepost != null">
        auto_milepost,
      </if>
      <if test="validateBudgetHumans != null">
        validate_budget_humans,
      </if>
      <if test="validateBudgetTravels != null">
        validate_budget_travels,
      </if>
      <if test="validateBudgetFees != null">
        validate_budget_fees,
      </if>
      <if test="validateBudgetMaterials != null">
        validate_budget_materials,
      </if>
      <if test="validateContract != null">
        validate_contract,
      </if>
      <if test="validateMiplepostHelp != null">
        validate_miplepost_help,
      </if>
      <if test="validateMiplepostMain != null">
        validate_miplepost_main,
      </if>
      <if test="validateBusiness != null">
        validate_business,
      </if>
      <if test="validateFinancal != null">
        validate_financal,
      </if>
      <if test="validateYearBudget != null">
        validate_year_budget,
      </if>
      <if test="validateWf != null">
        validate_wf,
      </if>
      <if test="validateAmount != null">
        validate_amount,
      </if>
      <if test="incomeMethod != null">
        income_method,
      </if>
      <if test="incomePoint != null">
        income_point,
      </if>
      <if test="milepostTemplateId != null">
        milepost_template_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="categoryTag != null">
        category_tag,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="locatorFlag != null">
        locator_flag,
      </if>
      <if test="validatePreview != null">
        validate_preview,
      </if>
      <if test="validateProjectType != null">
        validate_project_type,
      </if>
      <if test="validateCode != null">
        validate_code,
      </if>
      <if test="validateName != null">
        validate_name,
      </if>
      <if test="validateManager != null">
        validate_manager,
      </if>
      <if test="validatePriceType != null">
        validate_price_type,
      </if>
      <if test="validateCustomer != null">
        validate_customer,
      </if>
      <if test="validateProduct != null">
        validate_product,
      </if>
      <if test="validateOu != null">
        validate_ou,
      </if>
      <if test="validateSummary != null">
        validate_summary,
      </if>
      <if test="validateType != null">
        validate_type,
      </if>
      <if test="mainIncomeFlag != null">
        main_income_flag,
      </if>
      <if test="helpIncomeFlag != null">
        help_income_flag,
      </if>
      <if test="mainIncomeSubject != null">
        main_income_subject,
      </if>
      <if test="helpIncomeSubject != null">
        help_income_subject,
      </if>
      <if test="milepostDesignCanSubmit != null">
        milepost_design_can_submit,
      </if>
      <if test="milestoneBaseDate != null">
        milestone_base_date,
      </if>
      <if test="projectMemberDistinct != null">
        project_member_distinct,
      </if>
      <if test="validateObjectiveProject != null">
        validate_objective_project,
      </if>
      <if test="validateProjectLevel != null">
        validate_project_level,
      </if>
      <if test="validateResourceCode != null">
        validate_resource_code,
      </if>
      <if test="validateProjectMember != null">
        validate_project_member,
      </if>
      <if test="budgetControlFlag != null">
        budget_control_flag,
      </if>
      <if test="budgetControlRatio != null">
        budget_control_ratio,
      </if>
      <if test="validateProjectManual != null">
        validate_project_manual,
      </if>
      <if test="requriementDeliverMrp != null">
        requriement_deliver_mrp,
      </if>
      <if test="transferProject != null">
        transfer_project,
      </if>
      <if test="transferProjectTypeId != null">
        transfer_project_type_id,
      </if>
      <if test="projectProfitContributionRate != null">
        project_profit_contribution_rate,
      </if>
      <if test="purchaseContractType != null">
        purchase_contract_type,
      </if>
      <if test="wbsEnabled != null">
        wbs_enabled,
      </if>
      <if test="wbsTemplateInfoId != null">
        wbs_template_info_id,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="businessDepartment != null">
        business_department,
      </if>
      <if test="detailedAddress != null">
        detailed_address,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="relateAsset != null">
        relate_asset,
      </if>
      <if test="projectProgressPredictFlag != null">
        project_progress_predict_flag,
      </if>
      <if test="designPlanModuleStatusDefault != null">
        design_plan_module_status_default,
      </if>
      <if test="wbsDesignPlanAutoConfirm != null">
        wbs_design_plan_auto_confirm,
      </if>
      <if test="fieldConfig != null">
        field_config,
      </if>
      <if test="budgetConfig != null">
        budget_config,
      </if>
      <if test="codeRuleConfig != null">
        code_rule_config,
      </if>
      <if test="workOrderTaskConfig != null">
        work_order_task_config,
      </if>
      <if test="accountingConfig != null">
        accounting_config,
      </if>
      <if test="subjectConfig != null">
        subject_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="costMethod != null">
        #{costMethod,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="budgetDepId != null">
        #{budgetDepId,jdbcType=BIGINT},
      </if>
      <if test="budgetDepName != null">
        #{budgetDepName,jdbcType=VARCHAR},
      </if>
      <if test="autoMilepost != null">
        #{autoMilepost,jdbcType=TINYINT},
      </if>
      <if test="validateBudgetHumans != null">
        #{validateBudgetHumans,jdbcType=TINYINT},
      </if>
      <if test="validateBudgetTravels != null">
        #{validateBudgetTravels,jdbcType=TINYINT},
      </if>
      <if test="validateBudgetFees != null">
        #{validateBudgetFees,jdbcType=TINYINT},
      </if>
      <if test="validateBudgetMaterials != null">
        #{validateBudgetMaterials,jdbcType=TINYINT},
      </if>
      <if test="validateContract != null">
        #{validateContract,jdbcType=TINYINT},
      </if>
      <if test="validateMiplepostHelp != null">
        #{validateMiplepostHelp,jdbcType=TINYINT},
      </if>
      <if test="validateMiplepostMain != null">
        #{validateMiplepostMain,jdbcType=TINYINT},
      </if>
      <if test="validateBusiness != null">
        #{validateBusiness,jdbcType=TINYINT},
      </if>
      <if test="validateFinancal != null">
        #{validateFinancal,jdbcType=TINYINT},
      </if>
      <if test="validateYearBudget != null">
        #{validateYearBudget,jdbcType=TINYINT},
      </if>
      <if test="validateWf != null">
        #{validateWf,jdbcType=TINYINT},
      </if>
      <if test="validateAmount != null">
        #{validateAmount,jdbcType=TINYINT},
      </if>
      <if test="incomeMethod != null">
        #{incomeMethod,jdbcType=VARCHAR},
      </if>
      <if test="incomePoint != null">
        #{incomePoint,jdbcType=VARCHAR},
      </if>
      <if test="milepostTemplateId != null">
        #{milepostTemplateId,jdbcType=BIGINT},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=BIGINT},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="categoryTag != null">
        #{categoryTag,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="locatorFlag != null">
        #{locatorFlag,jdbcType=TINYINT},
      </if>
      <if test="validatePreview != null">
        #{validatePreview,jdbcType=TINYINT},
      </if>
      <if test="validateProjectType != null">
        #{validateProjectType,jdbcType=TINYINT},
      </if>
      <if test="validateCode != null">
        #{validateCode,jdbcType=TINYINT},
      </if>
      <if test="validateName != null">
        #{validateName,jdbcType=TINYINT},
      </if>
      <if test="validateManager != null">
        #{validateManager,jdbcType=TINYINT},
      </if>
      <if test="validatePriceType != null">
        #{validatePriceType,jdbcType=TINYINT},
      </if>
      <if test="validateCustomer != null">
        #{validateCustomer,jdbcType=TINYINT},
      </if>
      <if test="validateProduct != null">
        #{validateProduct,jdbcType=TINYINT},
      </if>
      <if test="validateOu != null">
        #{validateOu,jdbcType=TINYINT},
      </if>
      <if test="validateSummary != null">
        #{validateSummary,jdbcType=TINYINT},
      </if>
      <if test="validateType != null">
        #{validateType,jdbcType=TINYINT},
      </if>
      <if test="mainIncomeFlag != null">
        #{mainIncomeFlag,jdbcType=TINYINT},
      </if>
      <if test="helpIncomeFlag != null">
        #{helpIncomeFlag,jdbcType=TINYINT},
      </if>
      <if test="mainIncomeSubject != null">
        #{mainIncomeSubject,jdbcType=VARCHAR},
      </if>
      <if test="helpIncomeSubject != null">
        #{helpIncomeSubject,jdbcType=VARCHAR},
      </if>
      <if test="milepostDesignCanSubmit != null">
        #{milepostDesignCanSubmit,jdbcType=TINYINT},
      </if>
      <if test="milestoneBaseDate != null">
        #{milestoneBaseDate,jdbcType=TINYINT},
      </if>
      <if test="projectMemberDistinct != null">
        #{projectMemberDistinct,jdbcType=TINYINT},
      </if>
      <if test="validateObjectiveProject != null">
        #{validateObjectiveProject,jdbcType=TINYINT},
      </if>
      <if test="validateProjectLevel != null">
        #{validateProjectLevel,jdbcType=TINYINT},
      </if>
      <if test="validateResourceCode != null">
        #{validateResourceCode,jdbcType=TINYINT},
      </if>
      <if test="validateProjectMember != null">
        #{validateProjectMember,jdbcType=TINYINT},
      </if>
      <if test="budgetControlFlag != null">
        #{budgetControlFlag,jdbcType=TINYINT},
      </if>
      <if test="budgetControlRatio != null">
        #{budgetControlRatio,jdbcType=DECIMAL},
      </if>
      <if test="validateProjectManual != null">
        #{validateProjectManual,jdbcType=TINYINT},
      </if>
      <if test="requriementDeliverMrp != null">
        #{requriementDeliverMrp,jdbcType=TINYINT},
      </if>
      <if test="transferProject != null">
        #{transferProject,jdbcType=TINYINT},
      </if>
      <if test="transferProjectTypeId != null">
        #{transferProjectTypeId,jdbcType=BIGINT},
      </if>
      <if test="projectProfitContributionRate != null">
        #{projectProfitContributionRate,jdbcType=TINYINT},
      </if>
      <if test="purchaseContractType != null">
        #{purchaseContractType,jdbcType=VARCHAR},
      </if>
      <if test="wbsEnabled != null">
        #{wbsEnabled,jdbcType=TINYINT},
      </if>
      <if test="wbsTemplateInfoId != null">
        #{wbsTemplateInfoId,jdbcType=BIGINT},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="businessDepartment != null">
        #{businessDepartment,jdbcType=VARCHAR},
      </if>
      <if test="detailedAddress != null">
        #{detailedAddress,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="relateAsset != null">
        #{relateAsset,jdbcType=TINYINT},
      </if>
      <if test="projectProgressPredictFlag != null">
        #{projectProgressPredictFlag,jdbcType=TINYINT},
      </if>
      <if test="designPlanModuleStatusDefault != null">
        #{designPlanModuleStatusDefault,jdbcType=INTEGER},
      </if>
      <if test="wbsDesignPlanAutoConfirm != null">
        #{wbsDesignPlanAutoConfirm,jdbcType=INTEGER},
      </if>
      <if test="fieldConfig != null">
        #{fieldConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="budgetConfig != null">
        #{budgetConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="codeRuleConfig != null">
        #{codeRuleConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="workOrderTaskConfig != null">
        #{workOrderTaskConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="accountingConfig != null">
        #{accountingConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="subjectConfig != null">
        #{subjectConfig,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.ProjectTypeExample" resultType="java.lang.Long">
    select count(*) from project_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.ProjectType">
    update project_type
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="costMethod != null">
        cost_method = #{costMethod,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="budgetDepId != null">
        budget_dep_Id = #{budgetDepId,jdbcType=BIGINT},
      </if>
      <if test="budgetDepName != null">
        budget_dep_name = #{budgetDepName,jdbcType=VARCHAR},
      </if>
      <if test="autoMilepost != null">
        auto_milepost = #{autoMilepost,jdbcType=TINYINT},
      </if>
      <if test="validateBudgetHumans != null">
        validate_budget_humans = #{validateBudgetHumans,jdbcType=TINYINT},
      </if>
      <if test="validateBudgetTravels != null">
        validate_budget_travels = #{validateBudgetTravels,jdbcType=TINYINT},
      </if>
      <if test="validateBudgetFees != null">
        validate_budget_fees = #{validateBudgetFees,jdbcType=TINYINT},
      </if>
      <if test="validateBudgetMaterials != null">
        validate_budget_materials = #{validateBudgetMaterials,jdbcType=TINYINT},
      </if>
      <if test="validateContract != null">
        validate_contract = #{validateContract,jdbcType=TINYINT},
      </if>
      <if test="validateMiplepostHelp != null">
        validate_miplepost_help = #{validateMiplepostHelp,jdbcType=TINYINT},
      </if>
      <if test="validateMiplepostMain != null">
        validate_miplepost_main = #{validateMiplepostMain,jdbcType=TINYINT},
      </if>
      <if test="validateBusiness != null">
        validate_business = #{validateBusiness,jdbcType=TINYINT},
      </if>
      <if test="validateFinancal != null">
        validate_financal = #{validateFinancal,jdbcType=TINYINT},
      </if>
      <if test="validateYearBudget != null">
        validate_year_budget = #{validateYearBudget,jdbcType=TINYINT},
      </if>
      <if test="validateWf != null">
        validate_wf = #{validateWf,jdbcType=TINYINT},
      </if>
      <if test="validateAmount != null">
        validate_amount = #{validateAmount,jdbcType=TINYINT},
      </if>
      <if test="incomeMethod != null">
        income_method = #{incomeMethod,jdbcType=VARCHAR},
      </if>
      <if test="incomePoint != null">
        income_point = #{incomePoint,jdbcType=VARCHAR},
      </if>
      <if test="milepostTemplateId != null">
        milepost_template_id = #{milepostTemplateId,jdbcType=BIGINT},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=BIGINT},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="categoryName != null">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="categoryTag != null">
        category_tag = #{categoryTag,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="locatorFlag != null">
        locator_flag = #{locatorFlag,jdbcType=TINYINT},
      </if>
      <if test="validatePreview != null">
        validate_preview = #{validatePreview,jdbcType=TINYINT},
      </if>
      <if test="validateProjectType != null">
        validate_project_type = #{validateProjectType,jdbcType=TINYINT},
      </if>
      <if test="validateCode != null">
        validate_code = #{validateCode,jdbcType=TINYINT},
      </if>
      <if test="validateName != null">
        validate_name = #{validateName,jdbcType=TINYINT},
      </if>
      <if test="validateManager != null">
        validate_manager = #{validateManager,jdbcType=TINYINT},
      </if>
      <if test="validatePriceType != null">
        validate_price_type = #{validatePriceType,jdbcType=TINYINT},
      </if>
      <if test="validateCustomer != null">
        validate_customer = #{validateCustomer,jdbcType=TINYINT},
      </if>
      <if test="validateProduct != null">
        validate_product = #{validateProduct,jdbcType=TINYINT},
      </if>
      <if test="validateOu != null">
        validate_ou = #{validateOu,jdbcType=TINYINT},
      </if>
      <if test="validateSummary != null">
        validate_summary = #{validateSummary,jdbcType=TINYINT},
      </if>
      <if test="validateType != null">
        validate_type = #{validateType,jdbcType=TINYINT},
      </if>
      <if test="mainIncomeFlag != null">
        main_income_flag = #{mainIncomeFlag,jdbcType=TINYINT},
      </if>
      <if test="helpIncomeFlag != null">
        help_income_flag = #{helpIncomeFlag,jdbcType=TINYINT},
      </if>
      <if test="mainIncomeSubject != null">
        main_income_subject = #{mainIncomeSubject,jdbcType=VARCHAR},
      </if>
      <if test="helpIncomeSubject != null">
        help_income_subject = #{helpIncomeSubject,jdbcType=VARCHAR},
      </if>
      <if test="milepostDesignCanSubmit != null">
        milepost_design_can_submit = #{milepostDesignCanSubmit,jdbcType=TINYINT},
      </if>
      <if test="milestoneBaseDate != null">
        milestone_base_date = #{milestoneBaseDate,jdbcType=TINYINT},
      </if>
      <if test="projectMemberDistinct != null">
        project_member_distinct = #{projectMemberDistinct,jdbcType=TINYINT},
      </if>
      <if test="validateObjectiveProject != null">
        validate_objective_project = #{validateObjectiveProject,jdbcType=TINYINT},
      </if>
      <if test="validateProjectLevel != null">
        validate_project_level = #{validateProjectLevel,jdbcType=TINYINT},
      </if>
      <if test="validateResourceCode != null">
        validate_resource_code = #{validateResourceCode,jdbcType=TINYINT},
      </if>
      <if test="validateProjectMember != null">
        validate_project_member = #{validateProjectMember,jdbcType=TINYINT},
      </if>
      <if test="budgetControlFlag != null">
        budget_control_flag = #{budgetControlFlag,jdbcType=TINYINT},
      </if>
      <if test="budgetControlRatio != null">
        budget_control_ratio = #{budgetControlRatio,jdbcType=DECIMAL},
      </if>
      <if test="validateProjectManual != null">
        validate_project_manual = #{validateProjectManual,jdbcType=TINYINT},
      </if>
      <if test="requriementDeliverMrp != null">
        requriement_deliver_mrp = #{requriementDeliverMrp,jdbcType=TINYINT},
      </if>
      <if test="transferProject != null">
        transfer_project = #{transferProject,jdbcType=TINYINT},
      </if>
      <if test="transferProjectTypeId != null">
        transfer_project_type_id = #{transferProjectTypeId,jdbcType=BIGINT},
      </if>
      <if test="projectProfitContributionRate != null">
        project_profit_contribution_rate = #{projectProfitContributionRate,jdbcType=TINYINT},
      </if>
      <if test="purchaseContractType != null">
        purchase_contract_type = #{purchaseContractType,jdbcType=VARCHAR},
      </if>
      <if test="wbsEnabled != null">
        wbs_enabled = #{wbsEnabled,jdbcType=TINYINT},
      </if>
      <if test="wbsTemplateInfoId != null">
        wbs_template_info_id = #{wbsTemplateInfoId,jdbcType=BIGINT},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="businessDepartment != null">
        business_department = #{businessDepartment,jdbcType=VARCHAR},
      </if>
      <if test="detailedAddress != null">
        detailed_address = #{detailedAddress,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="relateAsset != null">
        relate_asset = #{relateAsset,jdbcType=TINYINT},
      </if>
      <if test="projectProgressPredictFlag != null">
        project_progress_predict_flag = #{projectProgressPredictFlag,jdbcType=TINYINT},
      </if>
      <if test="designPlanModuleStatusDefault != null">
        design_plan_module_status_default = #{designPlanModuleStatusDefault,jdbcType=INTEGER},
      </if>
      <if test="wbsDesignPlanAutoConfirm != null">
        wbs_design_plan_auto_confirm = #{wbsDesignPlanAutoConfirm,jdbcType=INTEGER},
      </if>
      <if test="fieldConfig != null">
        field_config = #{fieldConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="budgetConfig != null">
        budget_config = #{budgetConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="codeRuleConfig != null">
        code_rule_config = #{codeRuleConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="workOrderTaskConfig != null">
        work_order_task_config = #{workOrderTaskConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="accountingConfig != null">
        accounting_config = #{accountingConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="subjectConfig != null">
        subject_config = #{subjectConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.ProjectType">
    update project_type
    set name = #{name,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      cost_method = #{costMethod,jdbcType=VARCHAR},
      order_num = #{orderNum,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      budget_dep_Id = #{budgetDepId,jdbcType=BIGINT},
      budget_dep_name = #{budgetDepName,jdbcType=VARCHAR},
      auto_milepost = #{autoMilepost,jdbcType=TINYINT},
      validate_budget_humans = #{validateBudgetHumans,jdbcType=TINYINT},
      validate_budget_travels = #{validateBudgetTravels,jdbcType=TINYINT},
      validate_budget_fees = #{validateBudgetFees,jdbcType=TINYINT},
      validate_budget_materials = #{validateBudgetMaterials,jdbcType=TINYINT},
      validate_contract = #{validateContract,jdbcType=TINYINT},
      validate_miplepost_help = #{validateMiplepostHelp,jdbcType=TINYINT},
      validate_miplepost_main = #{validateMiplepostMain,jdbcType=TINYINT},
      validate_business = #{validateBusiness,jdbcType=TINYINT},
      validate_financal = #{validateFinancal,jdbcType=TINYINT},
      validate_year_budget = #{validateYearBudget,jdbcType=TINYINT},
      validate_wf = #{validateWf,jdbcType=TINYINT},
      validate_amount = #{validateAmount,jdbcType=TINYINT},
      income_method = #{incomeMethod,jdbcType=VARCHAR},
      income_point = #{incomePoint,jdbcType=VARCHAR},
      milepost_template_id = #{milepostTemplateId,jdbcType=BIGINT},
      unit_id = #{unitId,jdbcType=BIGINT},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      category_name = #{categoryName,jdbcType=VARCHAR},
      category_tag = #{categoryTag,jdbcType=VARCHAR},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      locator_flag = #{locatorFlag,jdbcType=TINYINT},
      validate_preview = #{validatePreview,jdbcType=TINYINT},
      validate_project_type = #{validateProjectType,jdbcType=TINYINT},
      validate_code = #{validateCode,jdbcType=TINYINT},
      validate_name = #{validateName,jdbcType=TINYINT},
      validate_manager = #{validateManager,jdbcType=TINYINT},
      validate_price_type = #{validatePriceType,jdbcType=TINYINT},
      validate_customer = #{validateCustomer,jdbcType=TINYINT},
      validate_product = #{validateProduct,jdbcType=TINYINT},
      validate_ou = #{validateOu,jdbcType=TINYINT},
      validate_summary = #{validateSummary,jdbcType=TINYINT},
      validate_type = #{validateType,jdbcType=TINYINT},
      main_income_flag = #{mainIncomeFlag,jdbcType=TINYINT},
      help_income_flag = #{helpIncomeFlag,jdbcType=TINYINT},
      main_income_subject = #{mainIncomeSubject,jdbcType=VARCHAR},
      help_income_subject = #{helpIncomeSubject,jdbcType=VARCHAR},
      milepost_design_can_submit = #{milepostDesignCanSubmit,jdbcType=TINYINT},
      milestone_base_date = #{milestoneBaseDate,jdbcType=TINYINT},
      project_member_distinct = #{projectMemberDistinct,jdbcType=TINYINT},
      validate_objective_project = #{validateObjectiveProject,jdbcType=TINYINT},
      validate_project_level = #{validateProjectLevel,jdbcType=TINYINT},
      validate_resource_code = #{validateResourceCode,jdbcType=TINYINT},
      validate_project_member = #{validateProjectMember,jdbcType=TINYINT},
      budget_control_flag = #{budgetControlFlag,jdbcType=TINYINT},
      budget_control_ratio = #{budgetControlRatio,jdbcType=DECIMAL},
      validate_project_manual = #{validateProjectManual,jdbcType=TINYINT},
      requriement_deliver_mrp = #{requriementDeliverMrp,jdbcType=TINYINT},
      transfer_project = #{transferProject,jdbcType=TINYINT},
      transfer_project_type_id = #{transferProjectTypeId,jdbcType=BIGINT},
      project_profit_contribution_rate = #{projectProfitContributionRate,jdbcType=TINYINT},
      purchase_contract_type = #{purchaseContractType,jdbcType=VARCHAR},
      wbs_enabled = #{wbsEnabled,jdbcType=TINYINT},
      wbs_template_info_id = #{wbsTemplateInfoId,jdbcType=BIGINT},
      area = #{area,jdbcType=VARCHAR},
      business_department = #{businessDepartment,jdbcType=VARCHAR},
      detailed_address = #{detailedAddress,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      relate_asset = #{relateAsset,jdbcType=TINYINT},
      project_progress_predict_flag = #{projectProgressPredictFlag,jdbcType=TINYINT},
      design_plan_module_status_default = #{designPlanModuleStatusDefault,jdbcType=INTEGER},
      wbs_design_plan_auto_confirm = #{wbsDesignPlanAutoConfirm,jdbcType=INTEGER},
      field_config = #{fieldConfig,jdbcType=LONGVARCHAR},
      budget_config = #{budgetConfig,jdbcType=LONGVARCHAR},
      code_rule_config = #{codeRuleConfig,jdbcType=LONGVARCHAR},
      work_order_task_config = #{workOrderTaskConfig,jdbcType=LONGVARCHAR},
      accounting_config = #{accountingConfig,jdbcType=LONGVARCHAR},
      subject_config = #{subjectConfig,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.ProjectType">
    update project_type
    set name = #{name,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      cost_method = #{costMethod,jdbcType=VARCHAR},
      order_num = #{orderNum,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      budget_dep_Id = #{budgetDepId,jdbcType=BIGINT},
      budget_dep_name = #{budgetDepName,jdbcType=VARCHAR},
      auto_milepost = #{autoMilepost,jdbcType=TINYINT},
      validate_budget_humans = #{validateBudgetHumans,jdbcType=TINYINT},
      validate_budget_travels = #{validateBudgetTravels,jdbcType=TINYINT},
      validate_budget_fees = #{validateBudgetFees,jdbcType=TINYINT},
      validate_budget_materials = #{validateBudgetMaterials,jdbcType=TINYINT},
      validate_contract = #{validateContract,jdbcType=TINYINT},
      validate_miplepost_help = #{validateMiplepostHelp,jdbcType=TINYINT},
      validate_miplepost_main = #{validateMiplepostMain,jdbcType=TINYINT},
      validate_business = #{validateBusiness,jdbcType=TINYINT},
      validate_financal = #{validateFinancal,jdbcType=TINYINT},
      validate_year_budget = #{validateYearBudget,jdbcType=TINYINT},
      validate_wf = #{validateWf,jdbcType=TINYINT},
      validate_amount = #{validateAmount,jdbcType=TINYINT},
      income_method = #{incomeMethod,jdbcType=VARCHAR},
      income_point = #{incomePoint,jdbcType=VARCHAR},
      milepost_template_id = #{milepostTemplateId,jdbcType=BIGINT},
      unit_id = #{unitId,jdbcType=BIGINT},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      category_name = #{categoryName,jdbcType=VARCHAR},
      category_tag = #{categoryTag,jdbcType=VARCHAR},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      locator_flag = #{locatorFlag,jdbcType=TINYINT},
      validate_preview = #{validatePreview,jdbcType=TINYINT},
      validate_project_type = #{validateProjectType,jdbcType=TINYINT},
      validate_code = #{validateCode,jdbcType=TINYINT},
      validate_name = #{validateName,jdbcType=TINYINT},
      validate_manager = #{validateManager,jdbcType=TINYINT},
      validate_price_type = #{validatePriceType,jdbcType=TINYINT},
      validate_customer = #{validateCustomer,jdbcType=TINYINT},
      validate_product = #{validateProduct,jdbcType=TINYINT},
      validate_ou = #{validateOu,jdbcType=TINYINT},
      validate_summary = #{validateSummary,jdbcType=TINYINT},
      validate_type = #{validateType,jdbcType=TINYINT},
      main_income_flag = #{mainIncomeFlag,jdbcType=TINYINT},
      help_income_flag = #{helpIncomeFlag,jdbcType=TINYINT},
      main_income_subject = #{mainIncomeSubject,jdbcType=VARCHAR},
      help_income_subject = #{helpIncomeSubject,jdbcType=VARCHAR},
      milepost_design_can_submit = #{milepostDesignCanSubmit,jdbcType=TINYINT},
      milestone_base_date = #{milestoneBaseDate,jdbcType=TINYINT},
      project_member_distinct = #{projectMemberDistinct,jdbcType=TINYINT},
      validate_objective_project = #{validateObjectiveProject,jdbcType=TINYINT},
      validate_project_level = #{validateProjectLevel,jdbcType=TINYINT},
      validate_resource_code = #{validateResourceCode,jdbcType=TINYINT},
      validate_project_member = #{validateProjectMember,jdbcType=TINYINT},
      budget_control_flag = #{budgetControlFlag,jdbcType=TINYINT},
      budget_control_ratio = #{budgetControlRatio,jdbcType=DECIMAL},
      validate_project_manual = #{validateProjectManual,jdbcType=TINYINT},
      requriement_deliver_mrp = #{requriementDeliverMrp,jdbcType=TINYINT},
      transfer_project = #{transferProject,jdbcType=TINYINT},
      transfer_project_type_id = #{transferProjectTypeId,jdbcType=BIGINT},
      project_profit_contribution_rate = #{projectProfitContributionRate,jdbcType=TINYINT},
      purchase_contract_type = #{purchaseContractType,jdbcType=VARCHAR},
      wbs_enabled = #{wbsEnabled,jdbcType=TINYINT},
      wbs_template_info_id = #{wbsTemplateInfoId,jdbcType=BIGINT},
      area = #{area,jdbcType=VARCHAR},
      business_department = #{businessDepartment,jdbcType=VARCHAR},
      detailed_address = #{detailedAddress,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      relate_asset = #{relateAsset,jdbcType=TINYINT},
      project_progress_predict_flag = #{projectProgressPredictFlag,jdbcType=TINYINT},
      design_plan_module_status_default = #{designPlanModuleStatusDefault,jdbcType=INTEGER},
      wbs_design_plan_auto_confirm = #{wbsDesignPlanAutoConfirm,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>