<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectWbsBudgetChangeHistoryExtMapper">

    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ProjectWbsBudgetChangeHistory">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="activity_order_no" jdbcType="VARCHAR" property="activityOrderNo" />
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
        <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
        <result column="activity_type" jdbcType="VARCHAR" property="activityType" />
        <result column="price" jdbcType="DECIMAL" property="price" />
        <result column="baseline_cost" jdbcType="DECIMAL" property="baselineCost" />
        <result column="demand_cost" jdbcType="DECIMAL" property="demandCost" />
        <result column="on_the_way_cost" jdbcType="DECIMAL" property="onTheWayCost" />
        <result column="incurred_cost" jdbcType="DECIMAL" property="incurredCost" />
        <result column="remaining_cost" jdbcType="DECIMAL" property="remainingCost" />
        <result column="change_accumulate_cost" jdbcType="DECIMAL" property="changeAccumulateCost" />
        <result column="parent_wbs_id" jdbcType="BIGINT" property="parentWbsId" />
        <result column="parent_activity_id" jdbcType="BIGINT" property="parentActivityId" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
        <result column="version" jdbcType="BIGINT" property="version" />
        <result column="wbs_full_code" jdbcType="VARCHAR" property="wbsFullCode" />
        <result column="wbs_last_code" jdbcType="VARCHAR" property="wbsLastCode" />
        <result column="dynamic_wbs_template_rule_ids" jdbcType="VARCHAR" property="dynamicWbsTemplateRuleIds" />
        <result column="dynamic_fields" jdbcType="VARCHAR" property="dynamicFields" />
        <result column="dynamic_values" jdbcType="VARCHAR" property="dynamicValues" />
        <result column="origin_id" jdbcType="BIGINT" property="originId" />
        <result column="header_id" jdbcType="BIGINT" property="headerId" />
        <result column="history_type" jdbcType="INTEGER" property="historyType" />
        <result column="show_case" jdbcType="TINYINT" property="showCase" />
        <result column="fee_type_id" jdbcType="BIGINT" property="feeTypeId" />
        <result column="fee_type_name" jdbcType="VARCHAR" property="feeTypeName" />
        <result column="fee_sync_ems" jdbcType="TINYINT" property="feeSyncEms" />
        <result column="project_wbs_receipts_id" jdbcType="BIGINT" property="projectWbsReceiptsId" />
    </resultMap>

    <sql id="Base_Column_List">
        t1.id,
        t1.project_id,
        t1.project_code,
        t1.description,
        t1.activity_order_no,
        t1.activity_code,
        t1.activity_name,
        t1.activity_type,
        t1.wbs_full_code,
        t1.wbs_last_code,
        t1.dynamic_wbs_template_rule_ids,
        t1.dynamic_fields,
        t1.dynamic_values,
        t1.price,
        t1.baseline_cost,
        t1.demand_cost,
        t1.on_the_way_cost,
        t1.incurred_cost,
        t1.remaining_cost,
        t1.change_accumulate_cost,
        t1.parent_wbs_id,
        t1.parent_activity_id,
        t1.create_by,
        t1.create_at,
        t1.update_by,
        t1.update_at,
        t1.deleted_flag,
        t1.version,
        t1.origin_id,
        t1.header_id,
        t1.history_type,
        t1.show_case,
        t1.fee_type_id,
        t1.fee_type_name,
        t1.fee_sync_ems,
        t1.project_wbs_receipts_id
    </sql>

    <!-- 查询预算汇总列表 -->
    <select id="listByParam" parameterType="java.util.Map" resultType="com.midea.pam.common.ctc.dto.ProjectWbsBudgetChangeHistoryDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
            pam_ctc.project_wbs_budget_change_history t1
        WHERE 1 = 1
        <if test="headerId != null and headerId != ''">
            AND t1.header_id = #{headerId}
        </if>
        <if test="projectId != null and projectId != ''">
            AND t1.project_id = #{projectId}
        </if>
        <if test="description != null and description != ''">
            AND t1.description LIKE concat('%', #{description, jdbcType=VARCHAR}, '%')
        </if>
        <if test="activityCode != null and activityCode != ''">
            AND t1.activity_code = #{activityCode}
        </if>
        <if test="activityType != null and activityType != ''">
            AND t1.activity_type = #{activityType}
        </if>
        <if test="activityName != null and activityName != ''">
            AND t1.activity_name LIKE concat('%', #{activityName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="minRemainingCost != null">
            AND t1.remaining_cost &gt;= #{minRemainingCost}
        </if>
        <if test="maxRemainingCost != null">
            AND t1.remaining_cost &lt;= #{maxRemainingCost}
        </if>
        <if test="idList != null and idList.size > 0">
            AND t1.id IN
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="parentWbsIdList != null and parentWbsIdList.size > 0">
            AND t1.parent_wbs_id IN
            <foreach collection="parentWbsIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="parentActivityIdList != null and parentActivityIdList.size > 0">
            AND t1.parent_activity_id IN
            <foreach collection="parentActivityIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dynamicFieldList != null and dynamicFieldList.size > 0">
            <foreach collection="dynamicFieldList" item="item" index="index">
                AND t1.id IN (
					SELECT project_wbs_budget_id
					FROM pam_ctc.project_wbs_budget_dynamic_change_history
					WHERE wbs_template_rule_detail_code = #{item.value}
					AND field_name = #{item.key}
					AND header_id = #{headerId}
                    <if test="projectId != null and projectId != ''">
                        AND project_id = #{projectId}
                    </if>
                )
            </foreach>
        </if>
        ORDER BY t1.id desc
    </select>

    <!-- 根据headerId删除记录 -->
    <delete id="deleteByHeaderId" parameterType="java.lang.Long">
        delete from project_wbs_budget_change_history
        where header_id = #{headerId,jdbcType=BIGINT}
    </delete>

    <!-- 查询总预算 -->
    <select id="sumBudgetCoust" resultType="decimal">
        select
            sum(price)
        from
            pam_ctc.project_wbs_budget_change_history h
        where h.deleted_flag = 0
            and h.project_id = #{projectId}
            and h.header_id = #{headerId}
            and h.history_type = #{historyType}
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsBudgetChangeHistory">
        insert into project_wbs_budget_change_history
            (id, project_id, project_code,
            description, activity_order_no, activity_code,
            activity_name, activity_type, price,
            baseline_cost, demand_cost, on_the_way_cost,
            incurred_cost, remaining_cost, change_accumulate_cost,
            parent_wbs_id, parent_activity_id, create_by,
            create_at, update_by, update_at,
            deleted_flag, version, wbs_full_code,
            wbs_summary_code, wbs_last_code, dynamic_wbs_template_rule_ids,
            dynamic_fields, dynamic_values, origin_id,
            header_id, history_type, show_case,
            fee_type_id, fee_type_name, fee_sync_ems, project_wbs_receipts_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.projectId,jdbcType=BIGINT}, #{item.projectCode,jdbcType=VARCHAR},
            #{item.description,jdbcType=VARCHAR}, #{item.activityOrderNo,jdbcType=VARCHAR}, #{item.activityCode,jdbcType=VARCHAR},
            #{item.activityName,jdbcType=VARCHAR}, #{item.activityType,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL},
            #{item.baselineCost,jdbcType=DECIMAL}, #{item.demandCost,jdbcType=DECIMAL}, #{item.onTheWayCost,jdbcType=DECIMAL},
            #{item.incurredCost,jdbcType=DECIMAL}, #{item.remainingCost,jdbcType=DECIMAL}, #{item.changeAccumulateCost,jdbcType=DECIMAL},
            #{item.parentWbsId,jdbcType=BIGINT}, #{item.parentActivityId,jdbcType=BIGINT}, #{item.createBy,jdbcType=BIGINT},
            #{item.createAt,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT}, #{item.updateAt,jdbcType=TIMESTAMP},
            #{item.deletedFlag,jdbcType=TINYINT}, #{item.version,jdbcType=BIGINT}, #{item.wbsFullCode,jdbcType=VARCHAR},
            #{item.wbsSummaryCode,jdbcType=VARCHAR}, #{item.wbsLastCode,jdbcType=VARCHAR}, #{item.dynamicWbsTemplateRuleIds,jdbcType=VARCHAR},
            #{item.dynamicFields,jdbcType=VARCHAR}, #{item.dynamicValues,jdbcType=VARCHAR}, #{item.originId,jdbcType=BIGINT},
            #{item.headerId,jdbcType=BIGINT}, #{item.historyType,jdbcType=INTEGER}, #{item.showCase,jdbcType=TINYINT},
            #{item.feeTypeId,jdbcType=BIGINT}, #{item.feeTypeName,jdbcType=VARCHAR}, #{item.feeSyncEms,jdbcType=TINYINT}, #{item.projectWbsReceiptsId,jdbcType=BIGINT})
        </foreach>
    </insert>
</mapper>