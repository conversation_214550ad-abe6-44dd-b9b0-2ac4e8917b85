<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PurchaseContractChangeHistoryExtMapper">

    <select id="findChangingContract" resultType="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory">
        select
        pcchi.id,
        pcchi.header_id,
        pcchi.origin_id,
        pcchi.history_type,
        pcchi.project_id,
        pcchi.code,
        pcchi.name,
        pcchi.ou_id,
        pcchi.type_id,
        pcchi.type_name,
        pcchi.legal_affairs_code,
        pcchi.vendor_id,
        pcchi.vendor_code,
        pcchi.vendor_name,
        pcchi.erp_vendor_site_id,
        pcchi.vendor_site_code,
        pcchi.amount,
        pcchi.excluding_tax_amount,
        pcchi.currency,
        pcchi.start_time,
        pcchi.end_time,
        pcchi.manager,
        pcchi.manager_name,
        pcchi.purchasing_follower,
        pcchi.purchasing_follower_name,
        pcchi.classes,
        pcchi.belong_area,
        pcchi.payment_method,
        pcchi.invoice_type,
        pcchi.is_electronic_contract,
        pcchi.other_name,
        pcchi.other_id,
        pcchi.other_phone,
        pcchi.other_email,
        pcchi.public_or_private,
        pcchi.seal_category,
        pcchi.seal_admin_account_ids,
        pcchi.if_upload_change_file,
        pcchi.annex,
        pcchi.remark,
        pcchi.status,
        pcchi.filing_date,
        pcchi.carryover_flag,
        pcchi.deleted_flag,
        pcchi.create_by,
        pcchi.create_at,
        pcchi.update_by,
        pcchi.update_at,
        pcchi.tax_id,
        pcchi.tax_rate,
        pcchi.legal_business_id,
        pcchi.legal_contract_num
        from
        purchase_contract_change_history pcchi
        inner join purchase_contract_change_header pcch on
        pcchi.header_id = pcch.id
        where pcch.status = 2
        and pcch.deleted_flag = 0
        and pcchi.deleted_flag = 0
        <if test="projectId != null">
            and pcchi.project_id = #{projectId,jdbcType=BIGINT}
        </if>
        <if test="countedContractIds != null and countedContractIds.size > 0">
            and pcchi.origin_id not in
            <foreach collection="countedContractIds" index="index" item="countedContractId" open="(" separator=","
                     close=")">
                #{countedContractId}
            </foreach>
        </if>
    </select>

    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory">
        insert into purchase_contract_change_history (id, header_id, origin_id,
        history_type, project_id, code,
        name, ou_id, type_id, type_name,
        legal_affairs_code, vendor_id, vendor_code,
        vendor_name, erp_vendor_site_id, vendor_site_code,
        amount, excluding_tax_amount, currency,
        start_time, end_time, manager,
        manager_name, purchasing_follower, purchasing_follower_name,
        classes, belong_area, payment_method,
        invoice_type, is_electronic_contract, other_name,
        other_id, other_phone, other_email,
        public_or_private, seal_category, seal_admin_account_ids,
        if_upload_change_file, annex, remark,
        status, filing_date, carryover_flag,
        is_synchronize_legal_system_flag, notsync_type, notsync_reason,
        deleted_flag, create_by, create_at,
        update_by, update_at, tax_id,
        tax_rate, legal_business_id, legal_contract_num,
        execute_contract_percent_total, original_contract_annex, contract_terms)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.headerId,jdbcType=BIGINT}, #{item.originId,jdbcType=BIGINT},
            #{item.historyType,jdbcType=INTEGER}, #{item.projectId,jdbcType=BIGINT}, #{item.code,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR}, #{item.ouId,jdbcType=BIGINT}, #{item.typeId,jdbcType=BIGINT}, #{item.typeName,jdbcType=VARCHAR},
            #{item.legalAffairsCode,jdbcType=VARCHAR}, #{item.vendorId,jdbcType=BIGINT}, #{item.vendorCode,jdbcType=VARCHAR},
            #{item.vendorName,jdbcType=VARCHAR}, #{item.erpVendorSiteId,jdbcType=VARCHAR}, #{item.vendorSiteCode,jdbcType=VARCHAR},
            #{item.amount,jdbcType=DECIMAL}, #{item.excludingTaxAmount,jdbcType=DECIMAL}, #{item.currency,jdbcType=VARCHAR},
            #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP}, #{item.manager,jdbcType=BIGINT},
            #{item.managerName,jdbcType=VARCHAR}, #{item.purchasingFollower,jdbcType=BIGINT}, #{item.purchasingFollowerName,jdbcType=VARCHAR},
            #{item.classes,jdbcType=TINYINT}, #{item.belongArea,jdbcType=TINYINT}, #{item.paymentMethod,jdbcType=VARCHAR},
            #{item.invoiceType,jdbcType=TINYINT}, #{item.isElectronicContract,jdbcType=TINYINT}, #{item.otherName,jdbcType=VARCHAR},
            #{item.otherId,jdbcType=VARCHAR}, #{item.otherPhone,jdbcType=VARCHAR}, #{item.otherEmail,jdbcType=VARCHAR},
            #{item.publicOrPrivate,jdbcType=TINYINT}, #{item.sealCategory,jdbcType=TINYINT}, #{item.sealAdminAccountIds,jdbcType=VARCHAR},
            #{item.ifUploadChangeFile,jdbcType=VARCHAR}, #{item.annex,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
            #{item.status,jdbcType=INTEGER}, #{item.filingDate,jdbcType=TIMESTAMP}, #{item.carryoverFlag,jdbcType=TINYINT},
            #{item.isSynchronizeLegalSystemFlag,jdbcType=TINYINT}, #{item.notsyncType,jdbcType=VARCHAR}, #{item.notsyncReason,jdbcType=VARCHAR},
            #{item.deletedFlag,jdbcType=TINYINT}, #{item.createBy,jdbcType=BIGINT}, #{item.createAt,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=BIGINT}, #{item.updateAt,jdbcType=TIMESTAMP}, #{item.taxId,jdbcType=BIGINT},
            #{item.taxRate,jdbcType=VARCHAR}, #{item.legalBusinessId,jdbcType=VARCHAR}, #{item.legalContractNum,jdbcType=VARCHAR},
            #{item.executeContractPercentTotal,jdbcType=DECIMAL}, #{item.originalContractAnnex,jdbcType=LONGVARCHAR},
            #{item.contractTerms,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>

</mapper>