<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PurchaseContractBudgetExtMapper">

    <sql id="Budget_Column_List">
        budget.id,
        budget.purchase_contract_id as purchaseContractId,
        budget.purchase_requirement_id as purchaseRequirementId,
        budget.material_id as materialId,
        budget.wbs_summary_code as wbsSummaryCode,
        budget.activity_code as activityCode,
        budget.price,
        budget.total_price as totalPrice,
        budget.local_total_price as localTotalPrice,
        budget.number,
        budget.budget_execute_amount_total as budgetExecuteAmountTotal,
        budget.local_budget_execute_amount_total as localBudgetExecuteAmountTotal,
        budget.budget_execute_percent_total as budgetExecutePercentTotal,
        budget.row,
        budget.publish_time as publishTime,
        budget.purchase_contract_create_at as purchaseContractCreateAt,
        budget.contract_approval_time as contractApprovalTime,
        budget.contract_change_header_id as contractChangeHeaderId,
        budget.create_by as createBy,
        budget.create_at as createAt,
        budget.update_by as updateBy,
        budget.update_at as updateAt,
        budget.deleted_flag as deletedFlag,
        budget.version,
    </sql>

    <!-- 获取采购合同wbs关联预算 -->
    <select id="getWbsContractBudgetByParam" resultType="com.midea.pam.common.ctc.dto.PurchaseContractBudgetDto">
        select
            <include refid="Budget_Column_List"/>
            <!-- 物料描述 -->
            requirement.materiel_descr as materielDescr,
            <!-- 需求类型 -->
            requirement.requirement_type as requirementType,
            <!-- 物料中类 -->
            material.coding_middleclass as codingMiddleclass,
            <!-- 物料小类 -->
            material.material_type as materialType,
            <!-- 图号型号 -->
            material.model,
            <!-- 品牌 -->
            material.brand,
            <!-- 图纸版本号 -->
            material.chart_version as chartVersion,
            <!-- 采购合同号 -->
            pc.code as contractCode,
            <!-- 采购合同状态 -->
            pc.status as contractStatus,
            <!-- 合同不含税金额 -->
            pc.excluding_tax_amount as excludingTaxAmount,
            <!-- 详细设计单据id -->
            receipts.id as receiptsId,
            <!-- 页面类型：0-通用页面，1-新页面 -->
            receipts.web_type as webType,
            <!-- 详细设计单据编号 -->
            receipts.requirement_code as requirementCode,
            <!-- wbsSummaryCode wbs编码（拼接） -->
            <!-- wbs需求预算占用（外包） -->
            <!-- @wbsDemandOsCost :=(select sum(pwrb.budget_occupied_amount) from pam_ctc.project_wbs_receipts_budget pwrb where pwrb.deleted_flag = 0 and pwrb.demand_type = 2 and pwrb.wbs_summary_code = budget.wbs_summary_code and pwrb.id = requirement.project_wbs_receipts_id) as wbsDemandOsCost, -->
            requirement.wbs_demand_os_cost as wbsDemandOsCost,
            <!-- 累计采购合同占用金额 （合同或合同预算变更审批中，取最大），取本位币 -->
            <!-- @contractTotalAmount :=(select sum(pcb.total_price) from pam_ctc.purchase_contract_budget pcb where pcb.deleted_flag = 0 and pcb.purchase_requirement_id = requirement.id) as contractTotalAmount, -->
            @contractTotalAmount :=(
                select
                    CASE WHEN changes.local_total_price is null THEN coalesce(sum(coalesce(pcb.local_total_price, 0)), 0)
                    ELSE coalesce(sum(GREATEST(coalesce(pcb.local_total_price, 0), coalesce(changes.local_total_price, 0))), 0) END
                from pam_ctc.purchase_contract_budget pcb
                left join (
                    select
                        budget_change.origin_id,
                        budget_change.total_price,
                        budget_change.local_total_price,
                        budget_change.number
                    from
                        pam_ctc.purchase_contract_change_header header,
                        pam_ctc.purchase_contract_budget_change_history budget_change
                    where header.id = budget_change .header_id
                    and header.status = 2
                    and budget_change.history_type = 1 ) as changes
                on pcb.id = changes.origin_id
                left join pam_ctc.purchase_contract contract on pcb.purchase_contract_id = contract.id
                left join pam_ctc.purchase_material_requirement pmr on pcb.purchase_requirement_id = pmr.id
                where pcb.deleted_flag = 0
                <!-- 草稿/驳回/作废的采购合同累计采购合同占用金额释放 -->
                and contract.status not in (1,3,9)
                and pmr.project_wbs_receipts_id = receipts.id
                and pcb.wbs_summary_code = requirement.wbs_summary_code) as contractTotalAmount,
            <!-- 剩余WBS需求预算占用（外包），= WBS需求预算占用（外包）- 累计采购合同占用金额 - 关闭金额 -->
            (requirement.wbs_demand_os_cost - @contractTotalAmount - requirement.closed_amount) as wbsRemainingDemandOsCost,
            <!-- 总需求量 -->
            requirement.need_total as needTotal,
            <!-- 已签订采购合同数量 （合同或合同预算变更审批中，取最大） -->
            <!-- @releasedQuantity :=(select sum(pcb.number) from pam_ctc.purchase_contract_budget pcb where pcb.deleted_flag = 0 and pcb.purchase_requirement_id = requirement.id) as releasedQuantity, -->
			@releasedQuantity :=(
                select
                    ifnull(sum(GREATEST(ifnull(pcb.number, 0), ifnull(changes.number, 0))), 0)
                from pam_ctc.purchase_contract_budget pcb
                left join (
                    select
                        budget_change.origin_id,
                        budget_change.total_price,
                        budget_change.number
                    from
                        pam_ctc.purchase_contract_change_header header,
                        pam_ctc.purchase_contract_budget_change_history budget_change
                    where header.id = budget_change .header_id
                    and header .status = 2
                    and budget_change.history_type = 1 ) as changes
                on pcb.id = changes.origin_id
                left join pam_ctc.purchase_contract contract on pcb.purchase_contract_id = contract.id
                where pcb.deleted_flag = 0
                <!-- 草稿/驳回/作废的采购合同已采购量释放 -->
                and contract.status <![CDATA[ <> ]]> 9
                and contract.deleted_flag = 0
                and pcb.purchase_requirement_id = requirement.id) as releasedQuantity,
            <!-- 未签订采购合同数量 = 总需求量 - 已签订采购合同数量 -->
            (requirement.need_total - ifnull(requirement.closed_quantity, 0) - @releasedQuantity) as unreleasedAmount,
            <!-- 单位 -->
            requirement.unit,
            <!-- 需求预算 -->
            (select sum(pwrb.budget_occupied_amount) from pam_ctc.project_wbs_receipts_budget pwrb where pwrb.deleted_flag = 0 and pwrb.project_wbs_receipts_id = receipts.id and pwrb.demand_type = 0) as demandCost,
            <!-- 需求预算(WBS维度) -->
            if(receipts.web_type = 1
            , (select COALESCE(sum(pwrb.budget_occupied_amount), 0) from pam_ctc.project_wbs_receipts_budget pwrb where pwrb.deleted_flag = 0 and pwrb.project_wbs_receipts_id = receipts.id and pwrb.demand_type = 0 and pwrb.wbs_summary_code = requirement.wbs_summary_code)
            , 0) as wbsDemandTotalCost,
            <!-- activityCode 活动事项编码 -->
            <!-- PAM物料编码 -->
            requirement.pam_code as pamCode
        from pam_ctc.purchase_contract_budget budget
        inner join pam_ctc.purchase_material_requirement requirement
            on requirement.id = budget.purchase_requirement_id
            and requirement.deleted_flag = 0
        left join pam_ctc.purchase_contract pc
            on budget.purchase_contract_id = pc.id
        left join pam_ctc.project project
            on project.id = requirement.project_id
            and project.deleted_flag = 0
        left join pam_basedata.material material
            on material.id = requirement.materiel_id
        left join pam_ctc.project_wbs_receipts receipts
            on receipts.id = requirement.project_wbs_receipts_id
            and receipts.deleted_flag = 0
        where budget.deleted_flag = 0

        <if test="purchaseContractId != null">
            and budget.purchase_contract_id = #{purchaseContractId}
        </if>
        <if test="purchaseType != null and purchaseType !=''">
            and requirement.purchase_type = #{purchaseType}
        </if>
        <if test="projectId != null">
            and requirement.project_id = #{projectId}
        </if>
        <if test="wbsSummaryCode != null and wbsSummaryCode !=''">
            and budget.wbs_summary_code = #{wbsSummaryCode}
        </if>
        <if test="projectWbsReceiptsId != null">
            and requirement.project_wbs_receipts_id = #{projectWbsReceiptsId}
        </if>
        <if test="projectWbsReceiptsIdList != null and projectWbsReceiptsIdList.size() > 0">
            and requirement.project_wbs_receipts_id in
            <foreach collection="projectWbsReceiptsIdList" item="projectWbsReceiptsId" index="index" open="(" separator="," close=")">
                #{projectWbsReceiptsId}
            </foreach>
        </if>
    </select>

     <!-- 获取采购合同wbs关联预算 -->
    <select id="countReleasedQuantity" resultType="com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto">
        select
            requirement.id,
            <!-- 总需求量 -->
            requirement.need_total as needTotal,
            <!-- 已签订采购合同数量 （合同或合同预算变更审批中，取最大） -->
            <!-- @releasedQuantity :=(select sum(pcb.number) from pam_ctc.purchase_contract_budget pcb where pcb.deleted_flag = 0 and pcb.purchase_requirement_id = requirement.id) as releasedQuantity, -->
			@releasedQuantity :=(
                select
                    ifnull(sum(GREATEST(ifnull(pcb.number, 0), ifnull(changes.number, 0))), 0)
                from pam_ctc.purchase_contract_budget pcb
                left join (
                    select
                        budget_change.origin_id,
                        budget_change.total_price,
                        budget_change.number
                    from
                        pam_ctc.purchase_contract_change_header header,
                        pam_ctc.purchase_contract_budget_change_history budget_change
                    where header.id = budget_change .header_id
                    and header .status = 2
                    and budget_change.history_type = 1 ) as changes
                on pcb.id = changes.origin_id
                left join pam_ctc.purchase_contract contract on pcb.purchase_contract_id = contract.id
                where pcb.deleted_flag = 0
                <!-- 草稿/驳回/作废的采购合同已采购量释放 -->
                and contract.status <![CDATA[ <> ]]> 9
                and contract.deleted_flag = 0
                <if test="purchaseContractId != null">
                    and pcb.purchase_contract_id <![CDATA[ <> ]]> #{purchaseContractId}
                </if>
                and pcb.purchase_requirement_id = requirement.id) as releasedQuantity,
            <!-- 未签订采购合同数量 = 总需求量 - 已签订采购合同数量 -->
            (requirement.need_total - ifnull(requirement.closed_quantity, 0) - @releasedQuantity) as unreleasedAmount,
            requirement.pam_code as pamCode
        from pam_ctc.purchase_material_requirement requirement
        where requirement.deleted_flag = 0
            <if test="requirementIdList != null and requirementIdList.size() > 0">
                and requirement.id in
                <foreach collection="requirementIdList" item="requirementId" index="index" open="(" separator="," close=")">
                    #{requirementId}
                </foreach>
            </if>
    </select>

    <select id="getWbsContractBudgetFromHistory" resultType="com.midea.pam.common.ctc.vo.PurchaseContractBudgetPdfVo">
        select
            material.pam_code as pamCode,
            CONCAT(IFNULL(material.item_info,""), "/", IFNULL(material.brand,""), "/", IFNULL(material.model,"")) as baseInfo,
            requirement.unit,
            requirement.design_release_lot_number as designReleaseLotNumber,
            receipts.project_code as projectCode,
            budget.row,
            budget.number,
            budget.price,
            budget.total_price as totalPrice,
            contract.delivery_date as promisedDate
        from
            pam_ctc.purchase_contract_budget_change_history as budget
        left join pam_basedata.material as material on
            budget.material_id = material.id
        left join pam_ctc.purchase_material_requirement as requirement on
            budget.purchase_requirement_id = requirement.id
        left join pam_ctc.project_wbs_receipts as receipts on
            requirement.project_wbs_receipts_id = receipts.id
        left join pam_ctc.purchase_contract_change_history as contract on
            budget.header_id = contract.header_id and contract.history_type = 1
        where
            budget.header_id = #{headId} and budget.deleted_flag = 0 and budget.history_type = 1
    </select>

    <select id="calculateReleasedQuantity" resultType="java.math.BigDecimal">
        select
            CASE WHEN changes.number is null THEN coalesce(sum(coalesce(pcb.number, 0)), 0)
            ELSE coalesce(sum(GREATEST(coalesce(pcb.number, 0), coalesce(changes.number, 0))), 0) END
        from pam_ctc.purchase_contract_budget pcb
        left join (
            select
                budget_change.origin_id,
                budget_change.total_price,
                budget_change.number
            from
                pam_ctc.purchase_contract_change_header header,
                pam_ctc.purchase_contract_budget_change_history budget_change
            where header.id = budget_change .header_id
            and header.status = 2
            and budget_change.history_type = 1 ) as changes
        on pcb.id = changes.origin_id
        left join pam_ctc.purchase_contract contract on pcb.purchase_contract_id = contract.id
        where pcb.deleted_flag = 0
        <!-- 草稿/驳回/作废的采购合同已采购量释放 -->
        and contract.status <![CDATA[ <> ]]> 9
        and contract.deleted_flag = 0
        and pcb.purchase_requirement_id = #{purchaseRequirementId}
    </select>

    <select id="selectHroRequirementBudget"
            resultType="com.midea.pam.common.ctc.dto.PurchaseContractBudgetDto">
        select
            hri.role_name roleName,
            hri.start_date startDate,
            hri.end_date endDate,
            pmr.requirement_code requirementCode,
            pmr.wbs_summary_code wbsSummaryCode,
            pmr.wbs_demand_os_cost wbsDemandOsCost,
            pmr.need_total needTotal,
            pmr.unit,
            pmr.activity_code activityCode,
            pmr.pam_code pamCode,
            pmr.materiel_id materialId,
            pmr.materiel_descr materielDescr,
            pcb.id,
            pcb.row,
            pcb.purchase_requirement_id purchaseRequirementId,
            pcb.`number`,
            pcb.price,
            pcb.total_price totalPrice,
            pcb.local_total_price localTotalPrice,
            pcb.deleted_flag
        from pam_ctc.purchase_contract_budget pcb
        inner join pam_ctc.purchase_material_requirement pmr on pcb.purchase_requirement_id = pmr.id
        inner join pam_ctc.hro_requirement_item hri on hri.id = pmr.project_wbs_receipts_id
        where pcb.purchase_contract_id = #{purchaseContractId}
        and pcb.deleted_flag  = 0
    </select>

    <select id="selectRequirementBudgetCostInfo"
            resultType="com.midea.pam.common.ctc.dto.PurchaseContractBudgetDto">
        select
        pcb.purchase_requirement_id purchaseRequirementId,
        pc.status contractStatus,
        GREATEST(pcb.local_total_price,ifnull(t.local_total_price,0)) localTotalPrice,
        GREATEST(pcb.`number`,ifnull(t.`number`,0)) `number`
        from pam_ctc.purchase_contract_budget pcb
        inner join pam_ctc.purchase_contract pc
        on pc.id = pcb.purchase_contract_id
        left join (
            select bc.origin_id,bc.local_total_price,bc.`number`
            from pam_ctc.purchase_contract_budget_change_history bc
            inner join pam_ctc.purchase_contract_change_header h on h.id = bc.header_id
            where bc.history_type = 1
            and bc.deleted_flag = 0
            and h.change_type = 2
            and h.status = 2
        ) t on t.origin_id = pcb.id
        where pcb.purchase_requirement_id in
        <foreach collection="purchaseRequirementIds" item="requirementId" open="(" separator="," close=")">
            #{requirementId}
        </foreach>
        and pc.status in (1,2,3,5,10)
        and pcb.deleted_flag = 0
    </select>

    <select id="selectRequirementBudget" resultType="com.midea.pam.common.ctc.dto.HroRequirementDto">
        select
        hr.requirement_code requirementCode,
        sum(hri.total_budget) totalBudget
        from pam_ctc.hro_requirement hr
        inner join pam_ctc.hro_requirement_item hri on hri.requirement_id = hr.id and hri.deleted_flag = 0
        where hr.requirement_code in
        <foreach collection="requirementCodes" item="requirementCode" open="(" separator="," close=")">
            #{requirementCode}
        </foreach>
        group by hr.requirement_code
    </select>

    <select id="getHroPurchaseBudgetInfo" resultType="com.midea.pam.common.ctc.vo.PurchaseContractBudgetPdfVo">
        select
            req.pam_code as pamCode,
            req.materiel_descr as baseInfo,
            req.unit,
            req.design_release_lot_number as designReleaseLotNumber,
            p.code as projectCode,
            bh.`row`,
            bh.`number`,
            bh.price,
            bh.total_price as totalPrice,
            ch.delivery_date as promisedDate
        from pam_ctc.purchase_contract_budget_change_history as bh
        inner join pam_ctc.purchase_material_requirement as req
            on bh.purchase_requirement_id = req.id
        inner join pam_ctc.project as p
            on p.id = req.project_id
        inner join pam_ctc.purchase_contract_change_history as ch
            on bh.header_id = ch.header_id and ch.history_type = 1
        where bh.header_id = #{headId}
        and bh.deleted_flag = 0
        and bh.history_type = 1
    </select>

    <select id="selectPurchaseContractBudgetInfoByContractIds"
            resultType="com.midea.pam.common.ctc.dto.PurchaseContractBudgetDto">
        select
            purchase_contract_id purchaseContractId,
            material_id materialId,
            sum(`number`) releasedQuantity
        from pam_ctc.purchase_contract_budget
        where purchase_contract_id in
            <foreach collection="contractIds" item="contractId" open="(" separator="," close=")">
                #{contractId}
            </foreach>
        and deleted_flag = 0
        group by purchase_contract_id,material_id
    </select>

    <select id="statBudgetByRole" resultType="com.midea.pam.common.ctc.dto.PurchaseContractBudgetDto">
        select
            hr.role_name roleName,
            sum(budget.`number`) releasedQuantity
        from pam_ctc.purchase_contract_budget budget
        inner join pam_basedata.hro_role hr on hr.id = budget.material_id
        where budget.purchase_contract_id = #{contractId}
        and budget.deleted_flag = 0
        group by hr.role_name
    </select>

    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractBudget">
        insert into purchase_contract_budget (id, purchase_contract_id, purchase_requirement_id,
            material_id, wbs_summary_code, activity_code,
            price, total_price, local_total_price,
            number, budget_execute_amount_total, local_budget_execute_amount_total,
            budget_execute_percent_total, row, publish_time,
            purchase_contract_create_at, contract_approval_time,
            contract_change_header_id, create_by, create_at,
            update_by, update_at, deleted_flag,
            version)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.purchaseContractId,jdbcType=BIGINT}, #{item.purchaseRequirementId,jdbcType=BIGINT},
            #{item.materialId,jdbcType=BIGINT}, #{item.wbsSummaryCode,jdbcType=VARCHAR}, #{item.activityCode,jdbcType=VARCHAR},
            #{item.price,jdbcType=DECIMAL}, #{item.totalPrice,jdbcType=DECIMAL}, #{item.localTotalPrice,jdbcType=DECIMAL},
            #{item.number,jdbcType=DECIMAL}, #{item.budgetExecuteAmountTotal,jdbcType=DECIMAL}, #{item.localBudgetExecuteAmountTotal,jdbcType=DECIMAL},
            #{item.budgetExecutePercentTotal,jdbcType=DECIMAL}, #{item.row,jdbcType=INTEGER}, #{item.publishTime,jdbcType=TIMESTAMP},
            #{item.purchaseContractCreateAt,jdbcType=TIMESTAMP}, #{item.contractApprovalTime,jdbcType=TIMESTAMP},
            #{item.contractChangeHeaderId,jdbcType=BIGINT}, #{item.createBy,jdbcType=BIGINT}, #{item.createAt,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=BIGINT}, #{item.updateAt,jdbcType=TIMESTAMP}, #{item.deletedFlag,jdbcType=TINYINT},
            #{item.version,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="batchUpdate"  parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update purchase_contract_budget
            <set>
                <if test="item.purchaseContractId != null">
                    purchase_contract_id = #{item.purchaseContractId,jdbcType=BIGINT},
                </if>
                <if test="item.purchaseRequirementId != null">
                    purchase_requirement_id = #{item.purchaseRequirementId,jdbcType=BIGINT},
                </if>
                <if test="item.materialId != null">
                    material_id = #{item.materialId,jdbcType=BIGINT},
                </if>
                <if test="item.wbsSummaryCode != null">
                    wbs_summary_code = #{item.wbsSummaryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.activityCode != null">
                    activity_code = #{item.activityCode,jdbcType=VARCHAR},
                </if>
                <if test="item.price != null">
                    price = #{item.price,jdbcType=DECIMAL},
                </if>
                <if test="item.totalPrice != null">
                    total_price = #{item.totalPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.localTotalPrice != null">
                    local_total_price = #{item.localTotalPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.number != null">
                    number = #{item.number,jdbcType=DECIMAL},
                </if>
                <if test="item.budgetExecuteAmountTotal != null">
                    budget_execute_amount_total = #{item.budgetExecuteAmountTotal,jdbcType=DECIMAL},
                </if>
                <if test="item.localBudgetExecuteAmountTotal != null">
                    local_budget_execute_amount_total = #{item.localBudgetExecuteAmountTotal,jdbcType=DECIMAL},
                </if>
                <if test="item.budgetExecutePercentTotal != null">
                    budget_execute_percent_total = #{item.budgetExecutePercentTotal,jdbcType=DECIMAL},
                </if>
                <if test="item.row != null">
                    row = #{item.row,jdbcType=INTEGER},
                </if>
                <if test="item.publishTime != null">
                    publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.purchaseContractCreateAt != null">
                    purchase_contract_create_at = #{item.purchaseContractCreateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.contractApprovalTime != null">
                    contract_approval_time = #{item.contractApprovalTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.contractChangeHeaderId != null">
                    contract_change_header_id = #{item.contractChangeHeaderId,jdbcType=BIGINT},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy,jdbcType=BIGINT},
                </if>
                <if test="item.createAt != null">
                    create_at = #{item.createAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy,jdbcType=BIGINT},
                </if>
                <if test="item.updateAt != null">
                    update_at = #{item.updateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.deletedFlag != null">
                    deleted_flag = #{item.deletedFlag,jdbcType=TINYINT},
                </if>
                <if test="item.version != null">
                    version = #{item.version,jdbcType=BIGINT},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="deleteByPurchaseContractId" parameterType="java.lang.Long">
        update purchase_contract_budget
        set deleted_flag = 1
        where purchase_contract_id = #{purchaseContractId,jdbcType=BIGINT}
    </update>

</mapper>