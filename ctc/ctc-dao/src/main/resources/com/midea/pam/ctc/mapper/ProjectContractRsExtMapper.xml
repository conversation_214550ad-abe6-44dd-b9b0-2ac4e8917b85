<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectContractRsExtMapper">
  <resultMap id="ProjectContractResultMap" type="com.midea.pam.common.ctc.dto.ProjectContractRsDto">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="contract_id" jdbcType="BIGINT" property="contractId" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="salesManager" jdbcType="BIGINT" property="salesManager" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="create_at" jdbcType="DATE" property="createAt" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_at" jdbcType="DATE" property="updateAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
  </resultMap>

    <!-- 查询合同客户 -->
  <select id="selectProjectCustomer" parameterType="java.lang.Long" resultMap="ProjectContractResultMap">
    select
      rs.contract_id,
      c.name as contract_name,
      c.customer_id,
      c.customer_name
    from
      project_contract_rs rs
    inner join contract c
      on c.id = rs.contract_id
    where rs.deleted_flag = 0
      and c.deleted_flag = 0
      and rs.project_id = #{projectId,jdbcType=BIGINT}
    group by c.customer_id
  </select>

  <!-- 查询项目关联合同 -->
  <select id="getContractRsByProjectId" resultType="com.midea.pam.common.ctc.dto.ProjectContractRsDto">
    select
      t1.project_id as projectId,
      t1.contract_id as contractId,
      t2.status,
      t2.code as contractCode,
      t2.name as contractName,
      t2.customer_id as customerId,
      t2.sales_manager as salesManager
    from project_contract_rs t1, contract t2
    where t1.contract_id = t2.id
      and t1.deleted_flag = 0
      and t2.deleted_flag = 0
      and t1.project_id = #{projectId,jdbcType=BIGINT}
  </select>

  <select id="getProjectContractRsByContractId" resultMap="ProjectContractResultMap">
    select
      pcr.*,
      c.code as contract_code,
      c.name as contract_name,
      p.code as project_code,
      p.name as project_name
    from
      (
        select
          id,
          project_id,
          contract_id,
          status,
          deleted_flag,
          create_at,
          create_by,
          update_at,
          update_by
        from
          pam_ctc.project_contract_rs
        where
          deleted_flag = 0
        union
        select
          pcrch.id,
          pcrch.project_id,
          pcrch.contract_id,
          pcrch.status,
          pcrch.deleted_flag,
          pcrch.create_at,
          pcrch.create_by,
          pcrch.update_at,
          pcrch.update_by
        from
          pam_ctc.project_history_header phh
          inner join pam_ctc.project_contract_rs_change_history pcrch on
          phh.id = pcrch.header_id
          and pcrch.deleted_flag = 0
        where
          phh.change_type in (6, 10)
          and pcrch.history_type = 1
          and phh.deleted_flag = 0
          and phh.status = 3) pcr
      inner join pam_ctc.contract c on
      c.id = pcr.contract_id
      inner join pam_ctc.project p on
      p.id = pcr.project_id
    where
      pcr.contract_id = #{contractId,jdbcType=BIGINT}
      and pcr.deleted_flag = 0
  </select>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" item="item" open=" " separator=" " close="">
      update project_contract_rs
      <set>
        <if test="item.projectId != null">
          project_id = #{item.projectId,jdbcType=BIGINT},
        </if>
        <if test="item.contractId != null">
          contract_id = #{item.contractId,jdbcType=BIGINT},
        </if>
        <if test="item.status != null">
          status = #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.deletedFlag != null">
          deleted_flag = #{item.deletedFlag,jdbcType=TINYINT},
        </if>
        <if test="item.createAt != null">
          create_at = #{item.createAt,jdbcType=DATE},
        </if>
        <if test="item.createBy != null">
          create_by = #{item.createBy,jdbcType=BIGINT},
        </if>
        <if test="item.updateAt != null">
          update_at = #{item.updateAt,jdbcType=DATE},
        </if>
        <if test="item.updateBy != null">
          update_by = #{item.updateBy,jdbcType=BIGINT},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT};
    </foreach>
  </update>

</mapper>