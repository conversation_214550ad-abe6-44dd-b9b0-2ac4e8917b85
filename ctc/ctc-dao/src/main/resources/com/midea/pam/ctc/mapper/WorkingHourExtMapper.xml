<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.WorkingHourExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.dto.WorkingHourResultDto">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="week" jdbcType="VARCHAR" property="week" />
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate" />
        <result column="apply_working_hours" jdbcType="DECIMAL" property="applyWorkingHours" />
        <result column="ihr_attend_hours" jdbcType="DECIMAL" property="ihrAttendHours" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="user_mip" jdbcType="VARCHAR" property="userMip" />
        <result column="org_id" jdbcType="BIGINT" property="orgId" />
        <result column="org_name" jdbcType="VARCHAR" property="orgName" />
        <result column="apply_org" jdbcType="VARCHAR" property="applyOrg" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="process_id" jdbcType="BIGINT" property="processId" />
        <result column="invoice_apply_header_id" jdbcType="BIGINT" property="invoiceApplyHeaderId" />
        <result column="level" jdbcType="VARCHAR" property="level" />
        <result column="labor_cost_type" jdbcType="VARCHAR" property="laborCostType" />
        <result column="cost_money" jdbcType="DECIMAL" property="costMoney" />
        <result column="actual_cost_money" jdbcType="DECIMAL" property="actualCostMoney" />
        <result column="actual_working_hours" jdbcType="DECIMAL" property="actualWorkingHours" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
        <result column="create_by" jdbcType="BIGINT" property="createBy" />
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
        <result column="update_by" jdbcType="BIGINT" property="updateBy" />
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
        <result column="approve_user_id" jdbcType="BIGINT" property="approveUserId" />
        <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime" />
        <result column="approve_user_name" jdbcType="VARCHAR" property="approveUserName" />
        <result column="user_type" jdbcType="VARCHAR" property="userType" />
        <result column="biz_unit_id" jdbcType="BIGINT" property="bizUnitId" />
        <result column="labor_cost_source_unit_id" jdbcType="BIGINT" property="laborCostSourceUnitId" />
        <result column="is_import" jdbcType="TINYINT" property="isImport" />
        <result column="rdm_flag" jdbcType="TINYINT" property="rdmFlag" />
        <result column="source_flag" jdbcType="TINYINT" property="sourceFlag" />
        <result column="write_off_status" jdbcType="TINYINT" property="writeOffStatus" />
        <result column="stay_approve_user_id" jdbcType="BIGINT" property="stayApproveUserId" />
        <result column="stay_approve_user_mip" jdbcType="VARCHAR" property="stayApproveUserMip" />
        <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode" />
        <result column="role_name" jdbcType="VARCHAR" property="roleName" />
        <result column="wbs_budget_code" jdbcType="VARCHAR" property="wbsBudgetCode" />
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode" />
        <result column="labor_wbs_cost_id" jdbcType="BIGINT" property="laborWbsCostId" />
        <result column="labor_wbs_cost_name" jdbcType="VARCHAR" property="laborWbsCostName" />
        <result column="project_activity_code" jdbcType="VARCHAR" property="projectActivityCode" />
        <result column="price_type" jdbcType="VARCHAR" property="priceType" />
        <result column="total_apply_working_hours" jdbcType="TINYINT" property="totalApplyWorkingHours" />
        <result column="total_confirm_working_hours" jdbcType="TINYINT" property="totalConfirmWorkingHours" />
  </resultMap>

  <update id="batchUpdateFromRdm">
    update
        working_hour wh
    inner join (
        select
           t.* ,
           if(t.total_apply_hours = 0, if(t.max_hour > ifnull(t.report_hour,0), t.report_hour, t.max_hour),
               if(t.max_hour >= (ifnull(t.report_hour,0)+t.total_apply_hours), t.report_hour, t.old_apply_hours)) as apply_working_hours,
           if(t.total_actual_hours = 0, if(t.max_hour > ifnull(t.confirm_hour,0), t.confirm_hour, t.max_hour),
               if(t.max_hour >= (ifnull(t.confirm_hour,0)+t.total_actual_hours), t.confirm_hour, t.old_actual_hours)) as actual_working_hours
        from (select
                rwh.*,
                wh.apply_working_hours as old_apply_hours,
                wh.actual_working_hours as old_actual_hours,
                ifnull(ocd.value,8) as max_hour,
                (select
                    ifnull(sum(if(status = 5, greatest(apply_working_hours,actual_working_hours), apply_working_hours)),0)
                from
                    working_hour
                where
                    user_mip = wh.user_mip
                    and apply_date = wh.apply_date
                    and rdm_flag = 0
                    and delete_flag = 0 and status in (2,4,5)) as total_apply_hours,
                    (select
                        ifnull(sum(if(status = 5, greatest(apply_working_hours,actual_working_hours), actual_working_hours)),0)
                    from
                        working_hour
                    where
                        user_mip = wh.user_mip
                        and apply_date = wh.apply_date
                        and rdm_flag = 0
                        and delete_flag = 0 and status in (2,4,5)) as total_actual_hours
                from
                    working_hour wh
                inner join rdm_working_hour rwh on
                    wh.user_mip = rwh.mipname
                    and wh.apply_date = rwh.att_date
                left join organization_custom_dict ocd on
                    wh.biz_unit_id = ocd.org_id
                    and ocd.name = '工时填报小时数上限'
                where
                    wh.rdm_flag = 1
                    and wh.delete_flag = 0) t) tt
        on
            wh.user_mip = tt.mipname
        and wh.apply_date = tt.att_date
    set
        <![CDATA[
        wh.apply_working_hours = tt.apply_working_hours,
        wh.actual_working_hours = tt.actual_working_hours,
        wh.create_at = tt.report_date,
        wh.status = if(tt.confirm_hour >= 0, 4, 2),
        wh.approve_time = tt.confirm_date,
        wh.delete_flag = if(((tt.max_hour < (ifnull(tt.confirm_hour,0)+tt.total_actual_hours) and tt.total_actual_hours > 0) or (tt.max_hour < (ifnull(tt.report_hour,0)+tt.total_apply_hours) and tt.total_apply_hours > 0)), 1, 0)
    where
        wh.rdm_flag = 1
        and wh.delete_flag = 0
        and ((ifnull(wh.apply_working_hours,0) <> ifnull(tt.apply_working_hours,0))
                  or (ifnull(wh.actual_working_hours,0) <> ifnull(tt.actual_working_hours,0)) or (wh.actual_working_hours is null and tt.confirm_hour is not null)
            or wh.create_at <> tt.report_date or wh.approve_time <> tt.confirm_date)
        ]]>
  </update>
    <delete id="batcheDeleteWorkingHourByRdm">
        update working_hour set delete_flag = 1
        where user_id = #{userId,jdbcType=BIGINT}
          and <![CDATA[ (status <> 4 or actual_working_hours is null or actual_working_hours <= 0 ) ]]>
          and project_id = #{projectId,jdbcType=BIGINT}
          <![CDATA[ and apply_date <= #{endTime} and apply_date >= #{startTime} ]]>
    </delete>

    <select id="selectFromRdm" resultMap="BaseResultMap">
      select distinct
          rwh.att_date as apply_date,
          rwh.report_hour as apply_working_hours,
          rwh.mipname as user_mip,
          p.id as project_id,
          p.code as project_code,
          p.price_type as price_type,
          rwh.confirm_hour as actual_working_hours,
          rwh.report_date as create_at,
          rwh.confirm_date as approve_time,
          rwh.docker as approve_user_name,
          p.unit_id as biz_unit_id,
          (select
               ifnull(sum(if(status = 5, greatest(apply_working_hours,actual_working_hours), apply_working_hours)),0)
           from
               working_hour
           where
               user_mip = rwh.mipname
             and apply_date = rwh.att_date
             and rdm_flag = 0
             and delete_flag = 0 and status in (2,4,5)) as total_apply_working_hours,
          (select
               ifnull(sum(if(status = 5, greatest(apply_working_hours,actual_working_hours), actual_working_hours)),0)
           from
               working_hour
           where
               user_mip = rwh.mipname
             and apply_date = rwh.att_date
             and rdm_flag = 0
             and delete_flag = 0 and status in (2,4,5)) as total_confirm_working_hours
      from
          rdm_working_hour rwh
      inner join project_resource_rel prr on
          rwh.mipname = prr.mipname
          <![CDATA[
          and rwh.att_date >= prr.start_time
          and rwh.att_date <= prr.end_time
          ]]>
      inner join project p on
          p.id = prr.project_id
      left join working_hour wh on
          rwh.mipname = wh.user_mip
          and rwh.att_date = wh.apply_date
          and wh.delete_flag = 0
          and wh.rdm_flag = 1
      where
          wh.id is null
          and (rwh.report_hour > 0 or rwh.confirm_hour > 0)
          and prr.deleted_flag = 0
          and rwh.deleted_flag = 0
          and p.deleted_flag = 0
          and p.status in (4,9,10,-3,7)
  </select>
    <select id="countWorkingHourByRdm" resultType="java.lang.Long">
        select count(*)
        from working_hour wh
        left join labor_cost_detail lcd on lcd.working_hour_id = wh.id
        left join cost_collection cc on lcd.cost_collection_id = cc.id
        where wh.user_id = #{userId}
          and wh.rdm_flag = 1
          and wh.delete_flag = 0
          and wh.project_id = #{projectId,jdbcType=BIGINT}
          <![CDATA[ and wh.apply_date <= #{endTime} and wh.apply_date >= #{startTime} ]]>
          and lcd.deleted_flag = 0
          and (lcd.accounting_flag = 1 or lcd.working_hour_accounting_id is not null or (cc.carry_status = 1 and cc.deleted_flag = 0))
    </select>

    <select id="countConfirmWorkingHour" resultType="java.lang.Long">
        select count(*)
        from working_hour wh
        where wh.user_id = #{userId}
            and wh.rdm_flag = 1
            and wh.delete_flag = 0
            and wh.status = 4
            and wh.project_id = #{projectId,jdbcType=BIGINT}
            <![CDATA[ and wh.apply_date <= #{endTime} and wh.apply_date >= #{startTime} ]]>
            and wh.actual_working_hours > 0
    </select>

    <select id="totalApplyWorkinghour" resultType="java.math.BigDecimal">
        select ifnull(sum(wh.apply_working_hours), 0)
        from working_hour wh
        where wh.user_id = #{userId}
          and wh.delete_flag = 0
          and wh.status in (2,4,5,6)
          and wh.biz_unit_id = #{bizUnitId}
          and wh.project_id in (select id from project where deleted_flag = 0 and (status >= 4 or status = -3) and status <![CDATA[ <> ]]> 12)
            <![CDATA[ and wh.apply_date <= #{endDate} and wh.apply_date >= #{startDate} ]]>
    </select>

    <select id="totalConfirmWorkinghour" resultType="java.math.BigDecimal">
        select ifnull(sum(wh.apply_working_hours), 0)
        from working_hour wh
        where wh.user_id = #{userId}
          and wh.delete_flag = 0
          and wh.status in (2,5)
          and wh.biz_unit_id = #{bizUnitId}
          and wh.project_id in (select id from project where deleted_flag = 0 and (status >= 4 or status = -3) and status <![CDATA[ <> ]]> 12)
          <![CDATA[ and wh.apply_date <= #{endDate} and wh.apply_date >= #{startDate} ]]>
    </select>

    <select id="getWorkingHourResultByUserId" resultType="com.midea.pam.common.ctc.dto.WorkingHourResultDto">
        select
            c.datelist as applyDate,
            #{userId} as userId,
            ifnull(iad.actual_hours, 0) as ihrAttendHours,
            (select ifnull(value,8) from organization_custom_dict
                where org_id = #{bizUnitId}
                and name = '工时填报小时数上限'
                and deleted_flag = 0 limit 1) as maxHour,
            (select
                ifnull(sum(apply_working_hours),0)
            from
                working_hour
            where
                user_id = #{userId}
                and biz_unit_id = #{bizUnitId}
                and apply_date = c.datelist
                and delete_flag = 0
                and status in (2,4,5) ) as totalApplyWorkingHours,
            ( select
                ifnull(sum(actual_working_hours),0)
            from
                working_hour
            where
                 user_id = #{userId}
                and biz_unit_id = #{bizUnitId}
                and apply_date = c.datelist
                and delete_flag = 0
                and status = 4 ) as totalConfirmWorkingHours
        from
            calendar c left
            join ihr_attend_detail iad on c.datelist = iad.attend_date
            and iad.user_id = #{userId}
            and iad.delete_flag = 0
        where
        <if test="dateList!=null">
            date_format(c.datelist, '%Y-%m-%d') in
            <foreach collection="dateList" item="date" open="(" separator="," close=")">
                #{date}
            </foreach>
        </if>
        <if test="dateList==null">
            <![CDATA[ c.datelist >= #{startTime} and c.datelist <= #{endTime} ]]>
        </if>
    </select>

    <select id="selectProjectMemberDistinct" resultType="com.midea.pam.common.ctc.dto.WorkingHourDto">
        select
            distinct
            wh.id,
            p.name as projectName,
            p.code as projectCode
        from
            working_hour wh
        inner join project p on
            p.id = wh.project_id
        inner join project_type pt on
            p.type = pt.id
        where
            p.deleted_flag = 0
            and wh.delete_flag = 0
            and wh.user_id = #{userId}
            and wh.apply_date = #{applyDate}
            and <![CDATA[ wh.status <> 1 ]]>
        <if test="projectId != null">
            and <![CDATA[ p.id <> #{projectId} ]]>
        </if>
        and pt.project_member_distinct = 1;
    </select>

    <select id="selectProjectMemberInternalDistinct" resultType="com.midea.pam.common.ctc.dto.WorkingHourDto">
        select
            wh.id,
            p.name as projectName,
            p.code as projectCode
        from
            working_hour wh
        inner join project p on
            p.id = wh.project_id
        where
            p.deleted_flag = 0
            and wh.delete_flag = 0
            and wh.user_id = #{userId}
            and wh.apply_date = #{applyDate}
            and wh.project_id = #{projectId}
            <if test="processId != null">
                and wh.process_id != #{processId}
            </if>
    </select>

    <select id="selectProjectMemberExternalDistinct" resultType="com.midea.pam.common.ctc.dto.WorkingHourDto">
        select
            wh.id,
            p.name as projectName,
            p.code as projectCode
        from
            working_hour wh
        inner join project p on
            p.id = wh.project_id
        where
            p.deleted_flag = 0
            and wh.delete_flag = 0
            and wh.vendor_code = #{vendorCode}
            and wh.apply_date = #{applyDate}
            and p.id = #{projectId}
            and wh.role_name = #{roleName}
            <if test="processId != null">
                and wh.process_id != #{processId}
            </if>
    </select>

    <select id="selectProjectMemberInternalDistinctWbs" resultType="java.lang.Integer">
        select count(*)
        from working_hour wh
        inner join project p on p.id = wh.project_id
        where p.deleted_flag = 0 and wh.delete_flag = 0
        and wh.user_id = #{userId}
        and wh.apply_date = #{applyDate}
        and wh.project_id = #{projectId}
        and wh.labor_wbs_cost_id = #{laborWbsCostId}
        and wh.wbs_budget_code = #{wbsSummaryCode}
        and wh.status not in (1,3)
        <if test="processId != null">
            and wh.process_id != #{processId}
        </if>
    </select>

    <select id="getApprovedWorkingHours" resultType="com.midea.pam.common.ctc.dto.WorkingHourDto">
        select
            distinct
            wh.id,
            p.code as projectCode,
            p.name as projectName,
            wh.user_id as userId,
            wh.user_mip as mipName,
            wh.project_id as projectId,
            wh.apply_date as applyDate,
            wh.apply_working_hours as applyWorkingHours,
            wh.create_at as createAt,
            wh.actual_working_hours as actualWorkingHours,
            wh.approve_time as approveTime,
            wh.status as status,
            wh.ihr_attend_hours as ihrAttendHours,
            wh.rdm_flag as rdmFlag,
            cc.carry_status as carryStatus,
            lcd.id as laborCostDetailId,
            if(cc.carry_status = 1 , wh.actual_working_hours, 0) as carryWorkingHours
        from
            working_hour wh
        inner join project p on wh.project_id = p.id
        inner join project_member pm on wh.project_id = pm.project_id and pm.user_id = #{userId}
        left join labor_cost_detail lcd on lcd.working_hour_id = wh.id and lcd.deleted_flag = 0
        left join cost_collection cc on lcd.cost_collection_id = cc.id and cc.deleted_flag = 0
        where
            wh.user_id = #{userId}
            and wh.delete_flag = 0
            and wh.actual_working_hours > 0
            and wh.project_id = #{projectId,jdbcType=BIGINT}
        order by wh.apply_date asc
    </select>

    <select id="findWorkingTimes" resultType="com.midea.pam.common.ctc.dto.WorkingHourDto">
        select distinct substring(apply_date, 1, 7) as applyDate,biz_unit_id as bizUnitId,level
        from working_hour
        where project_id = #{projectId}
          and status = 4
          and delete_flag = 0
          and apply_date is not null
          and level is not null
        order by create_at desc
    </select>

    <select id="countActualWorksTime" resultType="com.midea.pam.common.ctc.dto.WorkingHourDto">
        select project_id projectId,
               user_id userId,
               substring(apply_date, 1, 7) applyDate,
               biz_unit_id bizUnitId,
               level,
               sum(ifnull(actual_working_hours,0)) actualWorkingHours
        from working_hour
        where project_id = #{projectId}
          and status = 4
          and delete_flag = 0
          and apply_date is not null
          and level is not null
        group by project_id,user_id,level,biz_unit_id,substring(apply_date, 1, 7)
        order by create_at desc
    </select>

    <update id="batchUpdateFromIhr">
        update
            working_hour wh,
            transition_ihr_attend_detail tiad
        set
            wh.ihr_attend_hours = ifnull(tiad.actual_hours,wh.ihr_attend_hours)
        where
            wh.user_id = tiad.user_id
          and wh.apply_date = tiad.attend_date
          and wh.delete_flag = 0
          and ifnull(wh.ihr_attend_hours,0) != ifnull(tiad.actual_hours,0)
    </update>

    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.WorkingHour">
        insert into working_hour (id, week, apply_date,
            apply_working_hours, ihr_attend_hours, status,
            user_id, user_mip, org_id,
            org_name, apply_org, project_id,
            project_code, process_id, invoice_apply_header_id,
            level, labor_cost_type, cost_money,
            actual_cost_money, actual_working_hours, remarks,
            delete_flag, create_by, create_at,
            update_by, update_at, approve_user_id,
            approve_time, approve_user_name, user_type,
            biz_unit_id, labor_cost_source_unit_id, is_import,
            rdm_flag, source_flag, write_off_status,
            stay_approve_user_id, stay_approve_user_mip, vendor_code,
            role_name, wbs_budget_code, wbs_summary_code,
            labor_wbs_cost_id, labor_wbs_cost_name,
            project_activity_code, full_pay_ou_id
            )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.week,jdbcType=VARCHAR}, #{item.applyDate,jdbcType=TIMESTAMP},
            #{item.applyWorkingHours,jdbcType=DECIMAL}, #{item.ihrAttendHours,jdbcType=DECIMAL}, #{item.status,jdbcType=TINYINT},
            #{item.userId,jdbcType=BIGINT}, #{item.userMip,jdbcType=VARCHAR}, #{item.orgId,jdbcType=BIGINT},
            #{item.orgName,jdbcType=VARCHAR}, #{item.applyOrg,jdbcType=VARCHAR}, #{item.projectId,jdbcType=BIGINT},
            #{item.projectCode,jdbcType=VARCHAR}, #{item.processId,jdbcType=BIGINT}, #{item.invoiceApplyHeaderId,jdbcType=BIGINT},
            #{item.level,jdbcType=VARCHAR}, #{item.laborCostType,jdbcType=VARCHAR}, #{item.costMoney,jdbcType=DECIMAL},
            #{item.actualCostMoney,jdbcType=DECIMAL}, #{item.actualWorkingHours,jdbcType=DECIMAL}, #{item.remarks,jdbcType=VARCHAR},
            #{item.deleteFlag,jdbcType=INTEGER}, #{item.createBy,jdbcType=BIGINT}, #{item.createAt,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=BIGINT}, #{item.updateAt,jdbcType=TIMESTAMP}, #{item.approveUserId,jdbcType=BIGINT},
            #{item.approveTime,jdbcType=TIMESTAMP}, #{item.approveUserName,jdbcType=VARCHAR}, #{item.userType,jdbcType=VARCHAR},
            #{item.bizUnitId,jdbcType=BIGINT}, #{item.laborCostSourceUnitId,jdbcType=BIGINT}, #{item.isImport,jdbcType=TINYINT},
            #{item.rdmFlag,jdbcType=TINYINT}, #{item.sourceFlag,jdbcType=TINYINT}, #{item.writeOffStatus,jdbcType=TINYINT},
            #{item.stayApproveUserId,jdbcType=BIGINT}, #{item.stayApproveUserMip,jdbcType=VARCHAR}, #{item.vendorCode,jdbcType=VARCHAR},
            #{item.roleName,jdbcType=VARCHAR}, #{item.wbsBudgetCode,jdbcType=VARCHAR}, #{item.wbsSummaryCode,jdbcType=VARCHAR},
            #{item.laborWbsCostId,jdbcType=BIGINT}, #{item.laborWbsCostName,jdbcType=VARCHAR},
            #{item.projectActivityCode,jdbcType=VARCHAR}, #{item.fullPayOuId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <update id="batchUpdateStatus">
        update working_hour set status = #{status}
        where process_id = #{processId} and source_flag = #{sourceFlag}
    </update>

    <update id="batchUpdateStatusAndApproveInfo">
        update working_hour
        set status = #{status},
            approve_user_id = #{approveUserId},
            approve_time = current_timestamp(),
            approve_user_name = #{approveUserName}
        where process_id = #{processId} and source_flag = #{sourceFlag}
    </update>

    <update id="batchUpdateActualWorkingHours">
        update working_hour set actual_working_hours = apply_working_hours
        where process_id = #{processId} and source_flag = #{sourceFlag}
    </update>

    <update id="batchUpdateDeletedFlag">
        update working_hour set delete_flag = #{deleteFlag}
        where process_id = #{processId} and source_flag = #{sourceFlag}
    </update>

    <update id="updateLaborCostHardWorking">
        update pam_ctc.labor_cost_detail set hard_working = #{hardWorking}
        where id = #{id}
          and hard_working <![CDATA[ <> ]]> #{hardWorking}
          and working_hour_accounting_id is null
    </update>

    <select id="getPositionName" resultType="java.lang.String">
        select
            lp.name
        from
            pam_basedata.ltc_position lp
        where
            lp.id in (
            select
                lou.position_id
            from
                pam_basedata.ltc_org_user lou
            where
                lou.user_id = #{userId}
                and lou.type = 1
                and lou.status = 'Y')
    </select>

    <select id="getVendorName" resultType="java.lang.String">
        select
            vm.vendor_name
        from
            pam_basedata.vendor_mip vm
        where
            vm.vendor_mip_id = #{userId}
            and vm.deleted_flag = 0
    </select>

    <resultMap id="WorkingHourResultDto" type="com.midea.pam.common.ctc.dto.WorkingHourResultDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="week" jdbcType="VARCHAR" property="week"/>
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate"/>
        <result column="apply_working_hours" jdbcType="DECIMAL" property="applyWorkingHours"/>
        <result column="max_hour" jdbcType="DECIMAL" property="maxHour"/>
        <result column="total_apply_working_hours" jdbcType="DECIMAL" property="totalApplyWorkingHours"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="user_type" jdbcType="VARCHAR" property="userType"/>
        <result column="user_mip" jdbcType="VARCHAR" property="name"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="vendor_name" jdbcType="VARCHAR" property="vendorName"/>
        <result column="hr_org_name" jdbcType="VARCHAR" property="hrDepartment"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="manager_id" jdbcType="BIGINT" property="managerId"/>
        <result column="manager_name" jdbcType="VARCHAR" property="managerName"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="project_create_time" jdbcType="TIMESTAMP" property="projectCreateTime"/>
        <result column="price_type" jdbcType="VARCHAR" property="priceType"/>
        <result column="process_id" jdbcType="BIGINT" property="processId"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="labor_cost_type" jdbcType="VARCHAR" property="laborCostType"/>
        <result column="cost_money" jdbcType="DECIMAL" property="costMoney"/>
        <result column="actual_working_hours" jdbcType="DECIMAL" property="actualWorkingHours"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag"/>
        <result column="is_collection" jdbcType="INTEGER" property="isCollection"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="approve_user_id" jdbcType="BIGINT" property="approveUserId" />
        <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime" />
        <result column="approve_user_name" jdbcType="VARCHAR" property="approveUserName" />
        <result column="is_import" jdbcType="TINYINT" property="isImport" />
        <result column="rdm_flag" jdbcType="TINYINT" property="rdmFlag" />
        <result column="write_off_status" jdbcType="INTEGER" property="writeOffStatus" />
        <result column="stay_approve_user_id" jdbcType="BIGINT" property="stayApproveUserId" />
        <result property="laborWbsCostId" jdbcType="BIGINT" column="labor_wbs_cost_id"/>
        <result property="laborWbsCostName" jdbcType="VARCHAR" column="labor_wbs_cost_name"/>
        <result property="wbsBudgetCode" jdbcType="VARCHAR" column="wbs_budget_code"/>
    </resultMap>

    <sql id="conditionForDisplay">
        and wh.delete_flag = 0
        <if test="userIds != null">
            and wh.user_id in
            <foreach collection="userIds" item="userId" index="index" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="projectIds != null">
            and wh.project_id in
            <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="projectName != null">
            and p.name like concat('%', #{projectName}, '%')
        </if>
        <if test="managerName != null">
            and p.manager_name like concat('%', #{managerName}, '%')
        </if>
        <if test="status != null">
            and wh.status in
            <foreach collection="status" item="stat" index="index" open="(" separator="," close=")">
                #{stat}
            </foreach>
        </if>
        <if test="projectStatus != null">
            and p.status in
            <foreach collection="projectStatus" item="projectStat" index="index" open="(" separator="," close=")">
                #{projectStat}
            </foreach>
        </if>
        <if test="bizUnitId != null">
            and wh.biz_unit_id = #{bizUnitId}
        </if>
        <if test="rdmFlag != null">
            and wh.rdm_flag = #{rdmFlag}
        </if>
        <if test="businessFlag != null">
            <choose>
                <when test="businessFlag == 1">and p.project_source <![CDATA[ <> ]]> 3</when>
                <otherwise>and p.project_source = 3</otherwise>
            </choose>
        </if>
        <if test="managerId != null">
            and ((p.manager_id = #{managerId} and wh.stay_approve_user_id is null) or (wh.stay_approve_user_id = #{managerId}))
        </if>
        <if test="priceTypes != null">
            and p.price_type in
            <foreach collection="priceTypes" item="priceType" index="index" open="(" separator="," close=")">
                #{priceType}
            </foreach>
        </if>
        <if test="startDate != null">
            <![CDATA[ and wh.apply_date >=#{startDate,jdbcType=TIMESTAMP}]]>
        </if>
        <if test="endDate != null">
            <![CDATA[ and wh.apply_date <=#{endDate,jdbcType=TIMESTAMP}]]>
        </if>
        <if test="dates != null">
            and date_format(wh.apply_date, '%Y-%m-%d') in
            <foreach collection="dates" item="date" index="index" open="(" separator="," close=")">
                #{date}
            </foreach>
        </if>
        <if test="sourceFlags !=null and sourceFlags.size>0">
            and wh.source_flag in
            <foreach collection="sourceFlags" item="sourceFlag" open="(" separator="," close=")">
                #{sourceFlag}
            </foreach>
        </if>
    </sql>

    <select id="selectForDisplay" parameterType="com.midea.pam.common.ctc.dto.WorkingHourQueryDto"
            resultMap="WorkingHourResultDto">
        select
            distinct
            wh.id,
            wh.week,
            wh.apply_date,
            wh.apply_working_hours,
            wh.status,
            wh.user_id,
            wh.user_mip,
            wh.org_id,
            wh.org_name,
            wh.apply_org,
            wh.project_id,
            wh.process_id,
            wh.invoice_apply_header_id,
            wh.level,
            wh.labor_cost_type,
            wh.cost_money,
            wh.actual_cost_money,
            wh.actual_working_hours,
            wh.remarks,
            wh.delete_flag,
            wh.create_by,
            wh.create_at,
            wh.update_by,
            wh.update_at,
            wh.approve_user_id,
            wh.approve_time,
            wh.approve_user_name,
            wh.user_type,
            wh.biz_unit_id,
            wh.labor_cost_source_unit_id,
            wh.is_import,
            wh.rdm_flag,
            wh.source_flag,
            wh.write_off_status,
            wh.stay_approve_user_id,
            wh.stay_approve_user_mip,
            wh.vendor_code,
            wh.role_name,
            wh.wbs_budget_code,
            wh.wbs_summary_code,
            wh.labor_wbs_cost_id,
            wh.labor_wbs_cost_name,
            wh.project_activity_code,
            p.name as project_name,
            p.code as project_code,
            p.manager_id as manager_id,
            p.manager_name as manager_name,
            p.start_date as project_create_time,
            p.price_type as price_type,
            (
            select
                ifnull(sum(wh2.apply_working_hours), 0)
            from
                working_hour wh2
            where
                wh2.apply_date = wh.apply_date
                and
                wh2.status in (2, 4, 5)
                and wh2.user_id = wh.user_id
                and wh2.delete_flag = 0
                and wh2.id <![CDATA[<> ]]> wh.id) as
                total_apply_working_hours,
            if(lcd.accounting_flag = 1
                or lcd.working_hour_accounting_id > 0
                or cc.carry_status = 1,
                1,
                0) as is_collection,
            ifnull(ocd.value, 8) as max_hour
        from
            working_hour wh
        inner join project p on
            p.id = wh.project_id
        left join organization_custom_dict ocd on
            wh.biz_unit_id = ocd.org_id
            and ocd.name = '工时填报小时数上限' and ocd.deleted_flag = 0
        left join labor_cost_detail lcd on
            wh.id = lcd.working_hour_id
            and lcd.deleted_flag = 0
        left join cost_collection cc on
            lcd.cost_collection_id = cc.id
            and cc.deleted_flag = 0
        where
            1 = 1
            and (p.status >= 4
                or p.status = -3)
            and p.status <![CDATA[ <> ]]> 12
            and wh.delete_flag = 0
            and p.deleted_flag = 0
        <include refid="conditionForDisplay"></include>

        order by wh.apply_date asc
    </select>

    <select id="selectPageByMonth" parameterType="com.midea.pam.common.ctc.dto.WorkingHourQueryDto"
            resultMap="WorkingHourResultDto">
        select
        distinct wh.project_id as project_id, wh.user_id as user_id, wh.user_type as user_type, wh.role_name as role_name, wh.user_mip as user_mip
        from working_hour wh
        left join project p on p.id = wh.project_id
        where 1=1
        and (p.status >= 4 or p.status = -3) and p.status <![CDATA[ <> ]]> 12
        and wh.delete_flag = 0
        and p.deleted_flag = 0
        <include refid="conditionForDisplay"></include>
        group by p.id,wh.user_id
        order by p.id desc
    </select>

    <select id="countForDisplay" parameterType="com.midea.pam.common.ctc.dto.WorkingHourQueryDto"
            resultType="java.lang.Long">
        select
        count(*)
        from working_hour wh
        left join project p on p.id = wh.project_id
        where 1=1
        and (p.status >= 4 or p.status = -3) and p.status <![CDATA[ <> ]]> 12
        and p.deleted_flag = 0
        <include refid="conditionForDisplay"></include>
    </select>

    <resultMap id="WorkingHourStatDto" type="com.midea.pam.common.ctc.dto.WorkingHourStatDto">
        <result column="project_id" jdbcType="VARCHAR" property="projectId"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_type" jdbcType="VARCHAR" property="userType"/>
        <result column="user_mip" jdbcType="VARCHAR" property="name"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="vendor_name" jdbcType="VARCHAR" property="vendorName"/>
        <result column="hr_org_name" jdbcType="VARCHAR" property="hrDepartment"/>
        <result column="january" jdbcType="VARCHAR" property="january"/>
        <result column="february" jdbcType="VARCHAR" property="february"/>
        <result column="march" jdbcType="VARCHAR" property="march"/>
        <result column="april" jdbcType="VARCHAR" property="april"/>
        <result column="may" jdbcType="VARCHAR" property="may"/>
        <result column="june" jdbcType="VARCHAR" property="june"/>
        <result column="july" jdbcType="VARCHAR" property="july"/>
        <result column="august" jdbcType="VARCHAR" property="august"/>
        <result column="september" jdbcType="VARCHAR" property="september"/>
        <result column="october" jdbcType="VARCHAR" property="october"/>
        <result column="november" jdbcType="VARCHAR" property="november"/>
        <result column="december" jdbcType="VARCHAR" property="december"/>
        <result column="total" jdbcType="VARCHAR" property="total"/>
    </resultMap>
    <select id="statProjectAndMemberByYear" parameterType="com.midea.pam.common.ctc.dto.WorkingHourQueryDto" resultMap="WorkingHourStatDto">
        select p.id as project_id, p.name as project_name, p.code as project_code, wh.user_id, wh.user_type as user_type, wh.user_mip as user_mip, wh.level as role_name, IFNULL(sum(wh.actual_working_hours),0) as total,
        IFNULL(sum(case MONTH(wh.apply_date) when 1 then wh.actual_working_hours else 0 end), 0) as january,
        IFNULL(sum(case MONTH(wh.apply_date) when 2 then wh.actual_working_hours else 0 end), 0) as february,
        IFNULL(sum(case MONTH(wh.apply_date) when 3 then wh.actual_working_hours else 0 end), 0) as march,
        IFNULL(sum(case MONTH(wh.apply_date) when 4 then wh.actual_working_hours else 0 end), 0) as april,
        IFNULL(sum(case MONTH(wh.apply_date) when 5 then wh.actual_working_hours else 0 end), 0) as may,
        IFNULL(sum(case MONTH(wh.apply_date) when 6 then wh.actual_working_hours else 0 end), 0) as june,
        IFNULL(sum(case MONTH(wh.apply_date) when 7 then wh.actual_working_hours else 0 end), 0) as july,
        IFNULL(sum(case MONTH(wh.apply_date) when 8 then wh.actual_working_hours else 0 end), 0) as august,
        IFNULL(sum(case MONTH(wh.apply_date) when 9 then wh.actual_working_hours else 0 end), 0) as september,
        IFNULL(sum(case MONTH(wh.apply_date) when 10 then wh.actual_working_hours else 0 end), 0) as october,
        IFNULL(sum(case MONTH(wh.apply_date) when 11 then wh.actual_working_hours else 0 end), 0) as november,
        IFNULL(sum(case MONTH(wh.apply_date) when 12 then wh.actual_working_hours else 0 end), 0) as december
        from pam_ctc.working_hour wh
        left join pam_ctc.project p on p.id=wh.project_id
        where 1=1
        and (p.status >= 4 or p.status = -3) and p.status <![CDATA[ <> ]]> 12
        and p.deleted_flag = 0
        <include refid="conditionForDisplay"></include>
        group by p.id, p.name, wh.user_id
    </select>

    <select id="statProjectByYear" parameterType="com.midea.pam.common.ctc.dto.WorkingHourQueryDto" resultMap="WorkingHourStatDto">
        select p.id as project_id, p.name as project_name, p.code as project_code,
        IFNULL(sum(case MONTH(wh.apply_date) when 1 then wh.actual_working_hours else 0 end), 0) as january,
        IFNULL(sum(case MONTH(wh.apply_date) when 2 then wh.actual_working_hours else 0 end), 0) as february,
        IFNULL(sum(case MONTH(wh.apply_date) when 3 then wh.actual_working_hours else 0 end), 0) as march,
        IFNULL(sum(case MONTH(wh.apply_date) when 4 then wh.actual_working_hours else 0 end), 0) as april,
        IFNULL(sum(case MONTH(wh.apply_date) when 5 then wh.actual_working_hours else 0 end), 0) as may,
        IFNULL(sum(case MONTH(wh.apply_date) when 6 then wh.actual_working_hours else 0 end), 0) as june,
        IFNULL(sum(case MONTH(wh.apply_date) when 7 then wh.actual_working_hours else 0 end), 0) as july,
        IFNULL(sum(case MONTH(wh.apply_date) when 8 then wh.actual_working_hours else 0 end), 0) as august,
        IFNULL(sum(case MONTH(wh.apply_date) when 9 then wh.actual_working_hours else 0 end), 0) as september,
        IFNULL(sum(case MONTH(wh.apply_date) when 10 then wh.actual_working_hours else 0 end), 0) as october,
        IFNULL(sum(case MONTH(wh.apply_date) when 11 then wh.actual_working_hours else 0 end), 0) as november,
        IFNULL(sum(case MONTH(wh.apply_date) when 12 then wh.actual_working_hours else 0 end), 0) as december
        from pam_ctc.working_hour wh
        left join pam_ctc.project p on p.id=wh.project_id
        where 1=1
        and (p.status >= 4 or p.status = -3) and p.status <![CDATA[ <> ]]> 12
        and p.deleted_flag = 0
        <include refid="conditionForDisplay"></include>
        group by p.id, p.name
    </select>

    <select id="getWeekInfo" resultType="com.midea.pam.common.ctc.dto.WorkingHourWeekInfoDTO">
        select
        concat(date_format(date_sub(apply_date,INTERVAL WEEKDAY(apply_date) DAY), '%Y-%m-%d'), ' - ',
        date_format(date_sub(apply_date,INTERVAL WEEKDAY(apply_date) - 6 DAY), '%Y-%m-%d')) as weekRegion,
        wh.status as status,
        count(*) as sum
        from pam_ctc.working_hour wh
        left join pam_ctc.project p on p.id = wh.project_id
        where 1=1
        and (p.status >= 4 or p.status = -3) and p.status <![CDATA[ <> ]]> 12
        and wh.delete_flag = 0
        and p.deleted_flag = 0
        and ((wh.rdm_flag = 1 and wh.status != 2) or wh.rdm_flag = 0)
        <if test="userIds != null">
            and wh.user_id in
            <foreach collection="userIds" item="userId" index="index" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="projectIds != null">
            and wh.project_id in
            <foreach collection="projectIds" item="projectId" index="index" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="projectName != null">
            and p.name like concat('%', #{projectName}, '%')
        </if>
        <if test="status != null">
            and wh.status in
            <foreach collection="status" item="stat" index="index" open="(" separator="," close=")">
                #{stat}
            </foreach>
        </if>
        <if test="projectStatus != null">
            and p.status in
            <foreach collection="projectStatus" item="projectStat" index="index" open="(" separator="," close=")">
                #{projectStat}
            </foreach>
        </if>
        <if test="bizUnitId != null">
            and wh.biz_unit_id = #{bizUnitId}
        </if>
        <if test="managerId != null">
            and ((p.manager_id = #{managerId} and wh.stay_approve_user_id is null) or (wh.stay_approve_user_id = #{managerId}))
        </if>
        <if test="priceTypes != null">
            and p.price_type in
            <foreach collection="priceTypes" item="priceType" index="index" open="(" separator="," close=")">
                #{priceType}
            </foreach>
        </if>
        <if test="startDate != null">
            <![CDATA[ and wh.apply_date >=#{startDate,jdbcType=TIMESTAMP}]]>
        </if>
        <if test="endDate != null">
            <![CDATA[ and wh.apply_date <=#{endDate,jdbcType=TIMESTAMP}]]>
        </if>
        <if test="dates != null">
            and date_format(wh.apply_date, '%Y-%m-%d') in
            <foreach collection="dates" item="date" index="index" open="(" separator="," close=")">
                #{date}
            </foreach>
        </if>
        group by date_sub(apply_date,INTERVAL WEEKDAY(apply_date) DAY), wh.status
        order by wh.apply_date asc
    </select>

    <select id="selectToStat" resultMap="BaseResultMap">
        select wh.id,
        wh.apply_date,
        wh.actual_working_hours,
        wh.project_id,
        wh.user_id,
        wh.level,
        wh.wbs_budget_code,
        wh.labor_wbs_cost_id,
        wh.labor_wbs_cost_name,
        wh.user_type
        from working_hour wh
        where wh.status = 4
        and wh.delete_flag = 0
        and wh.biz_unit_id = #{unitId}
        and wh.apply_date between #{startDate} and #{endDate}
        and wh.project_id in
        <foreach collection="projectIds" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>
        <if test="userType!=null">
            and wh.user_type = #{userType}
        </if>
    </select>

    <select id="selectWorkHourByYear" resultMap="BaseResultMap">
        select wh.id, wh.project_id, wh.apply_date,wh.actual_working_hours, wh.wbs_budget_code,wh.labor_wbs_cost_id,wh.labor_wbs_cost_name
        from working_hour wh
        where wh.status = 4
        and wh.user_id = #{userId}
        and wh.biz_unit_id = #{unitId}
        and wh.apply_date between #{startDate} and #{endDate}
    </select>

    <select id="selectWorkHour" resultMap="BaseResultMap">
        select id, week, apply_date, apply_working_hours, ihr_attend_hours, status, user_id, user_mip,
        apply_org, project_id, project_code, process_id, invoice_apply_header_id, level, labor_cost_type,
        cost_money, actual_cost_money, actual_working_hours, approve_user_id, approve_time, approve_user_name,
        user_type, biz_unit_id, labor_cost_source_unit_id, is_import, rdm_flag, source_flag,
        write_off_status, stay_approve_user_id, stay_approve_user_mip, vendor_code, role_name,
        wbs_budget_code, wbs_summary_code, labor_wbs_cost_id, labor_wbs_cost_name
        from working_hour
        <where>
            <if test="userId!=null">
                user_id = #{userId}
            </if>
            <if test="projectId!=null">
                and project_id = #{projectId}
            </if>
            <if test="applyDate!=null">
                and apply_date = #{applyDate}
            </if>
            <if test="wbsBudgetCode!=null">
                and wbs_budget_code = #{wbsBudgetCode}
            </if>
            <if test="laborWbsCostName != null and laborWbsCostName != ''">
                and labor_wbs_cost_name = #{laborWbsCostName}
            </if>
            and delete_flag = 0
        </where>
    </select>

    <select id="selectOrgValueByUserId" resultType="java.lang.String">
        select value
        from pam_ctc.organization_custom_dict
        where org_id in (
        select distinct u.parent_id
        from pam_basedata.unit u
        inner join pam_ctc.project p on p.unit_id = u.id and p.status in (4,7,9) and p.project_source = 3 and p.deleted_flag = 0
        inner join pam_ctc.project_member m on m.project_id = p.id and m.deleted_flag = 0
        where m.user_id = #{userId}
        and u.delete_flag = 0
        ) and name = '商机项目填报工时是否支持多角色'
    </select>

    <select id="checkIsExistsWbsProject" resultType="java.lang.Integer">
        select 1
        where exists  (
            select p.id
            from pam_ctc.project p
            inner join pam_ctc.project_member m on m.project_id = p.id and m.deleted_flag = 0
            where p.status in (4,7,9)
            and m.user_id = #{userId}
            and p.deleted_flag = 0
            and p.wbs_enabled = 1
        )
    </select>

    <select id="selectUserInfo" resultType="com.midea.pam.common.basedata.dto.UserInfoDto">
        SELECT
            lui.id,
            lui.username,
            lui.name,
            ifnull(lcs.labor_cost_name,lp.name) laborCostRole,
            lcs.hr_org_name hrOrgName
        FROM pam_basedata.ltc_user_info lui
        left join pam_basedata.org_labor_cost_type_set lcs on lcs.id = lui.labor_cost_type_set_id
        left join pam_basedata.ltc_org_user lou on lou.user_id = lui.id
            and lou.type = 1
            and lou.status = 'Y'
        left join pam_basedata.ltc_position lp on lp.id = lou.position_id
        where lui.id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="selectVendorNameByUserIds" resultType="com.midea.pam.common.basedata.dto.VendorMipDTO">
        select
            vendor_mip_id vendorMipId,
            vendor_name vendorName
        from pam_basedata.vendor_mip
        where vendor_mip_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and deleted_flag = 0
    </select>

    <select id="getToConfirmWorkingHours" resultType="com.midea.pam.common.ctc.dto.WorkingHourResultDto">
        select
            wh.id as id,
            wh.week as week,
            p.ou_id as ouId,
            wh.full_pay_ou_id as fullPayOuId,
            wh.apply_date as applyDate,
            DATE_FORMAT(wh.apply_date, '%Y%m') as statisticDate,
            wh.apply_working_hours as applyWorkingHours,
            wh.ihr_attend_hours as ihrAttendHours,
            wh.status as status,
            wh.user_id as userId,
            wh.user_mip as userMip,
            wh.org_id as orgId,
            wh.org_name as orgName,
            wh.apply_org as applyOrg,
            wh.project_id as projectId,
            wh.process_id as processId,
            wh.invoice_apply_header_id as invoiceApplyHeaderId,
            wh.level as level,
            wh.labor_cost_type as laborCostType,
            wh.labor_cost_type_set_id as laborCostTypeSetId,
            wh.cost_money as costMoney,
            wh.actual_cost_money as actualCostMoney,
            wh.actual_working_hours as actualWorkingHours,
            wh.remarks as remarks,
            wh.delete_flag as deleteFlag,
            wh.create_by as createBy,
            wh.create_at as createAt,
            wh.update_by as updateBy,
            wh.update_at as updateAt,
            wh.approve_user_id as approveUserId,
            wh.approve_time as approveTime,
            wh.approve_user_name as approveUserName,
            wh.user_type as userType,
            wh.biz_unit_id as bizUnitId,
            wh.labor_cost_source_unit_id as laborCostSourceUnitId,
            wh.is_import as isImport,
            wh.rdm_flag as rdmFlag,
            wh.source_flag as sourceFlag,
            wh.write_off_status as writeOffStatus,
            wh.stay_approve_user_id as stayApproveUserId,
            wh.stay_approve_user_mip as stayApproveUserMip,
            wh.vendor_code as vendorCode,
            wh.role_name as roleName,
            wh.wbs_budget_code as wbsBudgetCode,
            wh.labor_wbs_cost_id as laborWbsCostId,
            wh.labor_wbs_cost_name as laborWbsCostName,
            wh.project_activity_code as projectActivityCode
        from pam_ctc.working_hour wh
                 inner join pam_ctc.project p on wh.project_id = p.id
        where wh.project_id = #{projectId}
          and wh.status = 4
          and (wh.delete_flag = 0 or wh.delete_flag is null)
          and (wh.approve_time is null or wh.approve_time <![CDATA[ <= ]]> #{endDate,jdbcType=TIMESTAMP})
          and wh.actual_working_hours is not null
          and wh.actual_cost_money is null
    </select>
    <select id="selectToUpdateLaborCost" resultType="com.midea.pam.common.ctc.dto.WorkingHourResultDto"
            parameterType="com.midea.pam.common.ctc.dto.LaborCostDetailDto">
        select
            wh.biz_unit_id as bizUnitId,
            wh.labor_cost_source_unit_id as laborCostSourceUnitId,
            lcd.id as laborCostDetailId,
            p.ou_id as ouId,
            wh.user_id as userId
        from
            pam_ctc.labor_cost_detail lcd
        inner join pam_ctc.working_hour wh on
            lcd.working_hour_id = wh.id
        inner join pam_ctc.project p on
            p.id = wh.project_id
        where
            lcd.deleted_flag = 0
            and wh.delete_flag = 0
            and lcd.working_hour_accounting_id is null
            and lcd.user_type != 2
            <if test="ouId != null">
                and p.ou_id = #{ouId}
            </if>
            <if test="projectId != null">
                and p.id = #{projectId}
            </if>
            <if test="userId != null">
                and wh.user_id = #{userId}
            </if>
            <if test="applyOrg != null and applyOrg != ''">
                and wh.apply_org = #{applyOrg}
            </if>
            <if test="applyDateStart != null">
                <![CDATA[ and lcd.apply_date >= #{applyDateStart} ]]>
            </if>
            <if test="applyDateEnd!=null">
                <![CDATA[ and lcd.apply_date <= #{applyDateEnd} ]]>
            </if>
    </select>

    <select id="selectToUpdateLaborCostTypeSetId"
            resultType="com.midea.pam.common.ctc.dto.WorkingHourResultDto">
        -- 查询工时时点部门的hr费率配置ID
        select
            distinct
            wh.id as id,
            olcts.id as laborCostTypeSetId
        from
            pam_ctc.working_hour wh
        inner join pam_ctc.project p on
            p.id = wh.project_id
        inner join pam_basedata.org_labor_cost_type_set olcts on
            olcts.hr_org_name = wh.apply_org
            and olcts.deleted_flag = 0
            and olcts.labor_cost_type_code = wh.labor_cost_type
            and (olcts.company_id = wh.biz_unit_id
                or olcts.company_id = wh.labor_cost_source_unit_id)
        inner join pam_basedata.org_labor_cost_type_set_detail olc on
            olc.ou_id = p.ou_id
            and olc.org_labor_cost_type_set_id = olcts.id
            and olc.deleted_flag = 0
        where
            wh.delete_flag = 0
            <if test="applyDateStart == null">
                wh.labor_cost_type_set_id is null
            </if>
            <if test="bizUnitId != null">
                and wh.biz_unit_id = #{bizUnitId}
            </if>
            <if test="projectId != null">
                and p.id = #{projectId}
            </if>
            <if test="userId != null">
                and wh.user_id = #{userId}
            </if>
            <if test="applyOrg != null and applyOrg != ''">
                and wh.apply_org = #{applyOrg}
            </if>
            <if test="applyDateStart != null">
                <![CDATA[ and wh.apply_date >= #{applyDateStart} ]]>
            </if>
            <if test="applyDateEnd!=null">
                <![CDATA[ and wh.apply_date <= #{applyDateEnd} ]]>
            </if>
    </select>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" open=" " separator=" " close="">
            update working_hour
            <set>
                <if test="item.week != null">
                    week = #{item.week,jdbcType=VARCHAR},
                </if>
                <if test="item.applyDate != null">
                    apply_date = #{item.applyDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.applyWorkingHours != null">
                    apply_working_hours = #{item.applyWorkingHours,jdbcType=DECIMAL},
                </if>
                <if test="item.ihrAttendHours != null">
                    ihr_attend_hours = #{item.ihrAttendHours,jdbcType=DECIMAL},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=TINYINT},
                </if>
                <if test="item.userId != null">
                    user_id = #{item.userId,jdbcType=BIGINT},
                </if>
                <if test="item.userMip != null">
                    user_mip = #{item.userMip,jdbcType=VARCHAR},
                </if>
                <if test="item.orgId != null">
                    org_id = #{item.orgId,jdbcType=BIGINT},
                </if>
                <if test="item.orgName != null">
                    org_name = #{item.orgName,jdbcType=VARCHAR},
                </if>
                <if test="item.applyOrg != null">
                    apply_org = #{item.applyOrg,jdbcType=VARCHAR},
                </if>
                <if test="item.projectId != null">
                    project_id = #{item.projectId,jdbcType=BIGINT},
                </if>
                <if test="item.projectCode != null">
                    project_code = #{item.projectCode,jdbcType=VARCHAR},
                </if>
                <if test="item.processId != null">
                    process_id = #{item.processId,jdbcType=BIGINT},
                </if>
                <if test="item.invoiceApplyHeaderId != null">
                    invoice_apply_header_id = #{item.invoiceApplyHeaderId,jdbcType=BIGINT},
                </if>
                <if test="item.level != null">
                    level = #{item.level,jdbcType=VARCHAR},
                </if>
                <if test="item.laborCostType != null">
                    labor_cost_type = #{item.laborCostType,jdbcType=VARCHAR},
                </if>
                <if test="item.costMoney != null">
                    cost_money = #{item.costMoney,jdbcType=DECIMAL},
                </if>
                <if test="item.actualCostMoney != null">
                    actual_cost_money = #{item.actualCostMoney,jdbcType=DECIMAL},
                </if>
                <if test="item.actualWorkingHours != null">
                    actual_working_hours = #{item.actualWorkingHours,jdbcType=DECIMAL},
                </if>
                <if test="item.remarks != null">
                    remarks = #{item.remarks,jdbcType=VARCHAR},
                </if>
                <if test="item.deleteFlag != null">
                    delete_flag = #{item.deleteFlag,jdbcType=INTEGER},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy,jdbcType=BIGINT},
                </if>
                <if test="item.createAt != null">
                    create_at = #{item.createAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy,jdbcType=BIGINT},
                </if>
                <if test="item.updateAt != null">
                    update_at = #{item.updateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.approveUserId != null">
                    approve_user_id = #{item.approveUserId,jdbcType=BIGINT},
                </if>
                <if test="item.approveTime != null">
                    approve_time = #{item.approveTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.approveUserName != null">
                    approve_user_name = #{item.approveUserName,jdbcType=VARCHAR},
                </if>
                <if test="item.userType != null">
                    user_type = #{item.userType,jdbcType=VARCHAR},
                </if>
                <if test="item.bizUnitId != null">
                    biz_unit_id = #{item.bizUnitId,jdbcType=BIGINT},
                </if>
                <if test="item.laborCostSourceUnitId != null">
                    labor_cost_source_unit_id = #{item.laborCostSourceUnitId,jdbcType=BIGINT},
                </if>
                <if test="item.isImport != null">
                    is_import = #{item.isImport,jdbcType=TINYINT},
                </if>
                <if test="item.rdmFlag != null">
                    rdm_flag = #{item.rdmFlag,jdbcType=TINYINT},
                </if>
                <if test="item.sourceFlag != null">
                    source_flag = #{item.sourceFlag,jdbcType=TINYINT},
                </if>
                <if test="item.writeOffStatus != null">
                    write_off_status = #{item.writeOffStatus,jdbcType=TINYINT},
                </if>
                <if test="item.stayApproveUserId != null">
                    stay_approve_user_id = #{item.stayApproveUserId,jdbcType=BIGINT},
                </if>
                <if test="item.stayApproveUserMip != null">
                    stay_approve_user_mip = #{item.stayApproveUserMip,jdbcType=VARCHAR},
                </if>
                <if test="item.vendorCode != null">
                    vendor_code = #{item.vendorCode,jdbcType=VARCHAR},
                </if>
                <if test="item.roleName != null">
                    role_name = #{item.roleName,jdbcType=VARCHAR},
                </if>
                <if test="item.wbsBudgetCode != null">
                    wbs_budget_code = #{item.wbsBudgetCode,jdbcType=VARCHAR},
                </if>
                <if test="item.wbsSummaryCode != null">
                    wbs_summary_code = #{item.wbsSummaryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.laborWbsCostId != null">
                    labor_wbs_cost_id = #{item.laborWbsCostId,jdbcType=BIGINT},
                </if>
                <if test="item.laborWbsCostName != null">
                    labor_wbs_cost_name = #{item.laborWbsCostName,jdbcType=VARCHAR},
                </if>
                <if test="item.projectActivityCode != null">
                    project_activity_code = #{item.projectActivityCode,jdbcType=VARCHAR},
                </if>
                <if test="item.fullPayOuId != null">
                    full_pay_ou_id = #{item.fullPayOuId,jdbcType=BIGINT},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT};
        </foreach>
    </update>

    <update id="noWbsFixLaborCostTypeSetId">
        update pam_ctc.working_hour wh
        left join pam_basedata.labor_cost lc on wh.`level` = lc.name and wh.biz_unit_id = lc.biz_unit_id and delete_flag = 0
        set wh.labor_wbs_cost_id = lc.id
        where wh.delete_flag = 0
        and ( wh.labor_cost_type = 'role' or wh.labor_cost_type is null )
        and wh.`level` is not null
        and wh.labor_wbs_cost_id is null
    </update>

    <select id="queryFullPayOuIdList" resultType="com.midea.pam.common.basedata.entity.OperatingUnit">
        select distinct
            ou_id as operatingUnitId,
            ou_name as operatingUnitName
        from cnb_emp_basic_data
        where deleted_flag = 0
        and ou_id is not null
    </select>

    <update id="initFullPayOuId">
        UPDATE working_hour wh
        SET wh.full_pay_ou_id =
            (
                SELECT cebd.ou_id
                FROM cnb_emp_basic_data cebd
                WHERE cebd.user_id = wh.user_id
                AND cebd.statistic_date = DATE_FORMAT(wh.apply_date, '%Y%m')
                AND cebd.ou_id IS NOT NULL
                AND cebd.deleted_flag = 0
                LIMIT 1
            ),
        wh.ou_id =
            (
                SELECT p.ou_id
                FROM project p
                WHERE p.id = wh.project_id
            )
        WHERE wh.full_pay_ou_id IS NULL
        AND wh.delete_flag = 0
    </update>
</mapper>
