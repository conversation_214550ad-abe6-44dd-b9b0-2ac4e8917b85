<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.FormInstanceExtMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.gateway.entity.FormInstance">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="form_url" jdbcType="VARCHAR" property="formUrl" />
    <result column="form_instance_id" jdbcType="BIGINT" property="formInstanceId" />
    <result column="fd_instance_id" jdbcType="VARCHAR" property="fdInstanceId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="event_type" jdbcType="VARCHAR" property="eventType" />
    <result column="wf_status" jdbcType="VARCHAR" property="wfStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="BIT" property="deletedFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, form_url, form_instance_id, fd_instance_id, company_id, event_type, wf_status, 
    remark, create_by, create_at, update_by, update_at, deleted_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.gateway.entity.FormInstanceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pam_system.form_instance
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.gateway.entity.FormInstance">
    update pam_system.form_instance
    <set>
      <if test="formUrl != null">
        form_url = #{formUrl,jdbcType=VARCHAR},
      </if>
      <if test="formInstanceId != null">
        form_instance_id = #{formInstanceId,jdbcType=BIGINT},
      </if>
      <if test="fdInstanceId != null">
        fd_instance_id = #{fdInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="wfStatus != null">
        wf_status = #{wfStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectFormInstanceIdNotInFormInstanceId"  resultType="com.midea.pam.common.gateway.entity.FormInstance">
    select
    fi.id as id,
    fi.form_url as formUrl,
    fi.form_instance_id as formInstanceId,
    fi.fd_instance_id as fdInstanceId,
    fi.wf_status as wfStatus,
    fi.create_at as createAt,
    fi.deleted_flag as deletedFlag
    from pam_system.form_instance fi
    where
    form_url = 'projectReopenApp'
    <if test="formInstanceIds != null and formInstanceIds.size()>0">
      and form_instance_id not in
      <foreach collection="formInstanceIds" item="formInstanceId" index="index" open="(" separator="," close=")">
        #{formInstanceId}
      </foreach>
    </if>
  </select>

  <select id="selectFormInstanceIdforformUrl" resultType="com.midea.pam.common.gateway.entity.FormInstance">
    select
    fi.id as id,
    fi.form_url as formUrl,
    fi.form_instance_id as formInstanceId,
    fi.fd_instance_id as fdInstanceId,
    fi.wf_status as wfStatus,
    fi.create_at as createAt,
    fi.deleted_flag as deletedFlag
    from pam_system.form_instance fi
    where
    form_url = 'projectReopenApp'
  </select>

  <update id="updateDeletedFlag" parameterType="com.midea.pam.common.gateway.entity.FormInstance">
    update pam_system.form_instance
    set  deleted_flag = #{deletedFlag}
    where id = #{id}
  </update>
</mapper>