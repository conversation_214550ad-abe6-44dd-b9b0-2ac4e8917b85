<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectWbsReceiptsBudgetExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudget">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_wbs_receipts_id" jdbcType="BIGINT" property="projectWbsReceiptsId"/>
        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode"/>
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode"/>
        <result column="budget_occupied_amount" jdbcType="DECIMAL" property="budgetOccupiedAmount"/>
        <result column="demand_type" jdbcType="INTEGER" property="demandType"/>
        <result column="parentId" jdbcType="BIGINT" property="parentid"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag"/>
        <result column="version" jdbcType="BIGINT" property="version"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, project_wbs_receipts_id, wbs_summary_code, activity_code, budget_occupied_amount, 
    demand_type, parentId, create_by, create_at, update_by, update_at, deleted_flag, 
    version
  </sql>

    <update id="deleteByProjectWbsReceiptsId">
      update pam_ctc.project_wbs_receipts_budget
      set deleted_flag = 1 where project_wbs_receipts_id =#{projectWbsReceiptsId}
    </update>

    <select id="getByProjectIdAndWbsSummaryCode" resultType="com.midea.pam.common.ctc.dto.ProjectWbsBudgetSummaryDto">

    </select>

    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudget">
        insert into pam_ctc.project_wbs_receipts_budget (id, project_wbs_receipts_id, wbs_summary_code,
        activity_code, budget_occupied_amount, demand_type,
        parentId, create_by, create_at,
        update_by, update_at, deleted_flag,
        version, init)
        values
        <foreach collection="list" item="receiptsBudget" index="index" separator=",">
            (#{receiptsBudget.id,jdbcType=BIGINT}, #{receiptsBudget.projectWbsReceiptsId,jdbcType=BIGINT},
            #{receiptsBudget.wbsSummaryCode,jdbcType=VARCHAR},
            #{receiptsBudget.activityCode,jdbcType=VARCHAR}, #{receiptsBudget.budgetOccupiedAmount,jdbcType=DECIMAL},
            #{receiptsBudget.demandType,jdbcType=INTEGER},
            #{receiptsBudget.parentid,jdbcType=BIGINT}, #{receiptsBudget.createBy,jdbcType=BIGINT},
            #{receiptsBudget.createAt,jdbcType=TIMESTAMP},
            #{receiptsBudget.updateBy,jdbcType=BIGINT}, #{receiptsBudget.updateAt,jdbcType=TIMESTAMP},
            #{receiptsBudget.deletedFlag,jdbcType=TINYINT},
            #{receiptsBudget.version,jdbcType=BIGINT}, #{receiptsBudget.init,jdbcType=TINYINT})
        </foreach>
    </insert>

    <update id="batchUpdateChangeHistory" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update project_wbs_receipts_budget_change_history
            <set>
                <if test="item.projectWbsChangeReceiptsId != null">
                    project_wbs_change_receipts_id = #{item.projectWbsChangeReceiptsId,jdbcType=BIGINT},
                </if>
                <if test="item.projectWbsPublishReceiptsId != null">
                    project_wbs_publish_receipts_id = #{item.projectWbsPublishReceiptsId,jdbcType=BIGINT},
                </if>
                <if test="item.wbsSummaryCode != null">
                    wbs_summary_code = #{item.wbsSummaryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.activityCode != null">
                    activity_code = #{item.activityCode,jdbcType=VARCHAR},
                </if>
                <if test="item.budgetOccupiedAmountBefore != null">
                    budget_occupied_amount_before = #{item.budgetOccupiedAmountBefore,jdbcType=DECIMAL},
                </if>
                <if test="item.budgetOccupiedAmountAfter != null">
                    budget_occupied_amount_after = #{item.budgetOccupiedAmountAfter,jdbcType=DECIMAL},
                </if>
                <if test="item.demandType != null">
                    demand_type = #{item.demandType,jdbcType=INTEGER},
                </if>
                <if test="item.parentid != null">
                    parentId = #{item.parentid,jdbcType=BIGINT},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy,jdbcType=BIGINT},
                </if>
                <if test="item.createAt != null">
                    create_at = #{item.createAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy,jdbcType=BIGINT},
                </if>
                <if test="item.updateAt != null">
                    update_at = #{item.updateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.deletedFlag != null">
                    deleted_flag = #{item.deletedFlag,jdbcType=TINYINT},
                </if>
                <if test="item.version != null">
                    version = #{item.version,jdbcType=BIGINT},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>