<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.CnbEmpBasicDataMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.CnbEmpBasicData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="statistic_date" jdbcType="VARCHAR" property="statisticDate" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="mip_name" jdbcType="VARCHAR" property="mipName" />
    <result column="employee_code" jdbcType="VARCHAR" property="employeeCode" />
    <result column="full_pay_unit_id" jdbcType="VARCHAR" property="fullPayUnitId" />
    <result column="local_full_unit_name" jdbcType="VARCHAR" property="localFullUnitName" />
    <result column="hr_ou_id" jdbcType="BIGINT" property="hrOuId" />
    <result column="ou_id" jdbcType="BIGINT" property="ouId" />
    <result column="ou_name" jdbcType="VARCHAR" property="ouName" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, statistic_date, user_id, mip_name, employee_code, full_pay_unit_id, local_full_unit_name, 
    hr_ou_id, ou_id, ou_name, create_by, create_at, update_by, update_at, deleted_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.CnbEmpBasicDataExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cnb_emp_basic_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cnb_emp_basic_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from cnb_emp_basic_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.CnbEmpBasicData">
    insert into cnb_emp_basic_data (id, statistic_date, user_id, 
      mip_name, employee_code, full_pay_unit_id, 
      local_full_unit_name, hr_ou_id, ou_id, 
      ou_name, create_by, create_at, 
      update_by, update_at, deleted_flag
      )
    values (#{id,jdbcType=BIGINT}, #{statisticDate,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, 
      #{mipName,jdbcType=VARCHAR}, #{employeeCode,jdbcType=VARCHAR}, #{fullPayUnitId,jdbcType=VARCHAR}, 
      #{localFullUnitName,jdbcType=VARCHAR}, #{hrOuId,jdbcType=BIGINT}, #{ouId,jdbcType=BIGINT}, 
      #{ouName,jdbcType=VARCHAR}, #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{deletedFlag,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.CnbEmpBasicData">
    insert into cnb_emp_basic_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="statisticDate != null">
        statistic_date,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="mipName != null">
        mip_name,
      </if>
      <if test="employeeCode != null">
        employee_code,
      </if>
      <if test="fullPayUnitId != null">
        full_pay_unit_id,
      </if>
      <if test="localFullUnitName != null">
        local_full_unit_name,
      </if>
      <if test="hrOuId != null">
        hr_ou_id,
      </if>
      <if test="ouId != null">
        ou_id,
      </if>
      <if test="ouName != null">
        ou_name,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="statisticDate != null">
        #{statisticDate,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="mipName != null">
        #{mipName,jdbcType=VARCHAR},
      </if>
      <if test="employeeCode != null">
        #{employeeCode,jdbcType=VARCHAR},
      </if>
      <if test="fullPayUnitId != null">
        #{fullPayUnitId,jdbcType=VARCHAR},
      </if>
      <if test="localFullUnitName != null">
        #{localFullUnitName,jdbcType=VARCHAR},
      </if>
      <if test="hrOuId != null">
        #{hrOuId,jdbcType=BIGINT},
      </if>
      <if test="ouId != null">
        #{ouId,jdbcType=BIGINT},
      </if>
      <if test="ouName != null">
        #{ouName,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.CnbEmpBasicDataExample" resultType="java.lang.Long">
    select count(*) from cnb_emp_basic_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.CnbEmpBasicData">
    update cnb_emp_basic_data
    <set>
      <if test="statisticDate != null">
        statistic_date = #{statisticDate,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="mipName != null">
        mip_name = #{mipName,jdbcType=VARCHAR},
      </if>
      <if test="employeeCode != null">
        employee_code = #{employeeCode,jdbcType=VARCHAR},
      </if>
      <if test="fullPayUnitId != null">
        full_pay_unit_id = #{fullPayUnitId,jdbcType=VARCHAR},
      </if>
      <if test="localFullUnitName != null">
        local_full_unit_name = #{localFullUnitName,jdbcType=VARCHAR},
      </if>
      <if test="hrOuId != null">
        hr_ou_id = #{hrOuId,jdbcType=BIGINT},
      </if>
      <if test="ouId != null">
        ou_id = #{ouId,jdbcType=BIGINT},
      </if>
      <if test="ouName != null">
        ou_name = #{ouName,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.CnbEmpBasicData">
    update cnb_emp_basic_data
    set statistic_date = #{statisticDate,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      mip_name = #{mipName,jdbcType=VARCHAR},
      employee_code = #{employeeCode,jdbcType=VARCHAR},
      full_pay_unit_id = #{fullPayUnitId,jdbcType=VARCHAR},
      local_full_unit_name = #{localFullUnitName,jdbcType=VARCHAR},
      hr_ou_id = #{hrOuId,jdbcType=BIGINT},
      ou_id = #{ouId,jdbcType=BIGINT},
      ou_name = #{ouName,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>