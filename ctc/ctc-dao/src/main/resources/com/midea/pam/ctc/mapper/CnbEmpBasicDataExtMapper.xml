<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.CnbEmpBasicDataExtMapper">

    <!-- 查询需要同步hard_working字段的数据 -->
    <select id="selectNeedSyncHardWorkingData" parameterType="java.lang.String" resultType="com.midea.pam.common.ctc.dto.LaborCostHardWorkingSyncDto">
        SELECT DISTINCT
            cebd.user_id as userId,
            cebd.statistic_date as statisticDate,
            p.ou_id as ouId,
            cebd.ou_id as fullPayOuId,
            lcd.hard_working as hardWorking,
            lcd.working_hour_id as workingHourId,
            lcd.id as laborCostDetailId
        FROM cnb_emp_basic_data cebd
        INNER JOIN labor_cost_detail lcd ON cebd.user_id = lcd.user_id 
            AND DATE_FORMAT(lcd.apply_date, '%Y%m') = cebd.statistic_date
        INNER JOIN project p ON lcd.project_id = p.id
        WHERE cebd.deleted_flag = 0
            AND lcd.deleted_flag = 0
            AND (lcd.status = 0 or lcd.status is null)
            AND p.ou_id != cebd.ou_id
            AND cebd.ou_id is not null
            AND cebd.create_at >= #{lastUpdateDate}
    </select>

    <!-- 批量更新labor_cost_detail表的hard_working字段 -->
    <update id="batchUpdateLaborCostDetailHardWorking" parameterType="java.util.List">
        UPDATE labor_cost_detail
        SET hard_working = CASE id
        <foreach collection="syncDataList" item="item">
            WHEN #{item.laborCostDetailId} THEN #{item.hardWorking}
        </foreach>
        END,
        update_at = NOW()
        WHERE id IN
        <foreach collection="syncDataList" item="item" open="(" separator="," close=")">
            #{item.laborCostDetailId}
        </foreach>
    </update>

</mapper>

