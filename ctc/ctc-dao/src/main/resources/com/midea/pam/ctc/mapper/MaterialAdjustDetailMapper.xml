<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.MaterialAdjustDetailMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.MaterialAdjustDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="header_id" jdbcType="BIGINT" property="headerId" />
    <result column="pam_code" jdbcType="VARCHAR" property="pamCode" />
    <result column="erp_code" jdbcType="VARCHAR" property="erpCode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="item_des" jdbcType="VARCHAR" property="itemDes" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="unit_code" jdbcType="VARCHAR" property="unitCode" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="material_class_id" jdbcType="BIGINT" property="materialClassId" />
    <result column="material_class" jdbcType="VARCHAR" property="materialClass" />
    <result column="material_middle_class_id" jdbcType="BIGINT" property="materialMiddleClassId" />
    <result column="material_middle_class" jdbcType="VARCHAR" property="materialMiddleClass" />
    <result column="material_small_class_id" jdbcType="BIGINT" property="materialSmallClassId" />
    <result column="material_small_class" jdbcType="VARCHAR" property="materialSmallClass" />
    <result column="material_type" jdbcType="VARCHAR" property="materialType" />
    <result column="material_type_new" jdbcType="VARCHAR" property="materialTypeNew" />
    <result column="material_id" jdbcType="BIGINT" property="materialId" />
    <result column="figure_number" jdbcType="VARCHAR" property="figureNumber" />
    <result column="chart_version" jdbcType="VARCHAR" property="chartVersion" />
    <result column="chart_version_new" jdbcType="VARCHAR" property="chartVersionNew" />
    <result column="unit_weight" jdbcType="DECIMAL" property="unitWeight" />
    <result column="unit_weight_new" jdbcType="DECIMAL" property="unitWeightNew" />
    <result column="machining_part_type" jdbcType="VARCHAR" property="machiningPartType" />
    <result column="machining_part_type_new" jdbcType="VARCHAR" property="machiningPartTypeNew" />
    <result column="surface_handle" jdbcType="VARCHAR" property="surfaceHandle" />
    <result column="surface_handle_new" jdbcType="VARCHAR" property="surfaceHandleNew" />
    <result column="material" jdbcType="VARCHAR" property="material" />
    <result column="material_new" jdbcType="VARCHAR" property="materialNew" />
    <result column="or_spare_parts_mask" jdbcType="VARCHAR" property="orSparePartsMask" />
    <result column="or_spare_parts_mask_new" jdbcType="VARCHAR" property="orSparePartsMaskNew" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="brand_material_code" jdbcType="VARCHAR" property="brandMaterialCode" />
    <result column="perchasing_leadtime" jdbcType="BIGINT" property="perchasingLeadtime" />
    <result column="min_perchase_quantity" jdbcType="BIGINT" property="minPerchaseQuantity" />
    <result column="min_package_quantity" jdbcType="BIGINT" property="minPackageQuantity" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sync_status" jdbcType="TINYINT" property="syncStatus" />
    <result column="sync_mes" jdbcType="VARCHAR" property="syncMes" />
    <result column="attribute1" jdbcType="VARCHAR" property="attribute1" />
    <result column="attribute2" jdbcType="VARCHAR" property="attribute2" />
    <result column="attribute3" jdbcType="VARCHAR" property="attribute3" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
    <result column="material_attribute" jdbcType="VARCHAR" property="materialAttribute" />
    <result column="inventory_type" jdbcType="VARCHAR" property="inventoryType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, header_id, pam_code, erp_code, name, item_des, unit, unit_code, model, material_class_id, 
    material_class, material_middle_class_id, material_middle_class, material_small_class_id, 
    material_small_class, material_type, material_type_new, material_id, figure_number, 
    chart_version, chart_version_new, unit_weight, unit_weight_new, machining_part_type, 
    machining_part_type_new, surface_handle, surface_handle_new, material, material_new, 
    or_spare_parts_mask, or_spare_parts_mask_new, brand, brand_material_code, perchasing_leadtime, 
    min_perchase_quantity, min_package_quantity, remark, sync_status, sync_mes, attribute1, 
    attribute2, attribute3, create_by, create_at, update_by, update_at, deleted_flag, 
    activity_code, material_attribute, inventory_type
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.MaterialAdjustDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from material_adjust_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from material_adjust_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from material_adjust_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.MaterialAdjustDetail">
    insert into material_adjust_detail (id, header_id, pam_code, 
      erp_code, name, item_des, 
      unit, unit_code, model, 
      material_class_id, material_class, material_middle_class_id, 
      material_middle_class, material_small_class_id, 
      material_small_class, material_type, material_type_new, 
      material_id, figure_number, chart_version, 
      chart_version_new, unit_weight, unit_weight_new, 
      machining_part_type, machining_part_type_new, 
      surface_handle, surface_handle_new, material, 
      material_new, or_spare_parts_mask, or_spare_parts_mask_new, 
      brand, brand_material_code, perchasing_leadtime, 
      min_perchase_quantity, min_package_quantity, 
      remark, sync_status, sync_mes, 
      attribute1, attribute2, attribute3, 
      create_by, create_at, update_by, 
      update_at, deleted_flag, activity_code, 
      material_attribute, inventory_type)
    values (#{id,jdbcType=BIGINT}, #{headerId,jdbcType=BIGINT}, #{pamCode,jdbcType=VARCHAR}, 
      #{erpCode,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{itemDes,jdbcType=VARCHAR}, 
      #{unit,jdbcType=VARCHAR}, #{unitCode,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, 
      #{materialClassId,jdbcType=BIGINT}, #{materialClass,jdbcType=VARCHAR}, #{materialMiddleClassId,jdbcType=BIGINT}, 
      #{materialMiddleClass,jdbcType=VARCHAR}, #{materialSmallClassId,jdbcType=BIGINT}, 
      #{materialSmallClass,jdbcType=VARCHAR}, #{materialType,jdbcType=VARCHAR}, #{materialTypeNew,jdbcType=VARCHAR}, 
      #{materialId,jdbcType=BIGINT}, #{figureNumber,jdbcType=VARCHAR}, #{chartVersion,jdbcType=VARCHAR}, 
      #{chartVersionNew,jdbcType=VARCHAR}, #{unitWeight,jdbcType=DECIMAL}, #{unitWeightNew,jdbcType=DECIMAL}, 
      #{machiningPartType,jdbcType=VARCHAR}, #{machiningPartTypeNew,jdbcType=VARCHAR}, 
      #{surfaceHandle,jdbcType=VARCHAR}, #{surfaceHandleNew,jdbcType=VARCHAR}, #{material,jdbcType=VARCHAR}, 
      #{materialNew,jdbcType=VARCHAR}, #{orSparePartsMask,jdbcType=VARCHAR}, #{orSparePartsMaskNew,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR}, #{brandMaterialCode,jdbcType=VARCHAR}, #{perchasingLeadtime,jdbcType=BIGINT}, 
      #{minPerchaseQuantity,jdbcType=BIGINT}, #{minPackageQuantity,jdbcType=BIGINT}, 
      #{remark,jdbcType=VARCHAR}, #{syncStatus,jdbcType=TINYINT}, #{syncMes,jdbcType=VARCHAR}, 
      #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, #{attribute3,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{updateAt,jdbcType=TIMESTAMP}, #{deletedFlag,jdbcType=TINYINT}, #{activityCode,jdbcType=VARCHAR}, 
      #{materialAttribute,jdbcType=VARCHAR}, #{inventoryType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.MaterialAdjustDetail">
    insert into material_adjust_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headerId != null">
        header_id,
      </if>
      <if test="pamCode != null">
        pam_code,
      </if>
      <if test="erpCode != null">
        erp_code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="itemDes != null">
        item_des,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="unitCode != null">
        unit_code,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="materialClassId != null">
        material_class_id,
      </if>
      <if test="materialClass != null">
        material_class,
      </if>
      <if test="materialMiddleClassId != null">
        material_middle_class_id,
      </if>
      <if test="materialMiddleClass != null">
        material_middle_class,
      </if>
      <if test="materialSmallClassId != null">
        material_small_class_id,
      </if>
      <if test="materialSmallClass != null">
        material_small_class,
      </if>
      <if test="materialType != null">
        material_type,
      </if>
      <if test="materialTypeNew != null">
        material_type_new,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
      <if test="figureNumber != null">
        figure_number,
      </if>
      <if test="chartVersion != null">
        chart_version,
      </if>
      <if test="chartVersionNew != null">
        chart_version_new,
      </if>
      <if test="unitWeight != null">
        unit_weight,
      </if>
      <if test="unitWeightNew != null">
        unit_weight_new,
      </if>
      <if test="machiningPartType != null">
        machining_part_type,
      </if>
      <if test="machiningPartTypeNew != null">
        machining_part_type_new,
      </if>
      <if test="surfaceHandle != null">
        surface_handle,
      </if>
      <if test="surfaceHandleNew != null">
        surface_handle_new,
      </if>
      <if test="material != null">
        material,
      </if>
      <if test="materialNew != null">
        material_new,
      </if>
      <if test="orSparePartsMask != null">
        or_spare_parts_mask,
      </if>
      <if test="orSparePartsMaskNew != null">
        or_spare_parts_mask_new,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="brandMaterialCode != null">
        brand_material_code,
      </if>
      <if test="perchasingLeadtime != null">
        perchasing_leadtime,
      </if>
      <if test="minPerchaseQuantity != null">
        min_perchase_quantity,
      </if>
      <if test="minPackageQuantity != null">
        min_package_quantity,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="syncStatus != null">
        sync_status,
      </if>
      <if test="syncMes != null">
        sync_mes,
      </if>
      <if test="attribute1 != null">
        attribute1,
      </if>
      <if test="attribute2 != null">
        attribute2,
      </if>
      <if test="attribute3 != null">
        attribute3,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="activityCode != null">
        activity_code,
      </if>
      <if test="materialAttribute != null">
        material_attribute,
      </if>
      <if test="inventoryType != null">
        inventory_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="headerId != null">
        #{headerId,jdbcType=BIGINT},
      </if>
      <if test="pamCode != null">
        #{pamCode,jdbcType=VARCHAR},
      </if>
      <if test="erpCode != null">
        #{erpCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="itemDes != null">
        #{itemDes,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="materialClassId != null">
        #{materialClassId,jdbcType=BIGINT},
      </if>
      <if test="materialClass != null">
        #{materialClass,jdbcType=VARCHAR},
      </if>
      <if test="materialMiddleClassId != null">
        #{materialMiddleClassId,jdbcType=BIGINT},
      </if>
      <if test="materialMiddleClass != null">
        #{materialMiddleClass,jdbcType=VARCHAR},
      </if>
      <if test="materialSmallClassId != null">
        #{materialSmallClassId,jdbcType=BIGINT},
      </if>
      <if test="materialSmallClass != null">
        #{materialSmallClass,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        #{materialType,jdbcType=VARCHAR},
      </if>
      <if test="materialTypeNew != null">
        #{materialTypeNew,jdbcType=VARCHAR},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
      <if test="figureNumber != null">
        #{figureNumber,jdbcType=VARCHAR},
      </if>
      <if test="chartVersion != null">
        #{chartVersion,jdbcType=VARCHAR},
      </if>
      <if test="chartVersionNew != null">
        #{chartVersionNew,jdbcType=VARCHAR},
      </if>
      <if test="unitWeight != null">
        #{unitWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitWeightNew != null">
        #{unitWeightNew,jdbcType=DECIMAL},
      </if>
      <if test="machiningPartType != null">
        #{machiningPartType,jdbcType=VARCHAR},
      </if>
      <if test="machiningPartTypeNew != null">
        #{machiningPartTypeNew,jdbcType=VARCHAR},
      </if>
      <if test="surfaceHandle != null">
        #{surfaceHandle,jdbcType=VARCHAR},
      </if>
      <if test="surfaceHandleNew != null">
        #{surfaceHandleNew,jdbcType=VARCHAR},
      </if>
      <if test="material != null">
        #{material,jdbcType=VARCHAR},
      </if>
      <if test="materialNew != null">
        #{materialNew,jdbcType=VARCHAR},
      </if>
      <if test="orSparePartsMask != null">
        #{orSparePartsMask,jdbcType=VARCHAR},
      </if>
      <if test="orSparePartsMaskNew != null">
        #{orSparePartsMaskNew,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="brandMaterialCode != null">
        #{brandMaterialCode,jdbcType=VARCHAR},
      </if>
      <if test="perchasingLeadtime != null">
        #{perchasingLeadtime,jdbcType=BIGINT},
      </if>
      <if test="minPerchaseQuantity != null">
        #{minPerchaseQuantity,jdbcType=BIGINT},
      </if>
      <if test="minPackageQuantity != null">
        #{minPackageQuantity,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="syncStatus != null">
        #{syncStatus,jdbcType=TINYINT},
      </if>
      <if test="syncMes != null">
        #{syncMes,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null">
        #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null">
        #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="activityCode != null">
        #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="materialAttribute != null">
        #{materialAttribute,jdbcType=VARCHAR},
      </if>
      <if test="inventoryType != null">
        #{inventoryType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.MaterialAdjustDetailExample" resultType="java.lang.Long">
    select count(*) from material_adjust_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.MaterialAdjustDetail">
    update material_adjust_detail
    <set>
      <if test="headerId != null">
        header_id = #{headerId,jdbcType=BIGINT},
      </if>
      <if test="pamCode != null">
        pam_code = #{pamCode,jdbcType=VARCHAR},
      </if>
      <if test="erpCode != null">
        erp_code = #{erpCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="itemDes != null">
        item_des = #{itemDes,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        unit_code = #{unitCode,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="materialClassId != null">
        material_class_id = #{materialClassId,jdbcType=BIGINT},
      </if>
      <if test="materialClass != null">
        material_class = #{materialClass,jdbcType=VARCHAR},
      </if>
      <if test="materialMiddleClassId != null">
        material_middle_class_id = #{materialMiddleClassId,jdbcType=BIGINT},
      </if>
      <if test="materialMiddleClass != null">
        material_middle_class = #{materialMiddleClass,jdbcType=VARCHAR},
      </if>
      <if test="materialSmallClassId != null">
        material_small_class_id = #{materialSmallClassId,jdbcType=BIGINT},
      </if>
      <if test="materialSmallClass != null">
        material_small_class = #{materialSmallClass,jdbcType=VARCHAR},
      </if>
      <if test="materialType != null">
        material_type = #{materialType,jdbcType=VARCHAR},
      </if>
      <if test="materialTypeNew != null">
        material_type_new = #{materialTypeNew,jdbcType=VARCHAR},
      </if>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=BIGINT},
      </if>
      <if test="figureNumber != null">
        figure_number = #{figureNumber,jdbcType=VARCHAR},
      </if>
      <if test="chartVersion != null">
        chart_version = #{chartVersion,jdbcType=VARCHAR},
      </if>
      <if test="chartVersionNew != null">
        chart_version_new = #{chartVersionNew,jdbcType=VARCHAR},
      </if>
      <if test="unitWeight != null">
        unit_weight = #{unitWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitWeightNew != null">
        unit_weight_new = #{unitWeightNew,jdbcType=DECIMAL},
      </if>
      <if test="machiningPartType != null">
        machining_part_type = #{machiningPartType,jdbcType=VARCHAR},
      </if>
      <if test="machiningPartTypeNew != null">
        machining_part_type_new = #{machiningPartTypeNew,jdbcType=VARCHAR},
      </if>
      <if test="surfaceHandle != null">
        surface_handle = #{surfaceHandle,jdbcType=VARCHAR},
      </if>
      <if test="surfaceHandleNew != null">
        surface_handle_new = #{surfaceHandleNew,jdbcType=VARCHAR},
      </if>
      <if test="material != null">
        material = #{material,jdbcType=VARCHAR},
      </if>
      <if test="materialNew != null">
        material_new = #{materialNew,jdbcType=VARCHAR},
      </if>
      <if test="orSparePartsMask != null">
        or_spare_parts_mask = #{orSparePartsMask,jdbcType=VARCHAR},
      </if>
      <if test="orSparePartsMaskNew != null">
        or_spare_parts_mask_new = #{orSparePartsMaskNew,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="brandMaterialCode != null">
        brand_material_code = #{brandMaterialCode,jdbcType=VARCHAR},
      </if>
      <if test="perchasingLeadtime != null">
        perchasing_leadtime = #{perchasingLeadtime,jdbcType=BIGINT},
      </if>
      <if test="minPerchaseQuantity != null">
        min_perchase_quantity = #{minPerchaseQuantity,jdbcType=BIGINT},
      </if>
      <if test="minPackageQuantity != null">
        min_package_quantity = #{minPackageQuantity,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="syncStatus != null">
        sync_status = #{syncStatus,jdbcType=TINYINT},
      </if>
      <if test="syncMes != null">
        sync_mes = #{syncMes,jdbcType=VARCHAR},
      </if>
      <if test="attribute1 != null">
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null">
        attribute2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null">
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="activityCode != null">
        activity_code = #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="materialAttribute != null">
        material_attribute = #{materialAttribute,jdbcType=VARCHAR},
      </if>
      <if test="inventoryType != null">
        inventory_type = #{inventoryType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.MaterialAdjustDetail">
    update material_adjust_detail
    set header_id = #{headerId,jdbcType=BIGINT},
      pam_code = #{pamCode,jdbcType=VARCHAR},
      erp_code = #{erpCode,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      item_des = #{itemDes,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      unit_code = #{unitCode,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      material_class_id = #{materialClassId,jdbcType=BIGINT},
      material_class = #{materialClass,jdbcType=VARCHAR},
      material_middle_class_id = #{materialMiddleClassId,jdbcType=BIGINT},
      material_middle_class = #{materialMiddleClass,jdbcType=VARCHAR},
      material_small_class_id = #{materialSmallClassId,jdbcType=BIGINT},
      material_small_class = #{materialSmallClass,jdbcType=VARCHAR},
      material_type = #{materialType,jdbcType=VARCHAR},
      material_type_new = #{materialTypeNew,jdbcType=VARCHAR},
      material_id = #{materialId,jdbcType=BIGINT},
      figure_number = #{figureNumber,jdbcType=VARCHAR},
      chart_version = #{chartVersion,jdbcType=VARCHAR},
      chart_version_new = #{chartVersionNew,jdbcType=VARCHAR},
      unit_weight = #{unitWeight,jdbcType=DECIMAL},
      unit_weight_new = #{unitWeightNew,jdbcType=DECIMAL},
      machining_part_type = #{machiningPartType,jdbcType=VARCHAR},
      machining_part_type_new = #{machiningPartTypeNew,jdbcType=VARCHAR},
      surface_handle = #{surfaceHandle,jdbcType=VARCHAR},
      surface_handle_new = #{surfaceHandleNew,jdbcType=VARCHAR},
      material = #{material,jdbcType=VARCHAR},
      material_new = #{materialNew,jdbcType=VARCHAR},
      or_spare_parts_mask = #{orSparePartsMask,jdbcType=VARCHAR},
      or_spare_parts_mask_new = #{orSparePartsMaskNew,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      brand_material_code = #{brandMaterialCode,jdbcType=VARCHAR},
      perchasing_leadtime = #{perchasingLeadtime,jdbcType=BIGINT},
      min_perchase_quantity = #{minPerchaseQuantity,jdbcType=BIGINT},
      min_package_quantity = #{minPackageQuantity,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      sync_status = #{syncStatus,jdbcType=TINYINT},
      sync_mes = #{syncMes,jdbcType=VARCHAR},
      attribute1 = #{attribute1,jdbcType=VARCHAR},
      attribute2 = #{attribute2,jdbcType=VARCHAR},
      attribute3 = #{attribute3,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      activity_code = #{activityCode,jdbcType=VARCHAR},
      material_attribute = #{materialAttribute,jdbcType=VARCHAR},
      inventory_type = #{inventoryType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>