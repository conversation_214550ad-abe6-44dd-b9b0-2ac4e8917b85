<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PurchaseOrderExtMapper">
    <sql id="queryCondition">
        <!-- 项目业务实体-->
        <if test="projectOuId != null and projectOuId != ''">
            AND purchaseOrder.ou_id = #{projectOuId, jdbcType=VARCHAR}
        </if>

        <!-- 模糊采购订单号-->
        <if test="fuzzyOrderNum != null and fuzzyOrderNum != ''">
            AND purchaseOrder.num like concat('%', #{fuzzyOrderNum, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 模糊供应商名称-->
        <if test="fuzzyVendorName != null and fuzzyVendorName != ''">
            AND purchaseOrder.vendor_name like concat('%', #{fuzzyVendorName, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 模糊供应商编码-->
        <if test="fuzzyVendorNum != null and fuzzyVendorNum != ''">
            AND purchaseOrder.vendor_num like concat('%', #{fuzzyVendorNum, jdbcType=VARCHAR}, '%')
        </if>

        <!-- 模糊供应商地点-->
        <if test="fuzzyVendorSiteCode != null and fuzzyVendorSiteCode != ''">
            AND purchaseOrder.vendor_site_code like concat('%', #{fuzzyVendorSiteCode, jdbcType=VARCHAR}, '%')
        </if>

        <!--创建日期检索-->
        <if test="orderCreateAtBegin != null and orderCreateAtEnd != null">
            AND (
                purchaseOrder.create_at &gt;= #{orderCreateAtBegin, jdbcType=TIMESTAMP}
                AND
                purchaseOrder.create_at &lt;= #{orderCreateAtEnd, jdbcType=TIMESTAMP}
            )
        </if>

        <!-- 订单状态-->
        <if test="orderStatus != null">
            AND purchaseOrder.order_status = #{orderStatus, jdbcType=INTEGER}
        </if>

        <!-- 同步状态-->
        <if test="syncStatus != null">
            AND purchaseOrder.sync_status = #{syncStatus, jdbcType=INTEGER}
        </if>

        <!-- 数据权限-->
        <if test="ouIdList != null and ouIdList.size() > 0">
            and purchaseOrder.ou_id in
            <foreach collection="ouIdList" item="ouId" index="index" open="(" separator="," close=")">
                #{ouId}
            </foreach>
        </if>

    </sql>

    <update id="batchUpdateStatus">
        update purchase_order
        set status = #{status}, order_status = #{orderStatus}
        where receipts_id = #{receiptsId}
    </update>

    <select id="selectListWithDetail"
            parameterType="com.midea.pam.common.ctc.dto.PurchaseOrderDto"
            resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDto">
        SELECT
            purchaseOrder.id,
            purchaseOrder.num,
            purchaseOrder.project_id as 'projectId',
            purchaseOrder.vendor_asl_id as 'vendorAslId',
            purchaseOrder.vendor_name as 'vendorName',
            purchaseOrder.vendor_site_code as 'vendorSiteCode',
            purchaseOrder.buyer_id as 'buyerId',
            purchaseOrder.buyer_name as 'buyerName',
            purchaseOrder.order_status as 'orderStatus',
            purchaseOrder.sync_status as 'syncStatus',
            purchaseOrder.deleted_flag as 'deletedFlag',
            purchaseOrder.create_by as 'createBy',
            purchaseOrder.create_at as 'createAt',
            purchaseOrder.update_by as 'updateBy',
            purchaseOrder.update_at as 'updateAt',
            purchaseOrder.ou_id as 'ouId',
            purchaseOrder.vendor_num as 'vendorNum'
        FROM
            `purchase_order` purchaseOrder
        WHERE
            purchaseOrder.deleted_flag = 0
            <include refid="queryCondition" />
        ORDER BY
            purchaseOrder.create_at desc
    </select>

    <select id="getDiscountMoneyByRequirementCode"
            resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto" parameterType="java.util.List">
        select
            po.id,
            po.order_status as orderStatus,
            po.num,
            pod.requirement_code as requirementCode,
            pod.wbs_summary_code as wbsSummaryCode,
            ifnull(pod.discount_money, 0)-ifnull(pod.cancel_num, 0)* ifnull(pod.discount_price, 0) as discountMoney,
            ifnull(po.conversion_rate, 1) as conversionRate
        from
            pam_ctc.purchase_order_detail pod
        inner join pam_ctc.purchase_order po on
            po.id = pod.purchase_order_id
        where
            pod.deleted_flag = 0
            and po.deleted_flag = 0
            and po.order_status in (1, 2, 3, 4, 5, 7, 11)
            and (pod.merge_rows = 0 or pod.merge_rows is null)
            and pod.requirement_code in
            <foreach collection="requirementCodeList" item="requirementCode" index="index" separator="," close=")" open="(">
                #{requirementCode}
            </foreach>
            <if test="wbsSummaryCodeList != null and wbsSummaryCodeList.size > 0">
                and pod.wbs_summary_code in
                <foreach collection="wbsSummaryCodeList" item="wbsSummaryCode" index="index" separator="," close=")" open="(">
                    #{wbsSummaryCode}
                </foreach>
            </if>
            <if test="num != null and num != ''">
                AND po.num <![CDATA[ <> ]]> #{num}
            </if>
        union all
        select
            po.id,
            po.order_status as orderStatus,
            po.num,
            pom.requirement_code as requirementCode,
            pom.wbs_summary_code as wbsSummaryCode,
            ifnull(pom.discount_money, 0) - ifnull(pom.cancel_num, 0)* ifnull(pom.discount_price, 0) as discountMoney,
            ifnull(po.conversion_rate, 1) as conversionRate
        from
            pam_ctc.purchase_order_merge pom
        inner join pam_ctc.purchase_order_detail pod on
            pod.id = pom.purchase_order_id
        inner join pam_ctc.purchase_order po on
            po.id = pod.purchase_order_id
        where
            pod.deleted_flag = 0
            and pom.deleted_flag = 0
            and po.deleted_flag = 0
            and pod.merge_rows = 1
            and po.order_status in (1, 2, 3, 4, 5, 7, 11)
            and pom.requirement_code in
            <foreach collection="requirementCodeList" item="requirementCode" index="index" separator="," close=")" open="(">
                #{requirementCode}
            </foreach>
            <if test="wbsSummaryCodeList != null and wbsSummaryCodeList.size > 0">
                and pom.wbs_summary_code in
                <foreach collection="wbsSummaryCodeList" item="wbsSummaryCode" index="index" separator="," close=")" open="(">
                    #{wbsSummaryCode}
                </foreach>
            </if>
            <if test="num != null and num != ''">
                AND po.num <![CDATA[ <> ]]> #{num}
            </if>
        union all
        select
            po.id,
            po.order_status as orderStatus,
            po.num,
            pod.requirement_code as requirementCode,
            pod.wbs_summary_code as wbsSummaryCode,
            ifnull(pod.discount_money, 0)-ifnull(pod.cancel_num, 0)* ifnull(pod.discount_price, 0) as discountMoney,
            ifnull(po.conversion_rate, 1) as conversionRate
        from
            pam_ctc.purchase_order_detail_change_history pod
        inner join pam_ctc.purchase_order_change_history poch on
            poch.id = pod.purchase_order_id
        inner join pam_ctc.purchase_order po on
            po.id = poch.origin_id
        inner join pam_ctc.purchase_order_change_record pocr on
            pocr.id = poch.record_id
            and pocr.id = pod.record_id
        where
            pod.deleted_flag = 0
            and po.deleted_flag = 0
            and poch.deleted_flag = 0
            and pocr.deleted_flag = 0
            and pod.history_type = 1
            and poch.history_type = 1
            and pod.origin_id is null
            and pocr.status = 2
            and (pod.merge_rows = 0 or pod.merge_rows is null)
            and pod.requirement_code in
            <foreach collection="requirementCodeList" item="requirementCode" index="index" separator="," close=")" open="(">
                #{requirementCode}
            </foreach>
            <if test="wbsSummaryCodeList != null and wbsSummaryCodeList.size > 0">
                and pod.wbs_summary_code in
                <foreach collection="wbsSummaryCodeList" item="wbsSummaryCode" index="index" separator="," close=")" open="(">
                    #{wbsSummaryCode}
                </foreach>
            </if>
            <if test="num != null and num != ''">
                AND po.num <![CDATA[ <> ]]> #{num}
            </if>
        union all
        select
            po.id,
            po.order_status as orderStatus,
            po.num,
            pom.requirement_code as requirementCode,
            pom.wbs_summary_code as wbsSummaryCode,
            ifnull(pom.discount_money, 0) - ifnull(pom.cancel_num, 0)* ifnull(pom.discount_price, 0) as discountMoney,
            ifnull(po.conversion_rate, 1) as conversionRate
        from
            pam_ctc.purchase_order_merge_change_history pom
        inner join pam_ctc.purchase_order_detail_change_history pod on
            pod.id = pom.purchase_order_id
        inner join pam_ctc.purchase_order_change_history poch on
            poch.id = pod.purchase_order_id
        inner join pam_ctc.purchase_order po on
            po.id = poch.origin_id
        inner join pam_ctc.purchase_order_change_record pocr on
            pocr.id = poch.record_id
            and pocr.id = pom.record_id
            and pocr.id = pod.record_id
        where
            pod.deleted_flag = 0
            and pom.deleted_flag = 0
            and po.deleted_flag = 0
            and poch.deleted_flag = 0
            and pod.history_type = 1
            and poch.history_type = 1
            and pom.history_type = 1
            and pocr.status = 2
            and pod.origin_id is null
            and pom.origin_id is null
            and pod.merge_rows = 1
            and pom.requirement_code in
            <foreach collection="requirementCodeList" item="requirementCode" index="index" separator="," close=")" open="(">
                #{requirementCode}
            </foreach>
            <if test="wbsSummaryCodeList != null and wbsSummaryCodeList.size > 0">
                and pom.wbs_summary_code in
                <foreach collection="wbsSummaryCodeList" item="wbsSummaryCode" index="index" separator="," close=")" open="(">
                    #{wbsSummaryCode}
                </foreach>
            </if>
            <if test="num != null and num != ''">
                AND po.num <![CDATA[ <> ]]> #{num}
            </if>
    </select>

    <select id="getDiscountMoneyByProjectWbsReceiptsIdList"
            resultType="com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto" parameterType="java.util.List">
        select
        po.id,
        po.order_status as orderStatus,
        po.num,
        pod.requirement_code as requirementCode,
        pod.project_wbs_receipts_id as projectWbsReceiptsId,
        pod.wbs_summary_code as wbsSummaryCode,
        ifnull(pod.discount_money, 0)-ifnull(pod.cancel_num, 0)* ifnull(pod.discount_price, 0) as discountMoney,
        ifnull(po.conversion_rate, 1) as conversionRate
        from
        pam_ctc.purchase_order_detail pod
        inner join pam_ctc.purchase_order po on
        po.id = pod.purchase_order_id
        where
        pod.deleted_flag = 0
        and po.deleted_flag = 0
        and po.order_status in (1, 2, 3, 4, 5, 7, 11)
        and (pod.merge_rows = 0 or pod.merge_rows is null)
        and pod.project_wbs_receipts_id in
        <foreach collection="projectWbsReceiptsIdList" item="projectWbsReceiptsId" index="index" separator="," close=")" open="(">
            #{projectWbsReceiptsId}
        </foreach>
        <if test="wbsSummaryCodeList != null and wbsSummaryCodeList.size > 0">
            and pod.wbs_summary_code in
            <foreach collection="wbsSummaryCodeList" item="wbsSummaryCode" index="index" separator="," close=")" open="(">
                #{wbsSummaryCode}
            </foreach>
        </if>
        <if test="num != null and num != ''">
            AND po.num <![CDATA[ <> ]]> #{num}
        </if>
        union all
        select
        po.id,
        po.order_status as orderStatus,
        po.num,
        pom.requirement_code as requirementCode,
        pod.project_wbs_receipts_id as projectWbsReceiptsId,
        pom.wbs_summary_code as wbsSummaryCode,
        ifnull(pom.discount_money, 0) - ifnull(pom.cancel_num, 0)* ifnull(pom.discount_price, 0) as discountMoney,
        ifnull(po.conversion_rate, 1) as conversionRate
        from
        pam_ctc.purchase_order_merge pom
        inner join pam_ctc.purchase_order_detail pod on
        pod.id = pom.purchase_order_id
        inner join pam_ctc.purchase_order po on
        po.id = pod.purchase_order_id
        where
        pod.deleted_flag = 0
        and pom.deleted_flag = 0
        and po.deleted_flag = 0
        and pod.merge_rows = 1
        and po.order_status in (1, 2, 3, 4, 5, 7, 11)
        and pod.project_wbs_receipts_id in
        <foreach collection="projectWbsReceiptsIdList" item="projectWbsReceiptsId" index="index" separator="," close=")" open="(">
            #{projectWbsReceiptsId}
        </foreach>
        <if test="wbsSummaryCodeList != null and wbsSummaryCodeList.size > 0">
            and pom.wbs_summary_code in
            <foreach collection="wbsSummaryCodeList" item="wbsSummaryCode" index="index" separator="," close=")" open="(">
                #{wbsSummaryCode}
            </foreach>
        </if>
        <if test="num != null and num != ''">
            AND po.num <![CDATA[ <> ]]> #{num}
        </if>
        union all
        select
        po.id,
        po.order_status as orderStatus,
        po.num,
        pod.requirement_code as requirementCode,
        pod.project_wbs_receipts_id as projectWbsReceiptsId,
        pod.wbs_summary_code as wbsSummaryCode,
        ifnull(pod.discount_money, 0)-ifnull(pod.cancel_num, 0)* ifnull(pod.discount_price, 0) as discountMoney,
        ifnull(po.conversion_rate, 1) as conversionRate
        from
        pam_ctc.purchase_order_detail_change_history pod
        inner join pam_ctc.purchase_order_change_history poch on
        poch.id = pod.purchase_order_id
        inner join pam_ctc.purchase_order po on
        po.id = poch.origin_id
        inner join pam_ctc.purchase_order_change_record pocr on
        pocr.id = poch.record_id
        and pocr.id = pod.record_id
        where
        pod.deleted_flag = 0
        and po.deleted_flag = 0
        and poch.deleted_flag = 0
        and pocr.deleted_flag = 0
        and pod.history_type = 1
        and poch.history_type = 1
        and pod.origin_id is null
        and pocr.status = 2
        and (pod.merge_rows = 0 or pod.merge_rows is null)
        and pod.project_wbs_receipts_id in
        <foreach collection="projectWbsReceiptsIdList" item="projectWbsReceiptsId" index="index" separator="," close=")" open="(">
            #{projectWbsReceiptsId}
        </foreach>
        <if test="wbsSummaryCodeList != null and wbsSummaryCodeList.size > 0">
            and pod.wbs_summary_code in
            <foreach collection="wbsSummaryCodeList" item="wbsSummaryCode" index="index" separator="," close=")" open="(">
                #{wbsSummaryCode}
            </foreach>
        </if>
        <if test="num != null and num != ''">
            AND po.num <![CDATA[ <> ]]> #{num}
        </if>
        union all
        select
        po.id,
        po.order_status as orderStatus,
        po.num,
        pom.requirement_code as requirementCode,
        pod.project_wbs_receipts_id as projectWbsReceiptsId,
        pom.wbs_summary_code as wbsSummaryCode,
        ifnull(pom.discount_money, 0) - ifnull(pom.cancel_num, 0)* ifnull(pom.discount_price, 0) as discountMoney,
        ifnull(po.conversion_rate, 1) as conversionRate
        from
        pam_ctc.purchase_order_merge_change_history pom
        inner join pam_ctc.purchase_order_detail_change_history pod on
        pod.id = pom.purchase_order_id
        inner join pam_ctc.purchase_order_change_history poch on
        poch.id = pod.purchase_order_id
        inner join pam_ctc.purchase_order po on
        po.id = poch.origin_id
        inner join pam_ctc.purchase_order_change_record pocr on
        pocr.id = poch.record_id
        and pocr.id = pom.record_id
        and pocr.id = pod.record_id
        where
        pod.deleted_flag = 0
        and pom.deleted_flag = 0
        and po.deleted_flag = 0
        and poch.deleted_flag = 0
        and pod.history_type = 1
        and poch.history_type = 1
        and pom.history_type = 1
        and pocr.status = 2
        and pod.origin_id is null
        and pom.origin_id is null
        and pod.merge_rows = 1
        and pod.project_wbs_receipts_id in
        <foreach collection="projectWbsReceiptsIdList" item="projectWbsReceiptsId" index="index" separator="," close=")" open="(">
            #{projectWbsReceiptsId}
        </foreach>
        <if test="wbsSummaryCodeList != null and wbsSummaryCodeList.size > 0">
            and pom.wbs_summary_code in
            <foreach collection="wbsSummaryCodeList" item="wbsSummaryCode" index="index" separator="," close=")" open="(">
                #{wbsSummaryCode}
            </foreach>
        </if>
        <if test="num != null and num != ''">
            AND po.num <![CDATA[ <> ]]> #{num}
        </if>
    </select>

    <select id="getVersionHistoryList" resultType="com.midea.pam.common.ctc.dto.PurchaseOrderChangeRecordDto">
        select
            pocr.id,
            pocr.change_type as changeType,
            pocr.change_name as createByName,
            pocr.create_at as createAt
        from
            pam_ctc.purchase_order_change_record pocr
        where
        pocr.id in ( select record_id from pam_ctc.purchase_order_change_history where origin_id = #{purchaseOrderId} and history_type  = 1 and deleted_flag  = 0)
        and pocr.deleted_flag  = 0 and pocr.status = 3
        order by pocr.create_at
    </select>

    <select id="getLatestChangRecordId" resultType="java.lang.Long">
        select
            poch.record_id
        from
            pam_ctc.purchase_order_change_history poch
        inner join pam_ctc.purchase_order_change_record pocr on
            poch.record_id = pocr.id
        where
            pocr.deleted_flag = 0
            and poch.deleted_flag = 0
            and poch.origin_id = #{purchaseOrderId}
        order by
            pocr.create_at desc
        limit 1
    </select>

    <select id="getDdInstanceId" parameterType="java.lang.Long" resultType="java.lang.String">
        select
            fi.fd_instance_id
        from
        pam_ctc.purchase_order po
        inner join pam_system.form_instance fi on
            po.receipts_id = fi.form_instance_id
            and fi.form_url = 'purchaseOrderApp'
            and fi.deleted_flag = 0
        where
            po.id = #{id}
            and po.deleted_flag = 0 limit 1
    </select>

    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.PurchaseOrder">
        insert into purchase_order (id, num, project_id,
        vendor_asl_id, vendor_name, vendor_site_code,
        buyer_id, buyer_name, order_status,
        sync_status, deleted_flag, erp_message,
        create_by, create_at, update_by,
        update_at, currency, conversion_type,
        conversion_date, conversion_rate, source,
        status, erp_order_status, dispatch_is,
        receipts_id, promised_date, tracking_date,
        org_id, org_name, vendor_num,
        payment_method_id, payment_method_name, payment_way,
        delivery_type, delivery_clause, tax_id,
        tax_rate, remark, pricing_type,
        currency_code, exchange_rate, approve_info,
        erp_buyer_id, init_flag, ou_id,secondary_inventory,secondary_inventory_name,
        contract_terms, over_budget_des,approval_time) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.num,jdbcType=VARCHAR}, #{item.projectId,jdbcType=BIGINT},
            #{item.vendorAslId,jdbcType=BIGINT}, #{item.vendorName,jdbcType=VARCHAR}, #{item.vendorSiteCode,jdbcType=VARCHAR},
            #{item.buyerId,jdbcType=BIGINT}, #{item.buyerName,jdbcType=VARCHAR}, #{item.orderStatus,jdbcType=INTEGER},
            #{item.syncStatus,jdbcType=INTEGER}, #{item.deletedFlag,jdbcType=TINYINT}, #{item.erpMessage,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=BIGINT}, #{item.createAt,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=BIGINT},
            #{item.updateAt,jdbcType=TIMESTAMP}, #{item.currency,jdbcType=VARCHAR}, #{item.conversionType,jdbcType=VARCHAR},
            #{item.conversionDate,jdbcType=TIMESTAMP}, #{item.conversionRate,jdbcType=DECIMAL}, #{item.source,jdbcType=VARCHAR},
            #{item.status,jdbcType=TINYINT}, #{item.erpOrderStatus,jdbcType=INTEGER}, #{item.dispatchIs,jdbcType=BIGINT},
            #{item.receiptsId,jdbcType=BIGINT}, #{item.promisedDate,jdbcType=TIMESTAMP}, #{item.trackingDate,jdbcType=TIMESTAMP},
            #{item.orgId,jdbcType=BIGINT}, #{item.orgName,jdbcType=VARCHAR}, #{item.vendorNum,jdbcType=VARCHAR},
            #{item.paymentMethodId,jdbcType=BIGINT}, #{item.paymentMethodName,jdbcType=VARCHAR}, #{item.paymentWay,jdbcType=VARCHAR},
            #{item.deliveryType,jdbcType=VARCHAR}, #{item.deliveryClause,jdbcType=VARCHAR}, #{item.taxId,jdbcType=BIGINT},
            #{item.taxRate,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.pricingType,jdbcType=TINYINT},
            #{item.currencyCode,jdbcType=VARCHAR}, #{item.exchangeRate,jdbcType=DECIMAL}, #{item.approveInfo,jdbcType=VARCHAR},
            #{item.erpBuyerId,jdbcType=BIGINT}, #{item.initFlag,jdbcType=INTEGER}, #{item.ouId,jdbcType=BIGINT},#{item.secondaryInventory,jdbcType=LONGVARCHAR},#{item.secondaryInventoryName,jdbcType=LONGVARCHAR},
            #{item.contractTerms,jdbcType=LONGVARCHAR}, #{item.overBudgetDes,jdbcType=LONGVARCHAR},#{item.approvalTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update purchase_order
            <set>
                <if test="item.num != null">
                    num = #{item.num,jdbcType=VARCHAR},
                </if>
                <if test="item.projectId != null">
                    project_id = #{item.projectId,jdbcType=BIGINT},
                </if>
                <if test="item.vendorAslId != null">
                    vendor_asl_id = #{item.vendorAslId,jdbcType=BIGINT},
                </if>
                <if test="item.vendorName != null">
                    vendor_name = #{item.vendorName,jdbcType=VARCHAR},
                </if>
                <if test="item.vendorSiteCode != null">
                    vendor_site_code = #{item.vendorSiteCode,jdbcType=VARCHAR},
                </if>
                <if test="item.buyerId != null">
                    buyer_id = #{item.buyerId,jdbcType=BIGINT},
                </if>
                <if test="item.buyerName != null">
                    buyer_name = #{item.buyerName,jdbcType=VARCHAR},
                </if>
                <if test="item.orderStatus != null">
                    order_status = #{item.orderStatus,jdbcType=INTEGER},
                </if>
                <if test="item.syncStatus != null">
                    sync_status = #{item.syncStatus,jdbcType=INTEGER},
                </if>
                <if test="item.deletedFlag != null">
                    deleted_flag = #{item.deletedFlag,jdbcType=TINYINT},
                </if>
                <if test="item.erpMessage != null">
                    erp_message = #{item.erpMessage,jdbcType=VARCHAR},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy,jdbcType=BIGINT},
                </if>
                <if test="item.createAt != null">
                    create_at = #{item.createAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy,jdbcType=BIGINT},
                </if>
                <if test="item.updateAt != null">
                    update_at = #{item.updateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.currency != null">
                    currency = #{item.currency,jdbcType=VARCHAR},
                </if>
                <if test="item.conversionType != null">
                    conversion_type = #{item.conversionType,jdbcType=VARCHAR},
                </if>
                <if test="item.conversionDate != null">
                    conversion_date = #{item.conversionDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.conversionRate != null">
                    conversion_rate = #{item.conversionRate,jdbcType=DECIMAL},
                </if>
                <if test="item.source != null">
                    source = #{item.source,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=TINYINT},
                </if>
                <if test="item.erpOrderStatus != null">
                    erp_order_status = #{item.erpOrderStatus,jdbcType=INTEGER},
                </if>
                <if test="item.dispatchIs != null">
                    dispatch_is = #{item.dispatchIs,jdbcType=BIGINT},
                </if>
                <if test="item.receiptsId != null">
                    receipts_id = #{item.receiptsId,jdbcType=BIGINT},
                </if>
                <if test="item.promisedDate != null">
                    promised_date = #{item.promisedDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.trackingDate != null">
                    tracking_date = #{item.trackingDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.orgId != null">
                    org_id = #{item.orgId,jdbcType=BIGINT},
                </if>
                <if test="item.orgName != null">
                    org_name = #{item.orgName,jdbcType=VARCHAR},
                </if>
                <if test="item.vendorNum != null">
                    vendor_num = #{item.vendorNum,jdbcType=VARCHAR},
                </if>
                <if test="item.paymentMethodId != null">
                    payment_method_id = #{item.paymentMethodId,jdbcType=BIGINT},
                </if>
                <if test="item.paymentMethodName != null">
                    payment_method_name = #{item.paymentMethodName,jdbcType=VARCHAR},
                </if>
                <if test="item.paymentWay != null">
                    payment_way = #{item.paymentWay,jdbcType=VARCHAR},
                </if>
                <if test="item.deliveryType != null">
                    delivery_type = #{item.deliveryType,jdbcType=VARCHAR},
                </if>
                <if test="item.deliveryClause != null">
                    delivery_clause = #{item.deliveryClause,jdbcType=VARCHAR},
                </if>
                <if test="item.taxId != null">
                    tax_id = #{item.taxId,jdbcType=BIGINT},
                </if>
                <if test="item.taxRate != null">
                    tax_rate = #{item.taxRate,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.pricingType != null">
                    pricing_type = #{item.pricingType,jdbcType=TINYINT},
                </if>
                <if test="item.currencyCode != null">
                    currency_code = #{item.currencyCode,jdbcType=VARCHAR},
                </if>
                <if test="item.exchangeRate != null">
                    exchange_rate = #{item.exchangeRate,jdbcType=DECIMAL},
                </if>
                <if test="item.approveInfo != null">
                    approve_info = #{item.approveInfo,jdbcType=VARCHAR},
                </if>
                <if test="item.erpBuyerId != null">
                    erp_buyer_id = #{item.erpBuyerId,jdbcType=BIGINT},
                </if>
                <if test="item.initFlag != null">
                    init_flag = #{item.initFlag,jdbcType=INTEGER},
                </if>
                <if test="item.ouId != null">
                    ou_id = #{item.ouId,jdbcType=BIGINT},
                </if>
                <if test="item.contractTerms != null">
                    contract_terms = #{item.contractTerms,jdbcType=LONGVARCHAR},
                </if>
                <if test="item.overBudgetDes != null">
                    over_budget_des = #{item.overBudgetDes,jdbcType=LONGVARCHAR},
                </if>
                <if test="item.approvalTime !=null">
                    approval_time = #{item.approvalTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

</mapper>