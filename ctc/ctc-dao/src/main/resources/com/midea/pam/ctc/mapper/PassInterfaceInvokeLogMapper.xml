<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PassInterfaceInvokeLogMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.PassInterfaceInvokeLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="service_system" jdbcType="VARCHAR" property="serviceSystem" />
    <result column="invoke_url" jdbcType="VARCHAR" property="invokeUrl" />
    <result column="invoke_status" jdbcType="TINYINT" property="invokeStatus" />
    <result column="invoke_result" jdbcType="VARCHAR" property="invokeResult" />
    <result column="invoke_time" jdbcType="TIMESTAMP" property="invokeTime" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.midea.pam.common.ctc.entity.PassInterfaceInvokeLog">
    <result column="invoke_params" jdbcType="LONGVARCHAR" property="invokeParams" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, apply_no, serial_no, service_system, invoke_url, invoke_status, invoke_result, 
    invoke_time, create_by, create_at, update_by, update_at, deleted_flag
  </sql>
  <sql id="Blob_Column_List">
    invoke_params
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.PassInterfaceInvokeLogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from pass_interface_invoke_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.PassInterfaceInvokeLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pass_interface_invoke_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from pass_interface_invoke_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pass_interface_invoke_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.PassInterfaceInvokeLog">
    insert into pass_interface_invoke_log (id, apply_no, serial_no, 
      service_system, invoke_url, invoke_status, 
      invoke_result, invoke_time, create_by, 
      create_at, update_by, update_at, 
      deleted_flag, invoke_params)
    values (#{id,jdbcType=BIGINT}, #{applyNo,jdbcType=VARCHAR}, #{serialNo,jdbcType=VARCHAR}, 
      #{serviceSystem,jdbcType=VARCHAR}, #{invokeUrl,jdbcType=VARCHAR}, #{invokeStatus,jdbcType=TINYINT}, 
      #{invokeResult,jdbcType=VARCHAR}, #{invokeTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=BIGINT}, 
      #{createAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, 
      #{deletedFlag,jdbcType=TINYINT}, #{invokeParams,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.PassInterfaceInvokeLog">
    insert into pass_interface_invoke_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="applyNo != null">
        apply_no,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="serviceSystem != null">
        service_system,
      </if>
      <if test="invokeUrl != null">
        invoke_url,
      </if>
      <if test="invokeStatus != null">
        invoke_status,
      </if>
      <if test="invokeResult != null">
        invoke_result,
      </if>
      <if test="invokeTime != null">
        invoke_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="invokeParams != null">
        invoke_params,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="applyNo != null">
        #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceSystem != null">
        #{serviceSystem,jdbcType=VARCHAR},
      </if>
      <if test="invokeUrl != null">
        #{invokeUrl,jdbcType=VARCHAR},
      </if>
      <if test="invokeStatus != null">
        #{invokeStatus,jdbcType=TINYINT},
      </if>
      <if test="invokeResult != null">
        #{invokeResult,jdbcType=VARCHAR},
      </if>
      <if test="invokeTime != null">
        #{invokeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="invokeParams != null">
        #{invokeParams,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.PassInterfaceInvokeLogExample" resultType="java.lang.Long">
    select count(*) from pass_interface_invoke_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.PassInterfaceInvokeLog">
    update pass_interface_invoke_log
    <set>
      <if test="applyNo != null">
        apply_no = #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceSystem != null">
        service_system = #{serviceSystem,jdbcType=VARCHAR},
      </if>
      <if test="invokeUrl != null">
        invoke_url = #{invokeUrl,jdbcType=VARCHAR},
      </if>
      <if test="invokeStatus != null">
        invoke_status = #{invokeStatus,jdbcType=TINYINT},
      </if>
      <if test="invokeResult != null">
        invoke_result = #{invokeResult,jdbcType=VARCHAR},
      </if>
      <if test="invokeTime != null">
        invoke_time = #{invokeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="invokeParams != null">
        invoke_params = #{invokeParams,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.PassInterfaceInvokeLog">
    update pass_interface_invoke_log
    set apply_no = #{applyNo,jdbcType=VARCHAR},
      serial_no = #{serialNo,jdbcType=VARCHAR},
      service_system = #{serviceSystem,jdbcType=VARCHAR},
      invoke_url = #{invokeUrl,jdbcType=VARCHAR},
      invoke_status = #{invokeStatus,jdbcType=TINYINT},
      invoke_result = #{invokeResult,jdbcType=VARCHAR},
      invoke_time = #{invokeTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      invoke_params = #{invokeParams,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.PassInterfaceInvokeLog">
    update pass_interface_invoke_log
    set apply_no = #{applyNo,jdbcType=VARCHAR},
      serial_no = #{serialNo,jdbcType=VARCHAR},
      service_system = #{serviceSystem,jdbcType=VARCHAR},
      invoke_url = #{invokeUrl,jdbcType=VARCHAR},
      invoke_status = #{invokeStatus,jdbcType=TINYINT},
      invoke_result = #{invokeResult,jdbcType=VARCHAR},
      invoke_time = #{invokeTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>