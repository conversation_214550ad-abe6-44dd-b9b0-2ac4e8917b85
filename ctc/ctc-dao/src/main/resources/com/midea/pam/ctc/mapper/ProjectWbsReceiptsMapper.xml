<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.ProjectWbsReceiptsMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.ProjectWbsReceipts">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="requirement_code" jdbcType="VARCHAR" property="requirementCode" />
    <result column="requirement_status" jdbcType="INTEGER" property="requirementStatus" />
    <result column="requirement_type" jdbcType="INTEGER" property="requirementType" />
    <result column="confirm_mode" jdbcType="INTEGER" property="confirmMode" />
    <result column="handle_by" jdbcType="BIGINT" property="handleBy" />
    <result column="handle_name" jdbcType="VARCHAR" property="handleName" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="handle_at" jdbcType="TIMESTAMP" property="handleAt" />
    <result column="design_release_lot_number" jdbcType="VARCHAR" property="designReleaseLotNumber" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="producer_id" jdbcType="BIGINT" property="producerId" />
    <result column="producer_name" jdbcType="VARCHAR" property="producerName" />
    <result column="process_name" jdbcType="VARCHAR" property="processName" />
    <result column="project_submit" jdbcType="TINYINT" property="projectSubmit" />
    <result column="unit_id" jdbcType="BIGINT" property="unitId" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="renson_remark" jdbcType="VARCHAR" property="rensonRemark" />
    <result column="submit_reason" jdbcType="VARCHAR" property="submitReason" />
    <result column="rel_receipts_id" jdbcType="BIGINT" property="relReceiptsId" />
    <result column="init" jdbcType="TINYINT" property="init" />
    <result column="init_sequence" jdbcType="VARCHAR" property="initSequence" />
    <result column="buyer_id" jdbcType="BIGINT" property="buyerId" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="web_type" jdbcType="INTEGER" property="webType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.midea.pam.common.ctc.entity.ProjectWbsReceipts">
    <result column="batch_upload_paths" jdbcType="LONGVARCHAR" property="batchUploadPaths" />
    <result column="batch_upload_path_ids" jdbcType="LONGVARCHAR" property="batchUploadPathIds" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, project_code, project_name, requirement_code, requirement_status, 
    requirement_type, confirm_mode, handle_by, handle_name, reason, handle_at, design_release_lot_number, 
    remark, producer_id, producer_name, process_name, project_submit, unit_id, create_by, 
    create_at, update_by, update_at, deleted_flag, version, renson_remark, submit_reason, 
    rel_receipts_id, init, init_sequence, buyer_id, buyer_name, web_type
  </sql>
  <sql id="Blob_Column_List">
    batch_upload_paths, batch_upload_path_ids
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project_wbs_receipts
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_wbs_receipts
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project_wbs_receipts
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_wbs_receipts
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsReceipts">
    insert into project_wbs_receipts (id, project_id, project_code, 
      project_name, requirement_code, requirement_status, 
      requirement_type, confirm_mode, handle_by, 
      handle_name, reason, handle_at, 
      design_release_lot_number, remark, producer_id, 
      producer_name, process_name, project_submit, 
      unit_id, create_by, create_at, 
      update_by, update_at, deleted_flag, 
      version, renson_remark, submit_reason, 
      rel_receipts_id, init, init_sequence, 
      buyer_id, buyer_name, web_type, 
      batch_upload_paths, batch_upload_path_ids
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{projectCode,jdbcType=VARCHAR}, 
      #{projectName,jdbcType=VARCHAR}, #{requirementCode,jdbcType=VARCHAR}, #{requirementStatus,jdbcType=INTEGER}, 
      #{requirementType,jdbcType=INTEGER}, #{confirmMode,jdbcType=INTEGER}, #{handleBy,jdbcType=BIGINT}, 
      #{handleName,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, #{handleAt,jdbcType=TIMESTAMP}, 
      #{designReleaseLotNumber,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{producerId,jdbcType=BIGINT}, 
      #{producerName,jdbcType=VARCHAR}, #{processName,jdbcType=VARCHAR}, #{projectSubmit,jdbcType=TINYINT}, 
      #{unitId,jdbcType=BIGINT}, #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{deletedFlag,jdbcType=TINYINT}, 
      #{version,jdbcType=BIGINT}, #{rensonRemark,jdbcType=VARCHAR}, #{submitReason,jdbcType=VARCHAR}, 
      #{relReceiptsId,jdbcType=BIGINT}, #{init,jdbcType=TINYINT}, #{initSequence,jdbcType=VARCHAR}, 
      #{buyerId,jdbcType=BIGINT}, #{buyerName,jdbcType=VARCHAR}, #{webType,jdbcType=INTEGER}, 
      #{batchUploadPaths,jdbcType=LONGVARCHAR}, #{batchUploadPathIds,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsReceipts">
    insert into project_wbs_receipts
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectCode != null">
        project_code,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="requirementCode != null">
        requirement_code,
      </if>
      <if test="requirementStatus != null">
        requirement_status,
      </if>
      <if test="requirementType != null">
        requirement_type,
      </if>
      <if test="confirmMode != null">
        confirm_mode,
      </if>
      <if test="handleBy != null">
        handle_by,
      </if>
      <if test="handleName != null">
        handle_name,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="handleAt != null">
        handle_at,
      </if>
      <if test="designReleaseLotNumber != null">
        design_release_lot_number,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="producerId != null">
        producer_id,
      </if>
      <if test="producerName != null">
        producer_name,
      </if>
      <if test="processName != null">
        process_name,
      </if>
      <if test="projectSubmit != null">
        project_submit,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="rensonRemark != null">
        renson_remark,
      </if>
      <if test="submitReason != null">
        submit_reason,
      </if>
      <if test="relReceiptsId != null">
        rel_receipts_id,
      </if>
      <if test="init != null">
        init,
      </if>
      <if test="initSequence != null">
        init_sequence,
      </if>
      <if test="buyerId != null">
        buyer_id,
      </if>
      <if test="buyerName != null">
        buyer_name,
      </if>
      <if test="webType != null">
        web_type,
      </if>
      <if test="batchUploadPaths != null">
        batch_upload_paths,
      </if>
      <if test="batchUploadPathIds != null">
        batch_upload_path_ids,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="requirementCode != null">
        #{requirementCode,jdbcType=VARCHAR},
      </if>
      <if test="requirementStatus != null">
        #{requirementStatus,jdbcType=INTEGER},
      </if>
      <if test="requirementType != null">
        #{requirementType,jdbcType=INTEGER},
      </if>
      <if test="confirmMode != null">
        #{confirmMode,jdbcType=INTEGER},
      </if>
      <if test="handleBy != null">
        #{handleBy,jdbcType=BIGINT},
      </if>
      <if test="handleName != null">
        #{handleName,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="handleAt != null">
        #{handleAt,jdbcType=TIMESTAMP},
      </if>
      <if test="designReleaseLotNumber != null">
        #{designReleaseLotNumber,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="producerId != null">
        #{producerId,jdbcType=BIGINT},
      </if>
      <if test="producerName != null">
        #{producerName,jdbcType=VARCHAR},
      </if>
      <if test="processName != null">
        #{processName,jdbcType=VARCHAR},
      </if>
      <if test="projectSubmit != null">
        #{projectSubmit,jdbcType=TINYINT},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="rensonRemark != null">
        #{rensonRemark,jdbcType=VARCHAR},
      </if>
      <if test="submitReason != null">
        #{submitReason,jdbcType=VARCHAR},
      </if>
      <if test="relReceiptsId != null">
        #{relReceiptsId,jdbcType=BIGINT},
      </if>
      <if test="init != null">
        #{init,jdbcType=TINYINT},
      </if>
      <if test="initSequence != null">
        #{initSequence,jdbcType=VARCHAR},
      </if>
      <if test="buyerId != null">
        #{buyerId,jdbcType=BIGINT},
      </if>
      <if test="buyerName != null">
        #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="webType != null">
        #{webType,jdbcType=INTEGER},
      </if>
      <if test="batchUploadPaths != null">
        #{batchUploadPaths,jdbcType=LONGVARCHAR},
      </if>
      <if test="batchUploadPathIds != null">
        #{batchUploadPathIds,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample" resultType="java.lang.Long">
    select count(*) from project_wbs_receipts
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsReceipts">
    update project_wbs_receipts
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="projectCode != null">
        project_code = #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="requirementCode != null">
        requirement_code = #{requirementCode,jdbcType=VARCHAR},
      </if>
      <if test="requirementStatus != null">
        requirement_status = #{requirementStatus,jdbcType=INTEGER},
      </if>
      <if test="requirementType != null">
        requirement_type = #{requirementType,jdbcType=INTEGER},
      </if>
      <if test="confirmMode != null">
        confirm_mode = #{confirmMode,jdbcType=INTEGER},
      </if>
      <if test="handleBy != null">
        handle_by = #{handleBy,jdbcType=BIGINT},
      </if>
      <if test="handleName != null">
        handle_name = #{handleName,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="handleAt != null">
        handle_at = #{handleAt,jdbcType=TIMESTAMP},
      </if>
      <if test="designReleaseLotNumber != null">
        design_release_lot_number = #{designReleaseLotNumber,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="producerId != null">
        producer_id = #{producerId,jdbcType=BIGINT},
      </if>
      <if test="producerName != null">
        producer_name = #{producerName,jdbcType=VARCHAR},
      </if>
      <if test="processName != null">
        process_name = #{processName,jdbcType=VARCHAR},
      </if>
      <if test="projectSubmit != null">
        project_submit = #{projectSubmit,jdbcType=TINYINT},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="rensonRemark != null">
        renson_remark = #{rensonRemark,jdbcType=VARCHAR},
      </if>
      <if test="submitReason != null">
        submit_reason = #{submitReason,jdbcType=VARCHAR},
      </if>
      <if test="relReceiptsId != null">
        rel_receipts_id = #{relReceiptsId,jdbcType=BIGINT},
      </if>
      <if test="init != null">
        init = #{init,jdbcType=TINYINT},
      </if>
      <if test="initSequence != null">
        init_sequence = #{initSequence,jdbcType=VARCHAR},
      </if>
      <if test="buyerId != null">
        buyer_id = #{buyerId,jdbcType=BIGINT},
      </if>
      <if test="buyerName != null">
        buyer_name = #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="webType != null">
        web_type = #{webType,jdbcType=INTEGER},
      </if>
      <if test="batchUploadPaths != null">
        batch_upload_paths = #{batchUploadPaths,jdbcType=LONGVARCHAR},
      </if>
      <if test="batchUploadPathIds != null">
        batch_upload_path_ids = #{batchUploadPathIds,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsReceipts">
    update project_wbs_receipts
    set project_id = #{projectId,jdbcType=BIGINT},
      project_code = #{projectCode,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      requirement_code = #{requirementCode,jdbcType=VARCHAR},
      requirement_status = #{requirementStatus,jdbcType=INTEGER},
      requirement_type = #{requirementType,jdbcType=INTEGER},
      confirm_mode = #{confirmMode,jdbcType=INTEGER},
      handle_by = #{handleBy,jdbcType=BIGINT},
      handle_name = #{handleName,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      handle_at = #{handleAt,jdbcType=TIMESTAMP},
      design_release_lot_number = #{designReleaseLotNumber,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      producer_id = #{producerId,jdbcType=BIGINT},
      producer_name = #{producerName,jdbcType=VARCHAR},
      process_name = #{processName,jdbcType=VARCHAR},
      project_submit = #{projectSubmit,jdbcType=TINYINT},
      unit_id = #{unitId,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      version = #{version,jdbcType=BIGINT},
      renson_remark = #{rensonRemark,jdbcType=VARCHAR},
      submit_reason = #{submitReason,jdbcType=VARCHAR},
      rel_receipts_id = #{relReceiptsId,jdbcType=BIGINT},
      init = #{init,jdbcType=TINYINT},
      init_sequence = #{initSequence,jdbcType=VARCHAR},
      buyer_id = #{buyerId,jdbcType=BIGINT},
      buyer_name = #{buyerName,jdbcType=VARCHAR},
      web_type = #{webType,jdbcType=INTEGER},
      batch_upload_paths = #{batchUploadPaths,jdbcType=LONGVARCHAR},
      batch_upload_path_ids = #{batchUploadPathIds,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.ProjectWbsReceipts">
    update project_wbs_receipts
    set project_id = #{projectId,jdbcType=BIGINT},
      project_code = #{projectCode,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      requirement_code = #{requirementCode,jdbcType=VARCHAR},
      requirement_status = #{requirementStatus,jdbcType=INTEGER},
      requirement_type = #{requirementType,jdbcType=INTEGER},
      confirm_mode = #{confirmMode,jdbcType=INTEGER},
      handle_by = #{handleBy,jdbcType=BIGINT},
      handle_name = #{handleName,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      handle_at = #{handleAt,jdbcType=TIMESTAMP},
      design_release_lot_number = #{designReleaseLotNumber,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      producer_id = #{producerId,jdbcType=BIGINT},
      producer_name = #{producerName,jdbcType=VARCHAR},
      process_name = #{processName,jdbcType=VARCHAR},
      project_submit = #{projectSubmit,jdbcType=TINYINT},
      unit_id = #{unitId,jdbcType=BIGINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      version = #{version,jdbcType=BIGINT},
      renson_remark = #{rensonRemark,jdbcType=VARCHAR},
      submit_reason = #{submitReason,jdbcType=VARCHAR},
      rel_receipts_id = #{relReceiptsId,jdbcType=BIGINT},
      init = #{init,jdbcType=TINYINT},
      init_sequence = #{initSequence,jdbcType=VARCHAR},
      buyer_id = #{buyerId,jdbcType=BIGINT},
      buyer_name = #{buyerName,jdbcType=VARCHAR},
      web_type = #{webType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>