<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PurchaseMaterialRequirementFreezeLogMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementFreezeLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="purchase_material_requirement_id" jdbcType="BIGINT" property="purchaseMaterialRequirementId" />
    <result column="operation_type" jdbcType="TINYINT" property="operationType" />
    <result column="freeze_reason" jdbcType="VARCHAR" property="freezeReason" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, purchase_material_requirement_id, operation_type, freeze_reason, create_at, create_by, 
    update_at, update_by, remark, deleted_flag
  </sql>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementFreezeLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from purchase_material_requirement_freeze_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_material_requirement_freeze_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_material_requirement_freeze_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementFreezeLog">
    insert into purchase_material_requirement_freeze_log (id, purchase_material_requirement_id, 
      operation_type, freeze_reason, create_at, 
      create_by, update_at, update_by, 
      remark, deleted_flag)
    values (#{id,jdbcType=BIGINT}, #{purchaseMaterialRequirementId,jdbcType=BIGINT}, 
      #{operationType,jdbcType=TINYINT}, #{freezeReason,jdbcType=VARCHAR}, #{createAt,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=BIGINT}, 
      #{remark,jdbcType=VARCHAR}, #{deletedFlag,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementFreezeLog">
    insert into purchase_material_requirement_freeze_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="purchaseMaterialRequirementId != null">
        purchase_material_requirement_id,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="freezeReason != null">
        freeze_reason,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="purchaseMaterialRequirementId != null">
        #{purchaseMaterialRequirementId,jdbcType=BIGINT},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=TINYINT},
      </if>
      <if test="freezeReason != null">
        #{freezeReason,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementFreezeLogExample" resultType="java.lang.Long">
    select count(*) from purchase_material_requirement_freeze_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementFreezeLog">
    update purchase_material_requirement_freeze_log
    <set>
      <if test="purchaseMaterialRequirementId != null">
        purchase_material_requirement_id = #{purchaseMaterialRequirementId,jdbcType=BIGINT},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=TINYINT},
      </if>
      <if test="freezeReason != null">
        freeze_reason = #{freezeReason,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementFreezeLog">
    update purchase_material_requirement_freeze_log
    set purchase_material_requirement_id = #{purchaseMaterialRequirementId,jdbcType=BIGINT},
      operation_type = #{operationType,jdbcType=TINYINT},
      freeze_reason = #{freezeReason,jdbcType=VARCHAR},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>