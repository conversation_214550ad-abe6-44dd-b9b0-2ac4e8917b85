<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.MilepostDesignPlanDetailExtMapper">
    <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_budget_material_id" jdbcType="BIGINT" property="projectBudgetMaterialId"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="milepost_id" jdbcType="BIGINT" property="milepostId"/>
        <result column="submit_record_id" jdbcType="BIGINT" property="submitRecordId"/>
        <result column="submit_record_rel_id" jdbcType="BIGINT" property="submitRecordRelId"/>
        <result column="change_record_id" jdbcType="BIGINT" property="changeRecordId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="num" jdbcType="VARCHAR" property="num"/>
        <result column="requirement_num" jdbcType="DECIMAL" property="requirementNum"/>
        <result column="whether_model" jdbcType="BIT" property="whetherModel"/>
        <result column="module_status" jdbcType="INTEGER" property="moduleStatus"/>
        <result column="ext" jdbcType="BIT" property="ext"/>
        <result column="materiel_descr" jdbcType="VARCHAR" property="materielDescr"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="unit_code" jdbcType="VARCHAR" property="unitCode"/>
        <result column="number" jdbcType="DECIMAL" property="number"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="brand" jdbcType="VARCHAR" property="brand"/>
        <result column="business_classification" jdbcType="VARCHAR" property="businessClassification"/>
        <result column="material_classification" jdbcType="VARCHAR" property="materialClassification"/>
        <result column="materiel_type" jdbcType="VARCHAR" property="materielType"/>
        <result column="machining_part_type" jdbcType="VARCHAR" property="machiningPartType"/>
        <result column="material" jdbcType="VARCHAR" property="material"/>
        <result column="unit_weight" jdbcType="DECIMAL" property="unitWeight"/>
        <result column="material_processing" jdbcType="VARCHAR" property="materialProcessing"/>
        <result column="budget_unit_price" jdbcType="DECIMAL" property="budgetUnitPrice"/>
        <result column="design_cost_id" jdbcType="BIGINT" property="designCostId"/>
        <result column="design_cost" jdbcType="DECIMAL" property="designCost"/>
        <result column="budget_subtotal" jdbcType="DECIMAL" property="budgetSubtotal"/>
        <result column="pam_code" jdbcType="VARCHAR" property="pamCode"/>
        <result column="erp_code" jdbcType="VARCHAR" property="erpCode"/>
        <result column="erp_code_source" jdbcType="INTEGER" property="erpCodeSource"/>
        <result column="materiel_status" jdbcType="INTEGER" property="materielStatus"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="generate_requirement" jdbcType="BIT" property="generateRequirement"/>
        <result column="deleted_flag" jdbcType="BIT" property="deletedFlag"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="init" jdbcType="BIT" property="init"/>
        <result column="material_category" jdbcType="VARCHAR" property="materialCategory"/>
        <result column="coding_middle_class" jdbcType="VARCHAR" property="codingMiddleClass"/>
        <result column="figure_number" jdbcType="VARCHAR" property="figureNumber"/>
        <result column="chart_version" jdbcType="VARCHAR" property="chartVersion"/>
        <result column="brand_material_code" jdbcType="VARCHAR" property="brandMaterialCode"/>
        <result column="or_spare_parts_mask" jdbcType="VARCHAR" property="orSparePartsMask"/>
        <result column="requirement_creat_date" jdbcType="TIMESTAMP" property="requirementCreatDate"/>

        <result column="wbs_summary_code" jdbcType="VARCHAR" property="wbsSummaryCode"/>
        <result column="wbs_layer" jdbcType="VARCHAR" property="wbsLayer"/>
        <result column="wbs_confirm_flag" jdbcType="TINYINT" property="wbsConfirmFlag"/>
        <result column="dispatch_is" jdbcType="TINYINT" property="dispatchIs"/>
        <result column="ext_is" jdbcType="TINYINT" property="extIs"/>
        <result column="activity_code" jdbcType="VARCHAR" property="activityCode"/>
        <result column="project_budget_type" jdbcType="VARCHAR" property="projectBudgetType"/>
        <result column="plan_designer" jdbcType="VARCHAR" property="planDesigner"/>
        <result column="design_release_lot_number" jdbcType="VARCHAR" property="designReleaseLotNumber"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="requirement_type" jdbcType="INTEGER" property="requirementType"/>
        <result column="publish_by" jdbcType="BIGINT" property="publishBy"/>
        <result column="publish_at" jdbcType="TIMESTAMP" property="publishAt"/>
        <result column="index_num" jdbcType="VARCHAR" property="indexNum"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Example_Where_Clause_ForWbsLayer">
        where
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
    </sql>

    <sql id="Base_Column_List">
        id, project_budget_material_id, project_id, milepost_id, submit_record_id, submit_record_rel_id,
        change_record_id, parent_id, level, num, whether_model, module_status, ext, materiel_descr,
        unit, unit_code, number, delivery_time, name, model, brand, business_classification,
        material_classification, materiel_type, machining_part_type, material, unit_weight,
        material_processing, budget_unit_price, design_cost_id, design_cost, budget_subtotal,
        pam_code, erp_code, erp_code_source, materiel_status, source, remark, status, generate_requirement,
        deleted_flag, create_by, create_at, update_by, update_at, init,item_cost_is_null as itemCostIsNull,
        material_category, coding_middle_class, figure_number, chart_version, brand_material_code, or_spare_parts_mask,
        wbs_summary_code,wbs_layer,wbs_confirm_flag,dispatch_is,ext_is,activity_code,project_budget_type,
        plan_designer,design_release_lot_number,description,requirement_type
    </sql>
    <sql id="Column_List">
        id, project_budget_material_id, project_id, milepost_id, submit_record_id, submit_record_rel_id,
        change_record_id, parent_id, level, num, whether_model, module_status, ext, materiel_descr,
        unit, unit_code, number, delivery_time, name, model, brand, business_classification,
        material_classification, materiel_type, machining_part_type, material, unit_weight,
        material_processing, budget_unit_price, design_cost_id,design_cost,
        budget_subtotal,
        pam_code, erp_code, erp_code_source, materiel_status, source, remark, status, generate_requirement,
        deleted_flag, create_by, create_at, update_by, update_at, init,
        t.purchase_status as purchaseStatus,
        ifnull(t2.purchase_num,0) as purchaseNum,
        ifnull(t3.approval_num,0) as approvalNum,
        item_cost_is_null as itemCostIsNull,
        material_category, coding_middle_class, figure_number, chart_version,
        brand_material_code, or_spare_parts_mask, requirement_creat_date,
        wbs_summary_code,wbs_layer,wbs_confirm_flag,dispatch_is,ext_is,activity_code,project_budget_type,
        plan_designer,design_release_lot_number,description
    </sql>
    <sql id="Column_ListNew">
        id,
        project_budget_material_id,
        project_id,
        milepost_id,
        submit_record_id,
        submit_record_rel_id,
        change_record_id,
        parent_id,
        level,
        num,
        whether_model,
        module_status,
        ext,
        materiel_descr,
        unit,
        unit_code,
        number,
        delivery_time,
        name,
        model,
        brand,
        business_classification,
        material_classification,
        materiel_type,
        machining_part_type,
        material,
        unit_weight,
        material_processing,
        budget_unit_price,
        design_cost_id,
        wbs_summary_code,
        wbs_layer,
        wbs_confirm_flag,
        dispatch_is,
        ext_is,
        activity_code,
        project_budget_type,
        plan_designer,
        design_release_lot_number,
        description,
        wbs_last_layer,
        requirement_type,
        IFNULL(
          IFNULL(
            (
              SELECT
                mc.item_cost
              FROM
                pam_basedata.material_cost mc
              WHERE
                mc.id = milepost_design_plan_detail.design_cost_id
            ),
            milepost_design_plan_detail.design_cost
          ),
          0
        ) design_cost,
        budget_subtotal,
        pam_code,
        erp_code,
        erp_code_source,
        materiel_status,
        source,
        remark,
        status,
        generate_requirement,
        deleted_flag,
        create_by,
        create_at,
        update_by,
        update_at,
        init,
        t.purchase_status as purchaseStatus,
        ifnull(t2.purchase_num, 0) as purchaseNum,
        ifnull(t3.approval_num, 0) as approvalNum,
        item_cost_is_null as itemCostIsNull,
        material_category,
        coding_middle_class,
        figure_number,
        chart_version,
        brand_material_code,
        or_spare_parts_mask,
        requirement_creat_date
    </sql>

    <sql id="Column_List_XS">
        id, project_budget_material_id, project_id, milepost_id, submit_record_id, submit_record_rel_id,
        change_record_id, parent_id, level, num, whether_model, module_status, ext, materiel_descr,
        unit, unit_code, number, delivery_time, name, model, brand, business_classification,
        material_classification, materiel_type, machining_part_type, material, unit_weight,
        material_processing, budget_unit_price, design_cost_id,
        wbs_summary_code,wbs_layer,wbs_confirm_flag,dispatch_is,ext_is,activity_code,project_budget_type,
        plan_designer,design_release_lot_number,description,wbs_last_layer,requirement_type,
        IFNULL(
        IFNULL((
        SELECT mc.item_cost
        FROM pam_basedata.material_cost mc
        WHERE mc.id = milepost_design_plan_detail.design_cost_id),milepost_design_plan_detail.design_cost),0)
        design_cost,
        budget_subtotal,
        pam_code, erp_code, erp_code_source, materiel_status, source, remark, status, generate_requirement,
        deleted_flag, create_by, create_at, update_by, update_at, init,
        item_cost_is_null as itemCostIsNull,
        material_category, coding_middle_class, figure_number, chart_version,
        brand_material_code, or_spare_parts_mask, requirement_creat_date,index_num
    </sql>

    <update id="updateDesignCost">
        update milepost_design_plan_detail m left join project p on m.project_id = p.id
        set m.design_cost = #{designCost}, m.design_cost_id = #{designCostId}
        where p.status not in (10,12) AND p.ou_id = #{ouId}
        AND m.erp_code = #{itemCode}
        <![CDATA[ and (m.design_cost is null or m.design_cost > #{designCost} or m.design_cost < #{designCost}) ]]>
        AND (m.deleted_flag = 0 or m.deleted_flag is null)
    </update>

    <delete id="deleteMilepostDesignPlanDetailsBySubmitId">
        delete from milepost_design_plan_detail where submit_record_id = #{submitId} and submit_record_id is not null
    </delete>

    <select id="findApprovedMilepostDesignPlanDetails" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        , (select ifnull(sum(releaseDetail.publish_num),0) from purchase_material_release_detail releaseDetail
        where releaseDetail.design_plan_detail_id = milepost_design_plan_detail.id and (releaseDetail.deleted_flag = 0
        or releaseDetail.deleted_flag is null)) as requirement_num
        from milepost_design_plan_detail
        WHERE deleted_flag = 0 and milepost_id = #{milepostId}
        and ( status !=-1 or (status is null and (material_classification <![CDATA[ <> ]]> '物料' or
        material_classification is null)))

    </select>

    <select id="findApprovedMilepostDesignPlanDetailByMap" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        , (select ifnull(sum(releaseDetail.publish_num),0) from purchase_material_release_detail releaseDetail
        where releaseDetail.design_plan_detail_id = milepost_design_plan_detail.id and (releaseDetail.deleted_flag = 0
        or releaseDetail.deleted_flag is null)) as requirement_num
        from milepost_design_plan_detail
        WHERE deleted_flag = 0 and project_id = #{projectId}
        <if test="milepostId != null">
            and milepost_id = #{milepostId}
        </if>
        and ( status !=-1 or (status is null and (material_classification <![CDATA[ <> ]]> '物料' or
        material_classification is null)))
    </select>

    <select id="selectList" parameterType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto"
            resultMap="BaseResultMap">
        select
            mdpd.id,
            mdpd.project_budget_material_id,
            mdpd.project_id,
            mdpd.milepost_id,
            mdpd.submit_record_id,
            mdpd.submit_record_rel_id,
            mdpd.change_record_id,
            mdpd.parent_id,
            mdpd.level,
            mdpd.num,
            mdpd.whether_model,
            mdpd.module_status,
            mdpd.ext,
            mdpd.materiel_descr,
            mdpd.unit,
            mdpd.unit_code,
            mdpd.number,
            mdpd.delivery_time,
            mdpd.name,
            mdpd.model,
            mdpd.brand,
            mdpd.business_classification,
            mdpd.material_classification,
            mdpd.materiel_type,
            mdpd.machining_part_type,
            mdpd.material,
            mdpd.unit_weight,
            mdpd.material_processing,
            mdpd.budget_unit_price,
            mdpd.design_cost_id,
            mdpd.wbs_summary_code,
            mdpd.wbs_layer,
            mdpd.wbs_confirm_flag,
            mdpd.dispatch_is,
            mdpd.ext_is,
            mdpd.activity_code,
            mdpd.project_budget_type,
            mdpd.plan_designer,
            mdpd.design_release_lot_number,
            mdpd.description,
            mdpd.wbs_last_layer,
            mdpd.requirement_type,
            IFNULL(
              IFNULL(
                (
                  SELECT
                    mc.item_cost
                  FROM
                    pam_basedata.material_cost mc
                  WHERE
                    mc.id = mdpd.design_cost_id
                ),
                mdpd.design_cost
              ),
              0
            ) design_cost,
            mdpd.budget_subtotal,
            mdpd.pam_code,
            mdpd.erp_code,
            mdpd.erp_code_source,
            mdpd.materiel_status,
            mdpd.source,
            mdpd.remark,
            mdpd.status,
            mdpd.generate_requirement,
            mdpd.deleted_flag,
            mdpd.create_by,
            mdpd.create_at,
            mdpd.update_by,
            mdpd.update_at,
            mdpd.init,
            t.purchase_status as purchaseStatus,
            ifnull(t.purchase_num, 0) as purchaseNum,
            ifnull(t.approval_num, 0) as approvalNum,
            mdpd.item_cost_is_null as itemCostIsNull,
            mdpd.material_category,
            mdpd.coding_middle_class,
            mdpd.figure_number,
            mdpd.chart_version,
            mdpd.brand_material_code,
            mdpd.or_spare_parts_mask,
            mdpd.requirement_creat_date,
            ifnull(if(pt.requriement_deliver_mrp = 1, mpmd.number, t2.publish_num), 0) as requirement_num
        from
          pam_ctc.milepost_design_plan_detail mdpd
          inner join pam_ctc.project p on mdpd.project_id = p.id
          inner join pam_ctc.project_type pt on p.type = pt.id
          left join (
            select
              rr2.design_plan_detail_id,
              prr.status as purchase_status,
              if(prr.status in (0, -1, 1, 3, 40), rr2.purchase_num, 0) as purchase_num,
              if(prr.status = -1, rr2.purchase_num, 0) as approval_num
            from
              milepost_design_plan_purchase_record prr
              inner join milepost_design_plan_purchase_record_relation rr2 on prr.id = rr2.confirm_record_id
              and rr2.deleted_flag = 0
            where
              prr.deleted_flag = 0
              <if test="projectId != null">
                  and prr.project_id = #{projectId}
              </if>
              <if test="id != null">
                  and rr2.design_plan_detail_id = #{id}
              </if>
              and prr.id in (
                select
                  MAX(pr.id)
                from
                  milepost_design_plan_detail md
                  inner join milepost_design_plan_purchase_record_relation rr on md.id = rr.design_plan_detail_id
                  and rr.deleted_flag = 0
                  inner join milepost_design_plan_purchase_record pr on rr.confirm_record_id = pr.id
                  and pr.deleted_flag = 0
                where
                  rr.`design_plan_detail_id` = rr2.design_plan_detail_id
                    <if test="projectId != null">
                        and md.project_id = #{projectId}
                    </if>
                    <if test="id != null">
                        and md.id = #{id}
                    </if>
                group by
                  rr.design_plan_detail_id
              )
          ) t on t.design_plan_detail_id = mdpd.id
          left join milepost_design_plan_material_detail mpmd on mpmd.design_plan_detail_id = mdpd.id
              and mpmd.deleted_flag = 0
          left join (select
                  releaseDetail.design_plan_detail_id,
                  ifnull(sum(releaseDetail.publish_num), 0) as publish_num
                from
                  purchase_material_release_detail releaseDetail
                where
                    releaseDetail.deleted_flag = 0
                    <if test="projectId != null">
                        and releaseDetail.project_id = #{projectId}
                    </if>
                    <if test="id != null">
                        and releaseDetail.design_plan_detail_id = #{id}
                    </if>
                group by releaseDetail.design_plan_detail_id) t2 on t2.design_plan_detail_id = mdpd.id
        where
        <choose>
            <when test="deletedFlag != null"> mdpd.deleted_flag = #{deletedFlag} </when>
            <otherwise> mdpd.deleted_flag = 0 </otherwise>
        </choose>
        <if test="projectId != null">
            and mdpd.project_id = #{projectId}
        </if>
        <if test="id != null">
            and mdpd.id = #{id}
        </if>
        <if test="milepostId != null">
            and mdpd.milepost_id = #{milepostId}
        </if>
        <if test="submitRecordId != null">
            and mdpd.submit_record_id = #{submitRecordId}
        </if>
        <if test="parentId != null">
            and mdpd.parent_id = #{parentId}
        </if>
        <if test="whetherModel != null">
            and mdpd.whether_model = #{whetherModel}
        </if>
        <if test="moduleStatus != null">
            and mdpd.module_status = #{moduleStatus}
        </if>
        <if test="materielDescr != null and materielDescr != ''">
            and mdpd.materiel_descr like concat('%', #{materielDescr}, '%')
        </if>
        <if test="deliveryTime != null">
            and mdpd.delivery_time = #{deliveryTime}
        </if>
        <if test="model != null">
            and mdpd.model = #{model}
        </if>
        <if test="wbsLayer != null and wbsLayer != ''">
            and mdpd.wbs_layer = #{wbsLayer}
        </if>
        <if test="pamCode != null and pamCode != ''">
            and mdpd.pam_code like concat(#{pamCode}, '%')
        </if>
        <if test="erpCode != null and erpCode != ''">
            and mdpd.erp_code like concat(#{erpCode}, '%')
        </if>
        <if test="wbsSummaryCode != null and wbsSummaryCode != ''">
            and mdpd.wbs_summary_code = #{wbsSummaryCode}
        </if>
        <if test="noIncludeId != null">
            and mdpd.id <![CDATA[<> ]]> #{noIncludeId}
        </if>
        <if test="generateRequirement != null">
            and mdpd.generate_requirement = #{generateRequirement}
        </if>
        <if test="projectBudgetMaterialId != null">
            and mdpd.project_budget_material_id = #{projectBudgetMaterialId}
        </if>
        <if test="status != null">
            and mdpd.status = #{status}
        </if>
        <if test="parentIds != null and parentIds.size() > 0">
            and mdpd.parent_id in
            <foreach collection="parentIds" item="parentId" index="index" open="(" separator="," close=")">
                #{parentId}
            </foreach>
        </if>
        <if test="designPlanDetailIds != null and designPlanDetailIds.size() > 0">
            and mdpd.id in
            <foreach collection="designPlanDetailIds" item="designPlanDetailId" index="index" open="(" separator="," close=")">
                #{designPlanDetailId}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and mdpd.status in
            <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>

    <select id="getNoGenerateRequirement" resultType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail">
        select * from pam_ctc.milepost_design_plan_detail d, pam_ctc.milepost_design_plan_detail pd
        where d.parent_id = pd.id and d.project_id = #{projectId}
        and d.status in (1, 18)
        and d.generate_requirement = 0
        and d.material_classification = '物料'
        and d.deleted_flag = 0 and pd.ext = 0;

    </select>
    <select id="getDesignBybudgetMaterialId" resultType="java.lang.Long">
        SELECT
        COUNT(0)
        FROM
        pam_ctc.milepost_design_plan_detail a
        INNER JOIN pam_ctc.milepost_design_plan_detail b
        ON a.id = b.parent_id
        AND b.deleted_flag = 0
        WHERE a.project_budget_material_id = #{budgetMaterialId}
        AND a.deleted_flag = 0
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        with recursive mdpd as (
        select *
        from milepost_design_plan_detail where deleted_flag != 1
        <if test="ids != null and ids.size>0">
            and id in
            <foreach collection="ids" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        union
        select a.*
        from milepost_design_plan_detail a
        join mdpd
        where a.id = mdpd.parent_id
        )
        select *
        from mdpd;
    </select>

    <select id="selectById" resultMap="BaseResultMap">
        with recursive mdpd as (
        select *
        from milepost_design_plan_detail where
        id = #{sourceId}
        union
        select a.*
        from milepost_design_plan_detail a
        join mdpd
        where a.parent_id = mdpd.id
        )
        select *
        from mdpd where mdpd.deleted_flag != 1;
    </select>

    <select id="selectDeliveryTime" resultMap="BaseResultMap">
        with recursive mdpd as (
        select m.*
        from milepost_design_plan_detail m
        where m.id = #{designPlanDetailId}
        union
        select a.*
        from milepost_design_plan_detail a
        join mdpd
        where a.id = mdpd.parent_id )
        select mdpd.delivery_time
        from mdpd
        where mdpd.whether_model = 1
        AND mdpd.project_budget_material_id IS NOT null
        and mdpd.delivery_time is not null
    </select>

    <update id="updateDeliveryTimeById">
        update
        milepost_design_plan_detail d
        set d.delivery_time = #{deliveryTime}
        where d.id = #{designPlanDetailId}
    </update>

    <select id="getMilepostPlanDesignByTicketTasksCode"
            resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto">
        select m2.shelves,
        tmmm.*
        from (
        select tmm.ticketTaskCode,
        tmm.pamCode,
        mmd.*
        from milepost_design_plan_detail mmd
        inner join (
        select t.ticket_task_code as ticketTaskCode,
        m.id as id,
        m.pam_code as pamCode
        from (with recursive mdpd as (
        select *
        from milepost_design_plan_detail
        where id in (
        select tt.milepost_design_detail_id
        from ticket_tasks tt
        where tt.id = #{ticketTasksId})
        union
        select a.*
        from milepost_design_plan_detail a
        join mdpd
        where a.parent_id = mdpd.id)
        select mdpd.*
        from mdpd) m
        left join ticket_tasks t on
        m.id = t.milepost_design_detail_id
        where t.milepost_design_detail_id is not null) tmm on
        mmd.parent_id = tmm.id where mmd.deleted_flag != 1
        <if test="materialCategoryList != null ">
            and mmd.material_category in
            <foreach collection="materialCategoryList" item="materialCategory" index="index" open="(" separator=","
                     close=")">
                #{materialCategory}
            </foreach>
        </if>
        ) tmmm
        left join pam_basedata.material m2 on
        m2.pam_code = tmmm.pamCode
    </select>

    <select id="selectByProjectIdAndPamCode"
            resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto">
        select * from milepost_design_plan_detail where pam_code = #{pamCode}
        <if test="projectIds != null ">
            and project_id in
            <foreach collection="projectIds" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
    </select>

    <select id="selectDesignPlanDetail" resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto">
        select distinct tt.id as id,
        mn.purchase_num as purchaseNum,
        tt.project_budget_material_id as projectBudgetMaterialId ,
        tt.project_id as projectId ,
        tt.milepost_id as milepostId ,
        tt.submit_record_id as submitRecordId ,
        tt.submit_record_rel_id as submitRecordRelId ,
        tt.change_record_id as changeRecordId ,
        tt.parent_id as parentId ,
        tt.level as level ,
        tt.num as num ,
        tt.whether_model as whetherModel ,
        tt.module_status as moduleStatus ,
        tt.ext as ext ,
        tt.materiel_descr as materielDescr ,
        tt.unit as unit ,
        tt.unit_code as unitCode ,
        tt.number as number ,
        tt.delivery_time as deliveryTime ,
        tt.name as name ,
        tt.model as model ,
        tt.brand as brand ,
        tt.business_classification as businessClassification ,
        tt.material_classification as materialClassification ,
        tt.materiel_type as materielType ,
        tt.machining_part_type as machiningPartType ,
        tt.material as material ,
        tt.unit_weight as unitWeight ,
        tt.material_processing as materialProcessing ,
        tt.budget_unit_price as budgetUnitPrice ,
        tt.design_cost_id as designCostId ,
        tt.design_cost as designCost ,
        tt.budget_subtotal as budgetSubtotal ,
        tt.pam_code as pamCode ,
        tt.erp_code as erpCode ,
        tt.erp_code_source as erpCodeSource ,
        tt.materiel_status as materielStatus ,
        tt.source as `source` ,
        tt.remark as remark ,
        tt.status as status ,
        tt.generate_requirement as generateRequirement ,
        tt.deleted_flag as deletedFlag ,
        tt.create_by as createBy ,
        tt.create_at as createAt ,
        tt.update_by as updateBy ,
        tt.update_at as updateAt ,
        tt.init as init ,
        tt.material_category as materialCategory ,
        tt.coding_middle_class as codingMiddleClass ,
        tt.figure_number as figureNumber ,
        tt.chart_version as chartVersion ,
        tt.brand_material_code as brandMaterialCode ,
        tt.or_spare_parts_mask as orSparePartsMask ,
        tt.requirement_creat_date as requirementCreatDate
        from
        (with recursive mdpd as (
        select
        *
        from
        milepost_design_plan_detail where deleted_flag != 1
        <if test="ids != null and ids.size>0">
            and id in
            <foreach collection="ids" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        union
        select
        a.*
        from
        milepost_design_plan_detail a
        join mdpd
        where
        a.id = mdpd.parent_id )
        select
        *
        from
        mdpd)tt left join milepost_design_plan_purchase_record_relation mn on tt.id = mn.design_plan_detail_id where
        mn.confirm_record_id = #{confirmId}
        and mn.deleted_flag = 0
        <if test="ids != null and ids.size>0">
            or tt.id not in
            <foreach collection="ids" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
    </select>

    <select id="selectDesignPlanDetailForPurchase"
            resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto">
        select distinct tt.id as id,
        mn.purchase_num as purchaseNum,
        tt.project_budget_material_id as projectBudgetMaterialId ,
        tt.project_id as projectId ,
        tt.milepost_id as milepostId ,
        tt.submit_record_id as submitRecordId ,
        tt.submit_record_rel_id as submitRecordRelId ,
        tt.change_record_id as changeRecordId ,
        tt.parent_id as parentId ,
        tt.level as level ,
        tt.num as num ,
        tt.whether_model as whetherModel ,
        tt.module_status as moduleStatus ,
        tt.ext as ext ,
        tt.materiel_descr as materielDescr ,
        tt.unit as unit ,
        tt.unit_code as unitCode ,
        tt.number as number ,
        tt.delivery_time as deliveryTime ,
        tt.name as name ,
        tt.model as model ,
        tt.brand as brand ,
        tt.business_classification as businessClassification ,
        tt.material_classification as materialClassification ,
        tt.materiel_type as materielType ,
        tt.machining_part_type as machiningPartType ,
        tt.material as material ,
        tt.unit_weight as unitWeight ,
        tt.material_processing as materialProcessing ,
        tt.budget_unit_price as budgetUnitPrice ,
        tt.design_cost_id as designCostId ,
        tt.design_cost as designCost ,
        tt.budget_subtotal as budgetSubtotal ,
        tt.pam_code as pamCode ,
        tt.erp_code as erpCode ,
        tt.erp_code_source as erpCodeSource ,
        tt.materiel_status as materielStatus ,
        tt.source as `source` ,
        tt.remark as remark ,
        tt.status as status ,
        tt.generate_requirement as generateRequirement ,
        tt.deleted_flag as deletedFlag ,
        tt.create_by as createBy ,
        tt.create_at as createAt ,
        tt.update_by as updateBy ,
        tt.update_at as updateAt ,
        tt.init as init ,
        tt.material_category as materialCategory ,
        tt.coding_middle_class as codingMiddleClass ,
        tt.figure_number as figureNumber ,
        tt.chart_version as chartVersion ,
        tt.brand_material_code as brandMaterialCode ,
        tt.or_spare_parts_mask as orSparePartsMask ,
        tt.requirement_creat_date as requirementCreatDate,
        tt.wbs_summary_code as wbsSummaryCode,
        tt.wbs_layer as wbsLayer,
        tt.dispatch_is as dispatchIs,
        tt.ext_is as extIs,
        tt.activity_code as activityCode,
        tt.project_budget_type as projectBudgetType,
        tt.plan_designer as planDesigner,
        tt.design_release_lot_number as designReleaseLotNumber,
        tt.description,
        tt.wbs_last_layer,
        tt.requirement_type as requirementType
        from
        (with recursive mdpd as (
        select
        *
        from
        milepost_design_plan_detail where deleted_flag != 1
        <if test="ids != null and ids.size>0">
            and id in
            <foreach collection="ids" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        union
        select
        a.*
        from
        milepost_design_plan_detail a
        join mdpd
        where
        a.id = mdpd.parent_id )
        select
        *
        from
        mdpd)tt left join project_wbs_receipts_design_plan_rel mn on tt.id = mn.design_plan_detail_id where
        mn.project_wbs_receipts_id = #{receiptsId}
        <if test="ids != null and ids.size>0">
            or tt.id not in
            <foreach collection="ids" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
    </select>

    <select id="selectDesignPlanDetailForConfirm" resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto">
        select
        tt.id as id,
        tt.project_budget_material_id as projectBudgetMaterialId ,
        tt.project_id as projectId ,
        tt.milepost_id as milepostId ,
        tt.submit_record_id as submitRecordId ,
        tt.submit_record_rel_id as submitRecordRelId ,
        tt.change_record_id as changeRecordId ,
        tt.parent_id as parentId ,
        tt.level as level ,
        tt.num as num ,
        tt.whether_model as whetherModel ,
        tt.module_status as moduleStatus ,
        tt.ext as ext ,
        tt.materiel_descr as materielDescr ,
        tt.unit as unit ,
        tt.unit_code as unitCode ,
        tt.number as number ,
        tt.delivery_time as deliveryTime ,
        tt.name as name ,
        tt.model as model ,
        tt.brand as brand ,
        tt.business_classification as businessClassification ,
        tt.material_classification as materialClassification ,
        tt.materiel_type as materielType ,
        tt.machining_part_type as machiningPartType ,
        tt.material as material ,
        tt.unit_weight as unitWeight ,
        tt.material_processing as materialProcessing ,
        tt.budget_unit_price as budgetUnitPrice ,
        tt.design_cost_id as designCostId ,
        tt.design_cost as designCost ,
        tt.budget_subtotal as budgetSubtotal ,
        tt.pam_code as pamCode ,
        tt.erp_code as erpCode ,
        tt.erp_code_source as erpCodeSource ,
        tt.materiel_status as materielStatus ,
        tt.source as `source` ,
        tt.remark as remark ,
        tt.status as status ,
        tt.generate_requirement as generateRequirement ,
        tt.deleted_flag as deletedFlag ,
        tt.create_by as createBy ,
        tt.create_at as createAt ,
        tt.update_by as updateBy ,
        tt.update_at as updateAt ,
        tt.init as init ,
        tt.material_category as materialCategory ,
        tt.coding_middle_class as codingMiddleClass ,
        tt.figure_number as figureNumber ,
        tt.chart_version as chartVersion ,
        tt.brand_material_code as brandMaterialCode ,
        tt.or_spare_parts_mask as orSparePartsMask ,
        tt.requirement_creat_date as requirementCreatDate,
        tt.wbs_summary_code as wbsSummaryCode,
        tt.wbs_layer as wbsLayer,
        tt.dispatch_is as dispatchIs,
        tt.ext_is as extIs,
        tt.activity_code as activityCode,
        tt.project_budget_type as projectBudgetType,
        tt.plan_designer as planDesigner,
        tt.design_release_lot_number as designReleaseLotNumber,
        tt.description,
        tt.wbs_last_layer,
        tt.requirement_type as requirementType
        from
        (with recursive mdpd as (
        select
        *
        from
        milepost_design_plan_detail where deleted_flag != 1
        <if test="ids != null and ids.size>0">
            and id in
            <foreach collection="ids" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
        union
        select
        a.*
        from
        milepost_design_plan_detail a
        join mdpd
        where
        a.id = mdpd.parent_id )
        select
        *
        from
        mdpd)tt
    </select>

    <update id="updateById">
        update
        milepost_design_plan_detail d
        set d.deleted_flag = 1
        where d.id = #{designPlanDetailId}
    </update>

    <select id="getTotalPurchaseNum" resultType="java.math.BigDecimal">
        select sum(purchase_num) from milepost_design_plan_purchase_record_relation
        where deleted_flag = 0 and design_plan_detail_id = #{designPlanDetailId}
    </select>

    <update id="updateByMilepostDesignPlanId">
        update
        pam_ctc.milepost_design_plan_detail d
        set d.material_category = #{materialCategory}
        where d.id = #{designId}
    </update>
    <select id="selectModel" resultMap="BaseResultMap">
        with recursive mdpd as (
        select *
        from pam_ctc.milepost_design_plan_detail
        where id = #{designId}
        union
        select a.*
        from pam_ctc.milepost_design_plan_detail a
        join mdpd
        where a.id = mdpd.parent_id)
        select mdpd.*
        from mdpd
        where mdpd.whether_model = 1
        and mdpd.project_budget_material_id is not null
    </select>

    <select id="selectTotalNumberById" resultMap="BaseResultMap">
        with recursive mdpd as (
        select id,parent_id,number,deleted_flag
        from milepost_design_plan_detail where
        id = #{designId}
        union
        select a.id,a.parent_id,a.number,a.deleted_flag
        from milepost_design_plan_detail a
        join mdpd
        where a.id = mdpd.parent_id
        )
        select *
        from mdpd where (mdpd.deleted_flag = 0 or mdpd.deleted_flag is null)
    </select>

    <select id="selectModule" resultMap="BaseResultMap">
        with recursive mdpd as (
        select m.*
        from milepost_design_plan_detail m
        where m.id = #{designPlanDetailId}
        union
        select a.*
        from milepost_design_plan_detail a
        join mdpd
        where a.id = mdpd.parent_id )
        select mdpd.*
        from mdpd
        where mdpd.whether_model = 1
        AND mdpd.project_budget_material_id IS NOT null
        and mdpd.delivery_time is not null
    </select>

    <!-- 逻辑删除by项目物料预算id -->
    <update id="logicallyDeleteByProjectBudgetMataerialId">
        update
        milepost_design_plan_detail d
        set d.deleted_flag = 1,
        d.update_by = #{updatedBy},
        d.update_at = NOW()
        where d.project_budget_material_id = #{projectBudgetMaterialId}
    </update>

    <update id="updateDetailForChangeByDetailIds">
        update
        milepost_design_plan_detail
        set update_by = #{updatedBy},
        update_at = NOW(),
        status = #{status}
        <if test="moduleStatus !=null and moduleStatus != ''">
            ,module_status = #{moduleStatus}
        </if>
        <where>
            <if test="detailIds != null and detailIds.size>0">
                and id in
                <foreach collection="detailIds" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </update>


    <select id="selectDetailAndReceiptsByCondition"
            parameterType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Column_List_XS"/>
        ,(
        select
        ifnull(sum(releaseDetail.publish_num), 0)
        from
        pam_ctc.purchase_material_release_detail releaseDetail
        where
        releaseDetail.design_plan_detail_id = milepost_design_plan_detail.id
        and (releaseDetail.deleted_flag = 0
        or releaseDetail.deleted_flag is null)) as requirement_num
        from
        pam_ctc.milepost_design_plan_detail

        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectMilepostDesignPlanPurchaseInfo"  resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanFieldDto">
        SELECT
        design_plan_detail_id as designPlanDetailId,
        prr.status  as purchaseStatus,
        ifnull(rr2.purchase_num,0) as purchaseNum
        FROM
        milepost_design_plan_purchase_record prr
        INNER JOIN milepost_design_plan_purchase_record_relation rr2
        ON prr.id = rr2.confirm_record_id
        AND rr2.deleted_flag = 0
        WHERE prr.deleted_flag = 0
        AND prr.id IN
        (SELECT
        MAX(pr.id)
        FROM
        milepost_design_plan_detail md
        INNER JOIN milepost_design_plan_purchase_record_relation rr
        ON md.id = rr.design_plan_detail_id
        AND rr.deleted_flag = 0
        INNER JOIN milepost_design_plan_purchase_record pr
        ON rr.confirm_record_id = pr.id
        AND pr.deleted_flag = 0
        WHERE rr.`design_plan_detail_id` = rr2.design_plan_detail_id
        <if test="detailIds != null and detailIds.size>0">
            and design_plan_detail_id in
            <foreach collection="detailIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>


        GROUP BY rr.design_plan_detail_id)

    </select>


    <select id="selectMilepostDesignPlanT1"  resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanFieldDto">
        select
        pwr2.requirement_code  as requirementCode,
        pwr2.requirement_status as requirementStatus,
        mdpdsh.design_plan_detail_id as designPlanDetailId
        from
        pam_ctc.milepost_design_plan_detail_submit_history mdpdsh
        left join pam_ctc.project_wbs_receipts pwr2 on
        mdpdsh.project_wbs_receipts_id = pwr2.id
        INNER JOIN pam_ctc.milepost_design_plan_detail mdpd
        ON mdpdsh.design_plan_detail_id = mdpd.id and mdpd.project_id = #{projectId} and (mdpd.wbs_last_layer is null or mdpd.wbs_last_layer = 1)
        where
        (pwr2.requirement_status != 4
        and pwr2.requirement_status != 5 )
        and pwr2.deleted_flag = 0
        and mdpdsh.deleted_flag = 0
        group by
        mdpdsh.design_plan_detail_id
    </select>

    <select id="selectMilepostDesignPlanT2"  resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanFieldDto">
        select
        pwr.requirement_code as requirementCode,
        pwr.requirement_status as requirementStatus,
        pwrdpr.design_plan_detail_id as designPlanDetailId
        from
        pam_ctc.project_wbs_receipts_design_plan_rel pwrdpr
        left join pam_ctc.project_wbs_receipts pwr on
        pwrdpr.project_wbs_receipts_id = pwr.id
        INNER JOIN pam_ctc.milepost_design_plan_detail mdpd
        ON pwrdpr.design_plan_detail_id = mdpd.id and mdpd.project_id = #{projectId} and (mdpd.wbs_last_layer is null or mdpd.wbs_last_layer = 1)
        where
        (pwr.requirement_status != 4
        and pwr.requirement_status != 5 )
        and pwr.deleted_flag = 0
        and pwrdpr.deleted_flag = 0
        group by
        pwrdpr.design_plan_detail_id

    </select>

    <select id="selectMilepostDesignPlanT3"  resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanFieldDto">
        select
        pwr3.requirement_code  as requirementCode,
        pwr3.requirement_status as requirementStatus,
        mdpdc.design_plan_detail_id as designPlanDetailId
        from
        pam_ctc.milepost_design_plan_detail_change mdpdc
        left join pam_ctc.project_wbs_receipts pwr3 on
        mdpdc.project_wbs_receipts_id = pwr3.id
        INNER JOIN pam_ctc.milepost_design_plan_detail mdpd
        ON mdpdc.design_plan_detail_id = mdpd.id and mdpd.project_id = #{projectId} and (mdpd.wbs_last_layer is null or mdpd.wbs_last_layer = 1)
        where
        mdpdc.project_wbs_receipts_id is not null
        and (pwr3.requirement_status != 4
        and pwr3.requirement_status != 5 )
        and pwr3.deleted_flag = 0
        and (mdpdc.change_type = 0
        or mdpdc.change_type is null)
        and mdpdc.deleted_flag = 0
        group by
        mdpdc.design_plan_detail_id
    </select>




    <select id="selectDetailByIds" resultMap="BaseResultMap">
        select *
        from milepost_design_plan_detail where (deleted_flag = 0 or deleted_flag is null)
        <if test="ids != null and ids.size>0">
            and id in
            <foreach collection="ids" item="it" index="index" open="(" separator="," close=")">
                #{it}
            </foreach>
        </if>
    </select>

    <update id="deleteByDetailIds">
        update milepost_design_plan_detail set deleted_flag = 1
        where 1=1
        <if test="detailIds != null and detailIds.size>0">
            and id in
            <foreach collection="detailIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <insert id="batchInsert" parameterType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail">
        insert into milepost_design_plan_detail (id, project_budget_material_id, project_id,
        milepost_id, submit_record_id, submit_record_rel_id,
        change_record_id, parent_id, level,
        num, whether_model, module_status,
        ext, materiel_descr, unit,
        unit_code, number, delivery_time,
        name, model, brand,
        business_classification, material_classification,
        materiel_type, machining_part_type, material,
        unit_weight, material_processing, budget_unit_price,
        design_cost_id, design_cost, budget_subtotal,
        pam_code, erp_code, erp_code_source,
        materiel_status, source, remark,
        status, generate_requirement, deleted_flag,
        create_by, create_at, update_by,
        update_at, init, item_cost_is_null,
        material_category, coding_middle_class, figure_number,
        chart_version, brand_material_code, or_spare_parts_mask,
        requirement_creat_date, perchasing_leadtime, min_perchase_quantity,
        min_package_quantity, wbs_summary_code, wbs_layer,
        wbs_description, wbs_confirm_flag, wbs_last_layer,
        dispatch_is, ext_is, activity_code,
        project_budget_type, plan_designer, design_release_lot_number,
        description, requirement_type)
        values
        <foreach collection="list" item="designPlanDetail" index="index" separator=",">
            (#{designPlanDetail.id,jdbcType=BIGINT}, #{designPlanDetail.projectBudgetMaterialId,jdbcType=BIGINT}, #{designPlanDetail.projectId,jdbcType=BIGINT},
            #{designPlanDetail.milepostId,jdbcType=BIGINT}, #{designPlanDetail.submitRecordId,jdbcType=BIGINT}, #{designPlanDetail.submitRecordRelId,jdbcType=BIGINT},
            #{designPlanDetail.changeRecordId,jdbcType=BIGINT}, #{designPlanDetail.parentId,jdbcType=BIGINT}, #{designPlanDetail.level,jdbcType=INTEGER},
            #{designPlanDetail.num,jdbcType=VARCHAR}, #{designPlanDetail.whetherModel,jdbcType=TINYINT}, #{designPlanDetail.moduleStatus,jdbcType=INTEGER},
            #{designPlanDetail.ext,jdbcType=TINYINT}, #{designPlanDetail.materielDescr,jdbcType=VARCHAR}, #{designPlanDetail.unit,jdbcType=VARCHAR},
            #{designPlanDetail.unitCode,jdbcType=VARCHAR}, #{designPlanDetail.number,jdbcType=DECIMAL}, #{designPlanDetail.deliveryTime,jdbcType=TIMESTAMP},
            #{designPlanDetail.name,jdbcType=VARCHAR}, #{designPlanDetail.model,jdbcType=VARCHAR}, #{designPlanDetail.brand,jdbcType=VARCHAR},
            #{designPlanDetail.businessClassification,jdbcType=VARCHAR}, #{designPlanDetail.materialClassification,jdbcType=VARCHAR},
            #{designPlanDetail.materielType,jdbcType=VARCHAR}, #{designPlanDetail.machiningPartType,jdbcType=VARCHAR}, #{designPlanDetail.material,jdbcType=VARCHAR},
            #{designPlanDetail.unitWeight,jdbcType=DECIMAL}, #{designPlanDetail.materialProcessing,jdbcType=VARCHAR}, #{designPlanDetail.budgetUnitPrice,jdbcType=DECIMAL},
            #{designPlanDetail.designCostId,jdbcType=BIGINT}, #{designPlanDetail.designCost,jdbcType=DECIMAL}, #{designPlanDetail.budgetSubtotal,jdbcType=DECIMAL},
            #{designPlanDetail.pamCode,jdbcType=VARCHAR}, #{designPlanDetail.erpCode,jdbcType=VARCHAR}, #{designPlanDetail.erpCodeSource,jdbcType=INTEGER},
            #{designPlanDetail.materielStatus,jdbcType=INTEGER}, #{designPlanDetail.source,jdbcType=VARCHAR}, #{designPlanDetail.remark,jdbcType=VARCHAR},
            #{designPlanDetail.status,jdbcType=INTEGER}, #{designPlanDetail.generateRequirement,jdbcType=TINYINT}, #{designPlanDetail.deletedFlag,jdbcType=TINYINT},
            #{designPlanDetail.createBy,jdbcType=BIGINT}, #{designPlanDetail.createAt,jdbcType=TIMESTAMP}, #{designPlanDetail.updateBy,jdbcType=BIGINT},
            #{designPlanDetail.updateAt,jdbcType=TIMESTAMP}, #{designPlanDetail.init,jdbcType=TINYINT}, #{designPlanDetail.itemCostIsNull,jdbcType=TINYINT},
            #{designPlanDetail.materialCategory,jdbcType=VARCHAR}, #{designPlanDetail.codingMiddleClass,jdbcType=VARCHAR}, #{designPlanDetail.figureNumber,jdbcType=VARCHAR},
            #{designPlanDetail.chartVersion,jdbcType=VARCHAR}, #{designPlanDetail.brandMaterialCode,jdbcType=VARCHAR}, #{designPlanDetail.orSparePartsMask,jdbcType=VARCHAR},
            #{designPlanDetail.requirementCreatDate,jdbcType=DATE}, #{designPlanDetail.perchasingLeadtime,jdbcType=BIGINT}, #{designPlanDetail.minPerchaseQuantity,jdbcType=BIGINT},
            #{designPlanDetail.minPackageQuantity,jdbcType=BIGINT}, #{designPlanDetail.wbsSummaryCode,jdbcType=VARCHAR}, #{designPlanDetail.wbsLayer,jdbcType=VARCHAR},
            #{designPlanDetail.wbsDescription,jdbcType=VARCHAR}, #{designPlanDetail.wbsConfirmFlag,jdbcType=TINYINT}, #{designPlanDetail.wbsLastLayer,jdbcType=TINYINT},
            #{designPlanDetail.dispatchIs,jdbcType=TINYINT}, #{designPlanDetail.extIs,jdbcType=TINYINT}, #{designPlanDetail.activityCode,jdbcType=VARCHAR},
            #{designPlanDetail.projectBudgetType,jdbcType=VARCHAR}, #{designPlanDetail.planDesigner,jdbcType=VARCHAR}, #{designPlanDetail.designReleaseLotNumber,jdbcType=VARCHAR},
            #{designPlanDetail.description,jdbcType=VARCHAR}, #{designPlanDetail.requirementType,jdbcType=INTEGER})
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" open=" " separator=" " close="">
            update milepost_design_plan_detail
            <set>
                <if test="item.projectBudgetMaterialId != null">
                    project_budget_material_id = #{item.projectBudgetMaterialId,jdbcType=BIGINT},
                </if>
                <if test="item.projectId != null">
                    project_id = #{item.projectId,jdbcType=BIGINT},
                </if>
                <if test="item.milepostId != null">
                    milepost_id = #{item.milepostId,jdbcType=BIGINT},
                </if>
                <if test="item.submitRecordId != null">
                    submit_record_id = #{item.submitRecordId,jdbcType=BIGINT},
                </if>
                <if test="item.submitRecordRelId != null">
                    submit_record_rel_id = #{item.submitRecordRelId,jdbcType=BIGINT},
                </if>
                <if test="item.changeRecordId != null">
                    change_record_id = #{item.changeRecordId,jdbcType=BIGINT},
                </if>
                <if test="item.parentId != null">
                    parent_id = #{item.parentId,jdbcType=BIGINT},
                </if>
                <if test="item.level != null">
                    level = #{item.level,jdbcType=INTEGER},
                </if>
                <if test="item.num != null">
                    num = #{item.num,jdbcType=VARCHAR},
                </if>
                <if test="item.whetherModel != null">
                    whether_model = #{item.whetherModel,jdbcType=TINYINT},
                </if>
                <if test="item.moduleStatus != null">
                    module_status = #{item.moduleStatus,jdbcType=INTEGER},
                </if>
                <if test="item.ext != null">
                    ext = #{item.ext,jdbcType=TINYINT},
                </if>
                <if test="item.materielDescr != null">
                    materiel_descr = #{item.materielDescr,jdbcType=VARCHAR},
                </if>
                <if test="item.unit != null">
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.unitCode != null">
                    unit_code = #{item.unitCode,jdbcType=VARCHAR},
                </if>
                <if test="item.number != null">
                    number = #{item.number,jdbcType=DECIMAL},
                </if>
                <if test="item.deliveryTime != null">
                    delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.name != null">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.model != null">
                    model = #{item.model,jdbcType=VARCHAR},
                </if>
                <if test="item.brand != null">
                    brand = #{item.brand,jdbcType=VARCHAR},
                </if>
                <if test="item.businessClassification != null">
                    business_classification = #{item.businessClassification,jdbcType=VARCHAR},
                </if>
                <if test="item.materialClassification != null">
                    material_classification = #{item.materialClassification,jdbcType=VARCHAR},
                </if>
                <if test="item.materielType != null">
                    materiel_type = #{item.materielType,jdbcType=VARCHAR},
                </if>
                <if test="item.machiningPartType != null">
                    machining_part_type = #{item.machiningPartType,jdbcType=VARCHAR},
                </if>
                <if test="item.material != null">
                    material = #{item.material,jdbcType=VARCHAR},
                </if>
                <if test="item.unitWeight != null">
                    unit_weight = #{item.unitWeight,jdbcType=DECIMAL},
                </if>
                <if test="item.materialProcessing != null">
                    material_processing = #{item.materialProcessing,jdbcType=VARCHAR},
                </if>
                <if test="item.budgetUnitPrice != null">
                    budget_unit_price = #{item.budgetUnitPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.designCostId != null">
                    design_cost_id = #{item.designCostId,jdbcType=BIGINT},
                </if>
                <if test="item.designCost != null">
                    design_cost = #{item.designCost,jdbcType=DECIMAL},
                </if>
                <if test="item.budgetSubtotal != null">
                    budget_subtotal = #{item.budgetSubtotal,jdbcType=DECIMAL},
                </if>
                <if test="item.pamCode != null">
                    pam_code = #{item.pamCode,jdbcType=VARCHAR},
                </if>
                <if test="item.erpCode != null">
                    erp_code = #{item.erpCode,jdbcType=VARCHAR},
                </if>
                <if test="item.erpCodeSource != null">
                    erp_code_source = #{item.erpCodeSource,jdbcType=INTEGER},
                </if>
                <if test="item.materielStatus != null">
                    materiel_status = #{item.materielStatus,jdbcType=INTEGER},
                </if>
                <if test="item.source != null">
                    source = #{item.source,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null">
                    status = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.generateRequirement != null">
                    generate_requirement = #{item.generateRequirement,jdbcType=TINYINT},
                </if>
                <if test="item.deletedFlag != null">
                    deleted_flag = #{item.deletedFlag,jdbcType=TINYINT},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy,jdbcType=BIGINT},
                </if>
                <if test="item.createAt != null">
                    create_at = #{item.createAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy,jdbcType=BIGINT},
                </if>
                <if test="item.updateAt != null">
                    update_at = #{item.updateAt,jdbcType=TIMESTAMP},
                </if>
                <if test="item.init != null">
                    init = #{item.init,jdbcType=TINYINT},
                </if>
                <if test="item.itemCostIsNull != null">
                    item_cost_is_null = #{item.itemCostIsNull,jdbcType=TINYINT},
                </if>
                <if test="item.materialCategory != null">
                    material_category = #{item.materialCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.codingMiddleClass != null">
                    coding_middle_class = #{item.codingMiddleClass,jdbcType=VARCHAR},
                </if>
                <if test="item.figureNumber != null">
                    figure_number = #{item.figureNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.chartVersion != null">
                    chart_version = #{item.chartVersion,jdbcType=VARCHAR},
                </if>
                <if test="item.brandMaterialCode != null">
                    brand_material_code = #{item.brandMaterialCode,jdbcType=VARCHAR},
                </if>
                <if test="item.orSparePartsMask != null">
                    or_spare_parts_mask = #{item.orSparePartsMask,jdbcType=VARCHAR},
                </if>
                <if test="item.requirementCreatDate != null">
                    requirement_creat_date = #{item.requirementCreatDate,jdbcType=DATE},
                </if>
                <if test="item.perchasingLeadtime != null">
                    perchasing_leadtime = #{item.perchasingLeadtime,jdbcType=BIGINT},
                </if>
                <if test="item.minPerchaseQuantity != null">
                    min_perchase_quantity = #{item.minPerchaseQuantity,jdbcType=BIGINT},
                </if>
                <if test="item.minPackageQuantity != null">
                    min_package_quantity = #{item.minPackageQuantity,jdbcType=BIGINT},
                </if>
                <if test="item.wbsSummaryCode != null">
                    wbs_summary_code = #{item.wbsSummaryCode,jdbcType=VARCHAR},
                </if>
                <if test="item.wbsLayer != null">
                    wbs_layer = #{item.wbsLayer,jdbcType=VARCHAR},
                </if>
                <if test="item.wbsDescription != null">
                    wbs_description = #{item.wbsDescription,jdbcType=VARCHAR},
                </if>
                <if test="item.wbsConfirmFlag != null">
                    wbs_confirm_flag = #{item.wbsConfirmFlag,jdbcType=TINYINT},
                </if>
                <if test="item.wbsLastLayer != null">
                    wbs_last_layer = #{item.wbsLastLayer,jdbcType=TINYINT},
                </if>
                <if test="item.dispatchIs != null">
                    dispatch_is = #{item.dispatchIs,jdbcType=TINYINT},
                </if>
                <if test="item.extIs != null">
                    ext_is = #{item.extIs,jdbcType=TINYINT},
                </if>
                <if test="item.activityCode != null">
                    activity_code = #{item.activityCode,jdbcType=VARCHAR},
                </if>
                <if test="item.projectBudgetType != null">
                    project_budget_type = #{item.projectBudgetType,jdbcType=VARCHAR},
                </if>
                <if test="item.planDesigner != null">
                    plan_designer = #{item.planDesigner,jdbcType=VARCHAR},
                </if>
                <if test="item.designReleaseLotNumber != null">
                    design_release_lot_number = #{item.designReleaseLotNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.description != null">
                    description = #{item.description,jdbcType=VARCHAR},
                </if>
                <if test="item.requirementType != null">
                    requirement_type = #{item.requirementType,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT};
        </foreach>
    </update>

    <select id="selectWbsLayerDetailByProjectId"
            parameterType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Column_ListNew"/>
        ,
        (
            select
              ifnull(sum(releaseDetail.publish_num), 0)
            from
              pam_ctc.purchase_material_release_detail releaseDetail
            where
              releaseDetail.design_plan_detail_id = milepost_design_plan_detail.id
              and (
                releaseDetail.deleted_flag = 0
                or releaseDetail.deleted_flag is null
              )
          ) as requirement_num
        from
          pam_ctc.milepost_design_plan_detail
          LEFT JOIN (
            SELECT
              design_plan_detail_id,
              prr.status AS purchase_status
            FROM
              milepost_design_plan_purchase_record prr
              INNER JOIN milepost_design_plan_purchase_record_relation rr2 ON prr.id = rr2.confirm_record_id
              AND rr2.deleted_flag = 0
            WHERE
              prr.deleted_flag = 0
              AND prr.id IN (
                SELECT
                  MAX(pr.id)
                FROM
                  milepost_design_plan_detail md
                  INNER JOIN milepost_design_plan_purchase_record_relation rr ON md.id = rr.design_plan_detail_id
                  AND rr.deleted_flag = 0
                  INNER JOIN milepost_design_plan_purchase_record pr ON rr.confirm_record_id = pr.id
                  AND pr.deleted_flag = 0
                WHERE
                  rr.`design_plan_detail_id` = rr2.design_plan_detail_id
                GROUP BY
                  rr.design_plan_detail_id
              )
          ) t ON t.design_plan_detail_id = milepost_design_plan_detail.id
          LEFT JOIN (
            SELECT
              design_plan_detail_id,
              rr2.purchase_num
            FROM
              milepost_design_plan_purchase_record prr
              INNER JOIN milepost_design_plan_purchase_record_relation rr2 ON prr.id = rr2.confirm_record_id
              AND rr2.deleted_flag = 0
            WHERE
              prr.deleted_flag = 0
              AND prr.status in (0, -1, 1, 3, 40)
              AND prr.id IN (
                SELECT
                  MAX(pr.id)
                FROM
                  milepost_design_plan_detail md
                  INNER JOIN milepost_design_plan_purchase_record_relation rr ON md.id = rr.design_plan_detail_id
                  AND rr.deleted_flag = 0
                  INNER JOIN milepost_design_plan_purchase_record pr ON rr.confirm_record_id = pr.id
                  AND pr.deleted_flag = 0
                WHERE
                  rr.`design_plan_detail_id` = rr2.design_plan_detail_id
                GROUP BY
                  rr.design_plan_detail_id
              )
          ) t2 ON t2.design_plan_detail_id = milepost_design_plan_detail.id
          LEFT JOIN (
            SELECT
              design_plan_detail_id,
              rr2.purchase_num AS approval_num
            FROM
              milepost_design_plan_purchase_record prr
              INNER JOIN milepost_design_plan_purchase_record_relation rr2 ON prr.id = rr2.confirm_record_id
              AND rr2.deleted_flag = 0
            WHERE
              prr.deleted_flag = 0
              AND prr.status in (-1)
              AND prr.id IN (
                SELECT
                  MAX(pr.id)
                FROM
                  milepost_design_plan_detail md
                  INNER JOIN milepost_design_plan_purchase_record_relation rr ON md.id = rr.design_plan_detail_id
                  AND rr.deleted_flag = 0
                  INNER JOIN milepost_design_plan_purchase_record pr ON rr.confirm_record_id = pr.id
                  AND pr.deleted_flag = 0
                WHERE
                  rr.`design_plan_detail_id` = rr2.design_plan_detail_id
                GROUP BY
                  rr.design_plan_detail_id
              )
          ) t3 ON t3.design_plan_detail_id = milepost_design_plan_detail.id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_ForWbsLayer"/>
            and milepost_design_plan_detail.wbs_layer is not null
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="getDetailsByParentId" resultMap="BaseResultMap">
        with recursive mdpd as (
        select *
        from milepost_design_plan_detail where
        id = #{detailId}
        union
        select a.*
        from milepost_design_plan_detail a
        join mdpd
        where a.parent_id = mdpd.id
        )
        select *,ifnull(ifnull(pwr2.requirement_code,pwr.requirement_code),pwr3.requirement_code) as requirementCode,
        ifnull(ifnull(pwr2.requirement_status,pwr.requirement_status),pwr3.requirement_status) as requirementStatus
        from mdpd
        left join (
        select
        pwr2.requirement_code ,
        pwr2.requirement_status ,
        mdpdsh.design_plan_detail_id
        from
        pam_ctc.milepost_design_plan_detail_submit_history mdpdsh
        left join pam_ctc.project_wbs_receipts pwr2 on
        mdpdsh.project_wbs_receipts_id = pwr2.id
        where
        (pwr2.requirement_status != 4
        and pwr2.requirement_status != 5)
        and pwr2.deleted_flag = 0
        and mdpdsh.deleted_flag = 0 group by mdpdsh.design_plan_detail_id)pwr2 on
        mdpd.id = pwr2.design_plan_detail_id and ( mdpd.wbs_last_layer !=0 or  mdpd.wbs_last_layer is null)
        left join (
        select
        pwr.requirement_code ,
        pwr.requirement_status ,
        pwrdpr.design_plan_detail_id
        from
        pam_ctc.project_wbs_receipts_design_plan_rel pwrdpr
        left join pam_ctc.project_wbs_receipts pwr on
        pwrdpr.project_wbs_receipts_id = pwr.id
        where
        (pwr.requirement_status != 4
        and pwr.requirement_status != 5)
        and pwr.deleted_flag = 0
        and pwrdpr.deleted_flag = 0 group by pwrdpr.design_plan_detail_id)pwr on
        mdpd.id = pwr.design_plan_detail_id and ( mdpd.wbs_last_layer !=0 or  mdpd.wbs_last_layer is null)
        left join (
        select
        pwr3.requirement_code ,
        pwr3.requirement_status ,
        mdpdc.design_plan_detail_id
        from
        pam_ctc.milepost_design_plan_detail_change mdpdc
        left join pam_ctc.project_wbs_receipts pwr3 on
        mdpdc.project_wbs_receipts_id = pwr3.id
        where
        mdpdc.project_wbs_receipts_id is not null
        and (pwr3.requirement_status != 4 and pwr3.requirement_status != 5)
        and pwr3.deleted_flag = 0 and mdpdc.change_type = 0
        and mdpdc.deleted_flag = 0 group by mdpdc.design_plan_detail_id)pwr3 on
        mdpd.id = pwr3.design_plan_detail_id and ( mdpd.wbs_last_layer !=0 or  mdpd.wbs_last_layer is null)
        where mdpd.deleted_flag != 1
    </select>

    <select id="selectNotPublishRequirementByQuery" resultMap="BaseResultMap">
        select
            detail.id,
            detail.project_id,
            detail.milepost_id,
            detail.submit_record_id,
            detail.change_record_id,
            detail.parent_id,
            detail.materiel_descr,
            detail.unit,
            detail.delivery_time,
            detail.name,
            detail.model,
            detail.brand,
            detail.material_classification,
            detail.materiel_type,
            detail.pam_code,
            detail.erp_code,
            detail.material_category,
            detail.coding_middle_class,
            detail.figure_number,
            (
            select
                ifnull(sum(releaseDetail.publish_num), 0)
            from
                purchase_material_release_detail releaseDetail
            where
                releaseDetail.design_plan_detail_id = detail.id
                and (releaseDetail.deleted_flag = 0
                    or releaseDetail.deleted_flag is null)) as requirement_num,
            ifnull(mdpsr.create_at, null) as publish_at,
            ifnull(mdpsr.create_by, null) as publish_by
        from
            milepost_design_plan_detail detail
        left join milepost_design_plan_submit_record mdpsr on
            detail.submit_record_id = mdpsr.id
        where
            (detail.deleted_flag = 0
                or detail.deleted_flag is null)
            and detail.material_category = '外购物料'
            <if test="projectIdList != null and projectIdList.size() > 0">
                and detail.project_id in
                <foreach collection="projectIdList" item="projectId" index="index" open="(" separator="," close=")">
                    #{projectId}
                </foreach>
            </if>
    </select>

    <select id="getTotalNumberById" resultType="java.math.BigDecimal">
        select EXP(SUM(LOG(b.number))) as totalNumber from
        (with recursive mdpd as (
        select
            id,parent_id,number,deleted_flag
        from
            milepost_design_plan_detail
        where
            id = #{designId}
        union
        select
            a.id,a.parent_id,a.number,a.deleted_flag
        from
            milepost_design_plan_detail a
        join mdpd
        where
            a.id = mdpd.parent_id
                )
                select
            *
        from
            mdpd
        where
            (mdpd.deleted_flag = 0 or mdpd.deleted_flag is null)) b
    </select>

<!--    -->
    <select id="queryMilepostDesignPlanDetailProjectIdCount"
            resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailProjectIdCount">
        select
            project_id as projectId,
            count(project_id)as count
        from
            pam_ctc.milepost_design_plan_detail mdpd
        where
            deleted_flag = 0
            and project_id is not null
        group by
            project_id ;

    </select>

    <select id="calculateMilepostDesignPlanDetailNumber"
            parameterType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto"
            resultMap="BaseResultMap">
        select
            md.id,
            md.pam_code,
            md.wbs_summary_code,
            md.delivery_time,
            md.project_id,
            if(md.module_status = 1 and md.deleted_flag = 0, md.number, 0) as 'number'
        from
            (with recursive cte as (
            select
                pm.id,
                pm.project_id,
                pm.parent_id,
                ifnull(pm.number, 0) as 'number',
                pm.pam_code ,
                pm.wbs_summary_code,
                pm.delivery_time,
                pm.module_status,
                pm.deleted_flag
            from
                pam_ctc.milepost_design_plan_detail pm
            where
                pm.parent_id = -1
        union all
            select
                m.id,
                m.project_id,
                m.parent_id,
                ifnull(m.number, 0) * ifnull(cte.number, 0) as 'number',
                m.pam_code ,
                m.wbs_summary_code ,
                m.delivery_time,
                m.module_status,
                m.deleted_flag
            from
                pam_ctc.milepost_design_plan_detail m
            inner join cte on
                cte.id = m.parent_id
        )
            select
                *
            from
                cte
        ) md
        where
            md.wbs_summary_code is not null
            and md.wbs_summary_code <![CDATA[ <> ]]> ''
            and md.pam_code is not null
            and md.pam_code <![CDATA[ <> ]]> ''
            and md.delivery_time is not null
            <if test="idList != null and idList.size() > 0">
                and md.id in
                <foreach collection="idList" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="wbsSummaryCode != null">
                and md.wbs_summary_code = #{wbsSummaryCode}
            </if>
            <if test="pamCode != null">
                and md.pam_code = #{pamCode}
            </if>
            <if test="deliveryTime != null">
                and md.delivery_time = #{deliveryTime}
            </if>
    </select>

        <select id="selectParentRecordsInApprovalById"  resultType="com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail">
            WITH RECURSIVE parent_tree AS (
                SELECT
                    id,
                    parent_id,
                    module_status,
                    materiel_descr,
                    CAST(id AS CHAR(50)) as path,
                    0 as depth
                FROM milepost_design_plan_detail
                WHERE id = #{designPlanId}

                UNION ALL

                SELECT
                    m.id,
                    m.parent_id,
                    m.module_status,
                    m.materiel_descr,
                    CONCAT(m.id, ',', p.path),
                    p.depth + 1
                FROM milepost_design_plan_detail m
                INNER JOIN parent_tree p ON p.parent_id = m.id
                WHERE (m.module_status = 2 OR m.id = #{designPlanId})
            )
            SELECT
                id,
                parent_id,
                module_status,
                materiel_descr
            FROM parent_tree
            ORDER BY depth ASC;
        </select>


    <update id="updateModuleStatusConfirmed">
        update milepost_design_plan_detail
        set module_status = 1
        where deleted_flag = 0
        and whether_model = 1
        and project_id = #{projectId}
    </update>

    <update id="updateModuleStatusUnConfirmed">
        update milepost_design_plan_detail
        set module_status = 0
        where deleted_flag = 0
        and module_status is null
        and project_id = #{projectId}
    </update>

    <update id="updateByMaterialCost" parameterType="com.midea.pam.common.basedata.dto.MaterialCostDto">
        UPDATE pam_ctc.milepost_design_plan_detail mdpd
        inner join pam_ctc.project p on
            mdpd.project_id = p.id
        SET
            mdpd.design_cost_id = #{id},
            mdpd.design_cost = #{itemCost},
            mdpd.item_cost_is_null =
            CASE
                WHEN #{materialCostType} = 3 THEN 1
            ELSE
                CASE WHEN #{itemCostIsNull} = TRUE THEN 1 ELSE 0 END
            END
        WHERE
            mdpd.erp_code = #{itemCode}
            AND p.ou_id = #{ouId}
            AND mdpd.deleted_flag = false
            AND mdpd.status IN (1, 18)
    </update>

    <select id="getMilepostDesignPlanDetails" resultType="com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto">
    select
        mdpd.id,
        mdpd.erp_code as erpCode,
        p.ou_id as ouId
    from
        pam_ctc.milepost_design_plan_detail mdpd
    inner join pam_ctc.project p on
        mdpd.project_id = p.id
    where
        mdpd.deleted_flag = false
        <if test="erpCodeList != null and erpCodeList.size>0 ">
            and mdpd.erp_code in
            <foreach collection="erpCodeList" item="erpCode" index="index" open="(" separator=","
                     close=")">
                #{erpCode}
            </foreach>
        </if>
        and mdpd.status in ( 1 , 18 )
    </select>

    <update id="updateStatusByProjectId">
        update milepost_design_plan_detail
        set status = #{status}
        where deleted_flag = 0
        and project_id = #{projectId}
    </update>

</mapper>