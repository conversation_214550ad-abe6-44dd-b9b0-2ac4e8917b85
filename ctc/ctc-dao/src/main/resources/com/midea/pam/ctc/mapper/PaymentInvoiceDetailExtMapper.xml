<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PaymentInvoiceDetailExtMapper">

    <sql id="Base_Column_List">
        pid.id as id,
        pid.invoice_type as invoiceType,
        pid.purchase_contract_id as purchaseContractId,
        pid.ou_id as ouId,
        pid.purchase_contract_code as purchaseContractCode,
        pid.purchase_contract_name as purchaseContractName,
        pid.vendor_id as vendorId,
        pid.vendor_code as vendorCode,
        pid.vendor_name as vendorName,
        pid.project_id as projectId,
        pid.project_code as projectCode,
        pid.project_name as projectName,
        pid.currency as currency,
        pid.attribute as attribute,
        pid.invoice_detail_code as invoiceDetailCode,
        pid.tax_included_price as taxIncludedPrice,
        pid.invoice_date as invoiceDate,
        pid.tax_amount as taxAmount,
        pid.tax_rate as taxRate,
        pid.tax_excluded_price as taxExcludedPrice,
        pid.remark as remark,
        pid.invoice_status as invoiceStatus,
        pid.create_by as createBy,
        pid.create_at as createAt,
        pid.update_by as updateBy,
        pid.update_at as updateAt,
        pid.deleted_flag as deletedFlag,
        pid.due_date as dueDate,
        pid.version as version
    </sql>

    <select id="selectPage" parameterType="com.midea.pam.common.ctc.query.PaymentInvoiceDetailQuery"
            resultType="com.midea.pam.common.ctc.dto.PaymentInvoiceDetailDto">
          select
            <include refid="Base_Column_List" />
          from
          payment_invoice_detail pid
          where 1 = 1
        <include refid="queryCondition" />
  </select>

    <select id="selectListByApplyId" parameterType="com.midea.pam.common.ctc.query.PaymentInvoiceDetailQuery"
            resultType="com.midea.pam.common.ctc.dto.PaymentInvoiceDetailDto">
        select
          <include refid="Base_Column_List" />,
          padr.amount as useAmount
        from
        payment_apply_detail_rel padr left join payment_invoice_detail pid on pid.id = padr.invoice_detail_id
        where
        pid.deleted_flag = 0 and padr.deleted_flag = 0
        <if test="applyId != null and applyId != ''">
            and padr.payment_apply_id = #{applyId}
        </if>
    </select>

    <select id="selectByPaymentInvoiceIds" resultType="com.midea.pam.common.ctc.entity.PaymentInvoiceDetail">
        select
            id,
            payment_invoice_id paymentInvoiceId
        from payment_invoice_detail
        where payment_invoice_id in
        <foreach collection="paymentInvoiceIds" item="paymentInvoiceId" open="(" separator="," close=")">
            #{paymentInvoiceId}
        </foreach>
        and deleted_flag = 0
    </select>

    <sql id="queryCondition">
        <!-- id-->
        <if test="id != null">
            AND pid.id = #{id}
        </if>
    </sql>

    <select id="selectListByPurchaseContractId" resultType="com.midea.pam.common.ctc.dto.PaymentInvoiceDetailDto">
        select
            pid.id as id,
            pid.payment_invoice_id as paymentInvoiceId,
            pid.invoice_type as invoiceType,
            pid.purchase_contract_id as purchaseContractId,
            pid.ou_id as ouId,
            pid.purchase_contract_code as purchaseContractCode,
            pid.purchase_contract_name as purchaseContractName,
            pid.vendor_id as vendorId,
            pid.vendor_code as vendorCode,
            pid.vendor_name as vendorName,
            pid.vendor_site_code as vendorSiteCode,
            pid.project_id as projectId,
            pid.project_code as projectCode,
            pid.project_name as projectName,
            pid.currency as currency,
            pid.attribute as attribute,
            pid.invoice_detail_code as invoiceDetailCode,
            pid.line_num as lineNum,
            pid.tax_included_price as taxIncludedPrice,
            pid.invoice_date as invoiceDate,
            pid.tax_amount as taxAmount,
            pid.tax_rate as taxRate,
            pid.tax_excluded_price as taxExcludedPrice,
            pid.remark as remark,
            pid.invoice_status as invoiceStatus,
            pid.create_by as createBy,
            pid.create_at as createAt,
            pid.update_by as updateBy,
            pid.update_at as updateAt,
            pid.deleted_flag as deletedFlag,
            pid.version as version,
            pid.due_date as dueDate,
            pi.ap_invoice_code as apInvoiceCode,
            pid.gsc_line_id as gscLineId,
            pid.gsc_invoice_number as gscInvoiceNumber
        from pam_ctc.payment_invoice_detail pid
        left join pam_ctc.payment_invoice pi on pid.payment_invoice_id = pi.id
        where pid.deleted_flag = 0
        <if test="purchaseContractId != null">
            and pid.purchase_contract_id = #{purchaseContractId}
        </if>
        order by pid.create_at desc
    </select>

    <update id="batchUpdate" parameterType="java.util.List">

        <foreach collection="list" item="item" separator=";">
            update payment_invoice_detail
        <set>
            <if test="item.paymentInvoiceId != null">
                <if test="item.paymentInvoiceId == -1">
                    payment_invoice_id = NULL,
                </if>
                <if test="item.paymentInvoiceId != -1">
                    payment_invoice_id = #{item.paymentInvoiceId,jdbcType=BIGINT},
                </if>
            </if>
            <if test="item.invoiceType != null">
                invoice_type = #{item.invoiceType,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseContractId != null">
                purchase_contract_id = #{item.purchaseContractId,jdbcType=BIGINT},
            </if>
            <if test="item.ouId != null">
                ou_id = #{item.ouId,jdbcType=BIGINT},
            </if>
            <if test="item.purchaseContractCode != null">
                purchase_contract_code = #{item.purchaseContractCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseContractName != null">
                purchase_contract_name = #{item.purchaseContractName,jdbcType=VARCHAR},
            </if>
            <if test="item.vendorId != null">
                vendor_id = #{item.vendorId,jdbcType=BIGINT},
            </if>
            <if test="item.vendorCode != null">
                vendor_code = #{item.vendorCode,jdbcType=VARCHAR},
            </if>
            <if test="item.vendorName != null">
                vendor_name = #{item.vendorName,jdbcType=VARCHAR},
            </if>
            <if test="item.vendorSiteCode != null">
                vendor_site_code = #{item.vendorSiteCode,jdbcType=VARCHAR},
            </if>
            <if test="item.projectId != null">
                project_id = #{item.projectId,jdbcType=BIGINT},
            </if>
            <if test="item.projectCode != null">
                project_code = #{item.projectCode,jdbcType=VARCHAR},
            </if>
            <if test="item.projectName != null">
                project_name = #{item.projectName,jdbcType=VARCHAR},
            </if>
            <if test="item.currency != null">
                currency = #{item.currency,jdbcType=VARCHAR},
            </if>
            <if test="item.attribute != null">
                attribute = #{item.attribute,jdbcType=VARCHAR},
            </if>
            <if test="item.invoiceDetailCode != null">
                invoice_detail_code = #{item.invoiceDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="item.lineNum != null">
                line_num = #{item.lineNum,jdbcType=INTEGER},
            </if>
            <if test="item.taxIncludedPrice != null">
                tax_included_price = #{item.taxIncludedPrice,jdbcType=DECIMAL},
            </if>
            <if test="item.invoiceDate != null">
                invoice_date = #{item.invoiceDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.taxAmount != null">
                tax_amount = #{item.taxAmount,jdbcType=DECIMAL},
            </if>
            <if test="item.taxRate != null">
                tax_rate = #{item.taxRate,jdbcType=DECIMAL},
            </if>
            <if test="item.taxExcludedPrice != null">
                tax_excluded_price = #{item.taxExcludedPrice,jdbcType=DECIMAL},
            </if>
            <if test="item.remark != null">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.invoiceStatus != null">
                invoice_status = #{item.invoiceStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.createBy != null">
                create_by = #{item.createBy,jdbcType=BIGINT},
            </if>
            <if test="item.createAt != null">
                create_at = #{item.createAt,jdbcType=TIMESTAMP},
            </if>
            <if test="item.updateBy != null">
                update_by = #{item.updateBy,jdbcType=BIGINT},
            </if>
            <if test="item.updateAt != null">
                update_at = #{item.updateAt,jdbcType=TIMESTAMP},
            </if>
            <if test="item.deletedFlag != null">
                deleted_flag = #{item.deletedFlag,jdbcType=TINYINT},
            </if>
            <if test="item.version != null">
                version = #{item.version,jdbcType=BIGINT},
            </if>
            <if test="item.billId != null">
                bill_id = #{item.billId,jdbcType=BIGINT},
            </if>
            <if test="item.paymentTerm != null">
                payment_term = #{item.paymentTerm,jdbcType=INTEGER},
            </if>
            <if test="item.dueDate != null">
                due_date = #{item.dueDate,jdbcType=DATE},
            </if>
            <if test="item.isManualAmendment != null">
                is_manual_amendment = #{item.isManualAmendment,jdbcType=TINYINT},
            </if>
        </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>

    </update>

    <update id="updateInvoiceAccountingSubject">
        update payment_invoice_detail
        set accounting_subject_project = #{accountingSubjectProject},accounting_subject_tax = #{accountingSubjectTax},update_at = now()
        where id = #{invoiceId}
    </update>

    <select id="getDetailListByContractIdAndTaxRate" resultType="com.midea.pam.common.ctc.entity.PaymentInvoiceDetail">
        select
            tax_amount,tax_excluded_price,tax_rate,tax_included_price
        from payment_invoice_detail
            where
                deleted_flag = 0
                and purchase_contract_id = #{purchaseContractId}
                and tax_rate = #{taxRate}
    </select>

    <update id="logicDeletePaymentInvoiceDetail">
         update pam_ctc.payment_invoice_detail set deleted_flag = 1 where payment_invoice_id = #{invoiceId}
    </update>

</mapper>