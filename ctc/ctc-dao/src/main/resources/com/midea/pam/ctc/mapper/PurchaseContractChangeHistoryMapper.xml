<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.midea.pam.ctc.mapper.PurchaseContractChangeHistoryMapper">
  <resultMap id="BaseResultMap" type="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="header_id" jdbcType="BIGINT" property="headerId" />
    <result column="origin_id" jdbcType="BIGINT" property="originId" />
    <result column="history_type" jdbcType="INTEGER" property="historyType" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="ou_id" jdbcType="BIGINT" property="ouId" />
    <result column="type_id" jdbcType="BIGINT" property="typeId" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="legal_affairs_code" jdbcType="VARCHAR" property="legalAffairsCode" />
    <result column="vendor_id" jdbcType="BIGINT" property="vendorId" />
    <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode" />
    <result column="vendor_name" jdbcType="VARCHAR" property="vendorName" />
    <result column="erp_vendor_site_id" jdbcType="VARCHAR" property="erpVendorSiteId" />
    <result column="vendor_site_code" jdbcType="VARCHAR" property="vendorSiteCode" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="excluding_tax_amount" jdbcType="DECIMAL" property="excludingTaxAmount" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="manager" jdbcType="BIGINT" property="manager" />
    <result column="manager_name" jdbcType="VARCHAR" property="managerName" />
    <result column="purchasing_follower" jdbcType="BIGINT" property="purchasingFollower" />
    <result column="purchasing_follower_name" jdbcType="VARCHAR" property="purchasingFollowerName" />
    <result column="classes" jdbcType="TINYINT" property="classes" />
    <result column="belong_area" jdbcType="TINYINT" property="belongArea" />
    <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod" />
    <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
    <result column="is_electronic_contract" jdbcType="TINYINT" property="isElectronicContract" />
    <result column="other_name" jdbcType="VARCHAR" property="otherName" />
    <result column="other_id" jdbcType="VARCHAR" property="otherId" />
    <result column="other_phone" jdbcType="VARCHAR" property="otherPhone" />
    <result column="other_email" jdbcType="VARCHAR" property="otherEmail" />
    <result column="public_or_private" jdbcType="TINYINT" property="publicOrPrivate" />
    <result column="seal_category" jdbcType="TINYINT" property="sealCategory" />
    <result column="seal_admin_account_ids" jdbcType="VARCHAR" property="sealAdminAccountIds" />
    <result column="annex" jdbcType="VARCHAR" property="annex" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="filing_date" jdbcType="TIMESTAMP" property="filingDate" />
    <result column="carryover_flag" jdbcType="TINYINT" property="carryoverFlag" />
    <result column="is_synchronize_legal_system_flag" jdbcType="TINYINT" property="isSynchronizeLegalSystemFlag" />
    <result column="gle_sign_flag" jdbcType="TINYINT" property="gleSignFlag" />
    <result column="notsync_type" jdbcType="VARCHAR" property="notsyncType" />
    <result column="notsync_reason" jdbcType="VARCHAR" property="notsyncReason" />
    <result column="deleted_flag" jdbcType="TINYINT" property="deletedFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="tax_id" jdbcType="BIGINT" property="taxId" />
    <result column="tax_rate" jdbcType="VARCHAR" property="taxRate" />
    <result column="legal_business_id" jdbcType="VARCHAR" property="legalBusinessId" />
    <result column="legal_contract_num" jdbcType="VARCHAR" property="legalContractNum" />
    <result column="execute_contract_percent_total" jdbcType="DECIMAL" property="executeContractPercentTotal" />
    <result column="delivery_type" jdbcType="VARCHAR" property="deliveryType" />
    <result column="delivery_clause" jdbcType="VARCHAR" property="deliveryClause" />
    <result column="delivery_date" jdbcType="TIMESTAMP" property="deliveryDate" />
    <result column="payment_term" jdbcType="INTEGER" property="paymentTerm" />
    <result column="contract_terms_flg" jdbcType="VARCHAR" property="contractTermsFlg" />
    <result column="contract_terms_ids" jdbcType="VARCHAR" property="contractTermsIds" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory">
    <result column="original_contract_annex" jdbcType="LONGVARCHAR" property="originalContractAnnex" />
    <result column="if_upload_change_file" jdbcType="LONGVARCHAR" property="ifUploadChangeFile" />
    <result column="contract_terms" jdbcType="LONGVARCHAR" property="contractTerms" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, header_id, origin_id, history_type, project_id, code, name, ou_id, type_id, type_name, 
    legal_affairs_code, vendor_id, vendor_code, vendor_name, erp_vendor_site_id, vendor_site_code, 
    amount, excluding_tax_amount, currency, start_time, end_time, manager, manager_name, 
    purchasing_follower, purchasing_follower_name, classes, belong_area, payment_method, 
    invoice_type, is_electronic_contract, other_name, other_id, other_phone, other_email, 
    public_or_private, seal_category, seal_admin_account_ids, annex, remark, status, 
    filing_date, carryover_flag, is_synchronize_legal_system_flag, gle_sign_flag, notsync_type,
    notsync_reason, deleted_flag, create_by, create_at, update_by, update_at, tax_id,
    tax_rate, legal_business_id, legal_contract_num, execute_contract_percent_total,
    delivery_type, delivery_clause, delivery_date, payment_term, contract_terms_flg,
    contract_terms_ids
  </sql>
  <sql id="Blob_Column_List">
    original_contract_annex, if_upload_change_file, contract_terms
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistoryExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from purchase_contract_change_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from purchase_contract_change_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from purchase_contract_change_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_contract_change_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory">
    insert into purchase_contract_change_history (id, header_id, origin_id, 
      history_type, project_id, code, 
      name, ou_id, type_id, type_name, 
      legal_affairs_code, vendor_id, vendor_code, 
      vendor_name, erp_vendor_site_id, vendor_site_code, 
      amount, excluding_tax_amount, currency, 
      start_time, end_time, manager, 
      manager_name, purchasing_follower, purchasing_follower_name, 
      classes, belong_area, payment_method, 
      invoice_type, is_electronic_contract, other_name, 
      other_id, other_phone, other_email, 
      public_or_private, seal_category, seal_admin_account_ids, 
      annex, remark, status, 
      filing_date, carryover_flag, is_synchronize_legal_system_flag, 
      gle_sign_flag, notsync_type, notsync_reason,
      deleted_flag, create_by, create_at,
      update_by, update_at, tax_id,
      tax_rate, legal_business_id, legal_contract_num,
      execute_contract_percent_total, delivery_type,
      delivery_clause, delivery_date, payment_term,
      contract_terms_flg, contract_terms_ids, original_contract_annex,
      if_upload_change_file, contract_terms
      )
    values (#{id,jdbcType=BIGINT}, #{headerId,jdbcType=BIGINT}, #{originId,jdbcType=BIGINT}, 
      #{historyType,jdbcType=INTEGER}, #{projectId,jdbcType=BIGINT}, #{code,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{ouId,jdbcType=BIGINT}, #{typeId,jdbcType=BIGINT}, #{typeName,jdbcType=VARCHAR}, 
      #{legalAffairsCode,jdbcType=VARCHAR}, #{vendorId,jdbcType=BIGINT}, #{vendorCode,jdbcType=VARCHAR}, 
      #{vendorName,jdbcType=VARCHAR}, #{erpVendorSiteId,jdbcType=VARCHAR}, #{vendorSiteCode,jdbcType=VARCHAR}, 
      #{amount,jdbcType=DECIMAL}, #{excludingTaxAmount,jdbcType=DECIMAL}, #{currency,jdbcType=VARCHAR}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{manager,jdbcType=BIGINT}, 
      #{managerName,jdbcType=VARCHAR}, #{purchasingFollower,jdbcType=BIGINT}, #{purchasingFollowerName,jdbcType=VARCHAR}, 
      #{classes,jdbcType=TINYINT}, #{belongArea,jdbcType=TINYINT}, #{paymentMethod,jdbcType=VARCHAR}, 
      #{invoiceType,jdbcType=TINYINT}, #{isElectronicContract,jdbcType=TINYINT}, #{otherName,jdbcType=VARCHAR}, 
      #{otherId,jdbcType=VARCHAR}, #{otherPhone,jdbcType=VARCHAR}, #{otherEmail,jdbcType=VARCHAR}, 
      #{publicOrPrivate,jdbcType=TINYINT}, #{sealCategory,jdbcType=TINYINT}, #{sealAdminAccountIds,jdbcType=VARCHAR}, 
      #{annex,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{filingDate,jdbcType=TIMESTAMP}, #{carryoverFlag,jdbcType=TINYINT}, #{isSynchronizeLegalSystemFlag,jdbcType=TINYINT}, 
      #{gleSignFlag,jdbcType=TINYINT}, #{notsyncType,jdbcType=VARCHAR}, #{notsyncReason,jdbcType=VARCHAR},
      #{deletedFlag,jdbcType=TINYINT}, #{createBy,jdbcType=BIGINT}, #{createAt,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=BIGINT}, #{updateAt,jdbcType=TIMESTAMP}, #{taxId,jdbcType=BIGINT},
      #{taxRate,jdbcType=VARCHAR}, #{legalBusinessId,jdbcType=VARCHAR}, #{legalContractNum,jdbcType=VARCHAR},
      #{executeContractPercentTotal,jdbcType=DECIMAL}, #{deliveryType,jdbcType=VARCHAR},
      #{deliveryClause,jdbcType=VARCHAR}, #{deliveryDate,jdbcType=TIMESTAMP}, #{paymentTerm,jdbcType=INTEGER},
      #{contractTermsFlg,jdbcType=VARCHAR}, #{contractTermsIds,jdbcType=VARCHAR}, #{originalContractAnnex,jdbcType=LONGVARCHAR},
      #{ifUploadChangeFile,jdbcType=LONGVARCHAR}, #{contractTerms,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory">
    insert into purchase_contract_change_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="headerId != null">
        header_id,
      </if>
      <if test="originId != null">
        origin_id,
      </if>
      <if test="historyType != null">
        history_type,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="ouId != null">
        ou_id,
      </if>
      <if test="typeId != null">
        type_id,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="legalAffairsCode != null">
        legal_affairs_code,
      </if>
      <if test="vendorId != null">
        vendor_id,
      </if>
      <if test="vendorCode != null">
        vendor_code,
      </if>
      <if test="vendorName != null">
        vendor_name,
      </if>
      <if test="erpVendorSiteId != null">
        erp_vendor_site_id,
      </if>
      <if test="vendorSiteCode != null">
        vendor_site_code,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="excludingTaxAmount != null">
        excluding_tax_amount,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="manager != null">
        manager,
      </if>
      <if test="managerName != null">
        manager_name,
      </if>
      <if test="purchasingFollower != null">
        purchasing_follower,
      </if>
      <if test="purchasingFollowerName != null">
        purchasing_follower_name,
      </if>
      <if test="classes != null">
        classes,
      </if>
      <if test="belongArea != null">
        belong_area,
      </if>
      <if test="paymentMethod != null">
        payment_method,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="isElectronicContract != null">
        is_electronic_contract,
      </if>
      <if test="otherName != null">
        other_name,
      </if>
      <if test="otherId != null">
        other_id,
      </if>
      <if test="otherPhone != null">
        other_phone,
      </if>
      <if test="otherEmail != null">
        other_email,
      </if>
      <if test="publicOrPrivate != null">
        public_or_private,
      </if>
      <if test="sealCategory != null">
        seal_category,
      </if>
      <if test="sealAdminAccountIds != null">
        seal_admin_account_ids,
      </if>
      <if test="annex != null">
        annex,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="filingDate != null">
        filing_date,
      </if>
      <if test="carryoverFlag != null">
        carryover_flag,
      </if>
      <if test="isSynchronizeLegalSystemFlag != null">
        is_synchronize_legal_system_flag,
      </if>
      <if test="gleSignFlag != null">
        gle_sign_flag,
      </if>
      <if test="notsyncType != null">
        notsync_type,
      </if>
      <if test="notsyncReason != null">
        notsync_reason,
      </if>
      <if test="deletedFlag != null">
        deleted_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="taxId != null">
        tax_id,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="legalBusinessId != null">
        legal_business_id,
      </if>
      <if test="legalContractNum != null">
        legal_contract_num,
      </if>
      <if test="executeContractPercentTotal != null">
        execute_contract_percent_total,
      </if>
      <if test="deliveryType != null">
        delivery_type,
      </if>
      <if test="deliveryClause != null">
        delivery_clause,
      </if>
      <if test="deliveryDate != null">
        delivery_date,
      </if>
      <if test="paymentTerm != null">
        payment_term,
      </if>
      <if test="contractTermsFlg != null">
        contract_terms_flg,
      </if>
      <if test="contractTermsIds != null">
        contract_terms_ids,
      </if>
      <if test="originalContractAnnex != null">
        original_contract_annex,
      </if>
      <if test="ifUploadChangeFile != null">
        if_upload_change_file,
      </if>
      <if test="contractTerms != null">
        contract_terms,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="headerId != null">
        #{headerId,jdbcType=BIGINT},
      </if>
      <if test="originId != null">
        #{originId,jdbcType=BIGINT},
      </if>
      <if test="historyType != null">
        #{historyType,jdbcType=INTEGER},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="ouId != null">
        #{ouId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        #{typeId,jdbcType=BIGINT},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="legalAffairsCode != null">
        #{legalAffairsCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorId != null">
        #{vendorId,jdbcType=BIGINT},
      </if>
      <if test="vendorCode != null">
        #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorName != null">
        #{vendorName,jdbcType=VARCHAR},
      </if>
      <if test="erpVendorSiteId != null">
        #{erpVendorSiteId,jdbcType=VARCHAR},
      </if>
      <if test="vendorSiteCode != null">
        #{vendorSiteCode,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="excludingTaxAmount != null">
        #{excludingTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="manager != null">
        #{manager,jdbcType=BIGINT},
      </if>
      <if test="managerName != null">
        #{managerName,jdbcType=VARCHAR},
      </if>
      <if test="purchasingFollower != null">
        #{purchasingFollower,jdbcType=BIGINT},
      </if>
      <if test="purchasingFollowerName != null">
        #{purchasingFollowerName,jdbcType=VARCHAR},
      </if>
      <if test="classes != null">
        #{classes,jdbcType=TINYINT},
      </if>
      <if test="belongArea != null">
        #{belongArea,jdbcType=TINYINT},
      </if>
      <if test="paymentMethod != null">
        #{paymentMethod,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="isElectronicContract != null">
        #{isElectronicContract,jdbcType=TINYINT},
      </if>
      <if test="otherName != null">
        #{otherName,jdbcType=VARCHAR},
      </if>
      <if test="otherId != null">
        #{otherId,jdbcType=VARCHAR},
      </if>
      <if test="otherPhone != null">
        #{otherPhone,jdbcType=VARCHAR},
      </if>
      <if test="otherEmail != null">
        #{otherEmail,jdbcType=VARCHAR},
      </if>
      <if test="publicOrPrivate != null">
        #{publicOrPrivate,jdbcType=TINYINT},
      </if>
      <if test="sealCategory != null">
        #{sealCategory,jdbcType=TINYINT},
      </if>
      <if test="sealAdminAccountIds != null">
        #{sealAdminAccountIds,jdbcType=VARCHAR},
      </if>
      <if test="annex != null">
        #{annex,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="filingDate != null">
        #{filingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="carryoverFlag != null">
        #{carryoverFlag,jdbcType=TINYINT},
      </if>
      <if test="isSynchronizeLegalSystemFlag != null">
        #{isSynchronizeLegalSystemFlag,jdbcType=TINYINT},
      </if>
      <if test="gleSignFlag != null">
        #{gleSignFlag,jdbcType=TINYINT},
      </if>
      <if test="notsyncType != null">
        #{notsyncType,jdbcType=VARCHAR},
      </if>
      <if test="notsyncReason != null">
        #{notsyncReason,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="taxId != null">
        #{taxId,jdbcType=BIGINT},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="legalBusinessId != null">
        #{legalBusinessId,jdbcType=VARCHAR},
      </if>
      <if test="legalContractNum != null">
        #{legalContractNum,jdbcType=VARCHAR},
      </if>
      <if test="executeContractPercentTotal != null">
        #{executeContractPercentTotal,jdbcType=DECIMAL},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=VARCHAR},
      </if>
      <if test="deliveryClause != null">
        #{deliveryClause,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDate != null">
        #{deliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentTerm != null">
        #{paymentTerm,jdbcType=INTEGER},
      </if>
      <if test="contractTermsFlg != null">
        #{contractTermsFlg,jdbcType=VARCHAR},
      </if>
      <if test="contractTermsIds != null">
        #{contractTermsIds,jdbcType=VARCHAR},
      </if>
      <if test="originalContractAnnex != null">
        #{originalContractAnnex,jdbcType=LONGVARCHAR},
      </if>
      <if test="ifUploadChangeFile != null">
        #{ifUploadChangeFile,jdbcType=LONGVARCHAR},
      </if>
      <if test="contractTerms != null">
        #{contractTerms,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistoryExample" resultType="java.lang.Long">
    select count(*) from purchase_contract_change_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory">
    update purchase_contract_change_history
    <set>
      <if test="headerId != null">
        header_id = #{headerId,jdbcType=BIGINT},
      </if>
      <if test="originId != null">
        origin_id = #{originId,jdbcType=BIGINT},
      </if>
      <if test="historyType != null">
        history_type = #{historyType,jdbcType=INTEGER},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="ouId != null">
        ou_id = #{ouId,jdbcType=BIGINT},
      </if>
      <if test="typeId != null">
        type_id = #{typeId,jdbcType=BIGINT},
      </if>
      <if test="typeName != null">
        type_name = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="legalAffairsCode != null">
        legal_affairs_code = #{legalAffairsCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorId != null">
        vendor_id = #{vendorId,jdbcType=BIGINT},
      </if>
      <if test="vendorCode != null">
        vendor_code = #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorName != null">
        vendor_name = #{vendorName,jdbcType=VARCHAR},
      </if>
      <if test="erpVendorSiteId != null">
        erp_vendor_site_id = #{erpVendorSiteId,jdbcType=VARCHAR},
      </if>
      <if test="vendorSiteCode != null">
        vendor_site_code = #{vendorSiteCode,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="excludingTaxAmount != null">
        excluding_tax_amount = #{excludingTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="manager != null">
        manager = #{manager,jdbcType=BIGINT},
      </if>
      <if test="managerName != null">
        manager_name = #{managerName,jdbcType=VARCHAR},
      </if>
      <if test="purchasingFollower != null">
        purchasing_follower = #{purchasingFollower,jdbcType=BIGINT},
      </if>
      <if test="purchasingFollowerName != null">
        purchasing_follower_name = #{purchasingFollowerName,jdbcType=VARCHAR},
      </if>
      <if test="classes != null">
        classes = #{classes,jdbcType=TINYINT},
      </if>
      <if test="belongArea != null">
        belong_area = #{belongArea,jdbcType=TINYINT},
      </if>
      <if test="paymentMethod != null">
        payment_method = #{paymentMethod,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="isElectronicContract != null">
        is_electronic_contract = #{isElectronicContract,jdbcType=TINYINT},
      </if>
      <if test="otherName != null">
        other_name = #{otherName,jdbcType=VARCHAR},
      </if>
      <if test="otherId != null">
        other_id = #{otherId,jdbcType=VARCHAR},
      </if>
      <if test="otherPhone != null">
        other_phone = #{otherPhone,jdbcType=VARCHAR},
      </if>
      <if test="otherEmail != null">
        other_email = #{otherEmail,jdbcType=VARCHAR},
      </if>
      <if test="publicOrPrivate != null">
        public_or_private = #{publicOrPrivate,jdbcType=TINYINT},
      </if>
      <if test="sealCategory != null">
        seal_category = #{sealCategory,jdbcType=TINYINT},
      </if>
      <if test="sealAdminAccountIds != null">
        seal_admin_account_ids = #{sealAdminAccountIds,jdbcType=VARCHAR},
      </if>
      <if test="annex != null">
        annex = #{annex,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="filingDate != null">
        filing_date = #{filingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="carryoverFlag != null">
        carryover_flag = #{carryoverFlag,jdbcType=TINYINT},
      </if>
      <if test="isSynchronizeLegalSystemFlag != null">
        is_synchronize_legal_system_flag = #{isSynchronizeLegalSystemFlag,jdbcType=TINYINT},
      </if>
      <if test="gleSignFlag != null">
        gle_sign_flag = #{gleSignFlag,jdbcType=TINYINT},
      </if>
      <if test="notsyncType != null">
        notsync_type = #{notsyncType,jdbcType=VARCHAR},
      </if>
      <if test="notsyncReason != null">
        notsync_reason = #{notsyncReason,jdbcType=VARCHAR},
      </if>
      <if test="deletedFlag != null">
        deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="taxId != null">
        tax_id = #{taxId,jdbcType=BIGINT},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="legalBusinessId != null">
        legal_business_id = #{legalBusinessId,jdbcType=VARCHAR},
      </if>
      <if test="legalContractNum != null">
        legal_contract_num = #{legalContractNum,jdbcType=VARCHAR},
      </if>
      <if test="executeContractPercentTotal != null">
        execute_contract_percent_total = #{executeContractPercentTotal,jdbcType=DECIMAL},
      </if>
      <if test="deliveryType != null">
        delivery_type = #{deliveryType,jdbcType=VARCHAR},
      </if>
      <if test="deliveryClause != null">
        delivery_clause = #{deliveryClause,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDate != null">
        delivery_date = #{deliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentTerm != null">
        payment_term = #{paymentTerm,jdbcType=INTEGER},
      </if>
      <if test="contractTermsFlg != null">
        contract_terms_flg = #{contractTermsFlg,jdbcType=VARCHAR},
      </if>
      <if test="contractTermsIds != null">
        contract_terms_ids = #{contractTermsIds,jdbcType=VARCHAR},
      </if>
      <if test="originalContractAnnex != null">
        original_contract_annex = #{originalContractAnnex,jdbcType=LONGVARCHAR},
      </if>
      <if test="ifUploadChangeFile != null">
        if_upload_change_file = #{ifUploadChangeFile,jdbcType=LONGVARCHAR},
      </if>
      <if test="contractTerms != null">
        contract_terms = #{contractTerms,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory">
    update purchase_contract_change_history
    set header_id = #{headerId,jdbcType=BIGINT},
      origin_id = #{originId,jdbcType=BIGINT},
      history_type = #{historyType,jdbcType=INTEGER},
      project_id = #{projectId,jdbcType=BIGINT},
      code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      ou_id = #{ouId,jdbcType=BIGINT},
      type_id = #{typeId,jdbcType=BIGINT},
      type_name = #{typeName,jdbcType=VARCHAR},
      legal_affairs_code = #{legalAffairsCode,jdbcType=VARCHAR},
      vendor_id = #{vendorId,jdbcType=BIGINT},
      vendor_code = #{vendorCode,jdbcType=VARCHAR},
      vendor_name = #{vendorName,jdbcType=VARCHAR},
      erp_vendor_site_id = #{erpVendorSiteId,jdbcType=VARCHAR},
      vendor_site_code = #{vendorSiteCode,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      excluding_tax_amount = #{excludingTaxAmount,jdbcType=DECIMAL},
      currency = #{currency,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      manager = #{manager,jdbcType=BIGINT},
      manager_name = #{managerName,jdbcType=VARCHAR},
      purchasing_follower = #{purchasingFollower,jdbcType=BIGINT},
      purchasing_follower_name = #{purchasingFollowerName,jdbcType=VARCHAR},
      classes = #{classes,jdbcType=TINYINT},
      belong_area = #{belongArea,jdbcType=TINYINT},
      payment_method = #{paymentMethod,jdbcType=VARCHAR},
      invoice_type = #{invoiceType,jdbcType=TINYINT},
      is_electronic_contract = #{isElectronicContract,jdbcType=TINYINT},
      other_name = #{otherName,jdbcType=VARCHAR},
      other_id = #{otherId,jdbcType=VARCHAR},
      other_phone = #{otherPhone,jdbcType=VARCHAR},
      other_email = #{otherEmail,jdbcType=VARCHAR},
      public_or_private = #{publicOrPrivate,jdbcType=TINYINT},
      seal_category = #{sealCategory,jdbcType=TINYINT},
      seal_admin_account_ids = #{sealAdminAccountIds,jdbcType=VARCHAR},
      annex = #{annex,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      filing_date = #{filingDate,jdbcType=TIMESTAMP},
      carryover_flag = #{carryoverFlag,jdbcType=TINYINT},
      is_synchronize_legal_system_flag = #{isSynchronizeLegalSystemFlag,jdbcType=TINYINT},
      gle_sign_flag = #{gleSignFlag,jdbcType=TINYINT},
      notsync_type = #{notsyncType,jdbcType=VARCHAR},
      notsync_reason = #{notsyncReason,jdbcType=VARCHAR},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      tax_id = #{taxId,jdbcType=BIGINT},
      tax_rate = #{taxRate,jdbcType=VARCHAR},
      legal_business_id = #{legalBusinessId,jdbcType=VARCHAR},
      legal_contract_num = #{legalContractNum,jdbcType=VARCHAR},
      execute_contract_percent_total = #{executeContractPercentTotal,jdbcType=DECIMAL},
      delivery_type = #{deliveryType,jdbcType=VARCHAR},
      delivery_clause = #{deliveryClause,jdbcType=VARCHAR},
      delivery_date = #{deliveryDate,jdbcType=TIMESTAMP},
      payment_term = #{paymentTerm,jdbcType=INTEGER},
      contract_terms_flg = #{contractTermsFlg,jdbcType=VARCHAR},
      contract_terms_ids = #{contractTermsIds,jdbcType=VARCHAR},
      original_contract_annex = #{originalContractAnnex,jdbcType=LONGVARCHAR},
      if_upload_change_file = #{ifUploadChangeFile,jdbcType=LONGVARCHAR},
      contract_terms = #{contractTerms,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.midea.pam.common.ctc.entity.PurchaseContractChangeHistory">
    update purchase_contract_change_history
    set header_id = #{headerId,jdbcType=BIGINT},
      origin_id = #{originId,jdbcType=BIGINT},
      history_type = #{historyType,jdbcType=INTEGER},
      project_id = #{projectId,jdbcType=BIGINT},
      code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      ou_id = #{ouId,jdbcType=BIGINT},
      type_id = #{typeId,jdbcType=BIGINT},
      type_name = #{typeName,jdbcType=VARCHAR},
      legal_affairs_code = #{legalAffairsCode,jdbcType=VARCHAR},
      vendor_id = #{vendorId,jdbcType=BIGINT},
      vendor_code = #{vendorCode,jdbcType=VARCHAR},
      vendor_name = #{vendorName,jdbcType=VARCHAR},
      erp_vendor_site_id = #{erpVendorSiteId,jdbcType=VARCHAR},
      vendor_site_code = #{vendorSiteCode,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      excluding_tax_amount = #{excludingTaxAmount,jdbcType=DECIMAL},
      currency = #{currency,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      manager = #{manager,jdbcType=BIGINT},
      manager_name = #{managerName,jdbcType=VARCHAR},
      purchasing_follower = #{purchasingFollower,jdbcType=BIGINT},
      purchasing_follower_name = #{purchasingFollowerName,jdbcType=VARCHAR},
      classes = #{classes,jdbcType=TINYINT},
      belong_area = #{belongArea,jdbcType=TINYINT},
      payment_method = #{paymentMethod,jdbcType=VARCHAR},
      invoice_type = #{invoiceType,jdbcType=TINYINT},
      is_electronic_contract = #{isElectronicContract,jdbcType=TINYINT},
      other_name = #{otherName,jdbcType=VARCHAR},
      other_id = #{otherId,jdbcType=VARCHAR},
      other_phone = #{otherPhone,jdbcType=VARCHAR},
      other_email = #{otherEmail,jdbcType=VARCHAR},
      public_or_private = #{publicOrPrivate,jdbcType=TINYINT},
      seal_category = #{sealCategory,jdbcType=TINYINT},
      seal_admin_account_ids = #{sealAdminAccountIds,jdbcType=VARCHAR},
      annex = #{annex,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      filing_date = #{filingDate,jdbcType=TIMESTAMP},
      carryover_flag = #{carryoverFlag,jdbcType=TINYINT},
      is_synchronize_legal_system_flag = #{isSynchronizeLegalSystemFlag,jdbcType=TINYINT},
      gle_sign_flag = #{gleSignFlag,jdbcType=TINYINT},
      notsync_type = #{notsyncType,jdbcType=VARCHAR},
      notsync_reason = #{notsyncReason,jdbcType=VARCHAR},
      deleted_flag = #{deletedFlag,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=BIGINT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=BIGINT},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      tax_id = #{taxId,jdbcType=BIGINT},
      tax_rate = #{taxRate,jdbcType=VARCHAR},
      legal_business_id = #{legalBusinessId,jdbcType=VARCHAR},
      legal_contract_num = #{legalContractNum,jdbcType=VARCHAR},
      execute_contract_percent_total = #{executeContractPercentTotal,jdbcType=DECIMAL},
      delivery_type = #{deliveryType,jdbcType=VARCHAR},
      delivery_clause = #{deliveryClause,jdbcType=VARCHAR},
      delivery_date = #{deliveryDate,jdbcType=TIMESTAMP},
      payment_term = #{paymentTerm,jdbcType=INTEGER},
      contract_terms_flg = #{contractTermsFlg,jdbcType=VARCHAR},
      contract_terms_ids = #{contractTermsIds,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>