package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTerms;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTermsExample;
import java.util.List;

public interface PurchaseOrderStandardTermsMapper extends Mapper {
    long countByExample(PurchaseOrderStandardTermsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PurchaseOrderStandardTerms record);

    int insertSelective(PurchaseOrderStandardTerms record);

    List<PurchaseOrderStandardTerms> selectByExampleWithBLOBs(PurchaseOrderStandardTermsExample example);

    List<PurchaseOrderStandardTerms> selectByExample(PurchaseOrderStandardTermsExample example);

    PurchaseOrderStandardTerms selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PurchaseOrderStandardTerms record);

    int updateByPrimaryKeyWithBLOBs(PurchaseOrderStandardTerms record);

    int updateByPrimaryKey(PurchaseOrderStandardTerms record);
}