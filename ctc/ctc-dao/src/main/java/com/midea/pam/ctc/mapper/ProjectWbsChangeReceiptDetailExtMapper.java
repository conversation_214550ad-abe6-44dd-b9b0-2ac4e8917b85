package com.midea.pam.ctc.mapper;

import com.midea.pam.common.ctc.entity.ProjectWbsChangeReceiptDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProjectWbsChangeReceiptDetailExtMapper extends ProjectWbsChangeReceiptDetailMapper {

    void batchInsert(List<ProjectWbsChangeReceiptDetail> list);

    List<ProjectWbsChangeReceiptDetail> queryByProjectWbsReceiptsId(@Param("projectWbsReceiptsId") Long projectWbsReceiptsId);

    void deleteByProjectWbsReceiptsId(@Param("projectWbsReceiptsId") Long projectWbsReceiptsId);
}
