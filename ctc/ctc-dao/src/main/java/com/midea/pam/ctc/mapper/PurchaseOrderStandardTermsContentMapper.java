package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTermsContent;
import com.midea.pam.common.ctc.entity.PurchaseOrderStandardTermsContentExample;
import java.util.List;

public interface PurchaseOrderStandardTermsContentMapper extends Mapper {
    long countByExample(PurchaseOrderStandardTermsContentExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PurchaseOrderStandardTermsContent record);

    int insertSelective(PurchaseOrderStandardTermsContent record);

    List<PurchaseOrderStandardTermsContent> selectByExample(PurchaseOrderStandardTermsContentExample example);

    PurchaseOrderStandardTermsContent selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PurchaseOrderStandardTermsContent record);

    int updateByPrimaryKey(PurchaseOrderStandardTermsContent record);
}