package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.basedata.dto.UserInfoDto;
import com.midea.pam.common.basedata.dto.VendorMipDTO;
import com.midea.pam.common.basedata.entity.OperatingUnit;
import com.midea.pam.common.ctc.dto.LaborCostDetailDto;
import com.midea.pam.common.ctc.dto.WorkingHourDto;
import com.midea.pam.common.ctc.dto.WorkingHourQueryDto;
import com.midea.pam.common.ctc.dto.WorkingHourResultDto;
import com.midea.pam.common.ctc.dto.WorkingHourStatDto;
import com.midea.pam.common.ctc.dto.WorkingHourWeekInfoDTO;
import com.midea.pam.common.ctc.entity.LaborCostDetail;
import com.midea.pam.common.ctc.entity.WorkingHour;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2019-06-10 10:47
 */
public interface WorkingHourExtMapper extends Mapper {

    void batchUpdateFromRdm();

    List<WorkingHourResultDto> selectFromRdm();

    List<WorkingHourResultDto> getWorkingHourResultByUserId(@Param("userId") Long userId,
                                                            @Param("startTime") Date startTime,
                                                            @Param("endTime") Date endTime,
                                                            @Param("bizUnitId") Long bizUnitId,
                                                            @Param("dateList") List<String> dateList);

    int batchInsert(List<WorkingHour> workingHours);

    void batchUpdate(List<WorkingHour> workingHours);

    long countWorkingHourByRdm(@Param("userId") Long userId, @Param("projectId") Long projectId, @Param("startTime") Date startTime, @Param(
            "endTime") Date endTime);

    long countConfirmWorkingHour(@Param("userId") Long userId, @Param("projectId") Long projectId, @Param("startTime") Date startTime, @Param(
            "endTime") Date endTime);

    int batcheDeleteWorkingHourByRdm(@Param("userId") Long userId, @Param("projectId") Long projectId, @Param("startTime") Date startTime, @Param(
            "endTime") Date endTime);

    BigDecimal totalApplyWorkinghour(WorkingHourQueryDto query);

    BigDecimal totalConfirmWorkinghour(WorkingHourQueryDto query);

    int batchUpdateFromIhr();

    List<WorkingHourDto> selectProjectMemberDistinct(@Param("userId") Long userId, @Param("applyDate") String applyDate,
                                                     @Param("projectId") Long projectId);

    /**
     * 工单工时导入校验唯一性（内部）
     *
     * @param userId
     * @param applyDate 出勤日期
     * @param projectId 项目id
     * @param processId 流程id
     * @return
     */
    List<WorkingHourDto> selectProjectMemberInternalDistinct(@Param("userId") Long userId,
                                                             @Param("applyDate") String applyDate,
                                                             @Param("projectId") Long projectId,
                                                             @Param("processId") Long processId);

    /**
     * 工单工时导入校验唯一性（外部）
     *
     * @param vendorCode 供应商编码
     * @param applyDate  出勤日期
     * @param projectId  项目id
     * @param roleName   角色名称
     * @param processId  流程id
     * @return
     */
    List<WorkingHourDto> selectProjectMemberExternalDistinct(@Param("vendorCode") String vendorCode,
                                                             @Param("applyDate") String applyDate,
                                                             @Param("projectId") Long projectId,
                                                             @Param("roleName") String roleName,
                                                             @Param("processId") Long processId);

    /**
     * WBS工时导入校验唯一性（内部）
     *
     * @param userId
     * @param projectId      项目id
     * @param laborWbsCostId 业务角色（wbs）id
     * @param applyDate      出勤日期
     * @param wbsSummaryCode WBS
     * @param processId      流程id
     * @return
     */
    int selectProjectMemberInternalDistinctWbs(@Param("userId") Long userId,
                                               @Param("projectId") Long projectId,
                                               @Param("laborWbsCostId") Long laborWbsCostId,
                                               @Param("applyDate") String applyDate,
                                               @Param("wbsSummaryCode") String wbsSummaryCode,
                                               @Param("processId") Long processId);

    List<WorkingHourDto> getApprovedWorkingHours(@Param("projectId") Long projectId, @Param("userId") Long userId);

    List<WorkingHourDto> findWorkingTimes(@Param("projectId") Long projectId);

    List<WorkingHourDto> countActualWorksTime(@Param("projectId") Long projectId, @Param("userId") Long userId);

    /**
     * 项目工时导入审批状态更改同步修改工时表对应数据
     *
     * @param processId  审批流程ID（工单工时导入主键ID）
     * @param sourceFlag 来源 4-工单工时 5-WBS工时
     * @param status     状态
     * @return
     */
    int batchUpdateStatus(@Param("processId") Long processId, @Param("sourceFlag") Integer sourceFlag, @Param("status") Integer status);

    /**
     * 项目工时导入审批状态更改同步修改工时表对应数据及审批信息
     *
     * @param processId       审批流程ID（项目工时导入主键ID）
     * @param sourceFlag      来源 4-工单工时 5-WBS工时
     * @param status          状态
     * @param approveUserName 审批人姓名
     * @param approveUserId   审批人ID
     * @return
     */
    int batchUpdateStatusAndApproveInfo(@Param("processId") Long processId,
                                        @Param("sourceFlag") Integer sourceFlag,
                                        @Param("status") Integer status,
                                        @Param("approveUserName") String approveUserName,
                                        @Param("approveUserId") Long approveUserId);

    /**
     * WBS工时导入审批通过后把申请工时数回写到确定工时数
     *
     * @param processId
     * @param sourceFlag 4-工单工时 5-WBS工时
     * @return
     */
    int batchUpdateActualWorkingHours(@Param("processId") Long processId, @Param("sourceFlag") Integer sourceFlag);

    /**
     * 工单工时导入审批状态作废更同步修改工时表对应数据为软删除
     *
     * @param processId
     * @param sourceFlag
     * @param deleteFlag
     * @return
     */
    int batchUpdateDeletedFlag(@Param("processId") Long processId, @Param("sourceFlag") Integer sourceFlag, @Param("deleteFlag") boolean deleteFlag);

    String getPositionName(@Param("userId") Long userId);

    String getVendorName(@Param("userId") Long userId);

    List<WorkingHourResultDto> selectForDisplay(WorkingHourQueryDto query);

    List<WorkingHourResultDto> selectPageByMonth(WorkingHourQueryDto query);

    List<WorkingHourStatDto> statProjectAndMemberByYear(WorkingHourQueryDto query);

    List<WorkingHourStatDto> statProjectByYear(WorkingHourQueryDto query);

    long countForDisplay(WorkingHourQueryDto query);

    List<WorkingHourWeekInfoDTO> getWeekInfo(WorkingHourQueryDto query);

    /**
     * 根据项目、用户信息、时间查询工时
     *
     * @param projectIds：项目id列表
     * @param unitId：单位id
     * @param userType：用户类型
     * @param startDate：起始时间
     * @param endDate：结束时间
     * @return : 工时列表
     */
    List<WorkingHourResultDto> selectToStat(@Param("projectIds") List<Long> projectIds,
                                            @Param("unitId") Long unitId,
                                            @Param("userType") String userType,
                                            @Param("startDate") Date startDate,
                                            @Param("endDate") Date endDate);

    /**
     * 查找userId用户在unitId单位下从startDate到endDate的工时
     *
     * @param userId：用户id
     * @param unitId：单位id
     * @param startDate：起始时间
     * @param endDate：结束时间
     * @return ： 工时列表
     */
    List<WorkingHour> selectWorkHourByYear(@Param("userId") Long userId,
                                           @Param("unitId") Long unitId,
                                           @Param("startDate") Date startDate,
                                           @Param("endDate") Date endDate);

    /**
     * 查询工时
     *
     * @param userId：用户id
     * @param projectId：项目id
     * @param applyDate：工时日期
     * @param wbsBudgetCode：wbs编码
     * @param laborWbsCostName：业务角色
     * @return 工时列表
     */
    List<WorkingHour> selectWorkHour(@Param("userId") Long userId,
                                     @Param("projectId") Long projectId,
                                     @Param("applyDate") Date applyDate,
                                     @Param("wbsBudgetCode") String wbsBudgetCode,
                                     @Param("laborWbsCostName") String laborWbsCostName);

    /**
     * 根据用户id查询组织参数“商机项目填报工时是否支持多角色”的值
     *
     * @param userId ： 用户id
     */
    List<String> selectOrgValueByUserId(@Param("userId") Long userId);

    Integer checkIsExistsWbsProject(@Param("userId") Long userId);

    List<UserInfoDto> selectUserInfo(@Param("userIds") List<Long> userIds);

    List<VendorMipDTO> selectVendorNameByUserIds(@Param("userIds") List<Long> userIds);

    List<WorkingHourResultDto> getToConfirmWorkingHours(@Param("startDate") Date startDate,
                                                        @Param("endDate") Date endDate, @Param("projectId") Long projectId);

    List<WorkingHourResultDto> selectToUpdateLaborCost(LaborCostDetailDto queryDto);

    void updateLaborCostHardWorking(LaborCostDetail laborCostDetail);

    List<WorkingHourResultDto> selectToUpdateLaborCostTypeSetId(WorkingHourResultDto queryDto);

    Integer noWbsFixLaborCostTypeSetId();

    Integer initFullPayOuId();

    List<OperatingUnit> queryFullPayOuIdList();
}
