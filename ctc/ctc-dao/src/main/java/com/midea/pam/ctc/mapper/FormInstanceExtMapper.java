package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.FormInstanceExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FormInstanceExtMapper extends Mapper {

    List<FormInstance> selectByExample(FormInstanceExample example);

    int updateByPrimaryKeySelective(FormInstance record);

    List<FormInstance> selectFormInstanceIdNotInFormInstanceId(@Param("formInstanceIds")List<Long> formInstanceIds);

    List<FormInstance> selectFormInstanceIdforformUrl();

    int updateDeletedFlag(FormInstance record);
}