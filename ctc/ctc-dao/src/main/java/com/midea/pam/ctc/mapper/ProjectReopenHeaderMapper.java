package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.ProjectReopenHeader;
import com.midea.pam.common.ctc.entity.ProjectReopenHeaderExample;
import java.util.List;

public interface ProjectReopenHeaderMapper extends Mapper {
    long countByExample(ProjectReopenHeaderExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectReopenHeader record);

    int insertSelective(ProjectReopenHeader record);

    List<ProjectReopenHeader> selectByExample(ProjectReopenHeaderExample example);

    ProjectReopenHeader selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProjectReopenHeader record);

    int updateByPrimaryKey(ProjectReopenHeader record);
}