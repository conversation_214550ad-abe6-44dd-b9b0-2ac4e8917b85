package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviation;
import com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsDeviationExample;
import java.util.List;

public interface PurchaseContractStandardTermsDeviationMapper extends Mapper {
    long countByExample(PurchaseContractStandardTermsDeviationExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PurchaseContractStandardTermsDeviation record);

    int insertSelective(PurchaseContractStandardTermsDeviation record);

    List<PurchaseContractStandardTermsDeviation> selectByExample(PurchaseContractStandardTermsDeviationExample example);

    PurchaseContractStandardTermsDeviation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PurchaseContractStandardTermsDeviation record);

    int updateByPrimaryKey(PurchaseContractStandardTermsDeviation record);
}