package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.PassInterfaceInvokeLog;
import com.midea.pam.common.ctc.entity.PassInterfaceInvokeLogExample;
import java.util.List;

public interface PassInterfaceInvokeLogMapper extends Mapper {
    long countByExample(PassInterfaceInvokeLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PassInterfaceInvokeLog record);

    int insertSelective(PassInterfaceInvokeLog record);

    List<PassInterfaceInvokeLog> selectByExampleWithBLOBs(PassInterfaceInvokeLogExample example);

    List<PassInterfaceInvokeLog> selectByExample(PassInterfaceInvokeLogExample example);

    PassInterfaceInvokeLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PassInterfaceInvokeLog record);

    int updateByPrimaryKeyWithBLOBs(PassInterfaceInvokeLog record);

    int updateByPrimaryKey(PassInterfaceInvokeLog record);
}