package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.StandardTermsDeviation;
import com.midea.pam.common.ctc.entity.StandardTermsDeviationExample;
import java.util.List;

public interface StandardTermsDeviationMapper extends Mapper {
    long countByExample(StandardTermsDeviationExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StandardTermsDeviation record);

    int insertSelective(StandardTermsDeviation record);

    List<StandardTermsDeviation> selectByExample(StandardTermsDeviationExample example);

    StandardTermsDeviation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StandardTermsDeviation record);

    int updateByPrimaryKey(StandardTermsDeviation record);
}