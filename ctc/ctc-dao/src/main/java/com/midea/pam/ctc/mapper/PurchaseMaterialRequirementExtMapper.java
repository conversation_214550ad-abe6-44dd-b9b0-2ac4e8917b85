package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialQuantityDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialReleaseDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirement;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface PurchaseMaterialRequirementExtMapper extends Mapper {

    List<PurchaseMaterialRequirementDto> queryOrderNumByProjectId(PurchaseMaterialRequirementDto query);

    List<PurchaseMaterialRequirementDto> queryOrderMergeNumByProjectId(PurchaseMaterialRequirementDto query);

    List<PurchaseMaterialRequirementDto> queryOrderHistoryNumByProjectId(PurchaseMaterialRequirementDto query);

    List<PurchaseMaterialRequirementDto> queryOrderMergeHistoryNumByProjectId(PurchaseMaterialRequirementDto query);

    /**
     * 根据需求id查询
     * 未采购量 = 总需求量 - 已采购量(已下达+已下单) - 关闭数量
     */
    BigDecimal queryUnreleasedNumByRequirementId(@Param("requirementId") Long requirementId, @Param("receiptsId") Long receiptsId);

    /**
     * 查询采购需求表批准供应商数量不一致的数据
     *
     * @return
     */
    List<PurchaseMaterialRequirement> selectApprovedSupplierNumber(@Param("organizationCode") String organizationCode);

    void batchInsert(List<PurchaseMaterialRequirement> requirementList);

    void batchInsert1(List<PurchaseMaterialRequirementDto> requirementList);

    /**
     * 查询未下达量
     *
     * @param query
     * @return
     */
    List<PurchaseMaterialRequirementDto> selectUnreleasedAmountByIds(PurchaseMaterialRequirementDto query);

    /**
     * 获取外购物料需求信息
     *
     * @param requirementIds 采购需求id
     * @return 外购物料需求信息
     */
    List<PurchaseMaterialRequirementDto> selectOutRequirementInfo(@Param("requirementIds") List<Long> requirementIds);

    /**
     * 查询已下达量
     *
     * @param requirementIdList
     * @return
     */
    List<PurchaseMaterialRequirementDto> selectReleasedQuantityByIds(@Param("requirementIdList") List<Long> requirementIdList);

    List<Long> selectIdsWithDetail(PurchaseMaterialRequirementDto query);

    /**
     * 查询最新发布日期
     *
     * @param requirementIdList
     * @return
     */
    List<PurchaseMaterialReleaseDetailDto> selectLatestPublishTime(@Param("requirementIdList") List<Long> requirementIdList);

    List<PurchaseMaterialRequirementDto> selectApprovedSupplierNumberFromMaterialDetail();

    void batchUpdateByPrimaryKey(@Param("list") List<PurchaseMaterialRequirement> requirementList);

    List<PurchaseMaterialRequirementDto> selectPurchaseMaterialRequirementList(PurchaseMaterialRequirementDto query);

    List<PurchaseMaterialQuantityDto> selectPurchaseMaterialReleasedQuantityList(@Param("requirementIdList") List<Long> requirementIdList);

    List<PurchaseMaterialQuantityDto> selectPurchaseMaterialOrderQuantityList(@Param("requirementIdList") List<Long> requirementIdList);

    List<PurchaseMaterialRequirementDto> calculateMaterialRequirementNumber(MilepostDesignPlanDetailDto dto);

    int updataNeedTotal(@Param("id") Long id);

    List<PurchaseMaterialRequirement> listAddressesByDeliveryCondition(@Param("projectIds")List<Long> projectIds, @Param("pamCodes")List<String> pamCodes, @Param("deliveryTimes")List<String> deliveryTimes);

    /**
     * 根据id 查询下达数
     */
    BigDecimal selectWbsReleasedQuantity(@Param("requirementId") Long requirementId);

    /**
     * 查询采购数
     */
    BigDecimal selectReleasedQuantity(@Param("requirementId") Long requirementId);
}