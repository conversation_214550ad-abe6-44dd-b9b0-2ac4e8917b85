package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.basedata.dto.MaterialCostDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailProjectIdCount;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanFieldDto;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailExample;
import com.midea.pam.common.ctc.query.MilepostDesignPlanNotPublishRequirementQuery;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface MilepostDesignPlanDetailExtMapper extends Mapper {

    List<MilepostDesignPlanDetailDto> findApprovedMilepostDesignPlanDetails(@Param("milepostId") Long milepostId);

    List<MilepostDesignPlanDetailDto> findApprovedMilepostDesignPlanDetailByMap(@Param("projectId") Long projectId,
                                                                                @Param("milepostId") Long milepostId);

    List<MilepostDesignPlanDetailDto> selectList(MilepostDesignPlanDetailDto query);

    int updateDesignCost(@Param("itemCode") String itemCode, @Param("designCostId") Long designCostId, @Param("ouId") Long ouId, @Param("designCost"
    ) BigDecimal designCost);

    /**
     * 项目详细设计方案中的采购物料未生成采购需求（不包含成品、模组、外包物料）
     *
     * @param projectId 项目ID
     * @return 详细设计
     */
    List<MilepostDesignPlanDetail> getNoGenerateRequirement(@Param("projectId") Long projectId);

    Long getDesignBybudgetMaterialId(Long budgetMaterialId);

    int deleteMilepostDesignPlanDetailsBySubmitId(@Param("submitId") Long submitId);

    List<MilepostDesignPlanDetailDto> selectByIds(@Param("ids") List<Long> ids);

    List<MilepostDesignPlanDetailDto> selectById(@Param("sourceId") Long sourceId);

    List<MilepostDesignPlanDetailDto> selectDeliveryTime(@Param("designPlanDetailId") Long designPlanDetailId);

    void updateDeliveryTimeById(@Param("deliveryTime") Date deliveryTime, @Param("designPlanDetailId") Long designPlanDetailId);

    List<MilepostDesignPlanDetailDto> getMilepostPlanDesignByTicketTasksCode(@Param("ticketTasksId") Long ticketTasksId,
                                                                             @Param("materialCategoryList") List<String> materialCategoryList);

    List<MilepostDesignPlanDetailDto> selectByProjectIdAndPamCode(@Param("projectIds") List<Long> projectIds, @Param("pamCode") String pamCode);

    List<MilepostDesignPlanDetailDto> selectDesignPlanDetail(@Param("ids") List<Long> ids, @Param("confirmId") Long confirmId);

    List<MilepostDesignPlanDetailDto> selectDesignPlanDetailForPurchase(@Param("ids") List<Long> ids, @Param("receiptsId") Long receiptsId);

    List<MilepostDesignPlanDetailDto> selectDesignPlanDetailForConfirm(@Param("ids") List<Long> ids);

    int updateById(@Param("designPlanDetailId") Long designPlanDetailId);

    BigDecimal getTotalPurchaseNum(@Param("designPlanDetailId") Long designPlanDetailId);

    int updateByMilepostDesignPlanId(@Param("designId") Long designId, @Param("materialCategory") String materialCategory);

    List<MilepostDesignPlanDetailDto> selectModel(@Param("designId") Long designId);

    List<MilepostDesignPlanDetailDto> selectTotalNumberById(@Param("designId") Long designId);

    List<MilepostDesignPlanDetailDto> selectModule(@Param("designPlanDetailId") Long designPlanDetailId);

    /**
     * 逻辑删除by项目物料预算id
     *
     * @param projectBudgetMaterialId 项目物料预算id
     * @param updatedBy               修改人
     * @return
     */
    int logicallyDeleteByProjectBudgetMataerialId(@Param("projectBudgetMaterialId") Long projectBudgetMaterialId, @Param("updatedBy") Long updatedBy);

    List<MilepostDesignPlanDetailDto> selectDetailAndReceiptsByCondition(MilepostDesignPlanDetailExample example);

    List<MilepostDesignPlanFieldDto> selectMilepostDesignPlanPurchaseInfo(@Param("detailIds") List<Long> detailIds);

    List<MilepostDesignPlanFieldDto> selectMilepostDesignPlanT1(@Param("projectId") Long projectId);
    List<MilepostDesignPlanFieldDto> selectMilepostDesignPlanT2(@Param("projectId") Long projectId);
    List<MilepostDesignPlanFieldDto> selectMilepostDesignPlanT3(@Param("projectId") Long projectId);

    List<MilepostDesignPlanDetailDto> selectDetailByIds(@Param("ids") List<Long> ids);

    void updateDetailForChangeByDetailIds(@Param("detailIds") List<Long> detailIds, @Param("updatedBy") Long updatedBy,
                                          @Param("status") Integer status,
                                          @Param("moduleStatus") Integer moduleStatus);

    @Select("select * from milepost_design_plan_detail where (deleted_flag = 0 or deleted_flag is null) and parent_id =#{parentId}")
    List<MilepostDesignPlanDetail> selectByParentId(@Param("parentId") Long parentId);

    void deleteByDetailIds(@Param("detailIds") List<Long> detailIds);

    void batchInsert(List<MilepostDesignPlanDetail> addDesignPlanDetailList);

    void batchUpdate(List<MilepostDesignPlanDetail> updateDesignPlanDetailList);

    List<MilepostDesignPlanDetailDto> selectWbsLayerDetailByProjectId(MilepostDesignPlanDetailExample milepostDesignPlanDetailExample);

    List<MilepostDesignPlanDetailDto> getDetailsByParentId(@Param("detailId") Long detailId);

    List<MilepostDesignPlanDetailDto> selectNotPublishRequirementByQuery(MilepostDesignPlanNotPublishRequirementQuery query);

    BigDecimal getTotalNumberById(@Param("designId") Long designId);

    @Select("select * from milepost_design_plan_detail where (deleted_flag = 0 or deleted_flag is null) " +
            "and (generate_requirement = 0 or generate_requirement is null) and status in(1,15,18) and parent_id =#{parentId}")
    List<MilepostDesignPlanDetail> selectPassedByParentId(@Param("parentId") Long parentId);

    /**
     * 统计 里程碑详细设计方案设计信息总项目ID关联的数量
     * @return
     */
    List<MilepostDesignPlanDetailProjectIdCount> queryMilepostDesignPlanDetailProjectIdCount();

    List<MilepostDesignPlanDetailDto> calculateMilepostDesignPlanDetailNumber(MilepostDesignPlanDetailDto dto);

    /**
     * 根据ID递归查询所有审批中(module_status=2)的父级记录
     * 说明：
     * 1. 查询时会自动排除已删除的记录(deleted_flag = 0)
     * 2. 只返回module_status = 2的记录
     * @param designPlanId 详设记录ID
     * @return 返回所有符合条件的父级记录列表
     */
    List<MilepostDesignPlanDetail> selectParentRecordsInApprovalById(@Param("designPlanId") Long designPlanId);

    int updateModuleStatusConfirmed(Long projectId);

    int updateModuleStatusUnConfirmed(Long projectId);

    List<MilepostDesignPlanDetailDto> getMilepostDesignPlanDetails(@Param("erpCodeList") List<String> erpCodeList);

    void updateByMaterialCost(MaterialCostDto materialCost);

    int updateStatusByProjectId(@Param("projectId") Long projectId, @Param("status") Integer status);
}