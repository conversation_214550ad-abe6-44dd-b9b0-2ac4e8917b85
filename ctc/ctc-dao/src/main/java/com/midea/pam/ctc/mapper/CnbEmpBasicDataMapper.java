package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.CnbEmpBasicData;
import com.midea.pam.common.ctc.entity.CnbEmpBasicDataExample;
import java.util.List;

public interface CnbEmpBasicDataMapper extends Mapper {
    long countByExample(CnbEmpBasicDataExample example);

    int deleteByPrimaryKey(Long id);

    int insert(CnbEmpBasicData record);

    int insertSelective(CnbEmpBasicData record);

    List<CnbEmpBasicData> selectByExample(CnbEmpBasicDataExample example);

    CnbEmpBasicData selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CnbEmpBasicData record);

    int updateByPrimaryKey(CnbEmpBasicData record);
}