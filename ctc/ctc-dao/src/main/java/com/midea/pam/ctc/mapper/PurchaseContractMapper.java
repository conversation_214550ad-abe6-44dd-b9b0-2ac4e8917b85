package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.PurchaseContract;
import com.midea.pam.common.ctc.entity.PurchaseContractExample;
import java.util.List;

public interface PurchaseContractMapper extends Mapper {
    long countByExample(PurchaseContractExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PurchaseContract record);

    int insertSelective(PurchaseContract record);

    List<PurchaseContract> selectByExampleWithBLOBs(PurchaseContractExample example);

    List<PurchaseContract> selectByExample(PurchaseContractExample example);

    PurchaseContract selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PurchaseContract record);

    int updateByPrimaryKeyWithBLOBs(PurchaseContract record);

    int updateByPrimaryKey(PurchaseContract record);
}