package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementPurchaseTypeChangeLog;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementPurchaseTypeChangeLogExample;
import java.util.List;

public interface PurchaseMaterialRequirementPurchaseTypeChangeLogMapper extends Mapper {
    long countByExample(PurchaseMaterialRequirementPurchaseTypeChangeLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PurchaseMaterialRequirementPurchaseTypeChangeLog record);

    int insertSelective(PurchaseMaterialRequirementPurchaseTypeChangeLog record);

    List<PurchaseMaterialRequirementPurchaseTypeChangeLog> selectByExampleWithBLOBs(PurchaseMaterialRequirementPurchaseTypeChangeLogExample example);

    List<PurchaseMaterialRequirementPurchaseTypeChangeLog> selectByExample(PurchaseMaterialRequirementPurchaseTypeChangeLogExample example);

    PurchaseMaterialRequirementPurchaseTypeChangeLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PurchaseMaterialRequirementPurchaseTypeChangeLog record);

    int updateByPrimaryKeyWithBLOBs(PurchaseMaterialRequirementPurchaseTypeChangeLog record);

    int updateByPrimaryKey(PurchaseMaterialRequirementPurchaseTypeChangeLog record);
}