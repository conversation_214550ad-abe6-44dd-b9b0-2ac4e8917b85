package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ProjectWbsReceiptsExtMapper extends Mapper {

    int updateReceiptsDel(Long id);

    int updateWbsRequirementBudgetDel(Long id);

    List<ProjectWbsReceiptsDto> selectByIds(@Param("ids") List<Long> ids);

    List<ProjectWbsReceiptsDto> selectApprovingReceiptsByIds(@Param("ids") List<Long> ids);

    List<ProjectWbsReceiptsDto> selectProcessReceiptsByDetailIds(@Param("detailIds") List<Long> detailIds);

    List<ProjectWbsReceiptsDto> selectByDtos(@Param("list") List<MilepostDesignPlanDetailDto> list);

    List<ProjectWbsReceiptsDto> selectByDetailIds(@Param("detailIds") List<Long> detailIds);

    ProjectWbsReceiptsDto selectDto(ProjectWbsReceiptsDto projectWbsReceiptsDto);

    @Select("select requirement_code from project_wbs_receipts where id = #{receiptsId}")
    String getRequirementCodeByReceiptsId(@Param("receiptsId") Long receiptsId);

    int batchInsert(List<ProjectWbsReceipts> list);

    /**
     * 查询当前状态不为作废和生效的需求发布单据数据
     *
     * @param projectId
     * @return
     */
    List<ProjectWbsReceipts> queryDesignInfoByProjectId(Long projectId);

    /**
     * 根据详设id查询对应的详设单据号
     *
     * @param designId
     * @return
     */
    String queryRequirementCodeByDesignId(Long designId);
}