package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.PurchaseContractStandardTerms;
import com.midea.pam.common.ctc.entity.PurchaseContractStandardTermsExample;
import java.util.List;

public interface PurchaseContractStandardTermsMapper extends Mapper {
    long countByExample(PurchaseContractStandardTermsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PurchaseContractStandardTerms record);

    int insertSelective(PurchaseContractStandardTerms record);

    List<PurchaseContractStandardTerms> selectByExampleWithBLOBs(PurchaseContractStandardTermsExample example);

    List<PurchaseContractStandardTerms> selectByExample(PurchaseContractStandardTermsExample example);

    PurchaseContractStandardTerms selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PurchaseContractStandardTerms record);

    int updateByPrimaryKeyWithBLOBs(PurchaseContractStandardTerms record);

    int updateByPrimaryKey(PurchaseContractStandardTerms record);
}