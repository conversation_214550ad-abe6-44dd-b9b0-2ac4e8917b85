package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.WbsTemplateInfo;
import com.midea.pam.common.ctc.entity.WbsTemplateInfoExample;
import java.util.List;

public interface WbsTemplateInfoMapper extends Mapper {
    long countByExample(WbsTemplateInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(WbsTemplateInfo record);

    int insertSelective(WbsTemplateInfo record);

    List<WbsTemplateInfo> selectByExample(WbsTemplateInfoExample example);

    WbsTemplateInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WbsTemplateInfo record);

    int updateByPrimaryKey(WbsTemplateInfo record);
}