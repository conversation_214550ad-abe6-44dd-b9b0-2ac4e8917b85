package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementFreezeLog;
import com.midea.pam.common.ctc.entity.PurchaseMaterialRequirementFreezeLogExample;
import java.util.List;

public interface PurchaseMaterialRequirementFreezeLogMapper extends Mapper {
    long countByExample(PurchaseMaterialRequirementFreezeLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PurchaseMaterialRequirementFreezeLog record);

    int insertSelective(PurchaseMaterialRequirementFreezeLog record);

    List<PurchaseMaterialRequirementFreezeLog> selectByExample(PurchaseMaterialRequirementFreezeLogExample example);

    PurchaseMaterialRequirementFreezeLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PurchaseMaterialRequirementFreezeLog record);

    int updateByPrimaryKey(PurchaseMaterialRequirementFreezeLog record);
}