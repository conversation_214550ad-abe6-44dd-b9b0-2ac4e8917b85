package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.ProjectWbsChangeReceiptDetail;
import com.midea.pam.common.ctc.entity.ProjectWbsChangeReceiptDetailExample;
import java.util.List;

public interface ProjectWbsChangeReceiptDetailMapper extends Mapper {
    long countByExample(ProjectWbsChangeReceiptDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectWbsChangeReceiptDetail record);

    int insertSelective(ProjectWbsChangeReceiptDetail record);

    List<ProjectWbsChangeReceiptDetail> selectByExample(ProjectWbsChangeReceiptDetailExample example);

    ProjectWbsChangeReceiptDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProjectWbsChangeReceiptDetail record);

    int updateByPrimaryKey(ProjectWbsChangeReceiptDetail record);
}