package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.StandardTermsContent;
import com.midea.pam.common.ctc.entity.StandardTermsContentExample;
import java.util.List;

public interface StandardTermsContentMapper extends Mapper {
    long countByExample(StandardTermsContentExample example);

    int deleteByPrimaryKey(Long id);

    int insert(StandardTermsContent record);

    int insertSelective(StandardTermsContent record);

    List<StandardTermsContent> selectByExample(StandardTermsContentExample example);

    StandardTermsContent selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(StandardTermsContent record);

    int updateByPrimaryKey(StandardTermsContent record);
}