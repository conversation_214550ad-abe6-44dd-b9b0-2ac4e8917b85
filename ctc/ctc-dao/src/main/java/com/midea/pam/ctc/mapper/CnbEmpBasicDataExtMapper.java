package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.dto.LaborCostHardWorkingSyncDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工发薪记录扩展Mapper
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface CnbEmpBasicDataExtMapper extends Mapper {

    /**
     * 查询需要同步hard_working字段的数据
     * 根据cnb_emp_basic_data的更新时间，查询发薪ou和项目ou不一致的数据，
     * 以user_id+statistic_date维度关联labor_cost_detail表
     *
     * @param lastUpdateDate 最后更新时间
     * @return 需要同步的数据列表
     */
    List<LaborCostHardWorkingSyncDto> selectNeedSyncHardWorkingData(@Param("lastUpdateDate") String lastUpdateDate);

    /**
     * 批量更新labor_cost_detail表的hard_working字段
     *
     * @param syncDataList 需要更新的数据列表
     * @return 更新的记录数
     */
    int batchUpdateLaborCostDetailHardWorking(@Param("syncDataList") List<LaborCostHardWorkingSyncDto> syncDataList);
}
