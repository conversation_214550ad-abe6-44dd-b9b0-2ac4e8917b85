package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.dto.ProjectWbsBudgetSummaryDto;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsBudgetChangeHistoryDto;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsBudget;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProjectWbsReceiptsBudgetExtMapper extends Mapper {

    ProjectWbsBudgetSummaryDto getByProjectIdAndWbsSummaryCode(@Param("projectId") Long projectId, @Param("wbsSummaryCode") String wbsSummaryCode);

    int deleteByProjectWbsReceiptsId(@Param("projectWbsReceiptsId") Long projectWbsReceiptsId);

    void batchInsert(List<ProjectWbsReceiptsBudget> projectWbsReceiptsBudgetList);

    int batchUpdateChangeHistory(List<ProjectWbsReceiptsBudgetChangeHistoryDto> projectWbsReceiptsBudgetChangeHistoryList);
}