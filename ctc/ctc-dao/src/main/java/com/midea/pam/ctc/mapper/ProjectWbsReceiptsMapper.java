package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.entity.ProjectWbsReceipts;
import com.midea.pam.common.ctc.entity.ProjectWbsReceiptsExample;
import java.util.List;

public interface ProjectWbsReceiptsMapper extends Mapper {
    long countByExample(ProjectWbsReceiptsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectWbsReceipts record);

    int insertSelective(ProjectWbsReceipts record);

    List<ProjectWbsReceipts> selectByExampleWithBLOBs(ProjectWbsReceiptsExample example);

    List<ProjectWbsReceipts> selectByExample(ProjectWbsReceiptsExample example);

    ProjectWbsReceipts selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProjectWbsReceipts record);

    int updateByPrimaryKeyWithBLOBs(ProjectWbsReceipts record);

    int updateByPrimaryKey(ProjectWbsReceipts record);
}