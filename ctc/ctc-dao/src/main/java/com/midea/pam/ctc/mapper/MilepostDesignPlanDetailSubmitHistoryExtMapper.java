package com.midea.pam.ctc.mapper;

import com.meicloud.light.mapper.Mapper;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailSubmitHistoryDto;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetailSubmitHistory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface MilepostDesignPlanDetailSubmitHistoryExtMapper extends Mapper {

    @Update(" UPDATE milepost_design_plan_detail_submit_history  SET deleted_flag = 1 WHERE `project_wbs_receipts_id` = #{receiptsId}")
    Integer deleteForReceipts(@Param("receiptsId") Long receiptsId);


    List<MilepostDesignPlanDetailSubmitHistoryDto> selectByIds(@Param("ids") List<Long> ids);

    List<MilepostDesignPlanDetailSubmitHistory> selectRepeatPamWBSDeliveryTimeList(@Param("projectId") Long projectId, @Param("wbsSummaryCode") String wbsSummaryCode);

    List<MilepostDesignPlanDetailSubmitHistoryDto> selectByDetailIds(@Param("ids") List<Long> ids);

    void batchUpdate(List<MilepostDesignPlanDetailSubmitHistory> updateSubmitHistoryList);
}