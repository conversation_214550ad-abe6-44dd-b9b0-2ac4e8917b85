# InterfaceInvokeLogAspect 使用说明

## 概述

`InterfaceInvokeLogAspect` 是一个基于 Spring AOP 的切面，用于统一处理接口调用的日志记录，包括请求参数、响应结果、执行状态等。通过使用 `@InterfaceInvokeLog` 注解，可以自动记录接口调用日志，减少重复代码。

## 功能特性

- **自动日志记录**：自动记录接口调用的请求参数、响应结果、执行状态到数据库
- **流水号管理**：自动生成交易流水号并存储到 ThreadLocal，支持自定义流水号字段
- **业务编号提取**：支持从方法参数中提取业务编号，支持嵌套属性访问
- **URL构建**：根据接口类型和URL后缀自动构建完整的请求URL
- **响应结果处理**：根据不同响应类型（EsbResponse、CarrierResponse）自动判断成功/失败状态
- **异常处理**：自动捕获和记录异常信息，设置失败状态
- **参数控制**：支持配置是否记录请求参数和响应结果
- **字符串截断**：自动处理超长字符串，防止数据库字段溢出
- **ThreadLocal清理**：自动清理ThreadLocal，防止内存泄漏

## 核心组件

### 1. @InterfaceInvokeLog 注解

```java
@InterfaceInvokeLog(
    businessCodeField = "paymentApplyCode",    // 业务编号字段名
    urlSuffix = "all-auz/pay/advance-payment", // URL后缀
    interfaceType = InterfaceInvokeLog.InterfaceType.GCEB, // 接口类型
    logParams = true,                          // 是否记录请求参数
    logResult = true,                          // 是否记录响应结果
    customSerialNoField = ""                   // 自定义流水号字段
)
```

### 2. 接口类型枚举

```java
public enum InterfaceType {
    GCEB("GCEB", "金融网关接口", "gfp.apiUrl"),
    GEMS("GEMS", "GEMS系统接口", "mgp.apiurl");
}
```

### 3. SerialNoContext 工具类

```java
// 设置流水号
SerialNoContext.set(serialNo);

// 获取流水号
String serialNo = SerialNoContext.get();

// 清理ThreadLocal
SerialNoContext.clear();
```

## 使用方法

### 1. 基本用法

```java
@Override
@InterfaceInvokeLog(
    businessCodeField = "paymentApplyCode",
    urlSuffix = "all-auz/pay/advance-payment",
    interfaceType = InterfaceInvokeLog.InterfaceType.GCEB
)
public EsbResponse<String> advancePaymentApply(PaymentApplyDto paymentApplyDto) {
    // 业务逻辑
    // 可以通过 SerialNoContext.get() 获取流水号
    String serialNo = SerialNoContext.get();

    // 执行业务逻辑
    return processPayment(paymentApplyDto, serialNo);
}
```

### 2. 支持嵌套属性

```java
@InterfaceInvokeLog(
    businessCodeField = "order.orderCode",  // 支持嵌套属性访问
    urlSuffix = "order/update",
    interfaceType = InterfaceInvokeLog.InterfaceType.GCEB
)
public EsbResponse<String> updateOrder(OrderRequestDto request) {
    // 业务逻辑
}
```

### 3. 自定义流水号

```java
@InterfaceInvokeLog(
    businessCodeField = "code",
    urlSuffix = "all-com/receive-pam-item-number",
    interfaceType = InterfaceInvokeLog.InterfaceType.GCEB,
    customSerialNoField = "ouId"  // 使用ouId作为流水号
)
public EsbResponse<String> pushItemNumber(ProjectDto project) {
    // 业务逻辑
}
```

### 4. 控制日志记录

```java
@InterfaceInvokeLog(
    businessCodeField = "requestId",
    urlSuffix = "sensitive/operation",
    interfaceType = InterfaceInvokeLog.InterfaceType.GCEB,
    logParams = false,  // 不记录请求参数
    logResult = false   // 不记录响应结果
)
public EsbResponse<String> sensitiveOperation(SensitiveDto request) {
    // 敏感操作，不记录详细参数
}
```

## 工作原理

### 1. 切面执行流程

1. **前置处理**：
   - 生成交易流水号（支持自定义字段或默认生成）
   - 将流水号存储到 ThreadLocal
   - 提取业务编号（支持嵌套属性）
   - 构建完整的请求URL
   - 记录请求参数（如果启用）
   - 插入数据库日志记录

2. **方法执行**：
   - 执行原始业务方法
   - 业务方法可通过 SerialNoContext.get() 获取流水号

3. **后置处理**：
   - 处理执行结果（根据响应类型判断成功/失败）
   - 自动设置流水号到返回值
   - 更新数据库日志状态和结果
   - 清理ThreadLocal，防止内存泄漏

4. **异常处理**：
   - 捕获异常信息
   - 设置日志状态为失败（2）
   - 记录异常信息到数据库
   - 重新抛出异常

### 2. 数据库日志记录

```java
// 插入日志
Long logId = passInterfaceInvokeLogService.insertLog(
    businessCode,    // 业务编号
    serialNo,        // 交易流水号
    requestParams,   // 请求参数（JSON格式）
    url,            // 请求URL
    serviceSystem   // 服务系统名称
);

// 更新日志
passInterfaceInvokeLog.setInvokeStatus(1);  // 1:成功, 2:失败
passInterfaceInvokeLog.setInvokeResult(result);
passInterfaceInvokeLogService.updateLog(passInterfaceInvokeLog);
```

### 3. 响应结果处理逻辑

```java
// GCEB接口：判断 responseCode.equals("000000")
if (result instanceof EsbResponse) {
    EsbResponse<?> esbResponse = (EsbResponse<?>) result;
    if ("000000".equals(esbResponse.getResponseCode())) {
        // 成功
        passInterfaceInvokeLog.setInvokeStatus(1);
    } else {
        // 失败
        passInterfaceInvokeLog.setInvokeStatus(2);
    }
}

// GEMS接口：判断 code.equals("0000")
if (result instanceof CarrierResponse) {
    CarrierResponse<?> carrierResponse = (CarrierResponse<?>) result;
    if ("0000".equals(carrierResponse.getCode())) {
        // 成功
        passInterfaceInvokeLog.setInvokeStatus(1);
    } else {
        // 失败
        passInterfaceInvokeLog.setInvokeStatus(2);
    }
}
```

## 数据库表结构

### pass_interface_invoke_log 表字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| apply_no | VARCHAR | 业务编号 |
| serial_no | VARCHAR | 交易流水号 |
| service_system | VARCHAR | 服务系统名称（GCEB、GEMS等） |
| invoke_url | VARCHAR | 调用接口URL |
| invoke_status | TINYINT | 调用状态：0-待调用，1-成功，2-失败 |
| invoke_result | VARCHAR | 接口返回内容或失败原因 |
| invoke_time | TIMESTAMP | 调用时间 |
| invoke_params | LONGVARCHAR | 调用接口参数（JSON格式） |
| create_by | BIGINT | 创建人 |
| create_at | TIMESTAMP | 创建时间 |
| update_by | BIGINT | 更新人 |
| update_at | TIMESTAMP | 更新时间 |
| deleted_flag | TINYINT | 删除标志 |

## 配置说明

### 1. 接口类型配置

```java
public enum InterfaceType {
    GCEB("GCEB", "金融网关接口", "gfp.apiUrl"),
    GEMS("GEMS", "GEMS系统接口", "mgp.apiurl");

    private final String systemName;      // 系统名称
    private final String description;     // 描述
    private final String configKey;       // 配置文件中的URL配置键
}
```

### 2. URL构建规则

```java
// 根据接口类型和URL后缀构建完整URL
private String buildHttpUrl(String urlSuffix, InterfaceType interfaceType) {
    String baseUrl = environment.getProperty(interfaceType.getConfigKey());
    return UriComponentsBuilder.fromHttpUrl(baseUrl)
            .path(urlSuffix)
            .build()
            .toString();
}
```

### 3. 流水号生成规则

```java
// 默认生成规则
String serialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime();

// 自定义流水号（如果配置了customSerialNoField）
String customSerialNo = extractFieldValue(firstArg, annotation.customSerialNoField());
```

## 实际使用示例

### 1. GCEB接口示例

```java
@Service
public class PassGcebToPamServiceImpl implements PassGcebToPamService {

    @Override
    @InterfaceInvokeLog(
        businessCodeField = "paymentApplyCode",
        urlSuffix = "all-auz/pay/advance-payment",
        interfaceType = InterfaceInvokeLog.InterfaceType.GCEB
    )
    public EsbResponse<String> advancePaymentApply(PaymentApplyDto paymentApplyDto) {
        Guard.notNull(paymentApplyDto, "付款申请单不能为空!");

        try {
            // 获取流水号
            String serialNo = SerialNoContext.get();

            // 构建请求参数
            JSONObject params = buildAdvancePaymentApplyParams(paymentApplyDto, serialNo);

            // 调用接口
            JSONObject jsonResult = callGceb(buildHttpUrl("all-auz/pay/advance-payment"), params);

            // 处理结果
            if (Objects.nonNull(jsonResult) && "000000".equals(jsonResult.getString("responseCode"))) {
                return new EsbResponse<>(serialNo, ResponseCodeEnums.SUCCESS.getCode(),
                                       "预付款申请成功", serialNo);
            } else {
                String result = jsonResult != null ? jsonResult.toJSONString() : "接口返回为空";
                return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), result, serialNo);
            }
        } catch (Exception e) {
            logger.error("预付款申请导入报错", e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), null);
        }
    }
}
```

### 2. GEMS接口示例

```java
@InterfaceInvokeLog(
    businessCodeField = "requestId",
    urlSuffix = "gems/data/sync",
    interfaceType = InterfaceInvokeLog.InterfaceType.GEMS
)
public CarrierResponse<String> syncGemsData(GemsRequestDto request) {
    try {
        String serialNo = SerialNoContext.get();

        // 调用GEMS接口
        CarrierResponse<String> response = carrierHelper.doPost(
            buildHttpUrl("gems/data/sync"),
            request,
            String.class
        );

        return response;
    } catch (Exception e) {
        logger.error("GEMS数据同步失败", e);
        return new CarrierResponse<>("9999", e.getMessage(), null);
    }
}
```

## 优化效果对比

### 优化前（手动编写重复代码）

```java
public EsbResponse<String> advancePaymentApply(PaymentApplyDto paymentApplyDto) {
    String serialNo = EsbConstant.PAM_REQUEST_ID + new Date().getTime();
    PassInterfaceInvokeLog passInterfaceInvokeLog = new PassInterfaceInvokeLog();

    try {
        // 构建参数
        JSONObject params = buildAdvancePaymentApplyParams(paymentApplyDto, serialNo);
        String url = buildHttpUrl("all-auz/pay/advance-payment");

        // 插入日志
        Long logId = passInterfaceInvokeLogService.insertLog(
            paymentApplyDto.getPaymentApplyCode(),
            serialNo,
            params.toJSONString(),
            url,
            "GCEB"
        );
        passInterfaceInvokeLog.setId(logId);

        // 调用接口
        JSONObject jsonResult = callGceb(url, params);

        // 处理结果
        if (Objects.nonNull(jsonResult) && "000000".equals(jsonResult.getString("responseCode"))) {
            passInterfaceInvokeLog.setInvokeStatus(1);
            passInterfaceInvokeLog.setInvokeResult(JSON.toJSONString(jsonResult));
            return new EsbResponse<>(serialNo, ResponseCodeEnums.SUCCESS.getCode(),
                                   "预付款申请成功", serialNo);
        } else {
            String result = jsonResult != null ? jsonResult.toJSONString() : "接口返回为空";
            passInterfaceInvokeLog.setInvokeStatus(2);
            passInterfaceInvokeLog.setInvokeResult(result);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), result, serialNo);
        }
    } catch (Exception e) {
        passInterfaceInvokeLog.setInvokeStatus(2);
        passInterfaceInvokeLog.setInvokeResult(e.getMessage());
        return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), serialNo);
    } finally {
        if (passInterfaceInvokeLog.getId() != null) {
            passInterfaceInvokeLogService.updateLog(passInterfaceInvokeLog);
        }
    }
}
```

### 优化后（使用注解）

```java
@InterfaceInvokeLog(
    businessCodeField = "paymentApplyCode",
    urlSuffix = "all-auz/pay/advance-payment",
    interfaceType = InterfaceInvokeLog.InterfaceType.GCEB
)
public EsbResponse<String> advancePaymentApply(PaymentApplyDto paymentApplyDto) {
    Guard.notNull(paymentApplyDto, "付款申请单不能为空!");

    try {
        String serialNo = SerialNoContext.get();
        JSONObject params = buildAdvancePaymentApplyParams(paymentApplyDto, serialNo);
        JSONObject jsonResult = callGceb(buildHttpUrl("all-auz/pay/advance-payment"), params);

        if (Objects.nonNull(jsonResult) && "000000".equals(jsonResult.getString("responseCode"))) {
            return new EsbResponse<>(serialNo, ResponseCodeEnums.SUCCESS.getCode(),
                                   "预付款申请成功", serialNo);
        } else {
            String result = jsonResult != null ? jsonResult.toJSONString() : "接口返回为空";
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), result, serialNo);
        }
    } catch (Exception e) {
        logger.error("预付款申请导入报错", e);
        return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), null);
    }
}
```

## 代码减少统计

- **代码行数减少**：每个方法减少约 20-25 行重复代码
- **维护成本降低**：日志记录逻辑集中管理，修改时只需修改切面
- **复用性提高**：其他服务也可以使用相同的注解和切面
- **错误率降低**：避免了手动编写重复代码可能产生的错误
- **一致性保证**：所有接口的日志记录格式和逻辑完全一致

## 注意事项

### 1. 方法签名要求
- 不再要求方法包含 serialNo 参数
- 可以通过 `SerialNoContext.get()` 获取流水号
- 切面会自动设置流水号到返回值（如果返回值支持）

### 2. 业务编号字段
- 确保业务对象中存在指定的业务编号字段
- 支持嵌套属性访问，如 `"order.orderCode"`
- 如果字段不存在或为空，业务编号将为空字符串

### 3. 异常处理
- 切面会自动处理异常，记录异常信息
- 不会改变原有的异常抛出行为
- 异常状态会自动设置为失败（2）

### 4. 性能影响
- 切面会增加少量的性能开销
- 主要开销在数据库日志记录操作
- 相比手动编写重复代码，整体性能更优

### 5. 事务处理
- 切面的日志记录操作会参与到方法的事务中
- 如果业务方法回滚，日志记录也会回滚
- 建议在独立事务中记录日志（如需要）

### 6. ThreadLocal管理
- 切面会自动生成流水号并存储到 ThreadLocal
- 使用后会自动清理，防止内存泄漏
- 不要在异步线程中使用 SerialNoContext

### 7. 字符串长度限制
- 数据库字段有长度限制，切面会自动截断超长字符串
- `invoke_result` 字段限制为 2000 字符
- `invoke_params` 为 LONGVARCHAR 类型，支持更长内容

### 8. 响应结果处理
- 切面会根据不同的响应类型自动判断成功/失败
- EsbResponse：判断 `responseCode.equals("000000")`
- CarrierResponse：判断 `code.equals("0000")`
- 其他类型默认为成功状态

## 扩展支持

### 1. 添加新的接口类型

```java
public enum InterfaceType {
    GCEB("GCEB", "金融网关接口", "gfp.apiUrl"),
    GEMS("GEMS", "GEMS系统接口", "mgp.apiurl"),
    // 添加新的接口类型
    NEW_SYSTEM("NEW_SYSTEM", "新系统接口", "new.system.apiUrl");
}
```

### 2. 自定义响应结果处理

如需支持新的响应类型，可以在切面的 `handleResult` 方法中添加处理逻辑：

```java
private void handleResult(Object result, PassInterfaceInvokeLog log,
                         boolean logResult, InterfaceType interfaceType) {
    if (result instanceof YourCustomResponse) {
        YourCustomResponse response = (YourCustomResponse) result;
        if ("SUCCESS".equals(response.getStatus())) {
            log.setInvokeStatus(1);
        } else {
            log.setInvokeStatus(2);
        }

        if (logResult) {
            log.setInvokeResult(truncateString(JSON.toJSONString(response)));
        }
    }
    // ... 其他处理逻辑
}
```

### 3. 详细日志输出版本

在 `ctc-web` 模块中有一个增强版本的切面，提供更详细的日志输出：

```java
// 记录接口调用开始日志
logger.info("=== 接口调用开始 === 流水号: {}, 方法: {}, 接口类型: {}, 业务编号: {}",
           serialNo, methodName, interfaceType.getSystemName(), businessCode);

// 记录方法执行时间
logger.info("接口方法执行完成 - 流水号: {}, 执行耗时: {}ms", serialNo, methodExecutionTime);

// 记录接口调用结束日志
logger.info("=== 接口调用结束 === 流水号: {}, 总耗时: {}ms, 状态: {}",
           serialNo, totalTime, getStatusText(passInterfaceInvokeLog.getInvokeStatus()));
```

## 故障排查

### 1. 流水号获取为空
- 检查是否在切面管理的方法中调用 `SerialNoContext.get()`
- 确认方法上有 `@InterfaceInvokeLog` 注解
- 检查切面是否正常工作

### 2. 业务编号提取失败
- 检查 `businessCodeField` 配置是否正确
- 确认对象中存在指定字段
- 检查嵌套属性路径是否正确

### 3. 数据库日志记录失败
- 检查数据库连接是否正常
- 确认表结构是否正确
- 检查字段长度是否超限

### 4. URL构建失败
- 检查配置文件中的URL配置是否正确
- 确认 `urlSuffix` 配置是否正确
- 检查 `InterfaceType` 的 `configKey` 是否匹配

## 总结

`InterfaceInvokeLogAspect` 通过 AOP 技术实现了接口调用日志的统一管理，大大减少了重复代码，提高了代码的可维护性和一致性。通过简单的注解配置，就能实现完整的接口调用日志记录功能，是一个非常实用的技术方案。