package com.midea.pam.ctc.sdp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * GERP通用数据传输对象
 * 用于SDP平台与GERP系统之间的通用数据交换
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(description = "GERP通用数据")
public class GERPCommInData {

    @ApiModelProperty("组织ID（按需传参）")
    private String orgId;

    @ApiModelProperty("多语言（按需传参）")
    private String nlsLanguage;

    @ApiModelProperty("通用ESB通信表数据")
    private List<PkgPEsbCommTbl> pkgPEsbCommTbl;

    /**
     * 通用ESB通信表数据项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @ApiModel(description = "通用ESB通信表数据项")
    public static class PkgPEsbCommTbl {

        @ApiModelProperty("ESB数据源")
        private String esbDataSource;

        @ApiModelProperty("来源代码")
        private String sourceCode;

        @ApiModelProperty("ESB序列号")
        private String esbSerialNum;

        @ApiModelProperty("来源编号")
        private String sourceNum;

        @ApiModelProperty("来源行号")
        private String sourceLineNum;

        @ApiModelProperty("来源明细号")
        private String sourceDtlNum;

        @ApiModelProperty("排序")
        private BigDecimal rank;

        @ApiModelProperty("列1")
        private String col1;

        @ApiModelProperty("列2")
        private String col2;

        @ApiModelProperty("列3")
        private String col3;

        @ApiModelProperty("列4")
        private String col4;

        @ApiModelProperty("列5")
        private String col5;

        @ApiModelProperty("列6")
        private String col6;

        @ApiModelProperty("列7")
        private String col7;

        @ApiModelProperty("列8")
        private String col8;

        @ApiModelProperty("列9")
        private String col9;

        @ApiModelProperty("列10")
        private String col10;

        @ApiModelProperty("列11")
        private String col11;

        @ApiModelProperty("列12")
        private String col12;

        @ApiModelProperty("列13")
        private String col13;

        @ApiModelProperty("列14")
        private String col14;

        @ApiModelProperty("列15")
        private String col15;

        @ApiModelProperty("列16")
        private String col16;

        @ApiModelProperty("列17")
        private String col17;

        @ApiModelProperty("列18")
        private String col18;

        @ApiModelProperty("列19")
        private String col19;

        @ApiModelProperty("列20")
        private String col20;

        @ApiModelProperty("列21")
        private String col21;

        @ApiModelProperty("列22")
        private String col22;

        @ApiModelProperty("列23")
        private String col23;

        @ApiModelProperty("列24")
        private String col24;

        @ApiModelProperty("列25")
        private String col25;

        @ApiModelProperty("列26")
        private String col26;

        @ApiModelProperty("列27")
        private String col27;

        @ApiModelProperty("列28")
        private String col28;

        @ApiModelProperty("列29")
        private String col29;

        @ApiModelProperty("列30")
        private String col30;

        @ApiModelProperty("列31")
        private String col31;

        @ApiModelProperty("列32")
        private String col32;

        @ApiModelProperty("列33")
        private String col33;

        @ApiModelProperty("列34")
        private String col34;

        @ApiModelProperty("列35")
        private String col35;

        @ApiModelProperty("列36")
        private String col36;

        @ApiModelProperty("列37")
        private String col37;

        @ApiModelProperty("列38")
        private String col38;

        @ApiModelProperty("列39")
        private String col39;

        @ApiModelProperty("列40")
        private String col40;

        @ApiModelProperty("列41")
        private String col41;

        @ApiModelProperty("列42")
        private String col42;

        @ApiModelProperty("列43")
        private String col43;

        @ApiModelProperty("列44")
        private String col44;

        @ApiModelProperty("列45")
        private String col45;

        @ApiModelProperty("列46")
        private String col46;

        @ApiModelProperty("列47")
        private String col47;

        @ApiModelProperty("列48")
        private String col48;

        @ApiModelProperty("列49")
        private String col49;

        @ApiModelProperty("列50")
        private String col50;

        @ApiModelProperty("列51")
        private String col51;

        @ApiModelProperty("列52")
        private String col52;

        @ApiModelProperty("列53")
        private String col53;

        @ApiModelProperty("列54")
        private String col54;

        @ApiModelProperty("列55")
        private String col55;

        @ApiModelProperty("列56")
        private String col56;

        @ApiModelProperty("列57")
        private String col57;

        @ApiModelProperty("列58")
        private String col58;

        @ApiModelProperty("列59")
        private String col59;

        @ApiModelProperty("列60")
        private String col60;

        @ApiModelProperty("列61")
        private String col61;

        @ApiModelProperty("列62")
        private String col62;

        @ApiModelProperty("列63")
        private String col63;

        @ApiModelProperty("列64")
        private String col64;

        @ApiModelProperty("列65")
        private String col65;

        @ApiModelProperty("列66")
        private String col66;

        @ApiModelProperty("列67")
        private String col67;

        @ApiModelProperty("列68")
        private String col68;

        @ApiModelProperty("列69")
        private String col69;

        @ApiModelProperty("列70")
        private String col70;

        @ApiModelProperty("列71")
        private String col71;

        @ApiModelProperty("列72")
        private String col72;

        @ApiModelProperty("列73")
        private String col73;

        @ApiModelProperty("列74")
        private String col74;

        @ApiModelProperty("列75")
        private String col75;

        @ApiModelProperty("列76")
        private String col76;

        @ApiModelProperty("列77")
        private String col77;

        @ApiModelProperty("列78")
        private String col78;

        @ApiModelProperty("列79")
        private String col79;

        @ApiModelProperty("列80")
        private String col80;
    }
}
