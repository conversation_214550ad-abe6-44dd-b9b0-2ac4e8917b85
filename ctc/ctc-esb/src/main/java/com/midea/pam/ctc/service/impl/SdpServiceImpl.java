package com.midea.pam.ctc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.midea.esb.RequestHeader;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.dto.InventoryDto;
import com.midea.pam.common.basedata.dto.MaterialDto;
import com.midea.pam.common.basedata.dto.OrganizationRelDto;
import com.midea.pam.common.basedata.entity.OrganizationRel;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.basedata.query.InventoryQuery;
import com.midea.pam.common.basedata.query.OrganizationRelQuery;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.DifferenceShareEsb;
import com.midea.pam.common.ctc.dto.CostAccountingErpDto;
import com.midea.pam.common.ctc.dto.CustomerTransferDto;
import com.midea.pam.common.ctc.dto.DifferenceShareEsbSyncDTO;
import com.midea.pam.common.ctc.dto.InvoiceReceivableErpDto;
import com.midea.pam.common.ctc.dto.MaterialSyncErpDto;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailErpDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceErpDto;
import com.midea.pam.common.ctc.dto.PaymentPlanErpDto;
import com.midea.pam.common.ctc.dto.PaymentPlanInvoiceErpDto;
import com.midea.pam.common.ctc.dto.PositiveAndNegativeInvoiceRecordDetails;
import com.midea.pam.common.ctc.dto.PositiveAndNegativeInvoiceRecordDto;
import com.midea.pam.common.ctc.dto.PrepayErpDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimInvoiceRelDto;
import com.midea.pam.common.ctc.dto.WriteOffDto;
import com.midea.pam.common.ctc.entity.Project;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CodePrefix;
import com.midea.pam.common.enums.OrgCustomDictOrgFrom;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.enums.TopicCodeEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.sdp.dto.PurchaseOrderPushFAPDto;
import com.midea.pam.common.sdp.dto.PurchaseOrderPushFAPLineDto;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtil;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.CorUserStatus;
import com.midea.pam.ctc.common.enums.ReceiptClaimEnum;
import com.midea.pam.ctc.esb.util.EsbCtcUtil;
import com.midea.pam.ctc.sdp.dto.FAPArTransferHeader;
import com.midea.pam.ctc.sdp.dto.GERPAPINVOICEData;
import com.midea.pam.ctc.sdp.dto.GERPAPPREPAY;
import com.midea.pam.ctc.sdp.dto.GERPApCanceData;
import com.midea.pam.ctc.sdp.dto.GERPApPaymentsData;
import com.midea.pam.ctc.sdp.dto.GERPCuxAPaymentPlanData;
import com.midea.pam.ctc.sdp.dto.GERPGlJournalsImportHeader;
import com.midea.pam.ctc.sdp.dto.GERPIcpGetSeqHeader;
import com.midea.pam.ctc.sdp.dto.GERPCuxArUnapplyHeader;
import com.midea.pam.ctc.sdp.dto.GERPCuxArUnapplyRequest;
import com.midea.pam.ctc.sdp.dto.GERPIcpNonrelateHeader;
import com.midea.pam.ctc.sdp.dto.GERPInvTransationHeader;
import com.midea.pam.ctc.sdp.dto.GERPPoCommonReqHeader;
import com.midea.pam.ctc.sdp.dto.GERPPoStandardPoData;
import com.midea.pam.ctc.sdp.dto.GERPPoStandardPoHeader;
import com.midea.pam.ctc.sdp.dto.GERPPoStandardPoLine;
import com.midea.pam.ctc.sdp.dto.GERPPoStandardPoPoll;
import com.midea.pam.ctc.sdp.vo.ResponseObj;
import com.midea.pam.ctc.service.BasedataExtService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.service.OrganizationRelExtService;
import com.midea.pam.ctc.service.ProjectService;
import com.midea.pam.ctc.service.SdpCarrierServicel;
import com.midea.pam.ctc.service.SdpService;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.sdp.dto.common.SdpResult;
import com.midea.sdp.dto.common.SdpTradeResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * @program: pam
 * @description: SdpServiceImpl
 * @author: ygx
 * @create: 2024-5-13 10:00
 **/
public class SdpServiceImpl implements SdpService {

    private static final Logger logger = LoggerFactory.getLogger(SdpServiceImpl.class);

    @Value("${sdp.appKey:}")
    private String appKey;

    @Value("${sdp.appSecret:}")
    private String appSecret;

    @Value("${sdp.driverDataInUrl:}")
    private String driverDataInUrl;

    @Resource
    private SdpCarrierServicel sdpCarrierServicel;

    @Resource
    private OrganizationRelExtService organizationRelExtService;

    @Resource
    private BasedataExtService basedataExtService;

    @Resource
    private OrganizationCustomDictService organizationCustomDictService;

    @Resource
    @Lazy
    private ProjectService projectService;


    /**
     * 截取businessId的后两段（日期和流水号）
     * 例如：SDP-PAM-GERPCUXAPAYMENTPLAN-250617-0005249 -> 250617-0005249
     *
     * @param businessId 完整的业务流水号
     * @return 截取后的后两段
     */
    private String extractLastTwoSegments(String businessId) {
        if (businessId == null || businessId.isEmpty()) {
            return businessId;
        }
        String[] segments = businessId.split("-");
        if (segments.length >= 2) {
            return segments[segments.length - 2] + "-" + segments[segments.length - 1];
        }
        return businessId;
    }

    /**
     * 流水号生成规则如下：SDP+系统编码+主题编码+YYMMDD+多位流水，必须唯一，举例：SDP-GSC-ITEM-210520-00000001（交易类必须遵守流水号生成规则）
     *
     * @param topicCodeEnum
     * @return
     */
    public String generateSequence(TopicCodeEnum topicCodeEnum) {
        String sequence = CacheDataUtils.generateSequence(Constants.SPD_TOPIC_CODE_LENGTH, topicCodeEnum.getTargetSystemCode());
        return "SDP-PAM-" + topicCodeEnum.getTopicCode() + "-" + DateUtils.format(new Date(), "yyMMdd") + "-" + sequence.substring(9);
    }

    /**
     * ResponseObj转EsbResponse
     *
     * @param businessId
     * @param sdpTradeResult
     * @return
     */
    private static EsbResponse getEsbResponse(String businessId, SdpTradeResult<ResponseObj> sdpTradeResult) {
        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    /**
     * PAM-ERP-047 GSC触发ERP请求
     */
    @Override
    public EsbResponse callGERPPoCommonReq(DifferenceShareEsbSyncDTO dto) {
        // 接口卡
        String interfaceCode = dto.getInterfaceCode();
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.getEnumByInterfaceCode(interfaceCode);
        Guard.notNull(topicCodeEnum, "该接口卡尚未接入SDP：" + interfaceCode);
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);
        logger.info("生成业务流水号：{}", businessId);

        // 构建requestObj
        GERPPoCommonReqHeader header = new GERPPoCommonReqHeader();
        header.setSourceCode("PAM");
        header.setSourceNum("PAM_"  + dto.getId());
        header.setEsbSerialNum(businessId);
        header.setEsbDataSource(dto.getInterfaceCode());
        header.setAppShotName(DifferenceShareEsb.APP_SHOT_NAME);
        header.setConcurrentProgramName(dto.getInterfaceName());

        if (DifferenceShareEsb.InvoicePriceDifferenceRecord.ESB_DATA_SOURCE.equals(interfaceCode)) {
            header.setArgument1(String.valueOf(dto.getOuId()));
            header.setArgument2(dto.getGlStartDate());
            header.setArgument3(dto.getGlEndDate());
            header.setArgument8("明细");
            header.setArgument11("PAM");
        } else if (DifferenceShareEsb.MaterialUpdateDifferenceRecord.ESB_DATA_SOURCE.equals(interfaceCode)) {
            header.setArgument1(String.valueOf(dto.getOrganizationId()));
            header.setArgument2(dto.getGlStartDate());
            header.setArgument3(dto.getGlEndDate());
        } else if (DifferenceShareEsb.PurchasePriceDifferenceRecord.ESB_DATA_SOURCE.equals(interfaceCode)) {
            header.setArgument1(String.valueOf(dto.getOuId()));
            header.setArgument2(String.valueOf(dto.getOrganizationId()));
            header.setArgument3(dto.getGlStartDate());
            header.setArgument4(dto.getGlEndDate());
            header.setArgument11("PAM");
        } else if (DifferenceShareEsb.SubjectBalanceRecord.ESB_DATA_SOURCE.equals(interfaceCode)) {
            header.setArgument1(String.valueOf(dto.getLedgerId())); // 帐套ID
            header.setArgument2("50428");
            header.setArgument3(dto.getCompanyCode());
            header.setArgument4(dto.getGlPeriod());
            header.setArgument5(dto.getGlPeriod());
            header.setArgument8("N");// 是否显示0数据（Y/N）
            header.setArgument9("PAM");
        }
        List<GERPPoCommonReqHeader> list = new ArrayList<>();
        list.add(header);

        Map<String, Object> requestObj = new HashMap<>();
        requestObj.put("pkgPIfaceHeadersTbl", list);

        //发起请求
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, requestObj);
        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null) {
            logger.info("SDP GSC触发ERP请求调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            logger.error("SDP GSC触发ERP请求调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, null);
        }
    }

    /**
     * PAM-ERP-034 采购订单写入 (包含采购订单更新,因为是同一个主题)
     */
    @Override
    public EsbResponse callGERPPoStandardPo(List<PurchaseOrderDto> dtos, Boolean isChange) {
        if (CollectionUtils.isEmpty(dtos)) {
            return new EsbResponse();
        }
        // 接口卡
        String interfaceCode = dtos.get(0).getEsbDataSource();
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.getEnumByInterfaceCode(interfaceCode);
        Guard.notNull(topicCodeEnum, "该接口卡尚未接入SDP：" + interfaceCode);
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);

        Integer operationType = isChange ? 1 : 0;

        Map<String, Object> requestObj = new HashMap<>();
        List<Object> headerList;
        try {
            headerList = new ArrayList<>();
            BigDecimal batchId = new BigDecimal(DateUtils.getCurrentDate().getTime());
            for (PurchaseOrderDto dto : dtos) {
                //header
                GERPPoStandardPoHeader header = new GERPPoStandardPoHeader();
                List<GERPPoStandardPoLine> lineList = new ArrayList<>();
                String syncSourceSystem = "PAM";

                if (StringUtils.isNotEmpty(dto.getSyncSourceSystem())) {
                    syncSourceSystem = dto.getSyncSourceSystem();
                }

                header.setSourceSystemCode(syncSourceSystem);
                header.setSourceOrderNo(dto.getNum());
                header.setHeaderDt(DateUtils.format(dto.getCreateAt(), DateUtils.FORMAT_SHORT));
                //是否急件
                header.setAttribute13("N");
                if (dto.getDispatchIs() != null && dto.getDispatchIs()) {
                    header.setAttribute13("Y");
                }
                //comments 备注 头;
                header.setComments("#");
                if (dto.getRemark() != null) {
                    header.setComments("#" + dto.getRemark());
                }

                header.setEsbDataSource(dto.getEsbDataSource());
                header.setSourceCode("PAM");
                header.setSourceNum("" + dto.getId());
                header.setEsbSerialNum(businessId);
                BigDecimal headerInterfaceId = new BigDecimal(dto.getNum()
                        .replace(CodePrefix.PURCHASE_ORDER_1.code(), "").replace(CodePrefix.PURCHASE_ORDER_3.code(), ""));
                header.setBatchId(batchId);

                Long scpPoHeaderId = Long.valueOf(BigDecimalUtils.stripTrailingZeros(headerInterfaceId).toPlainString() + operationType);
                if (dto.getNum() != null && StringUtils.isNotEmpty(dto.getNum().replace(CodePrefix.PURCHASE_ORDER_1.code(), "")
                        .replace(CodePrefix.PURCHASE_ORDER_3.code(), ""))) {
                    header.setHeaderInterfaceId(headerInterfaceId);
                    header.setScpPoHeaderId(scpPoHeaderId);
                }
                if (dto.getOuId() != null) {
                    header.setOrgId(new BigDecimal(dto.getOuId()));
                }
                header.setVendorCode(dto.getVendorNum());
                header.setVendorName(dto.getVendorName());
                header.setVendorSiteCode(dto.getVendorSiteCode());
                header.setVendorSiteId(dto.getErpVendorSiteId());
                header.setAgentId(new BigDecimal(dto.getErpBuyerId()));
                header.setCurrencyCode(Optional.ofNullable(dto.getCurrency()).orElse("CNY"));
//                header.setRate(Optional.ofNullable(dto.getConversionRate()).orElse(BigDecimal.ONE));
                header.setRateDate(DateUtils.format(dto.getConversionDate(), DateUtils.FORMAT_SHORT));
                header.setRateType(dto.getConversionType());
                header.setVendorId(dto.getVendorId());
                if (Objects.equals(CorUserStatus.Corporate.getCode(), dto.getConversionType())) {
                    header.setRateType(CorUserStatus.Corporate.getName());
                } else if (Objects.equals(CorUserStatus.User.getCode(), dto.getConversionType())) {
                    header.setRateType(CorUserStatus.User.getName());
                }
                header.setSegment1(dto.getNum());
                header.setAttribute1(dto.getOrderType());
                header.setAttribute2("BPA");
                //wbs
                if (dto.getPricingType() != null) {
                    // 一价一单, 一揽子协议
                    header.setAttribute1("DJ01");
                    header.setAttribute2("PO");
                }
                headerList.add(header);

                //line
                for (PurchaseOrderDetailDto orderDetailDto : dto.getPurchaseOrderDetailDtos()) {
                    GERPPoStandardPoLine line = new GERPPoStandardPoLine();

                    if (dto.getNum() != null && StringUtils.isNotEmpty(dto.getNum().replace(CodePrefix.PURCHASE_ORDER_1.code(), "")
                            .replace(CodePrefix.PURCHASE_ORDER_3.code(), ""))) {
                        line.setHeaderInterfaceId(new BigDecimal(dto.getNum()
                                .replace(CodePrefix.PURCHASE_ORDER_1.code(), "").replace(CodePrefix.PURCHASE_ORDER_3.code(), "")));
                    }
                    line.setLineNum(Optional.ofNullable(orderDetailDto.getLineNumber()).map(BigDecimal::new).orElse(BigDecimal.ONE));

                    line.setAttribute7(this.line_attribute7(orderDetailDto));
                    line.setAttribute6(orderDetailDto.getProjectNum());
                    if (orderDetailDto.getWbsSummaryCode() != null) {
                        line.setAttribute8(orderDetailDto.getWbsSummaryCode());
                    }
                    line.setUnitPrice(BigDecimal.ZERO);
                    if (orderDetailDto.getDiscountPrice() != null) {
                        line.setUnitPrice(orderDetailDto.getDiscountPrice());
                    }

                    line.setPamReceiveLocation(orderDetailDto.getDeliveryAddress());
                    line.setPamReceiverName(orderDetailDto.getConsignee());
                    line.setPamReceiverPhone(orderDetailDto.getContactPhone());
                    //查询对应物料的ERP ID
                    List<MaterialDto> materialDtoList = basedataExtService.getMaterialListByMaterialIds(Lists.newArrayList(orderDetailDto.getMaterielId()));
                    if (ListUtils.isNotEmpty(materialDtoList)) {
                        MaterialDto materialDto = materialDtoList.get(0);
                        Long itemId = materialDto.getItemId();
                        line.setItemId(new BigDecimal(itemId));
                    }

                    line.setItem(orderDetailDto.getErpCode());
                    line.setItemCode(orderDetailDto.getErpCode());
                    line.setQuantity(orderDetailDto.getOrderNum());
                    line.setUomCode(orderDetailDto.getUnitCode());
                    line.setUnitMeasLookupCode(orderDetailDto.getUnit());
                    // line.setNeedByDate(DateUtils.format(orderDetailDto.getDeliveryTime(), DateUtils.FORMAT_SHORT));
                    if (orderDetailDto.getOrganizationId() != null) {
                        line.setShipToOrganizationId(new BigDecimal(orderDetailDto.getOrganizationId()));
                    }
                    line.setAttribute1("0");
                    line.setAttribute5(orderDetailDto.getSecondaryInventoryName());
                    line.setHeaderDt(DateUtils.format(orderDetailDto.getCreateAt(), DateUtils.FORMAT_SHORT));
                    line.setRootOrgId(dto.getOuId());
                    line.setRootSourceSystemCode(syncSourceSystem);
                    line.setScpPoHeaderId(scpPoHeaderId);
                    line.setScpPoLineId(orderDetailDto.getId());
                    //poll
                    ArrayList<GERPPoStandardPoPoll> pollList = new ArrayList<>();
                    GERPPoStandardPoPoll poll = new GERPPoStandardPoPoll();
                    poll.setHeaderDt(DateUtils.format(orderDetailDto.getCreateAt(), DateUtils.FORMAT_SHORT));
                    poll.setRootOrgId(dto.getOuId());
                    poll.setRootSourceSystemCode(syncSourceSystem);
                    poll.setRootSourceOrderNo(dto.getNum());
                    poll.setShipmentNum(1L);
                    poll.setScpPoHeaderId(scpPoHeaderId);
                    poll.setQuantity(orderDetailDto.getOrderNum());
                    poll.setNeedByDate(DateUtils.format(orderDetailDto.getDeliveryTime(), DateUtils.FORMAT_SHORT));
                    OrganizationRel organizationRel = getOrganizationRel(dto.getOuId());
                    if (Objects.nonNull(organizationRel)) {
                        poll.setShipToOrganizationId(organizationRel.getOrganizationId());
                    } else {
                        logger.warn("[采购订单同步] 未查询到OU对应的组织关系, ouId={}", dto.getOuId());
                    }

                    pollList.add(poll);
                    //set poll
                    line.setPoll(pollList);
                    lineList.add(line);
                }
                header.setPol(lineList);

            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }

        //发起请求
        SdpResult sdpResult = sdpCarrierServicel.doSdpDataImport(targetSystemCode, topicCode, businessId, headerList);

        if (Objects.equals(sdpResult.getSuccess(), Boolean.TRUE) && Objects.equals(sdpResult.getMessage(), "success")
                && Objects.equals(sdpResult.getCode(), "0")) {
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpResult.getMessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpResult);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    private String line_attribute7(PurchaseOrderDetailDto purchaseOrderDetailDto) {
        if (null == purchaseOrderDetailDto) {
            return "#";
        }

        StringBuffer sb = new StringBuffer("#");
        // #批次号#PO行图纸版本号#跟踪日期#单价#折后#图号/型号#需求发布日期#承诺日期
        String designReleaseLotNumber = purchaseOrderDetailDto.getDesignReleaseLotNumber();
        String chartVersion = purchaseOrderDetailDto.getChartVersion();
        Date trackDate = purchaseOrderDetailDto.getTrackDate();
        BigDecimal unitPrice = purchaseOrderDetailDto.getUnitPrice();
        BigDecimal discount = purchaseOrderDetailDto.getDiscount();
        String model = purchaseOrderDetailDto.getModel();
        Date publishTime = purchaseOrderDetailDto.getPublishTime();
        Date contractAppointDate = purchaseOrderDetailDto.getContractAppointDate();
        if (StringUtils.isNotEmpty(designReleaseLotNumber)) {
            sb.append(designReleaseLotNumber);
        }
        sb.append("#");
        if (StringUtils.isNotEmpty(chartVersion)) {
            sb.append(chartVersion);
        }
        sb.append("#");
        if (Objects.nonNull(trackDate)) {
            sb.append(DateUtil.dateToString("yyyy-MM-dd", trackDate));
        }
        sb.append("#");
        if (Objects.nonNull(unitPrice)) {
            sb.append(unitPrice);
        }
        sb.append("#");
        if (Objects.nonNull(discount)) {
            sb.append(discount);
        }
        sb.append("#");
        if (StringUtils.isNotEmpty(model)) {
            sb.append(model);
        }
        sb.append("#");
        if (Objects.nonNull(publishTime)) {
            sb.append(DateUtil.dateToString("yyyy-MM-dd", publishTime));
        }
        sb.append("#");
        if (Objects.nonNull(contractAppointDate)) {
            sb.append(DateUtil.dateToString("yyyy-MM-dd", contractAppointDate));
        }

        return sb.toString();
    }

    /**
     * C-PAM-FAP-AC 客户间转款写入
     */
    @Override
    public EsbResponse callFAPArTransfer(List<CustomerTransferDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }
        // 接口卡
        String interfaceCode = "C-PAM-FAP-AC";
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.getEnumByInterfaceCode(interfaceCode);
        Guard.notNull(topicCodeEnum, "该接口卡尚未接入SDP：" + interfaceCode);
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);

        List<FAPArTransferHeader> headerList = new ArrayList<>();
        try {
            for (CustomerTransferDto dto : dtoList) {
                //header
                FAPArTransferHeader header = new FAPArTransferHeader();
                header.setInterfaceCode(interfaceCode);
                header.setSourceId(String.valueOf(dto.getId()));
                header.setSerialNumber(businessId);
                header.setSourceSysCode("PAM");
                header.setSceneCode("AC02");  //AC01-同OU转款，AC02-跨OU转款
                header.setSourceSysBusCode(dto.getTransferCode());
                header.setInOrgOriginCode("GERP");
                header.setInOrgId(new BigDecimal(dto.getTransferInOuId()));
                header.setOutOrgOriginCode("GERP");
                header.setOutOrgId(new BigDecimal(dto.getTransferOutOuId()));
                header.setInCustomerNumber(dto.getTransferInCustomerCode());
                header.setOutCustomerNumber(dto.getTransferOutCustomerCode());
                header.setGlDate(Optional.ofNullable(dto.getAuditDate()).map(DateUtils::format).orElse(null));
                header.setTransactionDate(Optional.ofNullable(dto.getAuditDate()).map(DateUtils::format).orElse(null));
                header.setInReceiptNumber(dto.getTransferCode());
                header.setOutReceiptNumber(dto.getTransferCode());
                header.setApplyReceiptNumber(dto.getOriginReceiptCode());
                header.setAmount(dto.getTransferAmount());
                header.setCurrencyCode(dto.getCurrency());
                header.setConversionType(CorUserStatus.getCodeByName(dto.getConversionType()));
                header.setConversionRate(dto.getConversionRate());
                header.setConversionDate(Optional.ofNullable(dto.getConversionDate()).map(DateUtils::format).orElse(null));
                header.setComments(dto.getRemark());
                header.setReversalFlag(Optional.ofNullable(dto.getReversalFlag()).orElse(BigDecimal.ZERO));  //可选值范围：否，非冲销---0 、是，冲销----1；
                header.setTerritoryCode("CN");
                header.setSourceSysBusId(String.valueOf(dto.getId()));
                header.setOrgOriginCode("GERP");
                header.setSourceSysBusType("DOMESTIC");
                header.setOutFactorDiscountAmount(String.valueOf(dto.getCommission()));
                header.setInFactorDiscountAmount(String.valueOf(dto.getCommission()));
                headerList.add(header);
            }

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }

        //发起请求
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, headerList);

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getCode(), "00000")) {
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getMsg());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    /**
     * PAM-ERP-029 记账写入
     */
    @Override
    public EsbResponse callGERPGlJournalsImport(List<CostAccountingErpDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }
        // 接口卡
        String interfaceCode = "PAM-ERP-029";
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.getEnumByInterfaceCode(interfaceCode);
        Guard.notNull(topicCodeEnum, "该接口卡尚未接入SDP：" + interfaceCode);
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);

        Map<String, Object> requestObj = new HashMap<>();
        List<GERPGlJournalsImportHeader> headerList = new ArrayList<>();
        try {
            for (CostAccountingErpDto dto : dtoList) {
                //header
                GERPGlJournalsImportHeader header = new GERPGlJournalsImportHeader();
                header.setEsbDataSource(dto.getEsbDataSource());
                header.setSourceCode("PAM");
                header.setSourceNum(dto.getId() + "_" + new Date().getTime() + dto.getLineNumber());
                header.setEsbSerialNum(businessId);
                header.setLedgerId(dto.getLedgerId());
                header.setStatus(dto.getStatus());
                header.setAccountingDate(dto.getAccountingDate());
                header.setActualFlag(dto.getActualFlag());
                header.setUserJeSourceName("PAM");
                header.setUserJeCategoryName(dto.getUserJeCategoryName());
                header.setCurrencyCode(dto.getCurrencyCode());
                header.setUserCurrencyConversionType(dto.getUserCurrencyConversionType());
                header.setCurrencyConversionDate(dto.getCurrencyConversionDate());
                header.setCurrencyConversionRate(dto.getCurrencyConversionRate());
                header.setBathName(dto.getBathName());
                header.setJournalName(dto.getJournalName());
                header.setSegment1(dto.getSegment1());
                header.setSegment2(dto.getSegment2());
                header.setSegment3(dto.getSegment3());
                header.setSegment4(dto.getSegment4());
                header.setSegment5(dto.getSegment5());
                header.setSegment6(dto.getSegment6());
                header.setSegment7(dto.getSegment7());
                header.setEnteredDr(dto.getEnterEddr());
                header.setEnteredCr(dto.getEnterEdcr());
                header.setAccountedDr(dto.getAccountEddr());
                header.setAccountedCr(dto.getAccountEdcr());
                header.setReference5(dto.getReference5());
                header.setReference10(dto.getReference10());
                header.setJgzzReconRef(dto.getJgzzreconref());
                header.setOperationType(dto.getOperationType());
                headerList.add(header);
            }
            requestObj.put("pkgPIfaceTbl", headerList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }

        //发起请求
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, requestObj);

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    @Override
    public EsbResponse callErpReceipt(List<ReceiptClaimDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }
        // 接口卡
        String interfaceCode = "PAM-ERP-019";
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.getEnumByInterfaceCode(interfaceCode);
        Guard.notNull(topicCodeEnum, "该接口卡尚未接入SDP：" + interfaceCode);
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);

        Map<String, Object> requestObj = new HashMap<>();
        List<Map<String, Object>> pkgPIfaceTbl = new ArrayList<>();
        try {
            for (ReceiptClaimDto dto : dtoList) {
                Map<String, Object> pkgPIfaceEntry = new HashMap<>();
                pkgPIfaceEntry.put("amount", dto.getClaimAmount());
                pkgPIfaceEntry.put("attribute2", "PAM");
                pkgPIfaceEntry.put("attribute3", com.midea.pam.common.util.StringUtils.substringByByte(dto.getAttribute3(), 140));
                pkgPIfaceEntry.put("attribute4", dto.getBudgetItemCode());
                pkgPIfaceEntry.put("attribute5", "\\" + (ObjectUtils.isEmpty(dto.getCommission()) ? "0" : dto.getCommission().toString()));
                pkgPIfaceEntry.put("attribute9", dto.getSerialNumber());
                pkgPIfaceEntry.put("attribute10", dto.getConntransCode());
                pkgPIfaceEntry.put("attribute11", dto.getTransferCode());
                String callingApi = null;
                if (ReceiptClaimEnum.SALE.getCode() == dto.getBusinessType()) {
                    callingApi = "CREATE_CASH";//标准收款
                } else {
                    callingApi = "CREATE_MISC";//杂项收款
                }
                pkgPIfaceEntry.put("callingApi", callingApi);
                pkgPIfaceEntry.put("comments", com.midea.pam.common.util.StringUtils.substringByByte(dto.getRemark(), 220));
                pkgPIfaceEntry.put("currencyCode", dto.getCurrencyCode());
                pkgPIfaceEntry.put("customerNumber", dto.getCustomerCode());
                pkgPIfaceEntry.put("esbDataSource", interfaceCode);
                pkgPIfaceEntry.put("esbSerialNum", businessId);
                pkgPIfaceEntry.put("glDate", DateUtils.format(dto.getAccountingDate(), DateUtils.FORMAT_SHORT));
                pkgPIfaceEntry.put("orgId", dto.getOuId());
                pkgPIfaceEntry.put("receiptDate", DateUtils.format(dto.getPayDate(), DateUtils.FORMAT_SHORT));
                pkgPIfaceEntry.put("receiptMethodId", dto.getRecMethod());
                pkgPIfaceEntry.put("receiptNumber", dto.getReceiptCode());
//                pkgPIfaceEntry.put("remittanceBankAccountId", dto.getRecBankId());
                pkgPIfaceEntry.put("remittanceBankAccountNum", dto.getRecAccountNo());
                pkgPIfaceEntry.put("receivablesTrxId", dto.getReceivablesTrxId());
                pkgPIfaceEntry.put("sourceCode", "PAM");
                pkgPIfaceEntry.put("sourceNum", "PAM" + new Date().getTime() + "_" + dto.getId());
                // 将每个 pkgPIfaceEntry 添加到 pkgPIfaceTbl 列表中
                pkgPIfaceTbl.add(pkgPIfaceEntry);
            }
            // 将 pkgPIfaceTbl 列表添加到 requestObj 中
            requestObj.put("pkgPIfaceTbl", pkgPIfaceTbl);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }

        //发起请求
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, requestObj);

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    @Override
    public EsbResponse callErpReverseReceipt(List<ReceiptClaimDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }
        // 接口卡
        String interfaceCode = "PAM-ERP-020";
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.getEnumByInterfaceCode(interfaceCode);
        Guard.notNull(topicCodeEnum, "该接口卡尚未接入SDP：" + interfaceCode);
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);

        Map<String, Object> requestObj = new HashMap<>();
        List<Map<String, Object>> pkgPIfaceTbl = new ArrayList<>();
        try {
            for (ReceiptClaimDto dto : dtoList) {
                Map<String, Object> pkgPIfaceEntry = new HashMap<>();
                pkgPIfaceEntry.put("esbDataSource", interfaceCode);
                pkgPIfaceEntry.put("esbSerialNum", businessId);
                pkgPIfaceEntry.put("orgId", dto.getOuId());
                pkgPIfaceEntry.put("receiptNumber", dto.getReceiptCode());
                pkgPIfaceEntry.put("reversalCategoryName", "冲销付款");
                pkgPIfaceEntry.put("reversalDate", DateUtils.format(dto.getErpReversalDate(), DateUtils.FORMAT_SHORT));
                pkgPIfaceEntry.put("reversalGlDate", DateUtils.format(dto.getErpReversalDate(), DateUtils.FORMAT_SHORT));
                pkgPIfaceEntry.put("reversalReasonName", "冲销付款");
                pkgPIfaceEntry.put("reversalComments", com.midea.pam.common.util.StringUtils.substringByByte(dto.getRemark(), 220));
                pkgPIfaceEntry.put("sourceCode", "PAM");
                pkgPIfaceEntry.put("sourceNum", "PAM" + new Date().getTime() + "_" + dto.getId());
                // 将每个 pkgPIfaceEntry 添加到 pkgPIfaceTbl 列表中
                pkgPIfaceTbl.add(pkgPIfaceEntry);
            }
            // 将 pkgPIfaceTbl 列表添加到 requestObj 中
            requestObj.put("pkgPIfaceTbl", pkgPIfaceTbl);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }

        //发起请求
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, requestObj);

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    @Override
    public EsbResponse callErpPaymentInvoice(List<PaymentInvoiceErpDto> dtoList) {
        logger.info("开始调用ERP付款发票接口，dtoList Size：{}", dtoList.size());

        TopicCodeEnum paymentInvoiceEnum = TopicCodeEnum.PAYMENT_INVOICE;
        // 生成流水号
        String businessId = generateSequence(paymentInvoiceEnum);
        logger.info("生成业务流水号：{}", businessId);

        // 创建GERPAPINVOICEData对象
        GERPAPINVOICEData gerpapinvoiceData = new GERPAPINVOICEData();

        gerpapinvoiceData.setOrgId(dtoList.get(0).getOrgId());

        ArrayList<GERPAPINVOICEData.PkgPIfaceHeadersTbl> headersTbls = new ArrayList<>();

        ArrayList<GERPAPINVOICEData.PkgPIfaceLinesTbl> lines = new ArrayList<>();
        // 处理发票数据
        for (PaymentInvoiceErpDto paymentInvoiceErpDto : dtoList) {
            logger.debug("处理发票数据，发票号：{}", paymentInvoiceErpDto.getInvoiceNum());
            // 设置发票头信息
            GERPAPINVOICEData.PkgPIfaceHeadersTbl header = new GERPAPINVOICEData.PkgPIfaceHeadersTbl();
            header.setSourceCode("PAM");
            header.setSourceNum("PAM_" + paymentInvoiceErpDto.getId());
            header.setEsbSerialNum(businessId);
            header.setEsbDataSource(paymentInvoiceEnum.getInterfaceCode());
            header.setOrgId(paymentInvoiceErpDto.getOrgId());
            header.setBatchName(paymentInvoiceErpDto.getBatchName());
            header.setInvoiceNum(paymentInvoiceErpDto.getInvoiceNum());
            header.setInvoiceTypeLookupCode(paymentInvoiceErpDto.getInvoiceTypeLookupCode());
            header.setInvoiceDate(paymentInvoiceErpDto.getInvoiceDate());
            header.setGlDate(paymentInvoiceErpDto.getGlDate());
            header.setVendorId(paymentInvoiceErpDto.getVendorId());
            header.setVendorSiteId(paymentInvoiceErpDto.getVendorSiteCode());
            header.setPaymentMethodCode(paymentInvoiceErpDto.getPaymentMethodCode());
            header.setPaymentMethod(paymentInvoiceErpDto.getPaymentMethod());
            header.setTermsId(paymentInvoiceErpDto.getTermsId());
            header.setTermsName(paymentInvoiceErpDto.getTermsName());
            header.setTermsDate(paymentInvoiceErpDto.getTermsDate());
            header.setCurrencyCode(Optional.ofNullable(paymentInvoiceErpDto.getCurrencyCode()).orElse("CNY"));
            header.setExchangeRateType(paymentInvoiceErpDto.getExchangeRateType());
            header.setExchangeRate(paymentInvoiceErpDto.getExchangeRate());
            header.setExchangeDate(paymentInvoiceErpDto.getExchangeRateDate());
            header.setInvoiceAmount(paymentInvoiceErpDto.getInvoiceAmount());
            header.setTaxAmount(paymentInvoiceErpDto.getTaxAmount());
            header.setTaxClassification(paymentInvoiceErpDto.getTaxClassification());
            header.setDescription(paymentInvoiceErpDto.getDescription());
            header.setAttribute3(paymentInvoiceErpDto.getAttribute3());
            header.setAttribute1("PAM");
            header.setAttribute7(paymentInvoiceErpDto.getAttribute7()); // 凭证类型

            if (StringUtils.isNotBlank(paymentInvoiceErpDto.getAttribute11())) {
                header.setAttribute11(paymentInvoiceErpDto.getAttribute11()); // 中转关联号
            }
            if (StringUtils.isNotBlank(paymentInvoiceErpDto.getAttributeCategory())) {
                header.setAttributeCategory(paymentInvoiceErpDto.getAttributeCategory()); // 业务标识
            }
            if (StringUtils.isNotBlank(paymentInvoiceErpDto.getAttribute13())) {
                header.setAttribute13(paymentInvoiceErpDto.getAttribute13());//出纳流水号
            }
            if (StringUtils.isNotBlank(paymentInvoiceErpDto.getAttribute14())) {
                header.setAttribute14(paymentInvoiceErpDto.getAttribute14());//收款银行账号
            }

            headersTbls.add(header);
            logger.debug("添加发票头信息完成，发票号：{}", paymentInvoiceErpDto.getInvoiceNum());

            //发票行
            List<PaymentInvoiceDetailErpDto> detailErpDtos = paymentInvoiceErpDto.getDetailErpDtos();
            int i = 1;
            logger.debug("开始处理发票行信息，发票号：{}，行数：{}", paymentInvoiceErpDto.getInvoiceNum(), detailErpDtos.size());
            // 设置发票行信息
            for (PaymentInvoiceDetailErpDto detailDto : detailErpDtos) {
                GERPAPINVOICEData.PkgPIfaceLinesTbl line = new GERPAPINVOICEData.PkgPIfaceLinesTbl();
                line.setSourceLineNum("PAM" + detailDto.getId());
                line.setInvoiceNum(paymentInvoiceErpDto.getInvoiceNum());
                line.setOrgId(paymentInvoiceErpDto.getOrgId());
                line.setLineNumber(new BigDecimal(i++));
                line.setLineTypeLookupCode(detailDto.getLineTypeLookUpCode());
                line.setOrgId(paymentInvoiceErpDto.getOrgId());
                line.setInvoiceNum(paymentInvoiceErpDto.getInvoiceNum());
                line.setAmount(detailDto.getAmount());
                line.setSegment1(detailDto.getSegment1());
                line.setSegment2(detailDto.getSegment2());
                line.setSegment3(detailDto.getSegment3());
                line.setSegment4(detailDto.getSegment4());
                line.setSegment5(detailDto.getSegment5());
                line.setSegment6(detailDto.getSegment6());
                line.setSegment7(detailDto.getSegment7());
                line.setReceiptNumber(detailDto.getReceiptNumber());
                line.setReceiptLineNumber(detailDto.getReceiptLineNumber());
                line.setItemCode(detailDto.getItemCode());
                line.setQuantityInvoiced(detailDto.getQuantityInvoiced());
                line.setUnitPrice(detailDto.getUnitPrice());
                line.setDescription(detailDto.getDescription());
                //对应每张发票明细中税额行不推送 ，原因：ERP根据税码获取科目，不能将税额行信息推送
              /*  if ((i-1) % 2 == 0) {
                    logger.info("发票明细中税额行不推送,detailDto:{}",JSON.toJSONString(detailDto));
                    continue;
                }*/
                lines.add(line);
            }
            logger.debug("处理发票行信息完成，发票号：{}", paymentInvoiceErpDto.getInvoiceNum());
        }
        gerpapinvoiceData.setPkgPIfaceHeadersTbl(headersTbls);
        gerpapinvoiceData.setPkgPIfaceLinesTbl(lines);
        logger.info("发票数据处理完成，发票头数量：{}，发票行数量：{}", headersTbls.size(), lines.size());

        //发起请求
        logger.info("开始调用SDP服务，目标系统：{}，主题：{}", paymentInvoiceEnum.getTargetSystemCode(), paymentInvoiceEnum.getTopicCode());
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(paymentInvoiceEnum.getTargetSystemCode(), paymentInvoiceEnum.getTopicCode(), businessId, gerpapinvoiceData);
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            logger.info("ERP付款发票接口调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            logger.error("ERP付款发票接口调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    @Override
    public EsbResponse callErpPaymentInvoiceThird(List<PaymentInvoiceErpDto> dtoList) {
        logger.info("开始调用ERP第三方付款发票接口，dtoList Size：{}", dtoList.size());

        TopicCodeEnum paymentInvoiceThird = TopicCodeEnum.PAYMENT_INVOICE_THIRD;
        // 生成流水号
        String businessId = generateSequence(paymentInvoiceThird);
        logger.info("生成业务流水号：{}", businessId);

        // 创建GERPAPINVOICEData对象
        GERPAPINVOICEData gerpapinvoiceData = new GERPAPINVOICEData();

        gerpapinvoiceData.setOrgId(dtoList.get(0).getOrgId());

        ArrayList<GERPAPINVOICEData.PkgPIfaceHeadersTbl> headersTbls = new ArrayList<>();

        ArrayList<GERPAPINVOICEData.PkgPIfaceLinesTbl> lines = new ArrayList<>();

        for (PaymentInvoiceErpDto paymentInvoiceErpDto : dtoList) {
            logger.debug("处理第三方发票数据，发票号：{}", paymentInvoiceErpDto.getInvoiceNum());
            //发票头
            GERPAPINVOICEData.PkgPIfaceHeadersTbl headersTbl = new GERPAPINVOICEData.PkgPIfaceHeadersTbl();
            // source_code
            headersTbl.setSourceCode("PAM");
            // source_num
            headersTbl.setSourceNum("PAM_" + paymentInvoiceErpDto.getId());
            // esb_serial_num
            headersTbl.setEsbSerialNum(businessId);
            // esb_data_source ：PAM-ERP-023-2
            headersTbl.setEsbDataSource(paymentInvoiceThird.getInterfaceCode());
            // org_id
            headersTbl.setOrgId(paymentInvoiceErpDto.getOrgId());
            // batch_name ：付款申请编码
            headersTbl.setBatchName(paymentInvoiceErpDto.getBatchName());
            // invoice_num ："PAM-" + 付款申请编码
            headersTbl.setInvoiceNum(paymentInvoiceErpDto.getInvoiceNum());
            // invoice_type_lookup_code ：DL
            headersTbl.setInvoiceTypeLookupCode(paymentInvoiceErpDto.getInvoiceTypeLookupCode());
            // invoice_date ：计划付款日期
            headersTbl.setInvoiceDate(paymentInvoiceErpDto.getInvoiceDate());
            // gl_date ：审批通过日期
            headersTbl.setGlDate(paymentInvoiceErpDto.getGlDate());
            // vendor_id ：erp供应商id
            headersTbl.setVendorId(paymentInvoiceErpDto.getVendorId());
            // vendor_site_id ：erp供应商地址id
            headersTbl.setVendorSiteId(paymentInvoiceErpDto.getVendorSiteCode());
            // payment_method_code ：DL
            headersTbl.setPaymentMethodCode(paymentInvoiceErpDto.getPaymentMethodCode());
            // payment_method ：DL
            headersTbl.setPaymentMethod(paymentInvoiceErpDto.getPaymentMethod());
            // terms_id ：付款申请记录的id
            headersTbl.setTermsId(paymentInvoiceErpDto.getTermsId());
            // terms_name ：付款申请记录的付款条件
            headersTbl.setTermsName(paymentInvoiceErpDto.getTermsName());
            // currency_code ：CNY
            headersTbl.setCurrencyCode(paymentInvoiceErpDto.getCurrencyCode());
            // exchange_rate ：0
            headersTbl.setExchangeRate(paymentInvoiceErpDto.getExchangeRate());
            // invoice_amount ：本次付款金额(含税)
            headersTbl.setInvoiceAmount(paymentInvoiceErpDto.getInvoiceAmount());
            // description ："PAM应付发票导入：" + 付款申请编码
            headersTbl.setDescription(paymentInvoiceErpDto.getDescription());
            // attribute3 ：预算项目号
            headersTbl.setAttribute3(paymentInvoiceErpDto.getAttribute3());
            // attribute1 ：PAM
            headersTbl.setAttribute11("PAM");
            // attribute7 凭证类型 ：\付
            headersTbl.setAttribute7(paymentInvoiceErpDto.getAttribute7());

            headersTbls.add(headersTbl);
            logger.debug("添加第三方发票头信息完成，发票号：{}", paymentInvoiceErpDto.getInvoiceNum());

            //发票行
            List<PaymentInvoiceDetailErpDto> detailErpDtos = paymentInvoiceErpDto.getDetailErpDtos();
            int i = 1;
            logger.debug("开始处理第三方发票行信息，发票号：{}，行数：{}", paymentInvoiceErpDto.getInvoiceNum(), detailErpDtos.size());
            for (PaymentInvoiceDetailErpDto detailDto : detailErpDtos) {
                GERPAPINVOICEData.PkgPIfaceLinesTbl line = new GERPAPINVOICEData.PkgPIfaceLinesTbl();
                // source_line_num ：PAM + 付款申请id
                line.setSourceLineNum("PAM" + detailDto.getId());
                // invoice_num ："PAM-" + 付款申请编码
                line.setInvoiceNum(paymentInvoiceErpDto.getInvoiceNum());
                // org_id ：ouId
                line.setOrgId(paymentInvoiceErpDto.getOrgId());
                // line_number ：1
                line.setLineNumber(new BigDecimal(i++));
                // line_type_lookup_code ：ITEM
                line.setLineTypeLookupCode(detailDto.getLineTypeLookUpCode());
                // amount ：本次付款金额(含税)
                line.setAmount(detailDto.getAmount());
                // description ："PAM应付发票导入，" + 付款申请编码
                line.setDescription(detailDto.getDescription());
                lines.add(line);
            }
            logger.debug("处理第三方发票行信息完成，发票号：{}", paymentInvoiceErpDto.getInvoiceNum());
        }
        gerpapinvoiceData.setPkgPIfaceHeadersTbl(headersTbls);
        gerpapinvoiceData.setPkgPIfaceLinesTbl(lines);
        logger.info("第三方发票数据处理完成，发票头数量：{}，发票行数量：{}", headersTbls.size(), lines.size());

        //发起请求
        logger.info("开始调用SDP服务，目标系统：{}，主题：{}", paymentInvoiceThird.getTargetSystemCode(), paymentInvoiceThird.getTopicCode());
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(paymentInvoiceThird.getTargetSystemCode(), paymentInvoiceThird.getTopicCode(), businessId, gerpapinvoiceData);
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            logger.info("ERP第三方付款发票接口调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            logger.error("ERP第三方付款发票接口调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }


    /**
     * PAM-ERP-032 子库存转移写入 (SDP平台实现)
     * 替换EsbServiceImpl#callCUXINVTRANSACTIONSAPIPKGPortTypeMaterialTransfer方法
     */
    @Override
    public EsbResponse callGERPInvTransationMaterialTransfer(List<MaterialSyncErpDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }

        // 接口卡
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.INV_TRANSATION_TRANSFER;
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);

        logger.info("开始调用子库存转移SDP接口，业务流水号：{}，数据量：{}", businessId, dtoList.size());

        Map<String, Object> requestObj = new HashMap<>();
        List<Object> headerList = new ArrayList<>();
        try {
            for (MaterialSyncErpDto dto : dtoList) {
                GERPInvTransationHeader header = new GERPInvTransationHeader();

                // 基础信息 - 完全保持与原ESB方法一致的设值逻辑
                header.setSourceCode("PAM");
                header.setSourceNum("PAM" + new Date().getTime() + "_" + dto.getId());
                header.setEsbSerialNum(businessId);
                header.setEsbDataSource(topicCodeEnum.getInterfaceCode());
                header.setSourceHeaderId(BigDecimal.valueOf(dto.getHeaderId()));

                // 组织信息
                header.setOrganizationId(BigDecimal.valueOf(dto.getOrganizationId()));
                header.setOrganizationCode(dto.getOrganizationCode());

                // 物料信息
                header.setItemNumber(dto.getMaterialCode());
                header.setSubinventory(dto.getInventoryCode());
                header.setTransactionType(dto.getTransactionType());
                header.setTransactionQuantity(dto.getAmount());
                header.setTransactionUom(dto.getUnit());
                header.setTransactionDate(StringUtils.isNotBlank(dto.getTransactionDate()) ? dto.getTransactionDate() : DateUtils.getNow());

                // 账户信息
                header.setAccountAlias(dto.getAliasId());
                header.setLocator(dto.getLocation());

                // 转移信息 - 子库存转移特有字段
                header.setTransferOrganizationId(BigDecimalUtils.longToBigDecimal(dto.getTransferOrganizationId()));
                header.setTransferOrganizationCode(dto.getTransferOrganizationCode());
                header.setTransferSubinventory(dto.getTransferInventory());
                header.setTransferLocator(dto.getTransferLocation());

                // 引用信息
                header.setTransactionReference(dto.getCode());

                headerList.add(header);
            }
            // 将 headerList 列表添加到 requestObj 中
            requestObj.put("pkgPIfaceTbl", headerList);
        } catch (Exception e) {
            logger.error("子库存转移数据转换异常，业务流水号：{}", businessId, e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }

        // 发起SDP请求
        logger.info("开始调用SDP服务，目标系统：{}，主题：{}", targetSystemCode, topicCode);
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, requestObj);
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            logger.info("ERP领料/退料接口调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            logger.error("ERP领料/退料接口调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    /**
     * PAM-ERP-033 领料/退料写入 (SDP平台实现)
     * 替换EsbServiceImpl#callCUXINVTRANSACTIONSAPIPKGPortTypeMaterial方法
     */
    @Override
    public EsbResponse callGERPInvTransationMaterial(List<MaterialSyncErpDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }

        // 接口卡
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.INV_TRANSATION;
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);

        logger.info("开始调用领料/退料SDP接口，业务流水号：{}，数据量：{}", businessId, dtoList.size());

        Map<String, Object> requestObj = new HashMap<>();
        List<Object> headerList = new ArrayList<>();
        try {
            for (MaterialSyncErpDto dto : dtoList) {
                GERPInvTransationHeader header = new GERPInvTransationHeader();

                // 基础信息 - 完全保持与原ESB方法一致的设值逻辑
                header.setSourceCode("PAM");
                header.setSourceNum("PAM" + new Date().getTime() + "_" + dto.getId());
                header.setEsbSerialNum(businessId);
                header.setEsbDataSource(dto.getEsbDataSource()); // PAM-ERP-033-1 or PAM-ERP-033-2

                // 业务字段 - 与原ESB方法完全一致的字段映射
                header.setSourceHeaderId(BigDecimal.valueOf(dto.getHeaderId())); // 头表ID
                header.setOrganizationId(BigDecimal.valueOf(dto.getOrganizationId())); // 库存组织ID
                header.setOrganizationCode(dto.getOrganizationCode()); // 库存组织编码
                header.setItemNumber(dto.getMaterialCode()); // 物料ERP ID
                header.setSubinventory(dto.getInventoryCode()); // 仓库编码
                header.setTransactionType(dto.getTransactionType());
                header.setTransactionQuantity(dto.getAmount()); // 数量
                header.setTransactionUom(dto.getUnit()); // 单位
                header.setTransactionDate(StringUtils.isNotBlank(dto.getTransactionDate()) ? dto.getTransactionDate() : DateUtils.getNow());
                header.setAccountAlias(dto.getAliasId()); // 账户别名ID
                header.setLocator(dto.getLocation());

                // 货架
                header.setAttribute13(dto.getSourceNumber());
                header.setAttribute6(dto.getProjectNumber());

                // 领料单推送erp需要带上工单任务号，退料单暂时不需要 - 与原ESB方法逻辑完全一致
                if (dto.getTicket_task_code() != null) {
                    header.setTransactionReference(dto.getCode() + "_" + dto.getTicket_task_code()); // 单号
                } else {
                    header.setTransactionReference(dto.getCode()); // 单号
                }

                // 科目组合格式需用"."分隔为7段，则"."为6个,不足则用0补充 - 与原ESB方法逻辑完全一致
                String concat = dto.getAliasConcat();
                if (concat.endsWith(".")) {
                    concat = concat.substring(0, concat.length() - 1); // 去除最后一个"."
                }
                int count = StringUtils.countMatches(concat, "."); // 计算"."个数
                for (int i = 1; i <= (6 - count); i++) {
                    concat = concat + ".0";
                }
                header.setAttribute15(concat); // 科目组合

                // 领料单或退料单来源于成本转移单时,这个字段会赋值 - 与原ESB方法逻辑完全一致
                if (!Objects.isNull(dto.getActualCost())) {
                    header.setAttribute14(dto.getActualCost().toPlainString());
                }

                headerList.add(header);
            }
            // 将 headerList 列表添加到 requestObj 中
            requestObj.put("pkgPIfaceTbl", headerList);
        } catch (Exception e) {
            logger.error("领料/退料数据转换异常，业务流水号：{}", businessId, e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }

        // 发起SDP请求
        logger.info("开始调用SDP服务，目标系统：{}，主题：{}", targetSystemCode, topicCode);
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, requestObj);
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            logger.info("ERP领料/退料接口调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            logger.error("ERP领料/退料接口调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }


    private OrganizationRel getOrganizationRel(Long ouId) {
        OrganizationRelQuery query = new OrganizationRelQuery();
        query.setOperatingUnitId(ouId);
        query.setPamEnabled(0);
        PageInfo<OrganizationRelDto> pageInfo = organizationRelExtService.invokeApiList(query);
        List<OrganizationRelDto> list = pageInfo.getList();
        if (ListUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * SDP平台发票取消接口
     * 支持应收发票取消(PAM-ERP-024)和应付发票取消[作废发票场景](PAM-ERP-071)
     *
     * @param dtoList           发票取消数据列表
     * @param businessTypeEnums 业务类型枚举
     * @return EsbResponse 响应结果
     */
    @Override
    public EsbResponse callSdpApCancel(List<PaymentInvoiceErpDto> dtoList, BusinessTypeEnums businessTypeEnums) {
        if (CollectionUtils.isEmpty(dtoList)) {
            logger.warn("SDP发票取消接口调用失败：发票数据列表为空");
            return new EsbResponse();
        }

        logger.info("开始调用SDP发票取消接口，业务类型：{}，发票数量：{}", businessTypeEnums.getCode(), dtoList.size());

        // 根据业务类型确定TopicCodeEnum
        TopicCodeEnum topicCodeEnum;
        if (BusinessTypeEnums.CANCEL_INVOICE.equals(businessTypeEnums)) {
            topicCodeEnum = TopicCodeEnum.CANCEL_INVOICE;
        } else if (BusinessTypeEnums.CANCEL_INVALID_PAYMENT_INVOICE.equals(businessTypeEnums)) {
            topicCodeEnum = TopicCodeEnum.CANCEL_INVALID_PAYMENT_INVOICE;
        } else {
            String errorMsg = "不支持的业务类型：" + businessTypeEnums.getCode();
            logger.error("SDP发票取消接口调用失败：{}", errorMsg);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), errorMsg, null);
        }

        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);
        logger.info("生成业务流水号：{}", businessId);

        // 创建GERPApCanceData对象
        GERPApCanceData gerpApCanceData = new GERPApCanceData();
        gerpApCanceData.setOrgId(dtoList.get(0).getOrgId());

        // 构建pkgPIfaceTbl列表，保持与原有callCUXAPCANCELAPIPKGPortType的设值逻辑一致
        List<GERPApCanceData.PkgPIfaceTbl> pkgPIfaceTblList = new ArrayList<>();
        try {
            for (PaymentInvoiceErpDto dto : dtoList) {
                logger.debug("处理发票取消数据，发票ID：{}", dto.getId());

                GERPApCanceData.PkgPIfaceTbl pkgPIfaceTbl = new GERPApCanceData.PkgPIfaceTbl();
                // 保持与原有EsbServiceImpl#callCUXAPCANCELAPIPKGPortType相同的设值逻辑
                pkgPIfaceTbl.setOrgId(dto.getOrgId());
                pkgPIfaceTbl.setInvoiceId(new BigDecimal(dto.getId()));
                pkgPIfaceTbl.setCancelDate(dto.getCancelDate());
                pkgPIfaceTbl.setSourceCode("PAM");
                pkgPIfaceTbl.setSourceNum("PAM_" + dto.getId());
                pkgPIfaceTbl.setEsbSerialNum(businessId);
                pkgPIfaceTbl.setEsbDataSource(topicCodeEnum.getInterfaceCode());

                pkgPIfaceTblList.add(pkgPIfaceTbl);
                logger.debug("添加发票取消数据完成，发票ID：{}", dto.getId());
            }
        } catch (Exception e) {
            logger.error("SDP发票取消数据处理异常，业务流水号：{}", businessId, e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }

        gerpApCanceData.setPkgPIfaceTbl(pkgPIfaceTblList);
        logger.info("SDP发票取消数据处理完成，发票数量：{}", pkgPIfaceTblList.size());

        // 发起SDP请求
        logger.info("开始调用SDP服务，目标系统：{}，主题：{}", topicCodeEnum.getTargetSystemCode(), topicCodeEnum.getTopicCode());
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(
                topicCodeEnum.getTargetSystemCode(),
                topicCodeEnum.getTopicCode(),
                businessId,
                gerpApCanceData
        );
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));

        // 处理响应结果
        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            logger.info("SDP发票取消接口调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            // 提取responsemessage作为result字段
            String result;
            if (sdpTradeResult.getResponseObj() != null &&
                    sdpTradeResult.getResponseObj().getResponsemessage() != null) {
                result = sdpTradeResult.getResponseObj().getResponsemessage();
            } else {
                // 如果没有responsemessage，则使用完整的响应信息
                String fullResult = JSON.toJSONString(sdpTradeResult);
                result = fullResult.length() > 3000 ? fullResult.substring(0, 3000) : fullResult;
            }
            logger.error("SDP发票取消接口调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result, businessId);
        }
    }

    /**
     * SDP平台付款计划写入接口
     * 对应ESB的callCUXAPPAYMENTPLANAPIPKGService方法，从ESB平台切换到SDP平台
     *
     * @param dtoList 付款计划数据列表
     * @return EsbResponse 响应结果
     */
    @Override
    public EsbResponse callSdpPaymentPlan(List<PaymentPlanErpDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            logger.warn("SDP付款计划写入接口调用失败：付款计划数据列表为空");
            return new EsbResponse();
        }

        logger.info("开始调用SDP付款计划写入接口，付款计划数量：{}", dtoList.size());

        // 使用PAYMENT_PLAN枚举
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.PAYMENT_PLAN;
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);
        logger.info("生成业务流水号：{}", businessId);

        // 创建GERPCuxAPaymentPlanData对象
        GERPCuxAPaymentPlanData gerpCuxAPaymentPlanData = new GERPCuxAPaymentPlanData();
        gerpCuxAPaymentPlanData.setOrgId(dtoList.get(0).getOrgId());

        // 构建三个数据列表，保持与原有callCUXAPPAYMENTPLANAPIPKGService的设值逻辑一致
        List<GERPCuxAPaymentPlanData.PkgPHeaderTbl> pkgPHeaderTblList = new ArrayList<>();
        List<GERPCuxAPaymentPlanData.PkgPLineTbl> pkgPLineTblList = new ArrayList<>();
        List<GERPCuxAPaymentPlanData.PkgPDistTbl> pkgPDistTblList = new ArrayList<>();

        try {
            for (PaymentPlanErpDto dto : dtoList) {
                logger.debug("处理付款计划数据，付款计划编号：{}", dto.getPayPlanNum());

                // 1. 构建付款计划头表数据（对应APPSCUXAPINVOICEPAYPGINTREC）
                GERPCuxAPaymentPlanData.PkgPHeaderTbl pkgPHeaderTbl = new GERPCuxAPaymentPlanData.PkgPHeaderTbl();
                // 保持与原有ESB方法相同的设值逻辑
                pkgPHeaderTbl.setPayPlanNum(dto.getPayPlanNum());
                pkgPHeaderTbl.setOrgId(dto.getOrgId());
                pkgPHeaderTbl.setBatchPaymetFlag("N");
                pkgPHeaderTbl.setSourceCode("PAM");
                pkgPHeaderTbl.setSourceNum(extractLastTwoSegments(businessId) + "_" + dto.getId());
                pkgPHeaderTbl.setEsbSerialNum(businessId);
                pkgPHeaderTbl.setEsbDataSource(topicCodeEnum.getInterfaceCode());

                pkgPHeaderTblList.add(pkgPHeaderTbl);

                // 2. 构建供应商业务字段数据（对应APPSCUXAPINVOICEPPINTREC）
                GERPCuxAPaymentPlanData.PkgPLineTbl pkgPLineTbl = new GERPCuxAPaymentPlanData.PkgPLineTbl();
                // 保持与原有ESB方法相同的设值逻辑
                pkgPLineTbl.setPayPlanNum(dto.getPayPlanNum());
                pkgPLineTbl.setCurrencyCode(dto.getCurrencyCode() != null ? dto.getCurrencyCode() : "CNY");
                pkgPLineTbl.setVendorId(dto.getVendorId());
                pkgPLineTbl.setVendorNum(dto.getVendorNum());
                pkgPLineTbl.setVendorSiteId(dto.getVendorSiteId());
                pkgPLineTbl.setActualPayWay(dto.getActualPayWay());
                pkgPLineTbl.setAccruedLiabilityNoTax(dto.getAccruedLiabilityNoTax());
                pkgPLineTbl.setPayableAccountUnDue(dto.getPayableAccountUnDue());
                pkgPLineTbl.setPayableAccountDue(dto.getPayableAccountDue());
                pkgPLineTbl.setPayableAccountSum(dto.getPayableAccountSum());
                pkgPLineTbl.setPayableBalance(dto.getPayableBalance());
                pkgPLineTbl.setDueAllowedPayable(dto.getDueAllowedPayable());
                pkgPLineTbl.setActualApplyPay(dto.getActualApplyPay());
                pkgPLineTbl.setAdvancePay(dto.getAdvancePay());
                pkgPLineTbl.setPreProjectNum(dto.getPreProjectNum());
                pkgPLineTbl.setDescription(dto.getDescription());
                pkgPLineTbl.setSourceLineNum(extractLastTwoSegments(businessId) + "_" + dto.getId());
                pkgPLineTbl.setBankAccountNo(dto.getBankAccountNo());
                pkgPLineTbl.setBankAccountName(dto.getBankAccountName());
                pkgPLineTbl.setBankName(dto.getBankName());
                pkgPLineTbl.setBankKeyCode(dto.getBankKeyCode());
                // 国际汇款相关字段
                pkgPLineTbl.setTerritoryShortName(dto.getTerritoryCode());
                pkgPLineTbl.setAddressLine(dto.getFullAddress());
                pkgPLineTbl.setPaymentType(dto.getPayType());
                pkgPLineTbl.setPaymentNature(dto.getPayNature());
                pkgPLineTbl.setExchangeCode(dto.getTranCode());
                pkgPLineTbl.setExchangeDesc(dto.getTranMessage());
                pkgPLineTbl.setCommitmentMethod(dto.getExpenseWay());
                pkgPLineTbl.setContractNumber(dto.getContractCode());
                pkgPLineTbl.setInvNumber(dto.getInvoiceCode());
                pkgPLineTbl.setExchangeOfficeCommits(dto.getBusinessRecordCode());
                pkgPLineTbl.setBondedFlag(dto.getFreeTaxGoods());
                pkgPLineTbl.setSwiftcode(dto.getSwiftCode());

                pkgPLineTblList.add(pkgPLineTbl);

                // 3. 处理发票集业务字段（对应APPSCUXAPINVOICEPIINTREC）
                List<PaymentPlanInvoiceErpDto> invoiceErpDtos = dto.getInvoiceErpDtos();
                if (ListUtils.isNotEmpty(invoiceErpDtos)) {
                    for (PaymentPlanInvoiceErpDto invoiceErpDto : invoiceErpDtos) {
                        GERPCuxAPaymentPlanData.PkgPDistTbl pkgPDistTbl = new GERPCuxAPaymentPlanData.PkgPDistTbl();
                        // 保持与原有ESB方法相同的设值逻辑
                        pkgPDistTbl.setPayPlanNum(dto.getPayPlanNum());
                        pkgPDistTbl.setVendorSiteId(dto.getVendorSiteId());
                        pkgPDistTbl.setInvoiceId(invoiceErpDto.getInvoiceId());
                        pkgPDistTbl.setInvoiceNum(invoiceErpDto.getInvoiceNum());
                        pkgPDistTbl.setApplyAmt(invoiceErpDto.getApplyAmt());
                        pkgPDistTbl.setDueDate(invoiceErpDto.getDueDate());

                        pkgPDistTblList.add(pkgPDistTbl);
                    }
                }

                logger.debug("添加付款计划数据完成，付款计划编号：{}", dto.getPayPlanNum());
            }
        } catch (Exception e) {
            logger.error("SDP付款计划数据处理异常，业务流水号：{}", businessId, e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }

        gerpCuxAPaymentPlanData.setPkgPHeaderTbl(pkgPHeaderTblList);
        gerpCuxAPaymentPlanData.setPkgPLineTbl(pkgPLineTblList);
        gerpCuxAPaymentPlanData.setPkgPDistTbl(pkgPDistTblList);
        logger.info("SDP付款计划数据处理完成，头表数量：{}，行表数量：{}，分配表数量：{}",
                pkgPHeaderTblList.size(), pkgPLineTblList.size(), pkgPDistTblList.size());

        // 发起SDP请求
        logger.info("开始调用SDP服务，目标系统：{}，主题：{}", topicCodeEnum.getTargetSystemCode(), topicCodeEnum.getTopicCode());
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(
                topicCodeEnum.getTargetSystemCode(),
                topicCodeEnum.getTopicCode(),
                businessId,
                gerpCuxAPaymentPlanData
        );
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));

        // 处理响应结果
        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            logger.info("SDP付款计划写入接口调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            // 提取responsemessage作为result字段
            String result;
            if (sdpTradeResult.getResponseObj() != null &&
                    sdpTradeResult.getResponseObj().getResponsemessage() != null) {
                result = sdpTradeResult.getResponseObj().getResponsemessage();
            } else {
                // 如果没有responsemessage，则使用完整的响应信息
                String fullResult = JSON.toJSONString(sdpTradeResult);
                result = fullResult.length() > 3000 ? fullResult.substring(0, 3000) : fullResult;
            }
            logger.error("SDP付款计划写入接口调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result, businessId);
        }
    }

    /**
     * SDP平台预付款核销接口
     * 对应ESB的callAPPSCUXAPPREPAYService方法，从ESB平台切换到SDP平台
     *
     * @param dtoList      预付款核销数据列表
     * @param businessType 业务类型
     * @return EsbResponse 响应结果
     */
    @Override
    public EsbResponse callSdpPrepay(List<PrepayErpDto> dtoList, String businessType) {
        if (CollectionUtils.isEmpty(dtoList)) {
            logger.warn("SDP预付款核销接口调用失败：预付款数据列表为空");
            return new EsbResponse();
        }

        logger.info("开始调用SDP预付款核销接口，业务类型：{}，预付款数量：{}", businessType, dtoList.size());

        // 根据业务类型确定TopicCodeEnum
        TopicCodeEnum topicCodeEnum;
        if (BusinessTypeEnums.PREPAY.getCode().equals(businessType)) {
            topicCodeEnum = TopicCodeEnum.PREPAY;
        } else if (BusinessTypeEnums.CANCEL_PREPAY.getCode().equals(businessType)) {
            topicCodeEnum = TopicCodeEnum.CANCEL_PREPAY;
        } else {
            String errorMsg = "不支持的业务类型：" + businessType;
            logger.error("SDP预付款核销接口调用失败：{}", errorMsg);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), errorMsg, null);
        }
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);
        logger.info("生成业务流水号：{}", businessId);

        // 创建GERPAPPREPAY对象
        GERPAPPREPAY gerpApprepay = new GERPAPPREPAY();
        gerpApprepay.setOrgId(dtoList.get(0).getOrgId());

        // 构建预付款核销数据列表，保持与原有callAPPSCUXAPPREPAYService的设值逻辑一致
        List<GERPAPPREPAY.PkgPHeaderTbl> pkgPHeaderTblList = new ArrayList<>();

        try {
            for (PrepayErpDto dto : dtoList) {
                logger.debug("处理预付款核销数据，预付款ID：{}", dto.getId());

                GERPAPPREPAY.PkgPHeaderTbl pkgPHeaderTbl = new GERPAPPREPAY.PkgPHeaderTbl();
                // 保持与原有ESB方法相同的设值逻辑
                pkgPHeaderTbl.setOrgId(dto.getOrgId());
                pkgPHeaderTbl.setApplyDate(dto.getApplyDate());
                pkgPHeaderTbl.setPrepayInvoiceId(dto.getPrepayInvoiceId());
                pkgPHeaderTbl.setAmount(dto.getAmount());
                pkgPHeaderTbl.setInvoiceId(dto.getInvoiceId());
                pkgPHeaderTbl.setCurrencyCode(dto.getCurrencyCode() != null ? dto.getCurrencyCode() : "CNY");
                pkgPHeaderTbl.setSourceCode("PAM");
                pkgPHeaderTbl.setSourceNum(extractLastTwoSegments(businessId) + "_" + dto.getId());
                pkgPHeaderTbl.setEsbSerialNum(businessId);
                pkgPHeaderTbl.setEsbDataSource(businessType);

                pkgPHeaderTblList.add(pkgPHeaderTbl);
                logger.debug("添加预付款核销数据完成，预付款ID：{}", dto.getId());
            }
        } catch (Exception e) {
            logger.error("SDP预付款核销数据处理异常，业务流水号：{}", businessId, e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }

        gerpApprepay.setPkgPHeaderTbl(pkgPHeaderTblList);
        logger.info("SDP预付款核销数据处理完成，预付款数量：{}", pkgPHeaderTblList.size());

        // 发起SDP请求
        logger.info("开始调用SDP服务，目标系统：{}，主题：{}", topicCodeEnum.getTargetSystemCode(), topicCodeEnum.getTopicCode());
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(
                topicCodeEnum.getTargetSystemCode(),
                topicCodeEnum.getTopicCode(),
                businessId,
                gerpApprepay
        );
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));

        // 处理响应结果
        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            logger.info("SDP预付款核销接口调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            // 提取responsemessage作为result字段
            String result;
            if (sdpTradeResult.getResponseObj() != null &&
                    sdpTradeResult.getResponseObj().getResponsemessage() != null) {
                result = sdpTradeResult.getResponseObj().getResponsemessage();
            } else {
                // 如果没有responsemessage，则使用完整的响应信息
                String fullResult = JSON.toJSONString(sdpTradeResult);
                result = fullResult.length() > 3000 ? fullResult.substring(0, 3000) : fullResult;
            }
            logger.error("SDP预付款核销接口调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result, businessId);
        }
    }


    @Override
    public EsbResponse callPurchaseOrderPushFap(List<PurchaseOrderDto> purchaseOrderDtoList) {

        TopicCodeEnum fapOrderCreateInterface = TopicCodeEnum.FAP_ORDER_CREATE_INTERFACE;
        // 生成流水号
        String businessId = generateSequence(fapOrderCreateInterface);
        logger.info("开始处理采购订单推送FAP，业务流水号：{}", businessId);

        ArrayList<PurchaseOrderPushFAPDto> purchaseOrderPushFAPDtos = new ArrayList<>();

        for (PurchaseOrderDto purchaseOrderDto : purchaseOrderDtoList) {

            PurchaseOrderPushFAPDto purchaseOrderPushFAPDto = new PurchaseOrderPushFAPDto();

            purchaseOrderPushFAPDto.setSerialNumber(businessId);

            purchaseOrderPushFAPDto.setSourceId(purchaseOrderDto.getId());

            purchaseOrderPushFAPDto.setSourceSysCode("PAM");

            purchaseOrderPushFAPDto.setSourceSysBusCode(purchaseOrderDto.getNum());

            purchaseOrderPushFAPDto.setSourceSysBusId(purchaseOrderDto.getId());

            purchaseOrderPushFAPDto.setInterfaceCode(fapOrderCreateInterface.getInterfaceCode());

            purchaseOrderPushFAPDto.setSceneCode("A411");

            purchaseOrderPushFAPDto.setOrderStatus("CONFIRM");

            purchaseOrderPushFAPDto.setSuOrgOriginCode("GERP");


            //供方组织信息
            String vendorNum = purchaseOrderDto.getVendorNum();
            //判断vendorNum是否E开头(内部供应商),不是内部供应商不能推送到FAP
            if (!vendorNum.startsWith("E")) {
                logger.error("采购订单推送FAP失败，供应商编码不是内部供应商，推送失败，OrderNum:{},vendorNum:{}", purchaseOrderDto.getNum(), vendorNum);
                throw new BizException(Code.ERROR, "供应商编码不是内部供应商，推送失败");
            }
            //获取vendorNum后六位
            String vendorNumLastSix = vendorNum.substring(vendorNum.length() - 6);
            List<OrganizationRelDto> suInvOrganizationRelList = basedataExtService.getOrganizationRelByCompanyCode(Lists.newArrayList(vendorNumLastSix));
            if (ListUtils.isEmpty(suInvOrganizationRelList)) {
                logger.error("采购订单推送FAP失败，供应商编码对应的ERP组织关系不存在，推送失败，OrderNum:{},vendorNum:{}", purchaseOrderDto.getNum(), vendorNum);
                throw new BizException(Code.ERROR, "供应商编码对应的ERP组织关系不存在，推送失败");
            }

            purchaseOrderPushFAPDto.setSuInvOrgCode(suInvOrganizationRelList.get(0).getOrganizationCode());

            purchaseOrderPushFAPDto.setReOrgOriginCode("GERP");

            //需方组织信息
            Long ouId = purchaseOrderDto.getOuId();
            logger.debug("处理采购订单推送FAP，获取OU信息，ouId：{}", ouId);
            List<OrganizationRel> reInvOrganizationRels = basedataExtService.queryByOuId(ouId);
            if (ListUtils.isEmpty(reInvOrganizationRels)) {
                logger.error("采购订单推送FAP失败，OU对应的ERP组织关系不存在，ouId:{}", ouId);
                throw new BizException(Code.ERROR, "OU对应的ERP组织关系不存在");
            }
            purchaseOrderPushFAPDto.setReInvOrgCode(reInvOrganizationRels.get(0).getOrganizationCode());

            purchaseOrderPushFAPDto.setReturnFlag(0);

            purchaseOrderPushFAPDto.setNoLogistReturnFlag(0);

            purchaseOrderPushFAPDto.setReversalFlag(0);

            purchaseOrderPushFAPDto.setSaleType("GOODS");

            Long createBy = purchaseOrderDto.getCreateBy();
            logger.debug("处理采购订单推送FAP，获取创建人信息，createBy：{}", createBy);
            UserInfo createUserInfo = CacheDataUtils.findUserById(createBy);
            if (Objects.isNull(createUserInfo)) {
                logger.error("采购订单推送FAP失败，创建人不存在，createBy:{}", createBy);
                throw new BizException(Code.ERROR, "创建人不存在");
            }
            purchaseOrderPushFAPDto.setCreatedBy(createUserInfo.getUsername());

            if (Objects.isNull(purchaseOrderDto.getCreateAt())) {
                logger.error("采购订单推送FAP失败，创建时间不存在，createAt:{}", purchaseOrderDto.getCreateAt());
                throw new BizException(Code.ERROR, "创建时间不存在");
            }
            //FAP方建议不传
//            purchaseOrderPushFAPDto.setCreationDate(DateUtils.format(purchaseOrderDto.getCreateAt(), DateUtils.FORMAT_LONG));

            purchaseOrderPushFAPDto.setLastUpdatedBy(createUserInfo.getUsername());
            //FAP方建议不传
//            purchaseOrderPushFAPDto.setLastUpdateDate(DateUtils.format(purchaseOrderDto.getCreateAt(), DateUtils.FORMAT_LONG));

            purchaseOrderPushFAPDto.setSourceSysBusType("DOMESTIC");

            if (purchaseOrderDto.getOuId() != null) {
                //按照采购订单所属OU查询采购订单类型.  组织参数配置值：格式：XX:XXX，例如：CB01:订单类型1.  仅获取“：”前面的数据，例如：CB01
                Set<String> valueSet = organizationCustomDictService.queryByName("采购订单类型", purchaseOrderDto.getOuId(), OrgCustomDictOrgFrom.OU);
                if (!CollectionUtils.isEmpty(valueSet) && valueSet.size() == 1) {
                    String next = valueSet.iterator().next();
                    if (com.midea.pam.common.util.StringUtils.isNotEmpty(next) || next.split(":").length == 2) {
                        purchaseOrderPushFAPDto.setPurchaseOrderType(next.split(":")[0]);
                    }
                }
            }


            purchaseOrderPushFAPDto.setCurrencyCode(purchaseOrderDto.getCurrencyCode());

            purchaseOrderPushFAPDto.setConversionType(purchaseOrderDto.getConversionType());

            purchaseOrderPushFAPDto.setConversionDate(DateUtils.format(purchaseOrderDto.getConversionDate(), DateUtils.FORMAT_LONG));

            List<PurchaseOrderDetailDto> purchaseOrderDetailDtos = purchaseOrderDto.getPurchaseOrderDetailDtos();
            logger.debug("处理采购订单推送FAP，开始处理订单明细，明细数量：{}", purchaseOrderDetailDtos != null ? purchaseOrderDetailDtos.size() : 0);

            if (ListUtils.isEmpty(purchaseOrderDetailDtos)) {
                logger.error("采购订单推送FAP失败，采购订单明细不存在，purchaseOrderId:{}", purchaseOrderDto.getId());
                throw new BizException(Code.ERROR, "采购订单明细不存在");
            }

            ArrayList<PurchaseOrderPushFAPLineDto> lineDtos = new ArrayList<>();
            Project project = projectService.selectByPrimaryKey(purchaseOrderDto.getProjectId());


            for (PurchaseOrderDetailDto purchaseOrderDetailDto : purchaseOrderDetailDtos) {
                logger.debug("处理采购订单推送FAP，处理订单明细，明细ID：{}", purchaseOrderDetailDto.getId());
                PurchaseOrderPushFAPLineDto lineDto = new PurchaseOrderPushFAPLineDto();
                lineDto.setReItemCode(purchaseOrderDetailDto.getErpCode());
//                lineDto.setReItemUom(purchaseOrderDetailDto.getUnitCode());
                if (Objects.nonNull(purchaseOrderDetailDto.getUnitPrice())) {
                    lineDto.setNoTaxUnitPrice(purchaseOrderDetailDto.getUnitPrice());
                }

                lineDto.setSourceSysBusLineSourceLineId(purchaseOrderDto.getId().toString());

                lineDto.setReceiveSubinvCode(purchaseOrderDto.getSecondaryInventoryName());

//                lineDto.setProjectCode(project.getCode());

                Project orderDetailProject = projectService.selectByPrimaryKey(purchaseOrderDetailDto.getProjectId());

                if (Objects.nonNull(orderDetailProject)) {
                    lineDto.setProjectCode(orderDetailProject.getCode());
                    //查询货位
                    String organizationCode = reInvOrganizationRels.get(0).getOrganizationCode();
                    String secondaryInventoryName = purchaseOrderDto.getSecondaryInventoryName();
                    String projectCode = orderDetailProject.getCode();
                    InventoryQuery inventoryQuery = new InventoryQuery();
                    inventoryQuery.setOrganizationCode(organizationCode);
                    inventoryQuery.setSecondaryInventoryName(secondaryInventoryName);
                    inventoryQuery.setLocator(projectCode);
                    List<InventoryDto> inventoryDtos = basedataExtService.selectInventoryList(inventoryQuery);
                    if (ListUtils.isNotEmpty(inventoryDtos)) {
                        //receiveLocationCode
                        logger.info("处理采购订单推送FAP，获取货位信息，货位ID：{},货位编码：{}", inventoryDtos.get(0).getId(), inventoryDtos.get(0).getLocator());
                        lineDto.setReceiveLocationCode(inventoryDtos.get(0).getLocator());
                    }
                }

                lineDto.setHeadId(purchaseOrderDto.getId());

                lineDto.setSourceSysBusId(purchaseOrderDto.getId().toString());

                lineDto.setSourceSysBusLineId(purchaseOrderDetailDto.getId().toString());

                lineDto.setSourceSysBusLineNum(purchaseOrderDetailDto.getLineNumber());

                lineDto.setSourceSysBusCode(purchaseOrderDto.getNum());

                lineDto.setRequestQuantity(purchaseOrderDetailDto.getOrderNum());


                Long materielId = purchaseOrderDetailDto.getMaterielId();
//                if(Objects.nonNull(materielId)){
//                    List<MaterialDto> materialDtoList = basedataExtService.getMaterialListByMaterialIds(Lists.newArrayList(materielId));
//                    if(ListUtils.isNotEmpty(materialDtoList)){
//                        MaterialDto materialDto = materialDtoList.get(0);
//                        String itemCode = materialDto.getItemCode();
//                        lineDto.setSuItemCode(itemCode);
//                    }else{
//                        logger.error("采购订单推送FAP失败，物料不存在，materielId:{}", materielId);
//                        throw new BizException(Code.ERROR, "物料不存在");
//                    }
//                }

                lineDtos.add(lineDto);
            }

            purchaseOrderPushFAPDto.setLines(lineDtos);
            purchaseOrderPushFAPDtos.add(purchaseOrderPushFAPDto);
        }

        logger.info("采购订单推送FAP处理完成，业务流水号：{}", businessId);

        //发起请求
        logger.info("开始调用SDP服务，目标系统：{}，主题：{}", fapOrderCreateInterface.getTargetSystemCode(), fapOrderCreateInterface.getTopicCode());
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(fapOrderCreateInterface.getTargetSystemCode(), fapOrderCreateInterface.getTopicCode(), businessId, purchaseOrderPushFAPDtos);
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getCode(), "00000")) {
            logger.info("处理采购订单推送FAP成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            logger.error("处理采购订单推送FAP调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    /**
     * PAM-ERP-034 采购订单写入 (使用GERPPoStandardPoData数据结构)
     * 参照EsbServiceImpl#callCUXPOSTANDARDPOAPIPKGPortType的原逻辑，适配SDP平台调用方式
     */
    @Override
    public EsbResponse callGERPPoStandardPoWithData(List<PurchaseOrderDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return new EsbResponse();
        }

        // 接口卡
        String interfaceCode = dtos.get(0).getEsbDataSource();

        TopicCodeEnum topicCodeEnum = null;

        if ("PAM-ERP-034".equals(interfaceCode)) {
            topicCodeEnum = TopicCodeEnum.PURCHASE_ORDER_STANDARD;
        } else if ("PAM-ERP-034-1".equals(interfaceCode)) {
            topicCodeEnum = TopicCodeEnum.PURCHASE_ORDER_CHANGE_STANDARD;
        }

        Guard.notNull(topicCodeEnum, "该接口卡尚未接入SDP：" + interfaceCode);

        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);

        GERPPoStandardPoData gerpPoStandardPoData;
        try {
            List<GERPPoStandardPoData.PkgPIfaceHeadersTbl> headersTbls = new ArrayList<>();
            List<GERPPoStandardPoData.PkgPIfaceLinesTbl> linesTbls = new ArrayList<>();
            BigDecimal batchId = new BigDecimal(DateUtils.getCurrentDate().getTime());

            for (PurchaseOrderDto dto : dtos) {
                // 构建采购订单头数据
                GERPPoStandardPoData.PkgPIfaceHeadersTbl header = GERPPoStandardPoData.PkgPIfaceHeadersTbl.builder()
                        .batchId(batchId)
                        .esbDataSource(dto.getEsbDataSource())
                        .sourceCode("PAM")
                        .sourceNum(businessId.substring(0, businessId.lastIndexOf("-", businessId.lastIndexOf("-") - 1)) + "_" + dto.getId())
                        .esbSerialNum(businessId)
                        .vendorNum(dto.getVendorNum())
                        .vendorName(dto.getVendorName())
                        .vendorSiteCode(dto.getVendorSiteCode())
                        .agentId(new BigDecimal(dto.getErpBuyerId()))
                        .currencyCode(Optional.ofNullable(dto.getCurrency()).orElse("CNY"))
                        .rate(Optional.ofNullable(dto.getConversionRate()).orElse(BigDecimal.ONE))
                        .segment1(dto.getNum())
                        .attribute1(dto.getOrderType())
                        .attribute2("BPA")
                        .build();

                // 设置组织ID
                if (dto.getOuId() != null) {
                    header.setOrgId(new BigDecimal(dto.getOuId()));
                }

                // 设置头接口ID
                if (dto.getNum() != null && StringUtils.isNotEmpty(dto.getNum().replace(CodePrefix.PURCHASE_ORDER_1.code(), "")
                        .replace(CodePrefix.PURCHASE_ORDER_3.code(), ""))) {
                    header.setHeaderInterfaceId(new BigDecimal(dto.getNum().replace(CodePrefix.PURCHASE_ORDER_1.code(), "")
                            .replace(CodePrefix.PURCHASE_ORDER_3.code(), "")));
                }

                // 设置汇率日期
                if (dto.getConversionDate() != null) {
                    header.setRateDate(DateUtils.format(dto.getConversionDate(), DateUtils.FORMAT_SHORT));
                } else {
                    header.setRateDate(DateUtils.format(new Date(), DateUtils.FORMAT_SHORT));
                }

                // 设置汇率类型
                String type = dto.getConversionType();
                if (type != null) {
                    if (Objects.equals(CorUserStatus.Corporate.getCode(), type)) {
                        header.setRateType(CorUserStatus.Corporate.getName());
                    } else if (Objects.equals(CorUserStatus.User.getCode(), type)) {
                        header.setRateType(CorUserStatus.User.getName());
                    } else {
                        header.setRateType(type);
                    }
                }

                // 设置是否急件
                header.setAttribute13("N");
                if (dto.getDispatchIs() != null && dto.getDispatchIs()) {
                    header.setAttribute13("Y");
                }

                // 设置备注
                header.setComments("#");
                if (dto.getRemark() != null) {
                    header.setComments("#" + dto.getRemark());
                }

                // 处理定价类型
                if (dto.getPricingType() != null) {
                    // 一价一单, 一揽子协议
                    header.setAttribute1("DJ01");
                    header.setAttribute2("PO");
                }

                headersTbls.add(header);

                // 构建采购订单行数据
                for (PurchaseOrderDetailDto orderDetailDto : dto.getPurchaseOrderDetailDtos()) {
                    GERPPoStandardPoData.PkgPIfaceLinesTbl line = GERPPoStandardPoData.PkgPIfaceLinesTbl.builder()
                            .item(orderDetailDto.getErpCode())
                            .quantity(orderDetailDto.getOrderNum())
                            .uomCode(orderDetailDto.getUnitCode())
                            .needByDate(DateUtils.format(orderDetailDto.getDeliveryTime(), DateUtils.FORMAT_SHORT))
                            .unitPrice(Optional.ofNullable(orderDetailDto.getDiscountPrice()).orElse(BigDecimal.ZERO))
                            .lineAttribute1("0")
                            .lineAttribute5(orderDetailDto.getSecondaryInventoryName())
                            .lineAttribute6(orderDetailDto.getProjectNum())
                            .lineAttribute7(this.line_attribute7(orderDetailDto))
                            .build();

                    // 设置头接口ID
                    if (dto.getNum() != null && StringUtils.isNotEmpty(dto.getNum().replace(CodePrefix.PURCHASE_ORDER_1.code(), "")
                            .replace(CodePrefix.PURCHASE_ORDER_3.code(), ""))) {
                        line.setHeaderInterfaceId(new BigDecimal(dto.getNum().replace(CodePrefix.PURCHASE_ORDER_1.code(), "")
                                .replace(CodePrefix.PURCHASE_ORDER_3.code(), "")));
                    }

                    // 设置行号
                    if (orderDetailDto.getLineNumber() != null) {
                        line.setLineNum(new BigDecimal(orderDetailDto.getLineNumber()));
                    } else {
                        line.setLineNum(BigDecimal.ONE);
                    }

                    // 设置WBS相关信息
                    if (orderDetailDto.getWbsSummaryCode() != null) {
                        line.setLineAttribute8(orderDetailDto.getWbsSummaryCode());
                    }

                    // 设置收货组织ID
                    if (orderDetailDto.getOrganizationId() != null) {
                        line.setShipToOrganizationId(new BigDecimal(orderDetailDto.getOrganizationId()));
                    }

                    linesTbls.add(line);
                }
            }

            gerpPoStandardPoData = GERPPoStandardPoData.builder()
                    .pkgPIfaceHeadersTbl(headersTbls)
                    .pkgPIfaceLinesTbl(linesTbls)
                    .build();

        } catch (Exception e) {
            logger.error("构建采购订单数据失败", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }

        // 发起SDP请求
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, gerpPoStandardPoData);

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }


    /**
     * PAM-ERP-073 应付付款（预付款付款,迪链票据接口卡）- SDP平台版本
     */
    @Override
    public EsbResponse callGERPApPayments(List<PaymentApplyDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }

        // 获取主题枚举
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.APPAYMENTS_PAYMENT_APPLY;
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);

        logger.info("开始处理应付付款数据，业务流水号：{}，数据量：{}", businessId, dtoList.size());

        // 构建SDP数据结构
        GERPApPaymentsData gerpApPaymentsData = new GERPApPaymentsData();

        // 设置组织ID和语言（从第一条记录获取）
        PaymentApplyDto firstDto = dtoList.get(0);
        gerpApPaymentsData.setOrgId(new BigDecimal(firstDto.getOuId()));

        // 构建头表数据
        List<GERPApPaymentsData.PkgPHeaderTbl> headerList = new ArrayList<>();
        // 构建行表数据
        List<GERPApPaymentsData.PkgPLineTbl> lineList = new ArrayList<>();

        for (PaymentApplyDto paymentApplyDto : dtoList) {
            logger.debug("处理付款申请数据，付款申请编号：{}", paymentApplyDto.getPaymentApplyCode());

            // 构建头信息
            GERPApPaymentsData.PkgPHeaderTbl header = GERPApPaymentsData.PkgPHeaderTbl.builder()
                    // OU组织ID
                    .orgId(new BigDecimal(paymentApplyDto.getOuId()))
                    // 组织代码
                    .orgCode(null) // ESB中未设置，保持null
                    // 付款申请编号
                    .fsCheckNumber(String.valueOf(paymentApplyDto.getPaymentApplyCode()))
                    // GTMS支票号
                    .gtmsCheckNumber(null) // ESB中未设置，保持null
                    // 付款类型标志：Q
                    .paymentTypeFlag("Q")
                    // ERP供应商ID
                    .vendorId(new BigDecimal(paymentApplyDto.getVendorErpId()))
                    // 供应商代码
                    .vendorCode(null) // ESB中未设置，保持null
                    // ERP供应商地址ID
                    .vendorSiteId(new BigDecimal(paymentApplyDto.getErpVendorSiteId()))
                    // 供应商地址代码
                    .vendorSiteCode(null) // ESB中未设置，保持null
                    // 审批通过日期
                    .checkDate(DateUtils.formatDate(paymentApplyDto.getAuditDate()))
                    // 银行账号ID
                    .bankAccountId(new BigDecimal(paymentApplyDto.getBankAccountId()))
                    // 银行账号名称
                    .bankAccountName(paymentApplyDto.getBankAccountName())
                    // 付款方法代码
                    .paymentMethodLookupCode(paymentApplyDto.getPaymentMethodCode())
                    // 币种
                    .currencyCode(paymentApplyDto.getCurrency())
                    // 用户转换类型
                    .userConversionType(null) // ESB中未设置，保持null
                    // 汇率
                    .exchangeRate(null) // ESB中未设置，保持null
                    // 汇率日期
                    .exchangeDate(null) // ESB中未设置，保持null
                    // 金额
                    .amount(paymentApplyDto.getTaxPayIncludedPrice())
                    // 付款配置代码
                    .paymentProfileCode(null) // ESB中未设置，保持null
                    // 付款文档名称
                    .paymentDocumentName(null) // ESB中未设置，保持null
                    // 预期价值日期
                    .anticipatedValueDate(null) // ESB中未设置，保持null
                    // 交易关联号
                    .transRelateNum(null) // ESB中未设置，保持null
                    // 初始状态
                    .initStatus(null) // ESB中未设置，保持null
                    // 备注
                    .remark(paymentApplyDto.getRemark())
                    // 属性类别
                    .attributeCategory(null) // ESB中未设置，保持null
                    // 扩展属性字段
                    .attribute2(null)
                    .attribute3(null)
                    .attribute4(null)
                    .attribute5(null)
                    .attribute6(null)
                    .attribute7(null)
                    .attribute8(null)
                    .attribute9(null)
                    .attribute10(null)
                    .attribute12(null)
                    .attribute13(null)
                    .attribute14(null)
                    .attribute15(null)
                    // 来源代码
                    .sourceCode("PAM")
                    // 来源编号
                    .sourceNum(extractLastTwoSegments(businessId) + "_" + paymentApplyDto.getId())
                    // ESB流水号
                    .esbSerialNum(businessId)
                    // ESB数据源：PAM-ERP-073
                    .esbDataSource(BusinessTypeEnums.APPAYMENTS_PAYMENT_APPLY.getCode())
                    .build();

            headerList.add(header);

            // 构建行信息
            GERPApPaymentsData.PkgPLineTbl line = GERPApPaymentsData.PkgPLineTbl.builder()
                    // 付款申请编号
                    .fsCheckNumber(paymentApplyDto.getPaymentApplyCode())
                    // 发票ID
                    .invoiceId(null) // ESB中未设置，保持null
                    // 发票号："PAM-" + 付款申请编号
                    .invoiceNum("PAM-" + paymentApplyDto.getPaymentApplyCode())
                    // 付款编号：1
                    .paymentNum(BigDecimal.valueOf(1))
                    // 本次付款金额(含税)
                    .amount(paymentApplyDto.getTaxPayIncludedPrice())
                    // 审批通过日期
                    .glDate(DateUtils.formatDate(paymentApplyDto.getAuditDate()))
                    // 来源行编号：1
                    .sourceLineNum("1")
                    // 属性类别
                    .attributeCategory(null) // ESB中未设置，保持null
                    // 付款申请编号
                    .attribute1(paymentApplyDto.getPaymentApplyCode())
                    // 其他扩展属性字段
                    .attribute2(null)
                    .attribute3(null)
                    .attribute4(null)
                    .attribute5(null)
                    .attribute6(null)
                    .attribute7(null)
                    .attribute8(null)
                    .attribute9(null)
                    .attribute10(null)
                    .attribute11(null)
                    .attribute12(null)
                    .attribute13(null)
                    .attribute14(null)
                    .attribute15(null)
                    .build();

            lineList.add(line);
            logger.debug("处理付款申请数据完成，付款申请编号：{}", paymentApplyDto.getPaymentApplyCode());
        }

        gerpApPaymentsData.setPkgPHeaderTbl(headerList);
        gerpApPaymentsData.setPkgPLineTbl(lineList);
        logger.info("应付付款数据处理完成，业务流水号：{}，头数量：{}，行数量：{}", businessId, headerList.size(), lineList.size());

        //发起请求
        logger.info("开始调用SDP服务，目标系统：{}，主题：{}", targetSystemCode, topicCode);
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, gerpApPaymentsData);
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            logger.info("应付付款接口调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            logger.error("应付付款接口调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }

    /**
     * PAM-ERP-073-1 正负发票核销 - SDP平台版本
     */
    @Override
    public EsbResponse callGERPPositiveAndNegativeRecord(List<PositiveAndNegativeInvoiceRecordDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }

        // 获取主题枚举
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.POSITIVE_AND_NEGATIVE_RECORD;
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);

        logger.info("开始处理正负发票核销数据，业务流水号：{}，数据量：{}", businessId, dtoList.size());

        // 构建SDP数据结构
        GERPApPaymentsData gerpApPaymentsData = new GERPApPaymentsData();

        // 设置组织ID和语言（从第一条记录获取）
        PositiveAndNegativeInvoiceRecordDto firstDto = dtoList.get(0);
        gerpApPaymentsData.setOrgId(new BigDecimal(firstDto.getOrgId()));

        // 构建头表数据
        List<GERPApPaymentsData.PkgPHeaderTbl> headerList = new ArrayList<>();
        // 构建行表数据
        List<GERPApPaymentsData.PkgPLineTbl> lineList = new ArrayList<>();

        for (PositiveAndNegativeInvoiceRecordDto recordDto : dtoList) {
            logger.debug("处理正负发票核销数据，核销关系ID：{}", recordDto.getApplyNo());

            // 构建头信息
            GERPApPaymentsData.PkgPHeaderTbl header = GERPApPaymentsData.PkgPHeaderTbl.builder()
                    // OU组织ID
                    .orgId(new BigDecimal(recordDto.getOrgId()))
                    // 组织代码
                    .orgCode(null) // ESB中未设置，保持null
                    // 付款申请编号
                    .fsCheckNumber(recordDto.getApplyNo())
                    // GTMS支票号
                    .gtmsCheckNumber(null) // ESB中未设置，保持null
                    // 付款类型标志
                    .paymentTypeFlag(recordDto.getTypeFlag())
                    // ERP供应商ID
                    .vendorId(new BigDecimal(recordDto.getVendorId()))
                    // 供应商代码
                    .vendorCode(null) // ESB中未设置，保持null
                    // ERP供应商地址ID
                    .vendorSiteId(new BigDecimal(recordDto.getVendorSitId()))
                    // 供应商地址代码
                    .vendorSiteCode(null) // ESB中未设置，保持null
                    // 审批通过日期
                    .checkDate(recordDto.getCheckDate())
                    // 银行账号ID
                    .bankAccountId(new BigDecimal(recordDto.getBankAccountId()))
                    // 银行账号名称
                    .bankAccountName(recordDto.getBankAccountName())
                    // 付款方法代码
                    .paymentMethodLookupCode(recordDto.getPaymentMethodCode())
                    // 币种
                    .currencyCode(recordDto.getCurrencyCode())
                    // 用户转换类型
                    .userConversionType(recordDto.getConversionType())
                    // 汇率日期
                    .exchangeDate(recordDto.getExchangeDate())
                    // 金额
                    .amount(new BigDecimal(recordDto.getAmount()))
                    // 付款配置代码
                    .paymentProfileCode(null) // ESB中未设置，保持null
                    // 付款文档名称
                    .paymentDocumentName(null) // ESB中未设置，保持null
                    // 预期价值日期
                    .anticipatedValueDate(null) // ESB中未设置，保持null
                    // 交易关联号
                    .transRelateNum(null) // ESB中未设置，保持null
                    // 初始状态
                    .initStatus(null) // ESB中未设置，保持null
                    // 备注
                    .remark(recordDto.getRemark())
                    // 属性类别
                    .attributeCategory(null) // ESB中未设置，保持null
                    // 扩展属性字段
                    .attribute2(null)
                    .attribute3(null)
                    .attribute4(null)
                    .attribute5(null)
                    .attribute6(null)
                    .attribute7(null)
                    .attribute8(null)
                    .attribute9(null)
                    .attribute10(null)
                    .attribute12(null)
                    .attribute13(null)
                    .attribute14(null)
                    .attribute15(null)
                    // 来源代码
                    .sourceCode(recordDto.getSourceCode())
                    // 来源编号
                    .sourceNum(extractLastTwoSegments(businessId) + "_" + recordDto.getSourceNum())
                    // ESB流水号
                    .esbSerialNum(businessId)
                    // ESB数据源：PAM-ERP-073-1
                    .esbDataSource(BusinessTypeEnums.POSITIVE_AND_NEGATIVE_RECORD.getCode())
                    .build();

            // 汇率
            if (StringUtils.isNotBlank(recordDto.getExchangeRate())) {
                header.setExchangeRate(new BigDecimal(recordDto.getExchangeRate()));
            }

            headerList.add(header);

            // 处理行信息
            List<PositiveAndNegativeInvoiceRecordDetails> lins = recordDto.getLins();
            if (CollectionUtils.isNotEmpty(lins)) {
                for (PositiveAndNegativeInvoiceRecordDetails lin : lins) {
                    logger.debug("处理正负发票核销行数据，发票编号：{}", lin.getInvoiceNum());

                    // 构建行信息
                    GERPApPaymentsData.PkgPLineTbl line = GERPApPaymentsData.PkgPLineTbl.builder()
                            // 付款申请编号
                            .fsCheckNumber(lin.getFsCheckNum())
                            // 发票号
                            .invoiceNum(lin.getInvoiceNum())
                            // 付款编号
                            .paymentNum(new BigDecimal(lin.getPaymentNum()))
                            // 发票付款金额
                            .amount(new BigDecimal(lin.getAmount()))
                            // 总账日期
                            .glDate(lin.getGlDate())
                            // 来源行编号
                            .sourceLineNum(lin.getLineNum())
                            // 属性类别
                            .attributeCategory(null) // ESB中未设置，保持null
                            // 来源系统付款申请单号
                            .attribute1(lin.getPaymentApplyId())
                            // 其他扩展属性字段
                            .attribute2(null)
                            .attribute3(null)
                            .attribute4(null)
                            .attribute5(null)
                            .attribute6(null)
                            .attribute7(null)
                            .attribute8(null)
                            .attribute9(null)
                            .attribute10(null)
                            .attribute11(null)
                            .attribute12(null)
                            .attribute13(null)
                            .attribute14(null)
                            .attribute15(null)
                            .build();

                    lineList.add(line);
                }
            }
            logger.debug("处理正负发票核销数据完成，核销关系ID：{}，行数量：{}", recordDto.getApplyNo(),
                    CollectionUtils.isNotEmpty(lins) ? lins.size() : 0);
        }

        gerpApPaymentsData.setPkgPHeaderTbl(headerList);
        gerpApPaymentsData.setPkgPLineTbl(lineList);
        logger.info("正负发票核销数据处理完成，业务流水号：{}，头数量：{}，行数量：{}", businessId, headerList.size(), lineList.size());

        //发起请求
        logger.info("开始调用SDP服务，目标系统：{}，主题：{}", targetSystemCode, topicCode);
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, gerpApPaymentsData);
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));

        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            logger.info("正负发票核销接口调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            logger.error("正负发票核销接口调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, businessId);
        }
    }


    @Override
    public EsbResponse callCuxicpGetSeqPkgPortType() {

        logger.info("开始调用SDP获取关联交易号接口");

        TopicCodeEnum topicCodeEnum = TopicCodeEnum.CUXICP_GET_SEQ_PKG_PORT_TYPE;

        // 主题
        String topicCode = topicCodeEnum.getTopicCode();

        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();

        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);
        logger.info("生成业务流水号：{}", businessId);

        GERPIcpGetSeqHeader gerpIcpGetSeqHeader = new GERPIcpGetSeqHeader();
        gerpIcpGetSeqHeader.setSdpSerialNum(businessId);
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        gerpIcpGetSeqHeader.setSdpPSourceNumber(headerRequest.getRequestId());

        //发起请求
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, gerpIcpGetSeqHeader);
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));

        // 处理响应结果
        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            logger.info("SDP获取关联交易号接口调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsetype(sdpTradeResult.getResponseObj().getResponsetype());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(sdpTradeResult.getResponseObj().getSdpXIcpSeqId());
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            logger.error("SDP获取关联交易号接口调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, null);
        }
    }

    @Override
    public EsbResponse callIcpNonrelate(List<CustomerTransferDto> dtoList) {
        TopicCodeEnum icpNonrelate = TopicCodeEnum.ICP_NONRELATE;
        // 主题
        String topicCode = icpNonrelate.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = icpNonrelate.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(icpNonrelate);

        GERPIcpNonrelateHeader gerpIcpNonrelateHeader = new GERPIcpNonrelateHeader();
        List<GERPIcpNonrelateHeader.PkgPIfaceHeadersTbl> pkgPIfaceHeadersTbl = new ArrayList<>();
        List<GERPIcpNonrelateHeader.PkgPIfaceArLinesTbl> pkgPIfaceArLinesTbl = new ArrayList<>();
        List<GERPIcpNonrelateHeader.PkgPIfaceApLinesTbl> pkgPIfaceApLinesTbl = new ArrayList<>();

        for (CustomerTransferDto dto : dtoList) {
            GERPIcpNonrelateHeader.PkgPIfaceHeadersTbl headersTbl = new GERPIcpNonrelateHeader.PkgPIfaceHeadersTbl();
            headersTbl.setEsbDataSource(BusinessTypeEnums.CUSTOMER_TRANSFER.getCode());
            headersTbl.setSourceCode("PAM");
            headersTbl.setEsbSerialNum(businessId);
            headersTbl.setSourceNum("PAM_" + dto.getId());
            headersTbl.setReqHeaderId(new BigDecimal(dto.getSeqId()));
            headersTbl.setSourceOrderNum(dto.getTransferCode());
            headersTbl.setNorelateTypesCode("ICP001");
            headersTbl.setSupplierOrgId(new BigDecimal(dto.getTransferInOuId()));
            headersTbl.setRequirementOrgId(new BigDecimal(dto.getTransferOutOuId()));
            headersTbl.setCurrencyCode(dto.getCurrency());
            headersTbl.setConversionRate(dto.getConversionRate());
            headersTbl.setConversionType(dto.getConversionType());
            headersTbl.setGlDate(DateUtils.format(dto.getAuditDate(), DateUtils.FORMAT_SHORT));
            headersTbl.setSdpSArInvoiceType("ICP组织间转款");
            headersTbl.setDescription("ICP组织间转款");
            headersTbl.setRequirementSystem("GERP");
            headersTbl.setSupplierSystem("GERP");
            pkgPIfaceHeadersTbl.add(headersTbl);

            GERPIcpNonrelateHeader.PkgPIfaceArLinesTbl arLinesTbl = new GERPIcpNonrelateHeader.PkgPIfaceArLinesTbl();
            arLinesTbl.setReqHeaderId(new BigDecimal(dto.getSeqId()));
            arLinesTbl.setSourceOrderNum(dto.getTransferCode());
            arLinesTbl.setReqLineNum(BigDecimal.ONE);
            arLinesTbl.setDescription("ICP组织间转款");
            arLinesTbl.setRequestQuantity(BigDecimal.ONE);
            arLinesTbl.setSettlementPrice(dto.getTransferAmount());
            arLinesTbl.setAmount(dto.getTransferAmount());
            pkgPIfaceArLinesTbl.add(arLinesTbl);

            GERPIcpNonrelateHeader.PkgPIfaceApLinesTbl apLinesTbl = new GERPIcpNonrelateHeader.PkgPIfaceApLinesTbl();
            apLinesTbl.setReqHeaderId(new BigDecimal(dto.getSeqId()));
            apLinesTbl.setSourceOrderNum(dto.getTransferCode());
            apLinesTbl.setReqLineNum(BigDecimal.ONE);
            apLinesTbl.setDescription("ICP组织间转款");
            apLinesTbl.setAmount(dto.getTransferAmount());
            pkgPIfaceApLinesTbl.add(apLinesTbl);
        }
        gerpIcpNonrelateHeader.setPkgPIfaceHeadersTbl(pkgPIfaceHeadersTbl);
        gerpIcpNonrelateHeader.setPkgPIfaceArLinesTbl(pkgPIfaceArLinesTbl);
        gerpIcpNonrelateHeader.setPkgPIfaceApLinesTbl(pkgPIfaceApLinesTbl);

        //发起请求
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, gerpIcpNonrelateHeader);
        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsetype(sdpTradeResult.getResponseObj().getResponsetype());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, null);
        }
    }


    /**
     * PAM-ERP-022 收款核销冲销
     */
    @Override
    public EsbResponse callCuxArUnapplyApiPkgPortType(List<WriteOffDto> writeOffDtoList) {
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.CUX_AR_UNAPPLY;
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);
        logger.info("开始处理收款核销冲销，业务流水号：{}", businessId);

        GERPCuxArUnapplyRequest gerpCuxArUnapplyRequest = new GERPCuxArUnapplyRequest();
        List<GERPCuxArUnapplyHeader> gerpCuxArUnapplyHeaders = new ArrayList<>();
        for (WriteOffDto writeOffDto : writeOffDtoList) {
            for (ReceiptClaimInvoiceRelDto receiptClaimInvoiceRelDto : writeOffDto.getWriteOffInvoiceList()) {
                GERPCuxArUnapplyHeader gerpCuxArUnapplyHeader = new GERPCuxArUnapplyHeader();
                gerpCuxArUnapplyHeader.setEsbDataSource(topicCodeEnum.getInterfaceCode());
                gerpCuxArUnapplyHeader.setSourceCode("PAM");
                gerpCuxArUnapplyHeader.setSourceNum("PAM_" + receiptClaimInvoiceRelDto.getId());
                gerpCuxArUnapplyHeader.setSourceLineNum("");
                gerpCuxArUnapplyHeader.setEsbSerialNum(businessId);
                gerpCuxArUnapplyHeader.setOrgId(BigDecimalUtils.longToBigDecimal(writeOffDto.getOuId()));
                gerpCuxArUnapplyHeader.setCustomerNumber(writeOffDto.getCustomerCode());
                gerpCuxArUnapplyHeader.setReceiptNumber(writeOffDto.getReceiptCode());
                gerpCuxArUnapplyHeader.setTrxNumber(receiptClaimInvoiceRelDto.getInvoiceCode());
                gerpCuxArUnapplyHeader.setReversalDate(DateUtils.format(receiptClaimInvoiceRelDto.getCancelAt(), DateUtils.FORMAT_SHORT));
                gerpCuxArUnapplyHeader.setReversalGlDate(DateUtils.format(receiptClaimInvoiceRelDto.getCancelAt(), DateUtils.FORMAT_SHORT));
                gerpCuxArUnapplyHeaders.add(gerpCuxArUnapplyHeader);
            }
        }
        gerpCuxArUnapplyRequest.setPkgPApplicationTbl(gerpCuxArUnapplyHeaders);

        //发起请求
        SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, gerpCuxArUnapplyRequest);
        logger.info("SDP服务调用完成，结果：{}", JSON.toJSONString(sdpTradeResult));
        // 处理响应结果
        if (Objects.equals(sdpTradeResult.getSuccess(), Boolean.TRUE) && sdpTradeResult.getResponseObj() != null
                && Objects.equals(sdpTradeResult.getResponseObj().getResponsecode(), "000000")) {
            logger.info("SDP收款核销冲销接口调用成功，业务流水号：{}", businessId);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            esbResponse.setResponsemessage(sdpTradeResult.getResponseObj().getResponsemessage());
            esbResponse.setResponsetype(sdpTradeResult.getResponseObj().getResponsetype());
            esbResponse.setData(businessId);
            return esbResponse;
        } else {
            String result = JSON.toJSONString(sdpTradeResult);
            logger.error("SDP收款核销冲销接口调用失败，业务流水号：{}，错误信息：{}", businessId, result);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), result.length() > 3000 ? result.substring(0, 3000) : result, null);
        }
    }

    @Override
    public EsbResponse callErpArInvoice(List<InvoiceReceivableErpDto> dtoList, String voucherType) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.AR_INVOICE;
        // 接口卡
        String interfaceCode = topicCodeEnum.getInterfaceCode();
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);
        try {
            //构建入参
            JSONObject jsonObject = this.buildErpArInvoiceParams(dtoList, businessId, voucherType);
            //发起请求
            logger.info("接口{}调用SDP服务业务流水号：{}", interfaceCode, businessId);
            SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, jsonObject);
            logger.info("接口{}调用SDP服务返回结果：{}", interfaceCode, JSON.toJSONString(sdpTradeResult));

            return getEsbResponse(businessId, sdpTradeResult);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }
    }

    @Override
    public EsbResponse callErpArTrxApply(List<InvoiceReceivableErpDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.AR_TRX_APPLY;
        // 接口卡
        String interfaceCode = topicCodeEnum.getInterfaceCode();
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);
        try {
            //构建入参
            JSONObject jsonObject = this.buildErpArTrxApplyParams(dtoList, businessId, interfaceCode);
            //发起请求
            logger.info("接口{}调用SDP服务业务流水号：{}", interfaceCode, businessId);
            SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, jsonObject);
            logger.info("接口{}调用SDP服务返回结果：{}", interfaceCode, JSON.toJSONString(sdpTradeResult));

            return getEsbResponse(businessId, sdpTradeResult);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }
    }

    @Override
    public EsbResponse callErpArApply(List<WriteOffDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new EsbResponse();
        }
        TopicCodeEnum topicCodeEnum = TopicCodeEnum.AR_APPLY;
        // 接口卡
        String interfaceCode = topicCodeEnum.getInterfaceCode();
        // 主题
        String topicCode = topicCodeEnum.getTopicCode();
        // 交易发起的目标系统
        String targetSystemCode = topicCodeEnum.getTargetSystemCode();
        // 生成流水号
        String businessId = generateSequence(topicCodeEnum);
        try {
            //构建入参
            JSONObject jsonObject = this.buildErpArApplyParams(dtoList, businessId, interfaceCode);
            //发起请求
            logger.info("接口{}调用SDP服务业务流水号：{}", interfaceCode, businessId);
            SdpTradeResult<ResponseObj> sdpTradeResult = sdpCarrierServicel.doSdpDataTrade(targetSystemCode, topicCode, businessId, jsonObject);
            logger.info("接口{}调用SDP服务返回结果：{}", interfaceCode, JSON.toJSONString(sdpTradeResult));

            return getEsbResponse(businessId, sdpTradeResult);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), businessId);
        }
    }

    private JSONObject buildErpArInvoiceParams(List<InvoiceReceivableErpDto> dtoList, String esbSerialNum, String voucherType) {
        JSONObject params = new JSONObject();
        for (InvoiceReceivableErpDto dto : dtoList) {
            dto.setSourceCode("PAM");
            dto.setSourceNum("PAM" + new Date().getTime() + dto.getLineNumber() + "_" + dto.getId());
            dto.setEsbSerialNum(esbSerialNum);
            // 根据备注长度截取,只取前220个字符
            if (StringUtils.length(dto.getComments()) > 220) {
                dto.setComments(StringUtils.substring(dto.getComments(), 0, 220));
            }
            dto.setDescription(com.midea.pam.common.util.StringUtils.filterControlCharacters(dto.getDescription()));
            dto.setHeaderAttribute3(voucherType);//凭证类型
            String headerattribute1 = "";
            if (StringUtils.isNotBlank(dto.getHeaderAttribute1())) {
                headerattribute1 += dto.getHeaderAttribute1();
            }
            if (StringUtils.isNotBlank(dto.getContractCode())) {
                headerattribute1 += "\\" + dto.getContractCode();//PAM销售子合同号
            }
            dto.setHeaderAttribute1(headerattribute1);
            //是否需要过滤？
            if (StringUtils.isEmpty(dto.getLineType())) {
                dto.setHeaderAttribute7(null);
                dto.setHeaderAttribute8(null);
                dto.setHeaderAttribute9(null);
                dto.setHeaderAttribute10(null);
                dto.setHeaderAttribute12(null);
                dto.setHeaderAttribute13(null);
                dto.setHeaderAttribute14(null);
            }
        }
        JSONArray pkgPIfaceLinesTbl = (JSONArray) JSONArray.toJSON(dtoList);
        params.put("pkgPIfaceLinesTbl", pkgPIfaceLinesTbl);
        return params;
    }

    private JSONObject buildErpArTrxApplyParams(List<InvoiceReceivableErpDto> dtoList, String esbSerialNum, String interfaceCode) {
        JSONObject params = new JSONObject();
        JSONArray pkgPApplicationTbl = new JSONArray();

        for (InvoiceReceivableErpDto dto : dtoList) {
            JSONObject line = new JSONObject();
            line.put("esbDataSource", interfaceCode);
            line.put("sourceCode", "PAM");
            line.put("sourceNum", "PAM" + new Date().getTime() + "_" + dto.getId());
            line.put("esbSerialNum", esbSerialNum);//流水号
            line.put("orgId", dto.getOrgId());//业务实体ID
            line.put("customerNumber", dto.getCustomerNumber());//客户编码
            line.put("cmTrxNumber", dto.getCmTrxNumber());//贷项发票编号
            line.put("invoiceTrxNumber", dto.getInvoiceTrxNumber());//借项发票编号
            line.put("applyDate", dto.getWriteOffDate());//核销日期
            line.put("glDate", dto.getGlDate());//总账日期
            line.put("appliedAmount", dto.getAppliedAmount());//核销金额（含税）
            pkgPApplicationTbl.add(line);
        }
        params.put("pkgPApplicationTbl", pkgPApplicationTbl);
        return params;
    }

    private JSONObject buildErpArApplyParams(List<WriteOffDto> dtoList, String esbSerialNum, String interfaceCode) {
        JSONObject params = new JSONObject();
        JSONArray pkgPApplicationTbl = new JSONArray();

        for (WriteOffDto writeOffDto : dtoList) {
            for (ReceiptClaimInvoiceRelDto dto : writeOffDto.getWriteOffInvoiceList()) {
                JSONObject line = new JSONObject();
                line.put("esbDataSource", interfaceCode);
                line.put("sourceCode", "PAM");
                line.put("sourceNum", "PAM" + new Date().getTime() + "_" + dto.getId());
                line.put("esbSerialNum", esbSerialNum);//流水号

                line.put("orgId", writeOffDto.getOuId());//业务实体ID
                line.put("customerNumber", writeOffDto.getCustomerCode());//客户编码
                line.put("trxNumber", dto.getInvoiceCode());
                line.put("receiptNumber", writeOffDto.getReceiptCode());
                line.put("appliedDate", DateUtils.format(new Date(), DateUtils.FORMAT_SHORT));
                line.put("appliedGlDate", DateUtils.format(dto.getGlDate(), DateUtils.FORMAT_SHORT));
                line.put("appliedAmount", dto.getWriteOffAmount());//本次发票核销金额
                //如果收款与事务处理使用相同币种，请不要输入交叉汇率
                if (!Objects.equals(dto.getCurrencyCode(), dto.getRcCurrencyCode())) {
                    line.put("unappliedAmount", dto.getReceiptWriteOffAmount());//本次回款核销金额
                }
                pkgPApplicationTbl.add(line);
            }
        }
        params.put("pkgPApplicationTbl", pkgPApplicationTbl);
        return params;
    }

}
