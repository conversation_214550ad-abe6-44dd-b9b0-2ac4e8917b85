package com.midea.pam.ctc.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.midea.esb.ReplyInformation;
import com.midea.esb.RequestHeader;
import com.midea.fdp.fdpbudgetiteminfoservice.v1.FDPBudgetItemInfoService;
import com.midea.fdp.fdpcnapsinfoservice.v1.CnapsqueryToSync;
import com.midea.fdp.fdpcnapsinfoservice.v1.FDPCnapsInfoService;
import com.midea.gceb.gcebitemnumberservice.v1.GcebItemNumberService;
import com.midea.gceb.gcebitemnumberservice.v1.Receive;
import com.midea.gceb.gcebtoperipheralinterfaceservice.v1.AdvancepaymentApply;
import com.midea.gceb.gcebtoperipheralinterfaceservice.v1.CancelTheClaim;
import com.midea.gceb.gcebtoperipheralinterfaceservice.v1.DrawbackApply;
import com.midea.gceb.gcebtoperipheralinterfaceservice.v1.DrawbackApplyCancel;
import com.midea.gceb.gcebtoperipheralinterfaceservice.v1.GcebToPeripheralInterfaceService;
import com.midea.geam.geampamsyncinvoice.v1.GEAMPamSyncInvoice;
import com.midea.geam.geampamsyncinvoice.v1.GetSyncInvoice;
import com.midea.geam.geampamsyncinvoice.v1.GetSyncInvoiceResponse;
import com.midea.geam.geampmspaystatus.v1.GetPmsPayStatus;
import com.midea.geam.geampmspaystatus.v1.GetPmsPayStatusResponse;
import com.midea.geam.geampmspurserver.v1.GetPmsPurServer;
import com.midea.geam.geampmspurserver.v1.GetPmsPurServerResponse;
import com.midea.gems.gemsadjustapplyservice.v1.CreateAdjustApply;
import com.midea.gems.gemsadjustapplyservice.v1.GemsAdjustApplyService;
import com.midea.gems.gemsbmfeedetailservice.v1.GEmsBmFeeDetailService;
import com.midea.gems.gemsbmfeedetailservice.v1.PageQueryFeeDetails;
import com.midea.gems.gemsbmfeedetailservice.v1.PageQueryFeeDetailsResponse;
import com.midea.gems.gemsbudgetnodeservice.v1.CreateBudgetNode;
import com.midea.gems.gemsbudgetnodeservice.v1.GEmsBudgetNodeService;
import com.midea.gems.gemsprojectnumberservice.v1.GemsProjectNumber;
import com.midea.gems.gemsprojectnumberservice.v1.GemsProjectNumberResponse;
import com.midea.gems.gemsprojectnumberservice.v1.GemsProjectNumberService;
import com.midea.pam.common.base.EsbResponse;
import com.midea.pam.common.basedata.entity.EsbMassQueryRecord;
import com.midea.pam.common.basedata.entity.StorageInventory;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.DifferenceShareEsb;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.crm.dto.CommonByErpDto;
import com.midea.pam.common.ctc.dto.BudgetItemSysReturnDto;
import com.midea.pam.common.ctc.dto.CnapsInfoReturnDto;
import com.midea.pam.common.ctc.dto.CnapsInfoSyncDTO;
import com.midea.pam.common.ctc.dto.CostAccountingErpDto;
import com.midea.pam.common.ctc.dto.CustomerTransferDto;
import com.midea.pam.common.ctc.dto.DifferenceShareEsbSyncDTO;
import com.midea.pam.common.ctc.dto.InvoiceApplyHeaderDto;
import com.midea.pam.common.ctc.dto.InvoiceReceivableDto;
import com.midea.pam.common.ctc.dto.InvoiceReceivableErpDto;
import com.midea.pam.common.ctc.dto.MaterialSyncErpDto;
import com.midea.pam.common.ctc.dto.PaymentApplyDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceDetailErpDto;
import com.midea.pam.common.ctc.dto.PaymentInvoiceErpDto;
import com.midea.pam.common.ctc.dto.PaymentPlanErpDto;
import com.midea.pam.common.ctc.dto.PaymentPlanInvoiceErpDto;
import com.midea.pam.common.ctc.dto.PositiveAndNegativeInvoiceRecordDetails;
import com.midea.pam.common.ctc.dto.PositiveAndNegativeInvoiceRecordDto;
import com.midea.pam.common.ctc.dto.PrepayErpDto;
import com.midea.pam.common.ctc.dto.ProjectBudgetFeeItemDto;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectProfitDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimDto;
import com.midea.pam.common.ctc.dto.ReceiptClaimInvoiceRelDto;
import com.midea.pam.common.ctc.dto.WriteOffDto;
import com.midea.pam.common.ctc.eam.PmsResult;
import com.midea.pam.common.ctc.entity.BudgetItem;
import com.midea.pam.common.ctc.entity.CnapsInfo;
import com.midea.pam.common.ctc.entity.ProjectBudgetCost;
import com.midea.pam.common.enums.BusinessTypeEnums;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.enums.CodePrefix;
import com.midea.pam.common.enums.EamInvokeEnums;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.enums.ResponseCodeEnums;
import com.midea.pam.common.esb.constant.EsbConstant;
import com.midea.pam.common.esb.util.EsbUtil;
import com.midea.pam.common.esb.vo.ERPMassQueryReturnVo;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtil;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.ctc.common.enums.CorUserStatus;
import com.midea.pam.ctc.common.enums.ReceiptClaimEnum;
import com.midea.pam.ctc.common.utils.HttpClientUtil;
import com.midea.pam.ctc.eam.service.service.EamPurchaseInvokeLogService;
import com.midea.pam.ctc.esb.config.EsbGfpProperties;
import com.midea.pam.ctc.esb.util.EsbCtcUtil;
import com.midea.pam.ctc.feign.basedata.feign.BasedataEsbMassQueryRecordFeignClient;
import com.midea.pam.ctc.service.EsbService;
import com.midea.pam.ctc.service.OrganizationCustomDictService;
import com.midea.pam.ctc.utils.ErpMassQueryStrategyHelper;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_cancel_api_pkg.CUXAPCANCELAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_cancel_api_pkg.do_insert.APPSCUXAPCANCELREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_cancel_api_pkg.do_insert.APPSCUXAPCANCELTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.CUXAPINVOICEAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.ISGServiceFaultMessage;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.APPSCUXAPINVOICELINEREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.APPSCUXAPINVOICELINETBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.APPSCUXAPINVOICEREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.APPSCUXAPINVOICETBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.CUXAPPAYMENTPLANAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.do_insert.APPSCUXAPINVOICEPAYPGINTREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.do_insert.APPSCUXAPINVOICEPAYPGINTTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.do_insert.APPSCUXAPINVOICEPIINTREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.do_insert.APPSCUXAPINVOICEPIINTTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.do_insert.APPSCUXAPINVOICEPPINTREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.do_insert.APPSCUXAPINVOICEPPINTTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.CUXAPPAYMENTSAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.APPSCUXAPCHECKREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.APPSCUXAPCHECKTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.APPSCUXAPPAYMENTREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.APPSCUXAPPAYMENTTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_prepay_api_pkg.CUXAPPREPAYAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_prepay_api_pkg.do_insert.APPSCUXAPPREPAYSREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_prepay_api_pkg.do_insert.APPSCUXAPPREPAYSTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_apply_api_pkg.CUXARAPPLYAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_apply_api_pkg.do_import.APPSCUXARAPPLYREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_apply_api_pkg.do_import.APPSCUXARAPPLYTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_invoice_api_pkg.CUXARINVOICEAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_invoice_api_pkg.do_import.APPSCUXARINVOICETBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.CUXARRECEIPTAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.receipt.APPSCUXARRECEIPTREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.receipt.APPSCUXARRECEIPTTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.reverse.APPSCUXARRECEIPTREVERSEREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.reverse.APPSCUXARRECEIPTREVERSETBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_trx_apply_api_pkg.CUXARTRXAPPLYAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_trx_apply_api_pkg.do_import.APPSCUXARTRXAPPLYREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_trx_apply_api_pkg.do_import.APPSCUXARTRXAPPLYTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_unapply_api_pkg.CUXARUNAPPLYAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_unapply_api_pkg.do_import.APPSCUXARUNAPPLYREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_unapply_api_pkg.do_import.APPSCUXARUNAPPLYTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.CUXESBCOMMONINAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.APPSCUXESBCOMMONINREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.APPSCUXESBCOMMONINTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_gl_journals_import_api_pkg.CUXGLJOURNALSIMPORTAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_gl_journals_import_api_pkg.do_import.APPSCUXGLJOURNALREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_gl_journals_import_api_pkg.do_import.APPSCUXGLJOURNALTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_get_seq_pkg.CUXICPGETSEQPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.CUXICPNONRELATEREQAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.do_import.APPSCUXICPINVOICEAPREQLNEREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.do_import.APPSCUXICPINVOICEAPREQLNETBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.do_import.APPSCUXICPINVOICEARREQLNEREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.do_import.APPSCUXICPINVOICEARREQLNETBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.do_import.APPSCUXICPINVOICEREQHDRREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.do_import.APPSCUXICPINVOICEREQHDRTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.CUXINVTRANSACTIONSAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.APPSCUXINVTRANSACTIONREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.APPSCUXINVTRANSACTIONTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_common_req_api_pkg.CUXPOCOMMONREQAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_common_req_api_pkg.do_import.APPSCUXPOCOMMONREQREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_common_req_api_pkg.do_import.APPSCUXPOCOMMONREQTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_standard_po_api_pkg.CUXPOSTANDARDPOAPIPKGPortType;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_standard_po_api_pkg.do_import.APPSCUXPOSTANDARDHEADERREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_standard_po_api_pkg.do_import.APPSCUXPOSTANDARDHEADERTBL;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_standard_po_api_pkg.do_import.APPSCUXPOSTANDARDLINEREC;
import com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_standard_po_api_pkg.do_import.APPSCUXPOSTANDARDLINETBL;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.xml.bind.JAXBElement;
import javax.xml.ws.Holder;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @program: pam
 * @description: EsbServiceImpl
 * @author: gaojh1
 * @create: 2019-3-12 10:51
 **/
public class EsbServiceImpl implements EsbService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final Logger LOGGER = LoggerFactory.getLogger(EsbServiceImpl.class);

    @Resource
    private EsbGfpProperties esbGfpProperties;
    @Resource
    private BasedataEsbMassQueryRecordFeignClient esbMassQueryRecordFeignClient;
    @Resource
    GcebItemNumberService gcebItemNumberService;
    @Resource
    GemsProjectNumberService gemsProjectNumberService;
    @Resource
    GEmsBudgetNodeService gemsBudgetNodeService;
    @Resource
    GemsAdjustApplyService gemsAdjustApplyService;
    @Resource
    private GEmsBmFeeDetailService gemsBmFeeDetailService;
    @Resource
    private GEAMPamSyncInvoice geamPamSyncInvoice;
    @Resource
    FDPBudgetItemInfoService fdpBudgetItemInfoService;
    @Resource
    FDPCnapsInfoService fdpCnapsInfoService;
    @Resource
    GcebToPeripheralInterfaceService gcebToPeripheralInterfaceService;
    @Resource
    OrganizationCustomDictService organizationCustomDictService;
    @Resource
    EamPurchaseInvokeLogService eamPurchaseInvokeLogService;
    @Resource
    CUXARINVOICEAPIPKGPortType cuxarinvoiceapipkgPortType;
    @Resource
    CUXARRECEIPTAPIPKGPortType cuxarreceiptapipkgPortType;
    @Resource
    CUXAPINVOICEAPIPKGPortType cuxapinvoiceapipkgPortType;
    @Resource
    CUXARAPPLYAPIPKGPortType cuxarapplyapipkgPortType;
    @Resource
    CUXARUNAPPLYAPIPKGPortType cuxarunapplyapipkgPortType;
    @Resource
    CUXESBCOMMONINAPIPKGPortType cuxesbcommoninapipkgPortType;
    @Resource
    CUXINVTRANSACTIONSAPIPKGPortType cuxinvtransactionsapipkgPortType;
    @Resource
    CUXICPNONRELATEREQAPIPKGPortType cuxicpnonrelatereqapipkgPortType;
    @Resource
    CUXAPPAYMENTSAPIPKGPortType cuxappaymentsapipkgPortType;
    @Resource
    CUXICPGETSEQPKGPortType cuxicpgetseqpkgPortType;
    @Resource
    CUXAPPAYMENTPLANAPIPKGPortType cuxappaymentplanapipkgPortType;
    @Resource
    CUXAPPREPAYAPIPKGPortType cuxapprepayapipkgPortType;
    @Resource
    CUXPOSTANDARDPOAPIPKGPortType cuxpostandardpoapipkgPortType;
    @Resource
    CUXPOCOMMONREQAPIPKGPortType cuxpocommonreqapipkgPortType;
    @Resource
    CUXAPCANCELAPIPKGPortType cuxapcancelapipkgPortType;
    @Resource
    CUXARTRXAPPLYAPIPKGPortType cuxartrxapplyapipkgPortType;
    @Resource
    CUXGLJOURNALSIMPORTAPIPKGPortType cuxgljournalsimportapipkgPortType;

    @Resource
    private ErpMassQueryStrategyHelper erpMassQueryStrategyHelper;

    @Value("${geam.url}")
    private String GRAMURL;

    @Value("${fdp.url}")
    private String FDPURL;

    /**
     * 项目号写入资金
     */
    @Override
    public EsbResponse callGcebItemNumberService(final ProjectDto project) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Receive.BodyInput bodyInput = new Receive.BodyInput();
        bodyInput.setBusinessType(BusinessTypeEnums.CREATE_PROJECT_CODE_GFP.getCode());
        bodyInput.setItemRecNum(1);

        bodyInput.setInputDate(EsbUtil.formatXMLGreDate(new Date()));
        bodyInput.setOutputDate(EsbUtil.formatXMLGreDate(new Date()));
        bodyInput.setTranscode(String.valueOf(project.getId())); //每笔交易的唯一流水号
        bodyInput.setSourcesystemcode(EsbConstant.PAM_REQUEST_ID);//PAM

        Receive.BodyInput.ItemDetail itemDetail = new Receive.BodyInput.ItemDetail();
        itemDetail.setOuId(String.valueOf(project.getOuId()));
        itemDetail.setOuCode(project.getOuCode());
        itemDetail.setOuName(project.getOuName());
        itemDetail.setItemNumber(project.getCode());
        itemDetail.setItemName(project.getName());
        itemDetail.setManagerMip(project.getManagerMip());
        itemDetail.setManagerMipName(project.getManagerName());
        itemDetail.setFinanceMip(project.getFinancialMip());
        itemDetail.setFinanceMipName(project.getFinancialName());
        itemDetail.setStatus(String.valueOf(ProjectStatus.APPROVALED.getCode()));
        bodyInput.getItemDetail().add(itemDetail);
        Holder<ReplyInformation> headOut = new Holder<ReplyInformation>();
        try {
            gcebItemNumberService.receive(bodyInput, headerRequest, headOut);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsetype(headOut.value.getResponseType());
            esbResponse.setResponsemessage(headOut.value.getResponseMessage());
            if ("N".equalsIgnoreCase(headOut.value.getResponseType())
                    || "000000".equalsIgnoreCase(headOut.value.getResponseCode())) {
                esbResponse.setResponsecode(ResponseCodeEnums.SUCESS.getCode());
            } else {
                esbResponse.setResponsecode(ResponseCodeEnums.FAULT.getCode());
            }
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * 项目号写入EMS
     */
    @Override
    public EsbResponse callGemsProjectNumberService(final ProjectDto project, Integer projectStatus) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        GemsProjectNumber.BodyInput bodyInput = new GemsProjectNumber.BodyInput();
        GemsProjectNumber.BodyInput.ItemDetail itemDetail = new GemsProjectNumber.BodyInput.ItemDetail();
        itemDetail.setOuId(String.valueOf(project.getOuId()));
        itemDetail.setOuCode(project.getOuCode());
        itemDetail.setOuName(project.getOuName());
        itemDetail.setProjectNumber(project.getCode());
        itemDetail.setProjectName(project.getName());
        itemDetail.setManagerMip(project.getManagerMip());
        itemDetail.setManagerMipName(project.getManagerName());
        itemDetail.setFinanceMip(project.getFinancialMip());
        itemDetail.setFinanceMipName(project.getFinancialName());
        itemDetail.setStatus(String.valueOf(projectStatus));
        // 预算维度: 1-项目号,2-wbs预算编制
        if (BooleanUtils.isTrue(project.getWbsEnabled())) {
            itemDetail.setAttribute1("2");
        } else {
            itemDetail.setAttribute1("1");
        }
        bodyInput.getItemDetail().add(itemDetail);
        try {
            final GemsProjectNumberResponse.Result res = gemsProjectNumberService.gemsProjectNumber(bodyInput,
                    headerRequest);
            return new EsbResponse(res.getResponseType(), res.getRespnoseCode(), res.getResponseMessage(),
                    headerRequest.getSerialNo());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-EMS-006 预算单元写入
     */
    @Override
    public EsbResponse callGemsCreateBudgetNode(final ProjectBudgetFeeItemDto budget) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        CreateBudgetNode.BmIoBudgetNode bmIoBudgetNode = new CreateBudgetNode.BmIoBudgetNode();
        bmIoBudgetNode.setSourceSystem(EsbConstant.PAM_REQUEST_ID);//PAM
        bmIoBudgetNode.setSourceOrderCode(String.valueOf(budget.getId()));
        bmIoBudgetNode.setSourceOrderID(String.valueOf(budget.getId()));
        bmIoBudgetNode.setBudgetHeadersId(budget.getBudgetHeaderId());
        bmIoBudgetNode.setBusiOrgCode(budget.getBudgetDepCode());
        bmIoBudgetNode.setBusiOrgName(budget.getBudgetDepName());
        bmIoBudgetNode.setProjectTypeCode(budget.getCode());
        if (Objects.equals(Boolean.TRUE, budget.getWbsEnabled())) {
            bmIoBudgetNode.setProjectTypeCode(budget.getWbsCode());
        }
        bmIoBudgetNode.setProjectTypeName(budget.getName());
        bmIoBudgetNode.setFeeTypeCode(budget.getFeeTypeCode());
        bmIoBudgetNode.setFeeTypeName(budget.getFeeTypeName());
        bmIoBudgetNode.setBudgetAmount(budget.getAmount());
        if (StringUtils.isNotEmpty(budget.getCreatedByCode())) {
            bmIoBudgetNode.setCreatedByCode(budget.getCreatedByCode());
        }
        if (budget.getCreatedAt() != null) {
            bmIoBudgetNode.setCreationDate(EsbUtil.formatXMLGreDate(budget.getCreatedAt()));
        }
        bmIoBudgetNode.setYearCode(DateUtils.getYear(new Date()));
        try {
            gemsBudgetNodeService.createBudgetNode(bmIoBudgetNode, headerRequest);
            return new EsbResponse("", ResponseCodeEnums.SUCESS.getCode(), ResponseCodeEnums.SUCESS.getMsg(),
                    headerRequest.getSerialNo());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-EMS-005 费用发生明细查询
     */
    @Override
    public List<ProjectBudgetCost> callGEmsBmFeeDetailService(String ouId, String lastUpdateDate) {
        List<ProjectBudgetCost> resultList = new ArrayList<>();
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        PageQueryFeeDetails.FeeDetails parm = new PageQueryFeeDetails.FeeDetails();
//        parm.setBudgetNodeId();
        parm.setOUId(ouId);
        if (!StringUtils.isEmpty(lastUpdateDate)) {
            parm.setLastUpdateDate(EsbUtil.formatXMLGreDate(DateUtils.parse(lastUpdateDate)));
        }
        List<PageQueryFeeDetailsResponse.FeeDetailsList> rstList = gemsBmFeeDetailService.pageQueryFeeDetails(parm,
                headerRequest);
        for (PageQueryFeeDetailsResponse.FeeDetailsList fee : rstList) {
            ProjectBudgetCost budgetCost = new ProjectBudgetCost();
            budgetCost.setOrderCode(fee.getOrderCode());//单号
            budgetCost.setApplyName(fee.getApplyName());//申请人名称
            budgetCost.setApplyDate(EsbUtil.xmlGcToDate(fee.getApplyDate()));
            budgetCost.setHrOrgName(fee.getOrgName());//行政部门
            budgetCost.setBudgetTreeName(fee.getBudgetName());//预算树
            budgetCost.setBudgetNodeName(fee.getBudgetNodeName());//预算单元
            budgetCost.setProjectName(fee.getBussinessTypeName());//项目名称
            budgetCost.setProjectCode(fee.getBussinessTypeCode());//项目号
            budgetCost.setFeeTypeName(fee.getFeeTypeName());//经济事项名称
            budgetCost.setAmount(fee.getFeeAmount());//金额
            budgetCost.setCurrency(fee.getCurrencyName());//币种
            budgetCost.setGlDate(EsbUtil.xmlGcToDate(fee.getGlDate()));//总账日期
            budgetCost.setOrderStatus(fee.getOrderStatus());//状态
            budgetCost.setImportErpStatus(fee.getImportErpStatus());//导ERP状态
            budgetCost.setLastUpdateDate(EsbUtil.xmlGcToDate(fee.getLastUpdateDate()));//最后更新时间
            if (StringUtils.isNotEmpty(fee.getAttribute1())) {
                budgetCost.setAttribute1(Long.valueOf(fee.getAttribute1()));//EMS唯一标识
            }
            resultList.add(budgetCost);
        }
        return resultList;
    }

    /**
     * PAM-EAM-001 采购申请查询
     */
    @Override
    public EsbResponse callGEAMPmsPurServer(String startDate, String endDate, String purchaseStatur, String modelKey) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        GetPmsPurServer.PmsApplyServer pmsApply = new GetPmsPurServer.PmsApplyServer();
        pmsApply.setStartDate(DateUtils.format(DateUtils.subtractDay(new Date(), -1), DateUtils.FORMAT_SHORT));
        pmsApply.setEndDate(DateUtils.format(new Date(), DateUtils.FORMAT_SHORT));
        // TZHT：投资  ITCT：IT项目 HN：费用化  WS：完工验收
        pmsApply.setModelKey("ITCT");
        if (StringUtils.isNotBlank(startDate)) {
            pmsApply.setStartDate(startDate);
        }
        if (StringUtils.isNotBlank(modelKey)) {
            pmsApply.setModelKey(modelKey);
        }
        if (StringUtils.isNotBlank(endDate)) {
            pmsApply.setEndDate(endDate);
        }
        if (StringUtils.isNotBlank(purchaseStatur)) {
            pmsApply.setAttribute1(purchaseStatur);
        }
        String requestStr = null;
        String result = null;
        try {
            requestStr = JSON.toJSONString(pmsApply);
            // 1.0esb接口弃用
//            GetPmsPurServerResponse.ResponsData responsData = geamPmsPurServer.getPmsPurServer(pmsApply, headerRequest);
            GetPmsPurServerResponse.ResponsData responsData = null;
            result = JSON.toJSONString(responsData);
            return new EsbResponse(responsData.getResponseType(), responsData.getResponseCode(),
                    responsData.getResponseMessage(), responsData.getPmsPurServerInfo());
        } catch (Exception e) {
            result = e.getMessage();
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        } finally {
            //插入调用日志表
            eamPurchaseInvokeLogService.insertOldEamPurchaseLog(StrUtil.blankToDefault(requestStr, StrUtil.EMPTY), result, "http://www.midea.com/geam/GEAMPmsPurServer/v1/getPmsPurServer");
        }
    }

    /**
     * PAM-EAM-002 HTTP请求 PAM开票校验接口
     *
     * @param modelKey     模块标识
     * @param contractCode EAM合同编号
     * @param invoiceMoney 开票金额（含税）
     * @param payStage     付款期数
     * @return
     */
    @Override
    public EsbResponse callCheckInvoiceServer(String modelKey, String contractCode, BigDecimal invoiceMoney,
                                              String payStage) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("modelKey", modelKey);
            jsonObject.put("contractCode", contractCode);
            jsonObject.put("invoiceMoney", invoiceMoney + "");
            jsonObject.put("payStage", payStage);
            String url = this.GRAMURL + Constants.EAM_CHECKINVOICE;
            //请求参数
            String requestStr = jsonObject.toJSONString();
            String resultData = HttpClientUtil.doPost(url, requestStr);
            //插入调用日志表
            eamPurchaseInvokeLogService.insertOldEamPurchaseLog(StrUtil.blankToDefault(requestStr, StrUtil.EMPTY), resultData, url);
            JSONObject object = JSON.parseObject(resultData);
            if (object == null) {
                return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), "开票校验失败", headerRequest.getSerialNo());
            }
            Boolean responsecode = object.getBoolean("success");
            String message = object.getString("message");
            if (responsecode != null && responsecode) {
                return new EsbResponse("", ResponseCodeEnums.SUCESS.getCode(), message, null);
            } else {
                return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), message, headerRequest.getSerialNo());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * 查询FDP系统Swift国际银行信息
     *
     * @param offset         从第几条记录开始查询
     * @param lastUpdateDate 查询开始时间
     * @return
     */
    @Override
    public List<CnapsInfo> callSwiftCnapsInfoService(Integer offset, String lastUpdateDate) {
        List<CnapsInfo> cnapsInfos = new ArrayList<>();
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("bizSystem", EsbConstant.PAM_REQUEST_ID); // 系统编码
            jsonObject.put("securityCode", esbGfpProperties.getSecuritycode()); // 安全码
            jsonObject.put("lastTime", lastUpdateDate + ""); // 查询开始时间 格式为：yyyy-MM-dd HH:mm:ss
            jsonObject.put("offset", offset + ""); // 默认从0开始
            jsonObject.put("limit", 2000 + ""); // 不传入时，每次最多返回2000条数据
            String url = this.FDPURL + Constants.FDP_SYNCSWIFT;
            logger.info("syncFromSwift 请求url为:{}", url);
            logger.info("syncFromSwift 请求报文为:{}", JSON.toJSONString(jsonObject));
            String resultData = HttpClientUtil.doPost(url, jsonObject.toJSONString());
            JSONObject object = JSON.parseObject(resultData);
            logger.info("syncFromSwift 响应报文为:{}", JSON.toJSONString(object));
            if (object == null) {
                return cnapsInfos;
            }
            JSONArray dataArr = (JSONArray) object.get("bo");
            if (dataArr == null) {
                return cnapsInfos;
            }
            List<CnapsInfoSyncDTO> cnapsInfoList = JSON.parseArray(dataArr.toJSONString(),
                    CnapsInfoSyncDTO.class);
            for (CnapsInfoSyncDTO syncCnapsInfo : cnapsInfoList) {
                CnapsInfo cnapsInfo = new CnapsInfo();
                cnapsInfo.setBankCode(syncCnapsInfo.getBankNumber());
                cnapsInfo.setBankName(syncCnapsInfo.getBankName());
                cnapsInfo.setSwiftCode(syncCnapsInfo.getSwiftCode());
                cnapsInfo.setCnapsCode(syncCnapsInfo.getSwiftCode());
                cnapsInfo.setBankTypeCode(syncCnapsInfo.getBankTypeCode());
                cnapsInfo.setBankTypeName(syncCnapsInfo.getBankTypeName());
                cnapsInfo.setCountryCode(syncCnapsInfo.getBankCountry());
                cnapsInfo.setCountryName(syncCnapsInfo.getBankCountry());
                cnapsInfo.setCity(syncCnapsInfo.getBankCity());
                cnapsInfo.setRemark(syncCnapsInfo.getRemark());
                if (StringUtils.isNotBlank(syncCnapsInfo.getStatus())) {
                    cnapsInfo.setStatus(Integer.valueOf(syncCnapsInfo.getStatus()));
                }
                if (StringUtils.isNotBlank(syncCnapsInfo.getVerifyMark())) {
                    cnapsInfo.setVerifyMark(Integer.valueOf(syncCnapsInfo.getVerifyMark()));
                }
                if (StringUtils.isNotBlank(syncCnapsInfo.getOriginalId())) {
                    cnapsInfo.setOriginalId(Long.valueOf(syncCnapsInfo.getOriginalId()));
                }
                cnapsInfo.setCreatedBy(syncCnapsInfo.getCreateBy());
                if (StringUtils.isNotBlank(syncCnapsInfo.getCreateTime())) {
                    cnapsInfo.setCreationDate(new Date(Long.valueOf(syncCnapsInfo.getCreateTime())));
                    cnapsInfo.setCreateAt(new Date(Long.valueOf(syncCnapsInfo.getCreateTime())));
                }
                if (StringUtils.isNotBlank(syncCnapsInfo.getUpdateTime())) {
                    cnapsInfo.setLastUpdateDate(new Date(Long.valueOf(syncCnapsInfo.getUpdateTime())));
                    cnapsInfo.setUpdateAt(new Date(Long.valueOf(syncCnapsInfo.getUpdateTime())));
                }
                if (StringUtils.isNotBlank(syncCnapsInfo.getUpdateBy())) {
                    cnapsInfo.setLastUpdatedBy(syncCnapsInfo.getUpdateBy());
                }
                cnapsInfo.setDeletedFlag(0);
                cnapsInfos.add(cnapsInfo);
            }
            logger.info("待新增或修改的数据量为:{}", cnapsInfos.size());
            return cnapsInfos;

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return cnapsInfos;
        }
    }

    /**
     * PAM-EAM-003 PAM开票状态同步接口
     */
    @Override
    public PmsResult callSyncInvoiceStatur(InvoiceApplyHeaderDto dto) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("modelKey", dto.getModelKey());
            jsonObject.put("contractCode", dto.getGeamContractCode());
            jsonObject.put("pamApplyStatus", dto.getEamStatus());
            jsonObject.put("pamInvoiceCode", dto.getApplyCode());
            jsonObject.put("currency", dto.getCurrencyCode());
            jsonObject.put("invoiceMoney", dto.getTotalTaxIncludedPrice() + "");
            jsonObject.put("payStage", dto.getPayStage());
            String url = this.GRAMURL + Constants.EAM_SYNCINVOICESTATUR;
            String requestStr = jsonObject.toJSONString();
            String resultData = HttpClientUtil.doPost(url, requestStr);
            JSONObject object = JSON.parseObject(resultData);
            //插入调用日志表
            eamPurchaseInvokeLogService.insertOldEamPurchaseLog(StrUtil.blankToDefault(requestStr, StrUtil.EMPTY), resultData, url);

            if (object == null) {
                return new PmsResult(Boolean.FALSE, EamInvokeEnums.SYNC_INVOICE_STATUS_FAIL.getName());
            }
            return new PmsResult(object.getBoolean("success"), object.getString("message"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return new PmsResult(Boolean.FALSE, EamInvokeEnums.SYNC_INVOICE_STATUS_FAIL.getName());
        }

    }

    /**
     * PAM-EAM-004 开票信息同步EAM
     *
     * @param modelKey  模块标识 TZHT：投资  ITCT：IT项目 HN：费用化  WS：完工验收
     * @param headerDto
     * @return
     */
    @Override
    public EsbResponse callGEAMPamSyncInvoice(String modelKey, InvoiceApplyHeaderDto headerDto) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        List<InvoiceReceivableDto> invoiceReceivableDtoList = headerDto.getInvoiceReceivableDtoList();
        if (ListUtils.isEmpty(invoiceReceivableDtoList)) {
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), "发票明细不能为空", headerRequest.getSerialNo());
        }
        try {
            GetSyncInvoice.PamPayApply pamPayApply = new GetSyncInvoice.PamPayApply();
            pamPayApply.setInvoiceCode(headerDto.getApplyCode()); // PAM开票单号
            pamPayApply.setPayStage(headerDto.getPayStage()); // 付款期数
            pamPayApply.setInvoiceMoney(headerDto.getTotalTaxIncludedPrice() != null ?
                    headerDto.getTotalTaxIncludedPrice().doubleValue() : 0.00); // 开票金额
            pamPayApply.setContractCode(headerDto.getGeamContractCode()); // 合同单号
            pamPayApply.setPoStatementNumber(headerDto.getPoStatementNumber()); // PO结算单编号
            pamPayApply.setSourceCode(headerRequest.getRequestId()); // 来源系统
            pamPayApply.setSeqId(headerDto.getSeqId()); // 关联交易号
            pamPayApply.setModelKey(modelKey); // 模块标识
            List<GetSyncInvoice.PamPayApply.PmsInvoiceInfo> pmsInvoiceInfo = new ArrayList<>();
            for (InvoiceReceivableDto receivableDto : invoiceReceivableDtoList) {
                GetSyncInvoice.PamPayApply.PmsInvoiceInfo invoiceInfo = new GetSyncInvoice.PamPayApply.PmsInvoiceInfo();
                invoiceInfo.setInvoiceCode(receivableDto.getInvoiceCode()); //发票号
                invoiceInfo.setBillType(receivableDto.getPlanInvoiceType() + ""); //发票类型
                invoiceInfo.setInvoiceMoney(receivableDto.getTaxIncludedPrice() != null ?
                        receivableDto.getTaxIncludedPrice().doubleValue() : 0.00); //发票金额（原币）
                invoiceInfo.setInvoiceMoneyNotax(receivableDto.getExclusiveOfTax() != null ?
                        receivableDto.getExclusiveOfTax().doubleValue() : 0.00); //不含税金额（原币）
                invoiceInfo.setTaxRate(receivableDto.getTaxRace() != null ? receivableDto.getTaxRace().doubleValue()
                        : 0.00); //税率%
                invoiceInfo.setTaxMoney(invoiceInfo.getInvoiceMoney() - invoiceInfo.getInvoiceMoneyNotax()); //税金（原币）
                invoiceInfo.setInvoiceDate(EsbCtcUtil.formatXMLGreDate(receivableDto.getInvoiceDate() == null ?
                        new Date() : receivableDto.getInvoiceDate())); //发票日期
                pmsInvoiceInfo.add(invoiceInfo);
            }
            pamPayApply.setPmsInvoiceInfo(pmsInvoiceInfo);
            GetSyncInvoiceResponse.ResponsData responsData = geamPamSyncInvoice.getSyncInvoice(pamPayApply,
                    headerRequest);
            return new EsbResponse(responsData.getResponseType(), responsData.getResponseCode(),
                    responsData.getResponseMessage(), responsData.getApplyCode());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }

    }

    @Override
    public String callGEAMPamSyncInvoice(InvoiceApplyHeaderDto dto) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        List<InvoiceReceivableDto> invoiceReceivableDtoList = dto.getInvoiceReceivableDtoList();
        if (ListUtils.isEmpty(invoiceReceivableDtoList)) {
            return "发票明细不能为空";
        }
        String requestStr = null;
        String result = null;
        try {
            GetSyncInvoice.PamPayApply pamPayApply = new GetSyncInvoice.PamPayApply();
            pamPayApply.setModelKey(dto.getModelkey()); // 模块标识
            pamPayApply.setInvoiceCode(dto.getApplyCode()); // PAM开票单号
            pamPayApply.setPayStage(dto.getPayStage()); // 付款期数
            pamPayApply.setInvoiceMoney(dto.getTotalTaxIncludedPrice() != null ?
                    dto.getTotalTaxIncludedPrice().doubleValue() : 0.00); // 开票金额
            pamPayApply.setContractCode(dto.getEamPurchaseCode()); // 合同单号
            pamPayApply.setPoStatementNumber(dto.getPoStatementNumber()); // PO结算单编号
            pamPayApply.setSourceCode(headerRequest.getRequestId()); // 来源系统
            pamPayApply.setSeqId(dto.getTransactionNumber()); // 关联交易号
            List<GetSyncInvoice.PamPayApply.PmsInvoiceInfo> pmsInvoiceInfo = new ArrayList<>();
            for (InvoiceReceivableDto receivableDto : invoiceReceivableDtoList) {
                GetSyncInvoice.PamPayApply.PmsInvoiceInfo invoiceInfo = new GetSyncInvoice.PamPayApply.PmsInvoiceInfo();
                invoiceInfo.setInvoiceCode(receivableDto.getExternalInvoiceNum()); //发票号
                invoiceInfo.setBillType(receivableDto.getPlanInvoiceType() == 1 ? "2" :
                        receivableDto.getPlanInvoiceType() + ""); //发票类型
                invoiceInfo.setInvoiceMoney(receivableDto.getTaxIncludedPrice() != null
                        ? receivableDto.getTaxIncludedPrice().doubleValue() : 0.00); //发票金额（原币）
                invoiceInfo.setInvoiceMoneyNotax(receivableDto.getExclusiveOfTax() != null
                        ? receivableDto.getExclusiveOfTax().doubleValue() : 0.00); //不含税金额（原币）
                invoiceInfo.setTaxRate(receivableDto.getTaxRace() != null
                        ? receivableDto.getTaxRace().doubleValue() : 0.00); //税率%
                invoiceInfo.setTaxMoney(invoiceInfo.getInvoiceMoney() - invoiceInfo.getInvoiceMoneyNotax()); //税金（原币）
                if (StringUtils.isNotBlank(receivableDto.getExternalInvoiceDate())) {
                    Date externalInvoiceDate = DateUtils.parse(receivableDto.getExternalInvoiceDate(), "yyyyMMdd");
                    invoiceInfo.setInvoiceDate(EsbCtcUtil.formatXMLGreDate(externalInvoiceDate)); //发票日期
                } else {
                    invoiceInfo.setInvoiceDate(EsbCtcUtil.formatXMLGreDate(new Date())); //发票日期
                }
                pmsInvoiceInfo.add(invoiceInfo);
            }
            pamPayApply.setPmsInvoiceInfo(pmsInvoiceInfo);
            requestStr = JSON.toJSONString(pamPayApply);
            GetSyncInvoiceResponse.ResponsData responsData = geamPamSyncInvoice.getSyncInvoice(pamPayApply, headerRequest);
            result = JSON.toJSONString(responsData);
            return responsData.getResponseMessage();
        } catch (Exception e) {
            result = e.getMessage();
            return result;
        } finally {
            //插入调用日志表
            eamPurchaseInvokeLogService.insertOldEamPurchaseLog(StrUtil.blankToDefault(requestStr, StrUtil.EMPTY), result, "http://www.midea.com/geam/GEAMPamSyncInvoice/v1/getSyncInvoice");
        }
    }

    /**
     * PAM-EAM-005 EAM付款申请状态同步接口
     *
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param pamInvoiceCode PAM开票单号
     * @param modelKey       模块标识
     * @return
     */
    @Override
    public EsbResponse callGEAMPmsPayStatus(String startDate, String endDate, String pamInvoiceCode, String modelKey) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        GetPmsPayStatus.PamApplySearch pamApplySearch = new GetPmsPayStatus.PamApplySearch();
        pamApplySearch.setStartDate(DateUtils.format(DateUtils.subtractDay(new Date(), -1), DateUtils.FORMAT_SHORT));
        pamApplySearch.setEndDate(DateUtils.format(new Date(), DateUtils.FORMAT_SHORT));
        // TZHT：投资  ITCT：IT项目 HN：费用化  WS：完工验收
        pamApplySearch.setModelKey("ITCT");
        if (StringUtils.isNotBlank(startDate)) {
            pamApplySearch.setStartDate(startDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            pamApplySearch.setEndDate(endDate);
        }
        if (StringUtils.isNotBlank(pamInvoiceCode)) {
            pamApplySearch.setPamInvoiceCode(pamInvoiceCode);
        }
        if (StringUtils.isNotBlank(modelKey)) {
            pamApplySearch.setModelKey(modelKey);
        }
        String requestStr = null;
        String result = null;
        try {
            requestStr = JSON.toJSONString(pamApplySearch);
            // 1.0esb接口弃用
//            GetPmsPayStatusResponse.ResponsData responsData = geamPmsPayStatus.getPmsPayStatus(pamApplySearch,
//                    headerRequest);
            GetPmsPayStatusResponse.ResponsData responsData = null;
            result = JSON.toJSONString(responsData);
            return new EsbResponse(responsData.getResponseType(), responsData.getResponseCode(),
                    responsData.getResponseMessage(), responsData.getPmsPayStatus());
        } catch (Exception e) {
            result = e.getMessage();
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        } finally {
            //插入调用日志表
            eamPurchaseInvokeLogService.insertOldEamPurchaseLog(StrUtil.blankToDefault(requestStr, StrUtil.EMPTY), result, "http://www.midea.com/geam/GEAMPmsPayStatus/v1/getPmsPayStatus");
        }
    }

    /**
     * PAM-EMS-008 预算调整变更写入
     */
    @Override
    public void callGemsAdjustApplyService() {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        CreateAdjustApply.EmsIoBmAdjustApplyH emsIoBmAdjustApplyH = new CreateAdjustApply.EmsIoBmAdjustApplyH();
        emsIoBmAdjustApplyH.setApplyByCode("dongci");
        emsIoBmAdjustApplyH.setApplyDate(EsbUtil.formatXMLGreDate(new Date()));
        emsIoBmAdjustApplyH.setReasonDesc("test");
        emsIoBmAdjustApplyH.setSourceSystem("PAM");
        emsIoBmAdjustApplyH.setSourceOrderType("BM");
        emsIoBmAdjustApplyH.setSourceOrderId("pam344103");
        emsIoBmAdjustApplyH.setSourceOrderCode("pam344103");
        emsIoBmAdjustApplyH.setSourceOrderUrl("TEST");
        emsIoBmAdjustApplyH.setBmAdjustType("BESIDE_BUDGET");
        emsIoBmAdjustApplyH.setIsDraft("N");
        CreateAdjustApply.EmsIoBmAdjustApplyH.EmsIoBmAdjustApplyL emsIoBmAdjustApplyL =
                new CreateAdjustApply.EmsIoBmAdjustApplyH.EmsIoBmAdjustApplyL();
        emsIoBmAdjustApplyL.setAdjustAmount(new BigDecimal(100));
        emsIoBmAdjustApplyL.setBudgetNodeId(19064425);
//		emsIoBmAdjustApplyL.setRemark();
        emsIoBmAdjustApplyL.setSourceOrderLineId("pam344104");
        emsIoBmAdjustApplyL.setSourceOrderId("pam344103");
        emsIoBmAdjustApplyL.setSourceOrderCode("pam344103");
        emsIoBmAdjustApplyL.setSourceSystem("PAM");
        emsIoBmAdjustApplyH.getEmsIoBmAdjustApplyL().add(emsIoBmAdjustApplyL);
        gemsAdjustApplyService.createAdjustApply(emsIoBmAdjustApplyH, headerRequest);
    }

    /**
     * PAM-GFP-001 预算项目号查询
     *
     * @return
     */
    @Override
    public List<BudgetItem> callFDPBudgetItemInfoService(Integer offset, Integer batchSize) {
        List<BudgetItem> items = new ArrayList<>();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bizSystem", EsbConstant.PAM_REQUEST_ID); // 系统编码
        jsonObject.put("securityCode", esbGfpProperties.getSecuritycode()); // 安全码
        //业务参数
        jsonObject.put("offset", offset + ""); // 默认从0开始
        jsonObject.put("limit", batchSize + ""); //每次多少条数据

        String url = this.FDPURL + Constants.FDP_BUDGET_ITEM;
        logger.info("预算项目号查询 请求url为:{}", url);
        logger.info("预算项目号查询 请求报文为:{}", JSON.toJSONString(jsonObject));
        String resultData = HttpClientUtil.doPost(url, jsonObject.toJSONString());
        JSONObject object = JSON.parseObject(resultData);
        logger.info("预算项目号查询 响应报文为:{}", JSON.toJSONString(object));
        if (object == null) {
            return items;
        }
        JSONArray dataArr = (JSONArray) object.get("bo");
        if (dataArr == null) {
            return items;
        }
        List<BudgetItemSysReturnDto> budgetItemSysReturnDtos = JSON.parseArray(dataArr.toJSONString(),
                BudgetItemSysReturnDto.class);
        for (BudgetItemSysReturnDto boResp : budgetItemSysReturnDtos) {
            BudgetItem budgetitem = new BudgetItem();
            budgetitem.setBudgetSystemCode(boResp.getBudgetSystemCode());
            budgetitem.setBudgetItemCode(boResp.getBudgetItemCode());
            budgetitem.setBudgetItemName(boResp.getBudgetItemName());
            budgetitem.setBudgetItemType(boResp.getBudgetItemType());
            budgetitem.setCategory(boResp.getCategory());
            budgetitem.setExpression(boResp.getExpression());
            budgetitem.setParentNode(boResp.getParentNode());
            budgetitem.setIsLeaf(boResp.getIsLeaf());
            budgetitem.setRemark(boResp.getRemark());
            budgetitem.setStatus(Integer.valueOf(boResp.getStatus()));
            budgetitem.setCreateAt(DateUtils.parse(DateUtils.format(new Date(boResp.getCreateTime()))));
            budgetitem.setCreateBy(Optional.ofNullable(CacheDataUtils.findUserByMip(boResp.getCreateBy())).map(UserInfo::getId).orElse(-1L));
            budgetitem.setUpdateAt(DateUtils.parse(DateUtils.format(new Date(boResp.getUpdateTime()))));
            budgetitem.setUpdateBy(Optional.ofNullable(CacheDataUtils.findUserByMip(boResp.getUpdateBy())).map(UserInfo::getId).orElse(-1L));
            budgetitem.setDeletedFlag(0);
            items.add(budgetitem);
        }
        return items;
    }

    /**
     * PAM-GFP-002 联行号查询
     *
     * @return
     */
    @Override
    public List<CnapsInfo> callFDPCnapsInfoService(Integer offset, String lastUpdateDate) {
        List<CnapsInfo> cnapsInfos = new ArrayList<>();
        // 记录接口日志
        EsbMassQueryRecord esbMassQueryRecord = new EsbMassQueryRecord();
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        try {
            CnapsqueryToSync.QueryCnapaData queryCnapaData = new CnapsqueryToSync.QueryCnapaData();
            queryCnapaData.setBizSystem(EsbConstant.PAM_REQUEST_ID);//PAM
            queryCnapaData.setSecurityCode(esbGfpProperties.getSecuritycode());
            queryCnapaData.setATTRIBUTE1(lastUpdateDate);//最后更新时间
            queryCnapaData.setOffset(offset);
            // 保存请求参数
            esbMassQueryRecord.setBusinessType("PAM-GFP-002");
            esbMassQueryRecord.setErpIpP01(lastUpdateDate);
            esbMassQueryRecord.setErpIpP02(offset != null ? offset + "" : null);
            esbMassQueryRecord.setStatus(1);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("bizSystem", EsbConstant.PAM_REQUEST_ID); // 系统编码
            jsonObject.put("securityCode", esbGfpProperties.getSecuritycode()); // 安全码
            jsonObject.put("version", "1.0.0"); // 版本号
            //业务参数
            jsonObject.put("lastTime", lastUpdateDate + ""); // 查询开始时间 格式为：yyyy-MM-dd HH:mm:ss
            jsonObject.put("offset", offset + ""); // 默认从0开始
            jsonObject.put("limit", 200 + ""); // 不传入时，每次最多返回2000条数据

            String url = this.FDPURL + Constants.FDP_CNAPS;
            logger.info("资金联行号同步 请求url为:{}", url);
            logger.info("资金联行号同步 请求报文为:{}", JSON.toJSONString(jsonObject));
            String resultData = HttpClientUtil.doPost(url, jsonObject.toJSONString());
            JSONObject object = JSON.parseObject(resultData);
            logger.info("资金联行号同步 响应报文为:{}", JSON.toJSONString(object));
            if (object == null) {
                return cnapsInfos;
            }
            JSONArray dataArr = (JSONArray) object.get("bo");
            if (dataArr == null) {
                return cnapsInfos;
            }
            List<CnapsInfoReturnDto> cnapsInfoList = JSON.parseArray(dataArr.toJSONString(),
                    CnapsInfoReturnDto.class);
            esbMassQueryRecord.setNum(cnapsInfoList != null ? Long.valueOf(cnapsInfoList.size()) : 0l);
            for (CnapsInfoReturnDto syncCnapsInfo : cnapsInfoList) {
                CnapsInfo cnapsInfo = new CnapsInfo();
                cnapsInfo.setBankCode(syncCnapsInfo.getBankCode());
                cnapsInfo.setBankName(syncCnapsInfo.getBankName());
                cnapsInfo.setCnapsCode(syncCnapsInfo.getCnapsCode());
                cnapsInfo.setOrgCode(syncCnapsInfo.getOrgCode());
                cnapsInfo.setBankTypeCode(syncCnapsInfo.getBankTypeCode());//总行代码
                cnapsInfo.setBankTypeName(syncCnapsInfo.getBankTypeName());//总行名称
                cnapsInfo.setBankAcceptanceBillApnCode(syncCnapsInfo.getBankAcceptanceBillApnCode());
                cnapsInfo.setBankAcceptanceBillApnName(syncCnapsInfo.getBankAcceptanceBillApnName());
                cnapsInfo.setCountryCode(syncCnapsInfo.getCountryCode());
                cnapsInfo.setCountryName(syncCnapsInfo.getCountryName());
                cnapsInfo.setProvincesCode(syncCnapsInfo.getProvincesCode());
                cnapsInfo.setProvince(syncCnapsInfo.getProvince());
                cnapsInfo.setCity(syncCnapsInfo.getCity());
                cnapsInfo.setCountry(syncCnapsInfo.getCountry());
                cnapsInfo.setAddress(syncCnapsInfo.getAddress());
                cnapsInfo.setParentCnaps(syncCnapsInfo.getParentCnaps());
                cnapsInfo.setParentCnapsName(syncCnapsInfo.getParentCnapsName());
                cnapsInfo.setStatus(Integer.valueOf(syncCnapsInfo.getStatus()));
                cnapsInfo.setCreateAt(DateUtils.parse(DateUtils.format(new Date(syncCnapsInfo.getCreateTime()))));
                cnapsInfo.setCreateBy(Optional.ofNullable(CacheDataUtils.findUserByMip(syncCnapsInfo.getCreateBy())).map(UserInfo::getId).orElse(-1L));
                cnapsInfo.setUpdateAt(DateUtils.parse(DateUtils.format(new Date(syncCnapsInfo.getUpdateTime()))));
                cnapsInfo.setUpdateBy(Optional.ofNullable(CacheDataUtils.findUserByMip(syncCnapsInfo.getUpdateBy())).map(UserInfo::getId).orElse(-1L));
                cnapsInfo.setDeletedFlag(0);
                cnapsInfos.add(cnapsInfo);
            }
        } catch (Exception e) {
            LOGGER.info("ERP通用查询异常, 流水号:{}", headerRequest.getSerialNo(), e);
            esbMassQueryRecord.setStatus(2);
            esbMassQueryRecord.setNum(0L);
        } finally {
            esbMassQueryRecord.setEsbSerialNo(headerRequest.getSerialNo());
            esbMassQueryRecordFeignClient.add(esbMassQueryRecord);
        }
        return cnapsInfos;
    }

    /**
     * PAM-GFP-003 预付款申请
     */
    @Override
    public void callGcebAdvancePaymentApply() {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headOut = new Holder<ReplyInformation>();
        AdvancepaymentApply input = new AdvancepaymentApply();
        AdvancepaymentApply.ReqData reqData = new AdvancepaymentApply.ReqData();
        reqData.setBusinesstype("05");
        reqData.setItemrecnum(1);
        reqData.setImportdate(EsbUtil.formatXMLGreDate(new Date()));
        reqData.setExportdate(EsbUtil.formatXMLGreDate(new Date()));
        reqData.setTranscode(headerRequest.getSerialNo());
        reqData.setSourcesystemcode("PAM");
        reqData.setRecivecode("ESCM");
        reqData.setApplyno("YF2019041202");
//        reqData.setContractno();
        reqData.setSupplierid("1962882");
        reqData.setSuppliername("中山大洋电机股份有限公司");
        reqData.setOuid("3081");
        reqData.setPaytype("ADVANCE");
        reqData.setPayamount(new BigDecimal(123));
        reqData.setCurrency("CNY");
        reqData.setPaymethod("MD_CD");
        reqData.setRecaccountname("中山大洋电机股份有限公司");
        reqData.setRecaccountno("2011028019200043115");
        reqData.setRecbankname("中国工商银行广东省分行清算中心（不办理对外业务）");
        reqData.setProvince("广东省");
        reqData.setCity("中山市");
        reqData.setBankcode("************");
        reqData.setSupplieraddress("1962882");
        reqData.setBudgetcode("0401");
        reqData.setRemark("PAM预付款测试数据，现汇");
        reqData.setCountry("CN");
        input.setReqData(reqData);
        gcebToPeripheralInterfaceService.advancepaymentApply(input, headerRequest, headOut);
    }

    /**
     * PAM-GFP-006 收款认领撤销
     */
    @Override
    public void callGcebCancelTheClaim(ReceiptClaimDto dto) {
        Integer status = null;
        String msg = StringUtil.EMPTY_STRING;
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        CancelTheClaim input = new CancelTheClaim();
        CancelTheClaim.ReqData reqData = new CancelTheClaim.ReqData();
        reqData.setBusinesstype("03");
        reqData.setItemrecnum(1);
        reqData.setImportdate(EsbUtil.formatXMLGreDate(new Date()));
        reqData.setExportdate(EsbUtil.formatXMLGreDate(new Date()));
        reqData.setTranscode(headerRequest.getSerialNo());
        reqData.setSourcesystemcode(headerRequest.getRequestId());
        reqData.setGtmscode(dto.getCashReceiptCode());
        reqData.setOuid(dto.getOuId().intValue());
        reqData.setStatus("1"); //todo
        reqData.setRemark(dto.getRemark());
        input.setReqData(reqData);
        try {
            gcebToPeripheralInterfaceService.cancelTheClaim(input, headerRequest, headerReply);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            status = ReceiptClaimEnum.GCEB_REVERSE_FAILED.getCode();//撤回失败
            msg = "PAM-GFP-006接口异常";
        }
        //返回结果处理
        if (!ObjectUtils.isEmpty(headerReply.value)) {
            if (EsbConstant.SUCCESS_CODE.equals(headerReply.value.getResponseCode())) {
                status = ReceiptClaimEnum.GCEB_REVERSED.getCode();//成功
            } else {
                msg = headerReply.value.getResponseCode() + headerReply.value.getResponseMessage();
                status = ReceiptClaimEnum.GCEB_REVERSE_FAILED.getCode();//撤回失败
            }
        }
        dto.setCashStatus(status);
        dto.setAttribute6(msg);
    }

    /**
     * PAM-GFP-008 退款申请单导入
     */
    @Override
    public void callGcebDrawbackApply() {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        DrawbackApply input = new DrawbackApply();
        DrawbackApply.ReqData reqData = new DrawbackApply.ReqData();
        reqData.setBusinesstype(EsbConstant.PAM_REQUEST_ID);
        reqData.setSourcesystemcode(EsbConstant.PAM_REQUEST_ID);
        reqData.setImportdate(DateUtils.format(new Date()));
        reqData.setExportdate(DateUtils.format(new Date()));
        reqData.setTranscode(headerRequest.getSerialNo());

        DrawbackApply.ReqData.Details details = new DrawbackApply.ReqData.Details();
        details.setRecivecode(headerRequest.getSerialNo());
        details.setCustomercode("E0110001");
        details.setCustomername("安得智联科技股份有限公司");
        details.setOuid("3081");
        details.setAmount(new BigDecimal(12));
        details.setCurrcy("CNY");
        details.setReciveaccount("2011028019200043115");
        details.setRecivebank("中国工商银行广东省分行清算中心（不办理对外业务）");
        details.setReciveopenbankno("************");
        details.setPaytype("ADVANCE");
        details.setBudgetitmecode("0401");
        reqData.getDetails().add(details);
        input.setReqData(reqData);
        Holder<ReplyInformation> headOut = new Holder<ReplyInformation>();
        gcebToPeripheralInterfaceService.drawbackApply(input, headerRequest, headOut);
    }

    @Override
    public void callGcebDrawbackApplyCancel() {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        DrawbackApplyCancel.ReqData reqData = new DrawbackApplyCancel.ReqData();
        reqData.setBusinesstype(EsbConstant.PAM_REQUEST_ID);
        reqData.setSourcesystemcode(EsbConstant.PAM_REQUEST_ID);
        reqData.setImportdate(DateUtils.format(new Date()));
        reqData.setExportdate(DateUtils.format(new Date()));
        reqData.setTranscode(headerRequest.getSerialNo());

        DrawbackApplyCancel.ReqData.Details details = new DrawbackApplyCancel.ReqData.Details();
        details.setRecivecode("PAM1555052656009");
        details.setOuid("3081");
        details.setStatus("12");
        details.setCancelreason("退款申请作废");
        reqData.getDetails().add(details);

        gcebToPeripheralInterfaceService.drawbackApplyCancel(reqData, headerRequest);
    }

    /**
     * PAM-ERP-018-1 结转收入发票写入
     * PAM-ERP-018-2 应收发票写入
     * PAM-ERP-018-3 退款申请发票入账
     * PAM-ERP-018-4 应收发票金税导入覆盖作废写入
     *
     * @param dtoList
     * @return
     */
    @Override
    public EsbResponse callCUXARINVOICEAPIPKGPortTypeBatch(List<InvoiceReceivableErpDto> dtoList, String voucherType) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_invoice_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getCuxArInvoiceApiPkgSOAHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_invoice_api_pkg.do_import.InputParameters param = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_invoice_api_pkg.do_import.InputParameters();
        Holder<ReplyInformation> headerReply = new Holder<>();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_invoice_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_invoice_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_invoice_api_pkg.do_import.ObjectFactory();

        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_invoice_api_pkg.do_import.APPSCUXARINVOICETBL appscuxarinvoicetbl = objFactory.createAPPSCUXARINVOICETBL();
        for (InvoiceReceivableErpDto dto : dtoList) {
            com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_invoice_api_pkg.do_import.APPSCUXARINVOICEREC importItem = objFactory.createAPPSCUXARINVOICEREC();
            importItem.setESBDATASOURCE(objFactory.createAPPSCUXARINVOICERECESBDATASOURCE(dto.getEsbDataSource()));
            //ESB数据来源
            importItem.setSOURCECODE(objFactory.createAPPSCUXARINVOICERECSOURCECODE(headerRequest.getRequestId()));
            //来源系统PAM
            importItem.setSOURCENUM(objFactory.createAPPSCUXARINVOICERECSOURCENUM(headerRequest.getSerialNo() + dto.getLineNumber() + "_" + dto.getId()));//来源编号
            importItem.setSOURCELINENUM(objFactory.createAPPSCUXARINVOICERECSOURCELINENUM(dto.getSourceLineNum()));
            //来源行编号
            importItem.setESBSERIALNUM(objFactory.createAPPSCUXARINVOICERECESBSERIALNUM(headerRequest.getSerialNo()));//ESB流水号

            importItem.setORGID(objFactory.createAPPSCUXARINVOICERECORGID(dto.getOrgId()));//业务实体ID
            importItem.setBATCHSOURCENAME(objFactory.createAPPSCUXARINVOICERECBATCHSOURCENAME(dto.getBatchSourceName()));//事务来源名称
            importItem.setCUSTOMERNUMBER(objFactory.createAPPSCUXARINVOICERECCUSTOMERNUMBER(dto.getCustomerNumber()));//客户编码
            importItem.setCUSTTRXTYPENAME(objFactory.createAPPSCUXARINVOICERECCUSTTRXTYPENAME(dto.getCustTrxTypeName()));//事务处理类型名称
            importItem.setTERMNAME(objFactory.createAPPSCUXARINVOICERECTERMNAME(dto.getTermName()));//付款条件
            importItem.setTRXDATE(objFactory.createAPPSCUXARINVOICERECTRXDATE(dto.getTrxDate()));//事务处理日期

            importItem.setGLDATE(objFactory.createAPPSCUXARINVOICERECGLDATE(dto.getGlDate()));//总账日期
            importItem.setTRXNUMBER(objFactory.createAPPSCUXARINVOICERECTRXNUMBER(dto.getTrxNumber()));//事务处理编号
            importItem.setLINENUMBER(objFactory.createAPPSCUXARINVOICERECLINENUMBER(dto.getLineNumber()));//事务处理行号
            importItem.setSALESORDER(objFactory.createAPPSCUXARINVOICERECSALESORDER(dto.getSalesOrder()));//销售订单号
            importItem.setSALESORDERLINE(objFactory.createAPPSCUXARINVOICERECSALESORDERLINE(dto.getSalesOrderLine()));//销售订单行
            importItem.setSALESREPNUMBER(objFactory.createAPPSCUXARINVOICERECSALESREPNUMBER(dto.getSalesrepNumber()));//销售人员
            importItem.setCURRENCYCODE(objFactory.createAPPSCUXARINVOICERECCURRENCYCODE(dto.getCurrencyCode()));//币种
            importItem.setCONVERSIONTYPE(objFactory.createAPPSCUXARINVOICERECCONVERSIONTYPE(dto.getConversionType()));//汇率类型
            importItem.setCONVERSIONDATE(objFactory.createAPPSCUXARINVOICERECCONVERSIONDATE(dto.getConversionDate()));//汇率时间
            importItem.setCONVERSIONRATE(objFactory.createAPPSCUXARINVOICERECCONVERSIONRATE(dto.getConversionRate()));//汇率

            importItem.setAMOUNT(objFactory.createAPPSCUXARINVOICERECAMOUNT(dto.getAmount()));//金额
            importItem.setQUANTITY(objFactory.createAPPSCUXARINVOICERECQUANTITY(dto.getQuantity()));//数量
            importItem.setQUANTITYORDERED(objFactory.createAPPSCUXARINVOICERECQUANTITYORDERED(dto.getQuantityOrdered()));//订单数量
            importItem.setUNITSELLINGPRICE(objFactory.createAPPSCUXARINVOICERECUNITSELLINGPRICE(dto.getUnitSellingPrice()));//单价
            importItem.setUNITSTANDARDPRICE(objFactory.createAPPSCUXARINVOICERECUNITSTANDARDPRICE(dto.getUnitStandardPrice()));//
            importItem.setTAXRATECODE(objFactory.createAPPSCUXARINVOICERECTAXRATECODE(dto.getTaxRateCode()));//税码
            importItem.setINVENTORYITEMCODE(objFactory.createAPPSCUXARINVOICERECINVENTORYITEMCODE(dto.getInventoryItemCode()));//物料编码
            importItem.setUOMCODE(objFactory.createAPPSCUXARINVOICERECUOMCODE(dto.getUomCode()));//单位
            importItem.setLINETYPE(objFactory.createAPPSCUXARINVOICERECLINETYPE(dto.getLineType()));//行类型
            // 根据备注长度截取,只取前220个字符
            if (StringUtils.isNotBlank(dto.getComments()) && dto.getComments().getBytes(Charset.forName("UTF-8")).length > 220) {
                importItem.setCOMMENTS(objFactory.createAPPSCUXARINVOICERECCOMMENTS
                        (com.midea.pam.common.util.StringUtils.substringByByte(dto.getComments(), 220)));//备注
            } else {
                importItem.setCOMMENTS(objFactory.createAPPSCUXARINVOICERECCOMMENTS(dto.getComments()));//备注
            }
            importItem.setDESCRIPTION(objFactory.createAPPSCUXARINVOICERECDESCRIPTION(com.midea.pam.common.util.StringUtils.filterControlCharacters(dto.getDescription())));//摘要
            importItem.setATTRIBUTE11(objFactory.createAPPSCUXARINVOICERECATTRIBUTE11(dto.getAttribute11()));//中转交联号
            importItem.setATTRIBUTE12(objFactory.createAPPSCUXARINVOICERECATTRIBUTE12(dto.getAttribute12()));//收入科目
            importItem.setHEADERATTRIBUTE6(objFactory.createAPPSCUXARINVOICERECHEADERATTRIBUTE6(dto.getHeaderAttribute6()));//应收发票到期日
            importItem.setHEADERATTRIBUTE3(objFactory.createAPPSCUXARINVOICERECHEADERATTRIBUTE3(voucherType));//凭证类型
            importItem.setHEADERATTRIBUTE11(objFactory.createAPPSCUXARINVOICERECHEADERATTRIBUTE11(dto.getHeaderAttribute11()));//中转关联号
            String headerattribute1 = "";
            if (StringUtils.isNotBlank(dto.getHeaderAttribute1())) {
                headerattribute1 += dto.getHeaderAttribute1();
            }
            if (StringUtils.isNotBlank(dto.getContractCode())) {
                headerattribute1 += "\\" + dto.getContractCode();//PAM销售子合同号
            }
            importItem.setHEADERATTRIBUTE1(objFactory.createAPPSCUXARINVOICERECHEADERATTRIBUTE1(headerattribute1));
            // 税控信息
            if (StringUtils.isNotEmpty(dto.getLineType())) {
                importItem.setLINETYPE(objFactory.createAPPSCUXARINVOICERECLINETYPE(dto.getLineType()));
                importItem.setHEADERATTRIBUTE7(objFactory.createAPPSCUXARINVOICERECHEADERATTRIBUTE7(dto.getHeaderAttribute7()));// 增值税发票号，2021-03-03新增：external_invoice_num
                importItem.setHEADERATTRIBUTE8(objFactory.createAPPSCUXARINVOICERECHEADERATTRIBUTE8(dto.getHeaderAttribute8()));// 发票代码，2021-03-03新增：external_invoice_code
                importItem.setHEADERATTRIBUTE9(objFactory.createAPPSCUXARINVOICERECHEADERATTRIBUTE9(dto.getHeaderAttribute9()));// 购方名称，2021-03-03新增：invoice_customer
                importItem.setHEADERATTRIBUTE10(objFactory.createAPPSCUXARINVOICERECHEADERATTRIBUTE10(dto.getHeaderAttribute10()));// 税额，必填，20210524 ADDED by yinzj1
                importItem.setHEADERATTRIBUTE12(objFactory.createAPPSCUXARINVOICERECHEADERATTRIBUTE12(dto.getHeaderAttribute12()));// 税票金额（含税），必填，20210524 ADDED by yinzj1
                // 失效之前导入的税控发票号，输入增值税发票号和发票代码，输入失效标识’C’
                importItem.setHEADERATTRIBUTE13(objFactory.createAPPSCUXARINVOICERECHEADERATTRIBUTE13(dto.getHeaderAttribute13()));
                importItem.setHEADERATTRIBUTE14(objFactory.createAPPSCUXARINVOICERECHEADERATTRIBUTE14(dto.getHeaderAttribute14()));// 开票人，2021-01-27新增：external_invoice_user
            }
            appscuxarinvoicetbl.getPIFACELINESTBLITEM().add(importItem);
        }
        JAXBElement<APPSCUXARINVOICETBL> jeApsCuxInvitemtbl =
                objFactory.createInputParametersPIFACELINESTBL(appscuxarinvoicetbl);
        param.setPIFACELINESTBL(jeApsCuxInvitemtbl);
        try {
            cuxarinvoiceapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (Exception e) {
            LOGGER.error("应收发票写入报错", e);
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-019 收款写入
     */
    @Override
    public EsbResponse callCUXARRECEIPTAPIPKGReceiptService(List<ReceiptClaimDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.receipt.OutputParameters> outParam = new javax.xml.ws.Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getReceiptApiSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.receipt.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.receipt.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.receipt.InputParameters param =
                objFactory.createInputParameters();

        APPSCUXARRECEIPTTBL appscuxarreceipttbl = objFactory.createAPPSCUXARRECEIPTTBL();
        for (ReceiptClaimDto dto : dtoList) {
            APPSCUXARRECEIPTREC appscuxarreceiptrec = objFactory.createAPPSCUXARRECEIPTREC();
            //收款信息
            appscuxarreceiptrec.setESBDATASOURCE(objFactory.createAPPSCUXARRECEIPTRECESBDATASOURCE(BusinessTypeEnums.RECEIPT_CLAIM.getCode()));
            appscuxarreceiptrec.setSOURCECODE(objFactory.createAPPSCUXARRECEIPTRECSOURCECODE(headerRequest.getRequestId()));//来源系统
            appscuxarreceiptrec.setATTRIBUTE2(objFactory.createAPPSCUXARRECEIPTRECATTRIBUTE2(headerRequest.getRequestId()));//收款来源
            appscuxarreceiptrec.setATTRIBUTE10(objFactory.createAPPSCUXARRECEIPTRECATTRIBUTE10(dto.getConntransCode()));//关联交易号
            appscuxarreceiptrec.setATTRIBUTE11(objFactory.createAPPSCUXARRECEIPTRECATTRIBUTE11(dto.getTransferCode()));//中转关联号
            appscuxarreceiptrec.setATTRIBUTE9(objFactory.createAPPSCUXARRECEIPTRECATTRIBUTE9(dto.getSerialNumber()));//出纳流水号
            appscuxarreceiptrec.setATTRIBUTE4(objFactory.createAPPSCUXARRECEIPTRECATTRIBUTE4(dto.getBudgetItemCode()));//预算项目号
            appscuxarreceiptrec.setSOURCENUM(objFactory.createAPPSCUXARRECEIPTRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));
            appscuxarreceiptrec.setESBSERIALNUM(objFactory.createAPPSCUXARRECEIPTRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxarreceiptrec.setCALLINGAPI(objFactory.createAPPSCUXARRECEIPTRECCALLINGAPI(dto.getCallingApi()));
            appscuxarreceiptrec.setORGID(objFactory.createAPPSCUXARRECEIPTRECORGID(new BigDecimal(dto.getOuId())));
            appscuxarreceiptrec.setRECEIPTNUMBER(objFactory.createAPPSCUXARRECEIPTRECRECEIPTNUMBER(dto.getReceiptCode()));
            appscuxarreceiptrec.setRECEIPTDATE(objFactory.createAPPSCUXARRECEIPTRECRECEIPTDATE(DateUtils.format(dto.getPayDate(), DateUtils.FORMAT_SHORT)));//收款日期
            appscuxarreceiptrec.setGLDATE(objFactory.createAPPSCUXARRECEIPTRECGLDATE(DateUtils.format(dto.getAccountingDate(), DateUtils.FORMAT_SHORT)));//总帐日期
            appscuxarreceiptrec.setCURRENCYCODE(objFactory.createAPPSCUXARRECEIPTRECCURRENCYCODE(dto.getCurrencyCode()));
            // 根据销售子合同编号长度截取,只取前140个字符
            appscuxarreceiptrec.setATTRIBUTE3(objFactory.createAPPSCUXARRECEIPTRECATTRIBUTE3(
                    (com.midea.pam.common.util.StringUtils.substringByByte(dto.getAttribute3(), 140))));//销售子合同编号
            appscuxarreceiptrec.setAMOUNT(objFactory.createAPPSCUXARRECEIPTRECAMOUNT(dto.getClaimAmount()));
            appscuxarreceiptrec.setCUSTOMERNUMBER(objFactory.createAPPSCUXARRECEIPTRECCUSTOMERNUMBER(dto.getCustomerCode()));
            appscuxarreceiptrec.setRECEIPTMETHODID(objFactory.createAPPSCUXARRECEIPTRECRECEIPTMETHODID(BigDecimalUtils.stringToBigDecimal(dto.getRecMethod())));
            appscuxarreceiptrec.setREMITTANCEBANKACCOUNTNUM(objFactory.createAPPSCUXARRECEIPTRECREMITTANCEBANKACCOUNTNUM(dto.getRecAccountNo()));
            appscuxarreceiptrec.setRECEIVABLESTRXID(objFactory.createAPPSCUXARRECEIPTRECRECEIVABLESTRXID(BigDecimalUtils.longToBigDecimal(dto.getReceivablesTrxId())));//收款活动ID
            // 根据备注长度截取,只取前220个字符
            appscuxarreceiptrec.setCOMMENTS(objFactory.createAPPSCUXARRECEIPTRECCOMMENTS(
                    (com.midea.pam.common.util.StringUtils.substringByByte(dto.getRemark(), 220))));//备注
            appscuxarreceiptrec.setATTRIBUTE5(objFactory.createAPPSCUXARRECEIPTRECATTRIBUTE5("\\" + (
                    ObjectUtils.isEmpty(dto.getCommission()) ? "0" : dto.getCommission().toString())));

            appscuxarreceipttbl.getPIFACETBLITEM().add(appscuxarreceiptrec);
        }
        param.setPIFACETBL(objFactory.createInputParametersPIFACETBL(appscuxarreceipttbl));
        try {
            EsbResponse esbResponse = new EsbResponse();
            cuxarreceiptapipkgPortType.receipt(headerRequest, soaHeader, param, headerReply, outParam);
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (Exception e) {
            LOGGER.error("PAM-ERP-019收款写入异常", e);
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-070 PAM收款合同分配变更同步ERP
     */
    @Override
    public EsbResponse callCUXARRECEIPTAPIPKGReceiptChangeService(List<ReceiptClaimDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.receipt.OutputParameters> outParam = new javax.xml.ws.Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getReceiptApiSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.receipt.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.receipt.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.receipt.InputParameters param =
                objFactory.createInputParameters();

        APPSCUXARRECEIPTTBL appscuxarreceipttbl = objFactory.createAPPSCUXARRECEIPTTBL();
        for (ReceiptClaimDto dto : dtoList) {
            APPSCUXARRECEIPTREC appscuxarreceiptrec = objFactory.createAPPSCUXARRECEIPTREC();
            //收款信息
            appscuxarreceiptrec.setSOURCECODE(objFactory.createAPPSCUXARRECEIPTRECSOURCECODE(headerRequest.getRequestId()));//来源系统
            appscuxarreceiptrec.setSOURCENUM(objFactory.createAPPSCUXARRECEIPTRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));
            appscuxarreceiptrec.setESBSERIALNUM(objFactory.createAPPSCUXARRECEIPTRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxarreceiptrec.setESBDATASOURCE(objFactory.createAPPSCUXARRECEIPTRECESBDATASOURCE(BusinessTypeEnums.RECEIPT_CLAIM_CHANGE.getCode()));
            appscuxarreceiptrec.setORGID(objFactory.createAPPSCUXARRECEIPTRECORGID(new BigDecimal(dto.getOuId())));
            appscuxarreceiptrec.setRECEIPTNUMBER(objFactory.createAPPSCUXARRECEIPTRECRECEIPTNUMBER(dto.getReceiptCode()));
            // 根据销售子合同编号长度截取,只取前140个字符
            appscuxarreceiptrec.setATTRIBUTE3(objFactory.createAPPSCUXARRECEIPTRECATTRIBUTE3(
                    (com.midea.pam.common.util.StringUtils.substringByByte(dto.getAttribute3(), 140))));//销售子合同编号
            appscuxarreceipttbl.getPIFACETBLITEM().add(appscuxarreceiptrec);
        }
        param.setPIFACETBL(objFactory.createInputParametersPIFACETBL(appscuxarreceipttbl));
        try {
            EsbResponse esbResponse = new EsbResponse();
            cuxarreceiptapipkgPortType.receipt(headerRequest, soaHeader, param, headerReply, outParam);
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (Exception e) {
            LOGGER.error("PAM-ERP-070 PAM收款合同分配变更同步ERP", e);
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-020 收款冲销
     */
    @Override
    public EsbResponse callCUXARRECEIPTAPIPKGReverseService(List<ReceiptClaimDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.reverse.OutputParameters> outParam = new javax.xml.ws.Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getReceiptApiSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.reverse.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.reverse.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_receipt_api_pkg.reverse.InputParameters param =
                objFactory.createInputParameters();

        APPSCUXARRECEIPTREVERSETBL appscuxarreceiptreversetbl = objFactory.createAPPSCUXARRECEIPTREVERSETBL();
        for (ReceiptClaimDto dto : dtoList) {
            APPSCUXARRECEIPTREVERSEREC appscuxarreceiptreverserec = objFactory.createAPPSCUXARRECEIPTREVERSEREC();
            //撤款信息
            appscuxarreceiptreverserec.setESBDATASOURCE(objFactory.createAPPSCUXARRECEIPTREVERSERECESBDATASOURCE(BusinessTypeEnums.RECEIPT_CLAIM_REVERSAL.getCode()));
            appscuxarreceiptreverserec.setSOURCECODE(objFactory.createAPPSCUXARRECEIPTREVERSERECSOURCECODE(headerRequest.getRequestId()));
            appscuxarreceiptreverserec.setSOURCENUM(objFactory.createAPPSCUXARRECEIPTREVERSERECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));
            appscuxarreceiptreverserec.setESBSERIALNUM(objFactory.createAPPSCUXARRECEIPTREVERSERECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxarreceiptreverserec.setORGID(objFactory.createAPPSCUXARRECEIPTREVERSERECORGID(new BigDecimal(dto.getOuId())));
            appscuxarreceiptreverserec.setRECEIPTNUMBER(objFactory.createAPPSCUXARRECEIPTREVERSERECRECEIPTNUMBER(dto.getReceiptCode()));
            appscuxarreceiptreverserec.setREVERSALGLDATE(objFactory.createAPPSCUXARRECEIPTREVERSERECREVERSALGLDATE(DateUtils.format(dto.getErpReversalDate(), DateUtils.FORMAT_SHORT)));//冲销总帐日期
            appscuxarreceiptreverserec.setREVERSALDATE(objFactory.createAPPSCUXARRECEIPTREVERSERECREVERSALDATE(DateUtils.format(dto.getErpReversalDate(), DateUtils.FORMAT_SHORT)));//冲销日期
            appscuxarreceiptreverserec.setREVERSALCATEGORYNAME(objFactory.createAPPSCUXARRECEIPTREVERSERECREVERSALCATEGORYNAME("冲销付款"));//冲销类别-必填，“冲销付款”
            appscuxarreceiptreverserec.setREVERSALREASONNAME(objFactory.createAPPSCUXARRECEIPTREVERSERECREVERSALREASONNAME("冲销付款"));//冲销原因-必填，“冲销付款”
            appscuxarreceiptreverserec.setREVERSALCOMMENTS(objFactory.createAPPSCUXARRECEIPTREVERSERECREVERSALCOMMENTS(dto.getRemark()));//备注
            appscuxarreceiptreversetbl.getPIFACETBLITEM().add(appscuxarreceiptreverserec);
        }
        param.setPIFACETBL(objFactory.createInputParametersPIFACETBL(appscuxarreceiptreversetbl));
        try {
            EsbResponse esbResponse = new EsbResponse();
            cuxarreceiptapipkgPortType.reverse(headerRequest, soaHeader, param, headerReply, outParam);
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (Exception e) {
            LOGGER.error("PAM-ERP-020收款冲销接口异常", e);
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-021 收款核销
     */
    @Override
    public EsbResponse callCUXARAPPLYAPIPKGPortTypeService(List<WriteOffDto> writeOffDtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_apply_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getArApplySoaHeader();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_apply_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_apply_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_apply_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_apply_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();

        APPSCUXARAPPLYTBL appscuxarapplytbl = objFactory.createAPPSCUXARAPPLYTBL();
        for (WriteOffDto writeOffDto : writeOffDtoList) {
            for (ReceiptClaimInvoiceRelDto dto : writeOffDto.getWriteOffInvoiceList()) {
                APPSCUXARAPPLYREC appscuxarapplyrec = objFactory.createAPPSCUXARAPPLYREC();
                appscuxarapplyrec.setESBDATASOURCE(objFactory.createAPPSCUXARAPPLYRECESBDATASOURCE("PAM-ERP-021"));
                appscuxarapplyrec.setSOURCECODE(objFactory.createAPPSCUXARAPPLYRECSOURCECODE(headerRequest.getRequestId()));
                appscuxarapplyrec.setSOURCENUM(objFactory.createAPPSCUXARAPPLYRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));
                appscuxarapplyrec.setESBSERIALNUM(objFactory.createAPPSCUXARAPPLYRECESBSERIALNUM(headerRequest.getSerialNo()));
                appscuxarapplyrec.setCUSTOMERNUMBER(objFactory.createAPPSCUXARAPPLYRECCUSTOMERNUMBER(writeOffDto.getCustomerCode()));
                appscuxarapplyrec.setTRXNUMBER(objFactory.createAPPSCUXARAPPLYRECTRXNUMBER(dto.getInvoiceCode()));
                appscuxarapplyrec.setRECEIPTNUMBER(objFactory.createAPPSCUXARAPPLYRECRECEIPTNUMBER(writeOffDto.getReceiptCode()));
                appscuxarapplyrec.setAPPLIEDAMOUNT(objFactory.createAPPSCUXARAPPLYRECAPPLIEDAMOUNT(dto.getWriteOffAmount()));//本次发票核销金额
                //如果收款与事务处理使用相同币种，请不要输入交叉汇率
                if (!Objects.equals(dto.getCurrencyCode(), dto.getRcCurrencyCode())) {
                    appscuxarapplyrec.setUNAPPLIEDAMOUNT(objFactory.createAPPSCUXARAPPLYRECUNAPPLIEDAMOUNT(dto.getReceiptWriteOffAmount()));//本次回款核销金额
                }
                appscuxarapplyrec.setAPPLIEDDATE(objFactory.createAPPSCUXARAPPLYRECAPPLIEDDATE(DateUtils.format(new Date(), DateUtils.FORMAT_SHORT)));
                appscuxarapplyrec.setAPPLIEDGLDATE(objFactory.createAPPSCUXARAPPLYRECAPPLIEDGLDATE(DateUtils.format(dto.getGlDate(), DateUtils.FORMAT_SHORT)));
                appscuxarapplyrec.setORGID(objFactory.createAPPSCUXARAPPLYRECORGID(BigDecimalUtils.longToBigDecimal(writeOffDto.getOuId())));
                appscuxarapplytbl.getPAPPLICATIONTBLITEM().add(appscuxarapplyrec);
            }
        }
        param.setPAPPLICATIONTBL(objFactory.createInputParametersPAPPLICATIONTBL(appscuxarapplytbl));
        EsbResponse esbResponse = new EsbResponse();
        try {
            cuxarapplyapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
        } catch (Exception e) {
            LOGGER.error("收款核销写入", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
        return esbResponse;
    }

    /**
     * PAM-ERP-022 收款核销冲销
     */
    @Override
    public EsbResponse callCUXARUNAPPLYAPIPKGPortTypeService(List<WriteOffDto> writeOffDtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_unapply_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getArUnapplySoaHeader();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_unapply_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_unapply_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_unapply_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_unapply_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();

        APPSCUXARUNAPPLYTBL appscuxarapplytbl = objFactory.createAPPSCUXARUNAPPLYTBL();
        for (WriteOffDto writeOffDto : writeOffDtoList) {
            for (ReceiptClaimInvoiceRelDto dto : writeOffDto.getWriteOffInvoiceList()) {
                APPSCUXARUNAPPLYREC appscuxarunapplyrec = objFactory.createAPPSCUXARUNAPPLYREC();
                appscuxarunapplyrec.setESBDATASOURCE(objFactory.createAPPSCUXARUNAPPLYRECESBDATASOURCE("PAM-ERP-022"));
                appscuxarunapplyrec.setSOURCECODE(objFactory.createAPPSCUXARUNAPPLYRECSOURCECODE(headerRequest.getRequestId()));
                appscuxarunapplyrec.setSOURCENUM(objFactory.createAPPSCUXARUNAPPLYRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));
                appscuxarunapplyrec.setSOURCELINENUM(objFactory.createAPPSCUXARUNAPPLYRECSOURCELINENUM(""));
                appscuxarunapplyrec.setESBSERIALNUM(objFactory.createAPPSCUXARUNAPPLYRECESBSERIALNUM(headerRequest.getSerialNo()));
                appscuxarunapplyrec.setORGID(objFactory.createAPPSCUXARUNAPPLYRECORGID(BigDecimalUtils.longToBigDecimal(writeOffDto.getOuId())));
                appscuxarunapplyrec.setCUSTOMERNUMBER(objFactory.createAPPSCUXARUNAPPLYRECCUSTOMERNUMBER(writeOffDto.getCustomerCode()));
                appscuxarunapplyrec.setRECEIPTNUMBER(objFactory.createAPPSCUXARUNAPPLYRECRECEIPTNUMBER(writeOffDto.getReceiptCode()));
                appscuxarunapplyrec.setTRXNUMBER(objFactory.createAPPSCUXARUNAPPLYRECTRXNUMBER(dto.getInvoiceCode()));
                appscuxarunapplyrec.setREVERSALDATE(objFactory.createAPPSCUXARUNAPPLYRECREVERSALDATE(DateUtils.format(dto.getCancelAt(), DateUtils.FORMAT_SHORT)));
                appscuxarunapplyrec.setREVERSALGLDATE(objFactory.createAPPSCUXARUNAPPLYRECREVERSALGLDATE(DateUtils.format(dto.getCancelAt(), DateUtils.FORMAT_SHORT)));
                appscuxarapplytbl.getPAPPLICATIONTBLITEM().add(appscuxarunapplyrec);
            }
        }
        param.setPAPPLICATIONTBL(objFactory.createInputParametersPAPPLICATIONTBL(appscuxarapplytbl));
        try {
            cuxarunapplyapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (Exception e) {
            LOGGER.error("PAM-ERP-022收款核销冲销口异常", e);
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-023 应付发票写入
     */
    @Override
    public EsbResponse callCUXAPINVOICEAPIPKGPortTypeService(List<PaymentInvoiceErpDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getApInvoiceSoaHeader();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();

        APPSCUXAPINVOICETBL appscuxapinvoicetbl = objFactory.createAPPSCUXAPINVOICETBL();
        APPSCUXAPINVOICELINETBL appscuxapinvoicelinetbl = objFactory.createAPPSCUXAPINVOICELINETBL();
        for (PaymentInvoiceErpDto paymentInvoiceErpDto : dtoList) {
            //发票头
            APPSCUXAPINVOICEREC appscuxapinvoicerec = new APPSCUXAPINVOICEREC();
            appscuxapinvoicerec.setSOURCECODE(objFactory.createAPPSCUXAPINVOICERECSOURCECODE(headerRequest.getRequestId()));
            appscuxapinvoicerec.setSOURCENUM(objFactory.createAPPSCUXAPINVOICERECSOURCENUM(headerRequest.getSerialNo() + "_" + paymentInvoiceErpDto.getId()));
            appscuxapinvoicerec.setESBSERIALNUM(objFactory.createAPPSCUXAPINVOICERECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxapinvoicerec.setESBDATASOURCE(objFactory.createAPPSCUXAPINVOICERECESBDATASOURCE(BusinessTypeEnums.PAYMENT_INVOICE.getCode()));
            appscuxapinvoicerec.setORGID(objFactory.createAPPSCUXAPINVOICELINERECORGID(paymentInvoiceErpDto.getOrgId()));
            appscuxapinvoicerec.setBATCHNAME(objFactory.createAPPSCUXAPINVOICERECBATCHNAME(paymentInvoiceErpDto.getBatchName()));
            appscuxapinvoicerec.setINVOICENUM(objFactory.createAPPSCUXAPINVOICELINERECINVOICENUM(paymentInvoiceErpDto.getInvoiceNum()));
            appscuxapinvoicerec.setINVOICETYPELOOKUPCODE(objFactory.createAPPSCUXAPINVOICERECINVOICETYPELOOKUPCODE(paymentInvoiceErpDto.getInvoiceTypeLookupCode()));
            appscuxapinvoicerec.setINVOICEDATE(objFactory.createAPPSCUXAPINVOICERECINVOICEDATE(paymentInvoiceErpDto.getInvoiceDate()));
            appscuxapinvoicerec.setGLDATE(objFactory.createAPPSCUXAPINVOICERECGLDATE(paymentInvoiceErpDto.getGlDate()));
            appscuxapinvoicerec.setVENDORID(objFactory.createAPPSCUXAPINVOICERECVENDORID(paymentInvoiceErpDto.getVendorId()));
            appscuxapinvoicerec.setVENDORSITEID(objFactory.createAPPSCUXAPINVOICERECVENDORSITEID(paymentInvoiceErpDto.getVendorSiteCode()));
            appscuxapinvoicerec.setPAYMENTMETHODCODE(objFactory.createAPPSCUXAPINVOICERECPAYMENTMETHODCODE(paymentInvoiceErpDto.getPaymentMethodCode()));
            appscuxapinvoicerec.setPAYMENTMETHOD(objFactory.createAPPSCUXAPINVOICERECPAYMENTMETHOD(paymentInvoiceErpDto.getPaymentMethod()));
            appscuxapinvoicerec.setTERMSID(objFactory.createAPPSCUXAPINVOICERECTERMSID(paymentInvoiceErpDto.getTermsId()));
            appscuxapinvoicerec.setTERMSNAME(objFactory.createAPPSCUXAPINVOICERECTERMSNAME(paymentInvoiceErpDto.getTermsName()));
            appscuxapinvoicerec.setTERMSDATE(objFactory.createAPPSCUXAPINVOICERECTERMSDATE(paymentInvoiceErpDto.getTermsDate()));
            appscuxapinvoicerec.setCURRENCYCODE(objFactory.createAPPSCUXAPINVOICERECCURRENCYCODE(Optional.ofNullable(paymentInvoiceErpDto.getCurrencyCode()).orElse("CNY")));
            appscuxapinvoicerec.setEXCHANGERATETYPE(objFactory.createAPPSCUXAPINVOICERECEXCHANGERATETYPE(paymentInvoiceErpDto.getExchangeRateType()));
            appscuxapinvoicerec.setEXCHANGERATE(objFactory.createAPPSCUXAPINVOICERECEXCHANGERATE(paymentInvoiceErpDto.getExchangeRate()));
            appscuxapinvoicerec.setEXCHANGEDATE(objFactory.createAPPSCUXAPINVOICERECEXCHANGEDATE(paymentInvoiceErpDto.getExchangeRateDate()));
            appscuxapinvoicerec.setINVOICEAMOUNT(objFactory.createAPPSCUXAPINVOICERECINVOICEAMOUNT(paymentInvoiceErpDto.getInvoiceAmount()));
            appscuxapinvoicerec.setTAXAMOUNT(objFactory.createAPPSCUXAPINVOICERECTAXAMOUNT(paymentInvoiceErpDto.getTaxAmount()));
            appscuxapinvoicerec.setTAXCLASSIFICATION(objFactory.createAPPSCUXAPINVOICERECTAXCLASSIFICATION(paymentInvoiceErpDto.getTaxClassification()));
            appscuxapinvoicerec.setDESCRIPTION(objFactory.createAPPSCUXAPINVOICERECDESCRIPTION(paymentInvoiceErpDto.getDescription()));
            appscuxapinvoicerec.setATTRIBUTE3(objFactory.createAPPSCUXAPINVOICELINERECATTRIBUTE3(paymentInvoiceErpDto.getAttribute3()));
            appscuxapinvoicerec.setATTRIBUTE1(objFactory.createAPPSCUXAPINVOICELINERECATTRIBUTE1("PAM"));
            appscuxapinvoicerec.setATTRIBUTE7(objFactory.createAPPSCUXAPINVOICERECATTRIBUTE7(paymentInvoiceErpDto.getAttribute7()));//凭证类型
            if (StringUtils.isNotBlank(paymentInvoiceErpDto.getAttribute11())) {
                appscuxapinvoicerec.setATTRIBUTE11(objFactory.createAPPSCUXAPINVOICERECATTRIBUTE11(paymentInvoiceErpDto.getAttribute11()));//中转关联号
            }
            if (StringUtils.isNotBlank(paymentInvoiceErpDto.getAttributeCategory())) {
                appscuxapinvoicerec.setATTRIBUTECATEGORY(objFactory.createAPPSCUXAPINVOICERECATTRIBUTECATEGORY(paymentInvoiceErpDto.getAttributeCategory()));//业务标识
            }
            if (StringUtils.isNotBlank(paymentInvoiceErpDto.getAttribute13())) {
                appscuxapinvoicerec.setATTRIBUTE13(objFactory.createAPPSCUXAPINVOICERECATTRIBUTE13(paymentInvoiceErpDto.getAttribute13()));//出纳流水号
            }
            if (StringUtils.isNotBlank(paymentInvoiceErpDto.getAttribute14())) {
                appscuxapinvoicerec.setATTRIBUTE14(objFactory.createAPPSCUXAPINVOICERECATTRIBUTE14(paymentInvoiceErpDto.getAttribute14()));//收款银行账号
            }
            appscuxapinvoicetbl.getPIFACEHEADERSTBLITEM().add(appscuxapinvoicerec);

            //发票行
            List<PaymentInvoiceDetailErpDto> detailErpDtos = paymentInvoiceErpDto.getDetailErpDtos();
            int i = 1;
            for (PaymentInvoiceDetailErpDto detailDto : detailErpDtos) {
                APPSCUXAPINVOICELINEREC appscuxapinvoicelinerec = new APPSCUXAPINVOICELINEREC();
                appscuxapinvoicelinerec.setSOURCELINENUM(objFactory.createAPPSCUXAPINVOICELINERECSOURCELINENUM("PAM" + detailDto.getId()));
                appscuxapinvoicelinerec.setINVOICENUM(objFactory.createAPPSCUXAPINVOICELINERECINVOICENUM(paymentInvoiceErpDto.getInvoiceNum()));
                appscuxapinvoicelinerec.setORGID(objFactory.createAPPSCUXAPINVOICELINERECORGID(paymentInvoiceErpDto.getOrgId()));
                appscuxapinvoicelinerec.setLINENUMBER(objFactory.createAPPSCUXAPINVOICELINERECLINENUMBER(new BigDecimal(i++)));
                appscuxapinvoicelinerec.setLINETYPELOOKUPCODE(objFactory.createAPPSCUXAPINVOICELINERECLINETYPELOOKUPCODE(detailDto.getLineTypeLookUpCode()));
                appscuxapinvoicelinerec.setAMOUNT(objFactory.createAPPSCUXAPINVOICELINERECAMOUNT(detailDto.getAmount()));
                appscuxapinvoicelinerec.setSEGMENT1(objFactory.createAPPSCUXAPINVOICELINERECSEGMENT1(detailDto.getSegment1()));
                appscuxapinvoicelinerec.setSEGMENT2(objFactory.createAPPSCUXAPINVOICELINERECSEGMENT2(detailDto.getSegment2()));
                appscuxapinvoicelinerec.setSEGMENT3(objFactory.createAPPSCUXAPINVOICELINERECSEGMENT3(detailDto.getSegment3()));
                appscuxapinvoicelinerec.setSEGMENT4(objFactory.createAPPSCUXAPINVOICELINERECSEGMENT4(detailDto.getSegment4()));
                appscuxapinvoicelinerec.setSEGMENT5(objFactory.createAPPSCUXAPINVOICELINERECSEGMENT5(detailDto.getSegment5()));
                appscuxapinvoicelinerec.setSEGMENT6(objFactory.createAPPSCUXAPINVOICELINERECSEGMENT6(detailDto.getSegment6()));
                appscuxapinvoicelinerec.setSEGMENT7(objFactory.createAPPSCUXAPINVOICELINERECSEGMENT7(detailDto.getSegment7()));
                appscuxapinvoicelinerec.setRECEIPTNUMBER(objFactory.createAPPSCUXAPINVOICELINERECRECEIPTNUMBER(detailDto.getReceiptNumber()));
                appscuxapinvoicelinerec.setRECEIPTLINENUMBER(objFactory.createAPPSCUXAPINVOICELINERECRECEIPTLINENUMBER(detailDto.getReceiptLineNumber()));
                appscuxapinvoicelinerec.setITEMCODE(objFactory.createAPPSCUXAPINVOICELINERECITEMCODE(detailDto.getItemCode()));
                appscuxapinvoicelinerec.setQUANTITYINVOICED(objFactory.createAPPSCUXAPINVOICELINERECQUANTITYINVOICED(detailDto.getQuantityInvoiced()));
                appscuxapinvoicelinerec.setUNITPRICE(objFactory.createAPPSCUXAPINVOICELINERECUNITPRICE(detailDto.getUnitPrice()));
                appscuxapinvoicelinerec.setDESCRIPTION(objFactory.createAPPSCUXAPINVOICELINERECDESCRIPTION(detailDto.getDescription()));
                //对应每张发票明细中税额行不推送 ，原因：ERP根据税码获取科目，不能将税额行信息推送
                // TODO 待推送SDP平台改造时一并修改，修改逻辑：去除税额行detailDto对象新建
                if ((i - 1) % 2 == 0) {
                    logger.info("发票明细中税额行不推送,detailDto:{}", JSON.toJSONString(detailDto));
                    continue;
                }
                appscuxapinvoicelinetbl.getPIFACELINESTBLITEM().add(appscuxapinvoicelinerec);
            }
        }
        param.setPIFACEHEADERSTBL(objFactory.createInputParametersPIFACEHEADERSTBL(appscuxapinvoicetbl));
        param.setPIFACELINESTBL(objFactory.createInputParametersPIFACELINESTBL(appscuxapinvoicelinetbl));

        try {
            cuxapinvoiceapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (ISGServiceFaultMessage isgServiceFaultMessage) {
            isgServiceFaultMessage.printStackTrace();
            LOGGER.error("应付发票写入", isgServiceFaultMessage);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), isgServiceFaultMessage.getMessage(),
                    headerRequest.getSerialNo());
        }

    }

    /**
     * PAM-ERP-023-2 第三方票据写入
     */
    @Override
    public EsbResponse<String> callCUXAPDLEAPIPKGPortTypeService(List<PaymentInvoiceErpDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getApInvoiceSoaHeader();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_invoice_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();

        APPSCUXAPINVOICETBL appscuxapinvoicetbl = objFactory.createAPPSCUXAPINVOICETBL();
        APPSCUXAPINVOICELINETBL appscuxapinvoicelinetbl = objFactory.createAPPSCUXAPINVOICELINETBL();
        for (PaymentInvoiceErpDto paymentInvoiceErpDto : dtoList) {
            //发票头
            APPSCUXAPINVOICEREC appscuxapinvoicerec = new APPSCUXAPINVOICEREC();
            // source_code
            appscuxapinvoicerec.setSOURCECODE(objFactory.createAPPSCUXAPINVOICERECSOURCECODE(headerRequest.getRequestId()));
            // source_num
            appscuxapinvoicerec.setSOURCENUM(objFactory.createAPPSCUXAPINVOICERECSOURCENUM(headerRequest.getSerialNo() + "_" + paymentInvoiceErpDto.getId()));
            // esb_serial_num
            appscuxapinvoicerec.setESBSERIALNUM(objFactory.createAPPSCUXAPINVOICERECESBSERIALNUM(headerRequest.getSerialNo()));
            // esb_data_source ：PAM-ERP-023-2
            appscuxapinvoicerec.setESBDATASOURCE(objFactory.createAPPSCUXAPINVOICERECESBDATASOURCE(BusinessTypeEnums.PAYMENT_INVOICE_THIRD.getCode()));
            // org_id
            appscuxapinvoicerec.setORGID(objFactory.createAPPSCUXAPINVOICELINERECORGID(paymentInvoiceErpDto.getOrgId()));
            // batch_name ：付款申请编码
            appscuxapinvoicerec.setBATCHNAME(objFactory.createAPPSCUXAPINVOICERECBATCHNAME(paymentInvoiceErpDto.getBatchName()));
            // invoice_num ："PAM-" + 付款申请编码
            appscuxapinvoicerec.setINVOICENUM(objFactory.createAPPSCUXAPINVOICELINERECINVOICENUM(paymentInvoiceErpDto.getInvoiceNum()));
            // invoice_type_lookup_code ：DL
            appscuxapinvoicerec.setINVOICETYPELOOKUPCODE(objFactory.createAPPSCUXAPINVOICERECINVOICETYPELOOKUPCODE(paymentInvoiceErpDto.getInvoiceTypeLookupCode()));
            // invoice_date ：计划付款日期
            appscuxapinvoicerec.setINVOICEDATE(objFactory.createAPPSCUXAPINVOICERECINVOICEDATE(paymentInvoiceErpDto.getInvoiceDate()));
            // gl_date ：审批通过日期
            appscuxapinvoicerec.setGLDATE(objFactory.createAPPSCUXAPINVOICERECGLDATE(paymentInvoiceErpDto.getGlDate()));
            // vendor_id ：erp供应商id
            appscuxapinvoicerec.setVENDORID(objFactory.createAPPSCUXAPINVOICERECVENDORID(paymentInvoiceErpDto.getVendorId()));
            // vendor_site_id ：erp供应商地址id
            appscuxapinvoicerec.setVENDORSITEID(objFactory.createAPPSCUXAPINVOICERECVENDORSITEID(paymentInvoiceErpDto.getVendorSiteCode()));
            // payment_method_code ：DL
            appscuxapinvoicerec.setPAYMENTMETHODCODE(objFactory.createAPPSCUXAPINVOICERECPAYMENTMETHODCODE(paymentInvoiceErpDto.getPaymentMethodCode()));
            // payment_method ：DL
            appscuxapinvoicerec.setPAYMENTMETHOD(objFactory.createAPPSCUXAPINVOICERECPAYMENTMETHOD(paymentInvoiceErpDto.getPaymentMethod()));
            // terms_id ：付款申请记录的id
            appscuxapinvoicerec.setTERMSID(objFactory.createAPPSCUXAPINVOICERECTERMSID(paymentInvoiceErpDto.getTermsId()));
            // terms_name ：付款申请记录的付款条件
            appscuxapinvoicerec.setTERMSNAME(objFactory.createAPPSCUXAPINVOICERECTERMSNAME(paymentInvoiceErpDto.getTermsName()));
            // currency_code ：CNY
            appscuxapinvoicerec.setCURRENCYCODE(objFactory.createAPPSCUXAPINVOICERECCURRENCYCODE(paymentInvoiceErpDto.getCurrencyCode()));
            // exchange_rate ：0
            appscuxapinvoicerec.setEXCHANGERATE(objFactory.createAPPSCUXAPINVOICERECEXCHANGERATE(paymentInvoiceErpDto.getExchangeRate()));
            // invoice_amount ：本次付款金额(含税)
            appscuxapinvoicerec.setINVOICEAMOUNT(objFactory.createAPPSCUXAPINVOICERECINVOICEAMOUNT(paymentInvoiceErpDto.getInvoiceAmount()));
            // description ："PAM应付发票导入：" + 付款申请编码
            appscuxapinvoicerec.setDESCRIPTION(objFactory.createAPPSCUXAPINVOICERECDESCRIPTION(paymentInvoiceErpDto.getDescription()));
            // attribute3 ：预算项目号
            appscuxapinvoicerec.setATTRIBUTE3(objFactory.createAPPSCUXAPINVOICELINERECATTRIBUTE3(paymentInvoiceErpDto.getAttribute3()));
            // attribute1 ：PAM
            appscuxapinvoicerec.setATTRIBUTE1(objFactory.createAPPSCUXAPINVOICELINERECATTRIBUTE1("PAM"));
            // attribute7 凭证类型 ：\付
            appscuxapinvoicerec.setATTRIBUTE7(objFactory.createAPPSCUXAPINVOICERECATTRIBUTE7(paymentInvoiceErpDto.getAttribute7()));

            appscuxapinvoicetbl.getPIFACEHEADERSTBLITEM().add(appscuxapinvoicerec);

            //发票行
            List<PaymentInvoiceDetailErpDto> detailErpDtos = paymentInvoiceErpDto.getDetailErpDtos();
            int i = 1;
            for (PaymentInvoiceDetailErpDto detailDto : detailErpDtos) {
                APPSCUXAPINVOICELINEREC appscuxapinvoicelinerec = new APPSCUXAPINVOICELINEREC();
                // source_line_num ：PAM + 付款申请id
                appscuxapinvoicelinerec.setSOURCELINENUM(objFactory.createAPPSCUXAPINVOICELINERECSOURCELINENUM("PAM" + detailDto.getId()));
                // invoice_num ："PAM-" + 付款申请编码
                appscuxapinvoicelinerec.setINVOICENUM(objFactory.createAPPSCUXAPINVOICELINERECINVOICENUM(paymentInvoiceErpDto.getInvoiceNum()));
                // org_id ：ouId
                appscuxapinvoicelinerec.setORGID(objFactory.createAPPSCUXAPINVOICELINERECORGID(paymentInvoiceErpDto.getOrgId()));
                // line_number ：1
                appscuxapinvoicelinerec.setLINENUMBER(objFactory.createAPPSCUXAPINVOICELINERECLINENUMBER(new BigDecimal(i++)));
                // line_type_lookup_code ：ITEM
                appscuxapinvoicelinerec.setLINETYPELOOKUPCODE(objFactory.createAPPSCUXAPINVOICELINERECLINETYPELOOKUPCODE(detailDto.getLineTypeLookUpCode()));
                // amount ：本次付款金额(含税)
                appscuxapinvoicelinerec.setAMOUNT(objFactory.createAPPSCUXAPINVOICELINERECAMOUNT(detailDto.getAmount()));
                // description ："PAM应付发票导入，" + 付款申请编码
                appscuxapinvoicelinerec.setDESCRIPTION(objFactory.createAPPSCUXAPINVOICELINERECDESCRIPTION(detailDto.getDescription()));
                appscuxapinvoicelinetbl.getPIFACELINESTBLITEM().add(appscuxapinvoicelinerec);
            }
        }
        param.setPIFACEHEADERSTBL(objFactory.createInputParametersPIFACEHEADERSTBL(appscuxapinvoicetbl));
        param.setPIFACELINESTBL(objFactory.createInputParametersPIFACELINESTBL(appscuxapinvoicelinetbl));

        try {
            cuxapinvoiceapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse<String> esbResponse = new EsbResponse<>();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (ISGServiceFaultMessage isgServiceFaultMessage) {
            isgServiceFaultMessage.printStackTrace();
            LOGGER.error("第三方票据写入", isgServiceFaultMessage);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), isgServiceFaultMessage.getMessage(),
                    headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-054 应收贷项发票核销
     */
    @Override
    public EsbResponse CUXARTRXAPPLYAPIPKGPortTypeService(List<InvoiceReceivableErpDto> dtoList) {

        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_trx_apply_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getTrxApplySoaHeader();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_trx_apply_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_trx_apply_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_trx_apply_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_trx_apply_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();

        APPSCUXARTRXAPPLYTBL appscuxapinvoicetbl = objFactory.createAPPSCUXARTRXAPPLYTBL();
        for (InvoiceReceivableErpDto erpDto : dtoList) {
            APPSCUXARTRXAPPLYREC appscuxartrxapplyrec = new APPSCUXARTRXAPPLYREC();
            appscuxartrxapplyrec.setSOURCECODE(objFactory.createAPPSCUXARTRXAPPLYRECSOURCECODE(headerRequest.getRequestId()));
            appscuxartrxapplyrec.setSOURCENUM(objFactory.createAPPSCUXARTRXAPPLYRECSOURCENUM(headerRequest.getSerialNo() + "_" + erpDto.getId()));
            appscuxartrxapplyrec.setESBSERIALNUM(objFactory.createAPPSCUXARTRXAPPLYRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxartrxapplyrec.setESBDATASOURCE(objFactory.createAPPSCUXARTRXAPPLYRECESBDATASOURCE(BusinessTypeEnums.WRITEOFF_INVOICE.getCode()));
            appscuxartrxapplyrec.setORGID(objFactory.createAPPSCUXARTRXAPPLYRECORGID(erpDto.getOrgId()));//业务实体
            appscuxartrxapplyrec.setCUSTOMERNUMBER(objFactory.createAPPSCUXARTRXAPPLYRECCUSTOMERNUMBER(erpDto.getCustomerNumber()));//客户编号
            appscuxartrxapplyrec.setCMTRXNUMBER(objFactory.createAPPSCUXARTRXAPPLYRECCMTRXNUMBER(erpDto.getCmTrxNumber()));//贷项发票编号
            appscuxartrxapplyrec.setINVOICETRXNUMBER(objFactory.createAPPSCUXARTRXAPPLYRECINVOICETRXNUMBER(erpDto.getInvoiceTrxNumber()));//借项发票编号
            appscuxartrxapplyrec.setAPPLYDATE(objFactory.createAPPSCUXARTRXAPPLYRECAPPLYDATE(erpDto.getWriteOffDate()));//核销日期
            appscuxartrxapplyrec.setGLDATE(objFactory.createAPPSCUXARTRXAPPLYRECGLDATE(erpDto.getGlDate()));//总账日期
            appscuxartrxapplyrec.setAPPLIEDAMOUNT(objFactory.createAPPSCUXARTRXAPPLYRECAPPLIEDAMOUNT(erpDto.getAppliedAmount()));//核销金额（含税）
            appscuxapinvoicetbl.getPAPPLICATIONTBLITEM().add(appscuxartrxapplyrec);
        }
        param.setPAPPLICATIONTBL(objFactory.createInputParametersPAPPLICATIONTBL(appscuxapinvoicetbl));

        try {
            cuxartrxapplyapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ar_trx_apply_api_pkg.ISGServiceFaultMessage isgServiceFaultMessage) {
            isgServiceFaultMessage.printStackTrace();
            LOGGER.error("应收贷项发票核销", isgServiceFaultMessage);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), isgServiceFaultMessage.getMessage(),
                    headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-024 应收发票取消
     * PAM-ERP-071 应付发票取消[作废发票场景]
     */
    @Override
    public EsbResponse callCUXAPCANCELAPIPKGPortType(List<PaymentInvoiceErpDto> dtoList,
                                                     BusinessTypeEnums businessTypeEnums) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_cancel_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getApCancelSoaHeader();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_cancel_api_pkg.do_insert.OutputParameters> outParam = new javax.xml.ws.Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_cancel_api_pkg.do_insert.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_cancel_api_pkg.do_insert.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_cancel_api_pkg.do_insert.InputParameters param =
                objFactory.createInputParameters();

        APPSCUXAPCANCELTBL appscuxapcanceltbl = objFactory.createAPPSCUXAPCANCELTBL();
        for (PaymentInvoiceErpDto paymentInvoiceErpDto : dtoList) {

            APPSCUXAPCANCELREC appscuxapcancelrec = new APPSCUXAPCANCELREC();
            appscuxapcancelrec.setSOURCECODE(objFactory.createAPPSCUXAPCANCELRECSOURCECODE(headerRequest.getRequestId()));
            appscuxapcancelrec.setSOURCENUM(objFactory.createAPPSCUXAPCANCELRECSOURCENUM(headerRequest.getSerialNo() + "_" + paymentInvoiceErpDto.getId()));
            appscuxapcancelrec.setESBSERIALNUM(objFactory.createAPPSCUXAPCANCELRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxapcancelrec.setESBDATASOURCE(objFactory.createAPPSCUXAPCANCELRECESBDATASOURCE(businessTypeEnums.getCode()));
            appscuxapcancelrec.setORGID(objFactory.createAPPSCUXAPCANCELRECORGID(paymentInvoiceErpDto.getOrgId()));
            //业务实体
            appscuxapcancelrec.setINVOICEID(objFactory.createAPPSCUXAPCANCELRECINVOICEID(BigDecimal.valueOf(paymentInvoiceErpDto.getId())));//发票ID
            appscuxapcancelrec.setCANCELDATE(objFactory.createAPPSCUXAPCANCELRECCANCELDATE(String.valueOf(paymentInvoiceErpDto.getCancelDate())));//取消发票日期

            appscuxapcanceltbl.getPIFACETBLITEM().add(appscuxapcancelrec);
        }
        param.setPIFACETBL(objFactory.createInputParametersPIFACETBL(appscuxapcanceltbl));

        try {
            cuxapcancelapipkgPortType.doINSERT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_cancel_api_pkg.ISGServiceFaultMessage isgServiceFaultMessage) {
            isgServiceFaultMessage.printStackTrace();
            LOGGER.error("应付发票取消", isgServiceFaultMessage);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), isgServiceFaultMessage.getMessage(),
                    headerRequest.getSerialNo());
        }

    }

    /**
     * PAM-ERP-032 子库存转移写入
     */
    @Override
    public EsbResponse callCUXINVTRANSACTIONSAPIPKGPortTypeMaterialTransfer(List<MaterialSyncErpDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getInvTrxSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.ObjectFactory objFactory =
                new
                        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        APPSCUXINVTRANSACTIONTBL appscuxinvtransactiontbl = objFactory.createAPPSCUXINVTRANSACTIONTBL();
        for (MaterialSyncErpDto dto : dtoList) {
            APPSCUXINVTRANSACTIONREC appscuxinvtransactionrec = objFactory.createAPPSCUXINVTRANSACTIONREC();
            appscuxinvtransactionrec.setESBDATASOURCE(objFactory.createAPPSCUXINVTRANSACTIONRECESBDATASOURCE(BusinessTypeEnums.TRANSFER_MATERIAL.getCode()));
            appscuxinvtransactionrec.setSOURCECODE(objFactory.createAPPSCUXINVTRANSACTIONRECSOURCECODE(headerRequest.getRequestId()));
            appscuxinvtransactionrec.setESBSERIALNUM(objFactory.createAPPSCUXINVTRANSACTIONRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxinvtransactionrec.setSOURCENUM(objFactory.createAPPSCUXINVTRANSACTIONRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));//异步回调唯一标识
            //业务字段
            appscuxinvtransactionrec.setSOURCEHEADERID(objFactory.createAPPSCUXINVTRANSACTIONRECSOURCEHEADERID(BigDecimal.valueOf(dto.getHeaderId())));//头表ID
            appscuxinvtransactionrec.setORGANIZATIONID(objFactory.createAPPSCUXINVTRANSACTIONRECORGANIZATIONID(BigDecimal.valueOf(dto.getOrganizationId())));//库存组织ID
            appscuxinvtransactionrec.setORGANIZATIONCODE(objFactory.createAPPSCUXINVTRANSACTIONRECORGANIZATIONCODE(dto.getOrganizationCode()));//库存组织编码
            appscuxinvtransactionrec.setITEMNUMBER(objFactory.createAPPSCUXINVTRANSACTIONRECITEMNUMBER(dto.getMaterialCode()));//物料ERP ID
            appscuxinvtransactionrec.setSUBINVENTORY(objFactory.createAPPSCUXINVTRANSACTIONRECSUBINVENTORY(dto.getInventoryCode()));//仓库编码
            appscuxinvtransactionrec.setTRANSACTIONTYPE(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONTYPE(dto.getTransactionType()));
            appscuxinvtransactionrec.setTRANSACTIONQUANTITY(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONQUANTITY(dto.getAmount()));//数量
            appscuxinvtransactionrec.setTRANSACTIONUOM(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONUOM(dto.getUnit()));//单位
            appscuxinvtransactionrec.setTRANSACTIONDATE(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONDATE(StringUtils.isNotBlank(dto.getTransactionDate()) ? dto.getTransactionDate() : DateUtils.getNow()));
            appscuxinvtransactionrec.setACCOUNTALIAS(objFactory.createAPPSCUXINVTRANSACTIONRECACCOUNTALIAS(dto.getAliasId()));//账户别名ID
            appscuxinvtransactionrec.setLOCATOR(objFactory.createAPPSCUXINVTRANSACTIONRECLOCATOR(dto.getLocation()));
            //货架
            appscuxinvtransactionrec.setTRANSACTIONREFERENCE(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONREFERENCE(dto.getCode()));//单号

            appscuxinvtransactionrec.setTRANSFERORGANIZATIONID(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSFERORGANIZATIONID(BigDecimalUtils.longToBigDecimal(dto.getTransferOrganizationId())));
            appscuxinvtransactionrec.setTRANSFERORGANIZATIONCODE(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSFERORGANIZATIONCODE(dto.getTransferOrganizationCode()));
            appscuxinvtransactionrec.setTRANSFERSUBINVENTORY(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSFERSUBINVENTORY(dto.getTransferInventory()));//目标子库
            appscuxinvtransactionrec.setTRANSFERLOCATOR(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSFERLOCATOR(dto.getTransferLocation()));//货位编码
            appscuxinvtransactiontbl.getPIFACETBLITEM().add(appscuxinvtransactionrec);
        }

        param.setPIFACETBL(objFactory.createInputParametersPIFACETBL(appscuxinvtransactiontbl));
        try {
            cuxinvtransactionsapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.ISGServiceFaultMessage e) {
            LOGGER.error("子库转移报错", e);
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }

    }

    /**
     * PAM-ERP-028 付款计划写入
     */
    @Override
    public EsbResponse callCUXAPPAYMENTPLANAPIPKGService(List<PaymentPlanErpDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getPayPlanSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.do_insert.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.do_insert.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.do_insert.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.do_insert.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        APPSCUXAPINVOICEPAYPGINTTBL appscuxapinvoicepaypginttbl = objFactory.createAPPSCUXAPINVOICEPAYPGINTTBL();
        APPSCUXAPINVOICEPIINTTBL appscuxapinvoicepiinttbl = objFactory.createAPPSCUXAPINVOICEPIINTTBL();
        APPSCUXAPINVOICEPPINTTBL appscuxapinvoiceppinttbl = objFactory.createAPPSCUXAPINVOICEPPINTTBL();

        for (PaymentPlanErpDto dto : dtoList) {
            APPSCUXAPINVOICEPAYPGINTREC appscuxapinvoicepaypgintrec = objFactory.createAPPSCUXAPINVOICEPAYPGINTREC();
            appscuxapinvoicepaypgintrec.setPAYPLANNUM(objFactory.createAPPSCUXAPINVOICEPAYPGINTRECPAYPLANNUM(dto.getPayPlanNum()));
            appscuxapinvoicepaypgintrec.setORGID(objFactory.createAPPSCUXAPINVOICEPAYPGINTRECORGID(dto.getOrgId()));
            appscuxapinvoicepaypgintrec.setBATCHPAYMETFLAG(objFactory.createAPPSCUXAPINVOICEPAYPGINTRECBATCHPAYMETFLAG("N"));
            appscuxapinvoicepaypgintrec.setSOURCECODE(objFactory.createAPPSCUXAPINVOICEPAYPGINTRECSOURCECODE(headerRequest.getRequestId()));
            appscuxapinvoicepaypgintrec.setSOURCENUM(objFactory.createAPPSCUXAPINVOICEPAYPGINTRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));
            appscuxapinvoicepaypgintrec.setESBSERIALNUM(objFactory.createAPPSCUXAPINVOICEPAYPGINTRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxapinvoicepaypgintrec.setESBDATASOURCE(objFactory.createAPPSCUXAPINVOICEPAYPGINTRECESBDATASOURCE(BusinessTypeEnums.PAYMENT_PLAN.getCode()));
            appscuxapinvoicepaypginttbl.getPHEADERTBLITEM().add(appscuxapinvoicepaypgintrec);
            //供应商业务字段
            APPSCUXAPINVOICEPPINTREC appscuxapinvoiceppintrec = objFactory.createAPPSCUXAPINVOICEPPINTREC();
            appscuxapinvoiceppintrec.setPAYPLANNUM(objFactory.createAPPSCUXAPINVOICEPAYPGINTRECPAYPLANNUM(dto.getPayPlanNum()));
            appscuxapinvoiceppintrec.setCURRENCYCODE(objFactory.createAPPSCUXAPINVOICEPPINTRECCURRENCYCODE(dto.getCurrencyCode() != null ? dto.getCurrencyCode() : "CNY"));
            appscuxapinvoiceppintrec.setVENDORID(objFactory.createAPPSCUXAPINVOICEPPINTRECVENDORID(dto.getVendorId()));
            appscuxapinvoiceppintrec.setVENDORNUM(objFactory.createAPPSCUXAPINVOICEPPINTRECVENDORNUM(dto.getVendorNum()));
            appscuxapinvoiceppintrec.setVENDORSITEID(objFactory.createAPPSCUXAPINVOICEPIINTRECVENDORSITEID(dto.getVendorSiteId()));
            appscuxapinvoiceppintrec.setACTUALPAYWAY(objFactory.createAPPSCUXAPINVOICEPPINTRECACTUALPAYWAY(dto.getActualPayWay()));
            appscuxapinvoiceppintrec.setACCRUEDLIABILITYNOTAX(objFactory.createAPPSCUXAPINVOICEPPINTRECACCRUEDLIABILITYNOTAX(dto.getAccruedLiabilityNoTax()));
            appscuxapinvoiceppintrec.setPAYABLEACCOUNTUNDUE(objFactory.createAPPSCUXAPINVOICEPPINTRECPAYABLEACCOUNTUNDUE(dto.getPayableAccountUnDue()));
            appscuxapinvoiceppintrec.setPAYABLEACCOUNTDUE(objFactory.createAPPSCUXAPINVOICEPPINTRECPAYABLEACCOUNTDUE(dto.getPayableAccountDue()));
            appscuxapinvoiceppintrec.setPAYABLEACCOUNTSUM(objFactory.createAPPSCUXAPINVOICEPPINTRECPAYABLEACCOUNTSUM(dto.getPayableAccountSum()));
            appscuxapinvoiceppintrec.setPAYABLEBALANCE(objFactory.createAPPSCUXAPINVOICEPPINTRECPAYABLEBALANCE(dto.getPayableBalance()));
            appscuxapinvoiceppintrec.setDUEALLOWEDPAYABLE(objFactory.createAPPSCUXAPINVOICEPPINTRECDUEALLOWEDPAYABLE(dto.getDueAllowedPayable()));
            appscuxapinvoiceppintrec.setACTUALAPPLYPAY(objFactory.createAPPSCUXAPINVOICEPPINTRECACTUALAPPLYPAY(dto.getActualApplyPay()));
            appscuxapinvoiceppintrec.setADVANCEPAY(objFactory.createAPPSCUXAPINVOICEPPINTRECADVANCEPAY(dto.getAdvancePay()));
            appscuxapinvoiceppintrec.setPREPROJECTNUM(objFactory.createAPPSCUXAPINVOICEPPINTRECPREPROJECTNUM(dto.getPreProjectNum()));
            appscuxapinvoiceppintrec.setDESCRIPTION(objFactory.createAPPSCUXAPINVOICEPPINTRECDESCRIPTION(dto.getDescription()));
            appscuxapinvoiceppintrec.setSOURCELINENUM(objFactory.createAPPSCUXAPINVOICEPPINTRECSOURCELINENUM(headerRequest.getSerialNo() + "_" + dto.getId()));
            appscuxapinvoiceppintrec.setBANKACCOUNTNO(objFactory.createAPPSCUXAPINVOICEPPINTRECBANKACCOUNTNO(dto.getBankAccountNo()));
            appscuxapinvoiceppintrec.setBANKACCOUNTNAME(objFactory.createAPPSCUXAPINVOICEPPINTRECBANKACCOUNTNAME(dto.getBankAccountName()));
            appscuxapinvoiceppintrec.setBANKNAME(objFactory.createAPPSCUXAPINVOICEPPINTRECBANKNAME(dto.getBankName()));
            appscuxapinvoiceppintrec.setBANKKEYCODE(objFactory.createAPPSCUXAPINVOICEPPINTRECBANKKEYCODE(dto.getBankKeyCode()));
            //国家代码
            appscuxapinvoiceppintrec.setTERRITORYSHORTNAME(objFactory.createAPPSCUXAPINVOICEPPINTRECTERRITORYSHORTNAME(dto.getTerritoryCode()));
            //详细地址
            appscuxapinvoiceppintrec.setADDRESSLINE(objFactory.createAPPSCUXAPINVOICEPPINTRECADDRESSLINE(dto.getFullAddress()));
            //付款类型
            appscuxapinvoiceppintrec.setPAYMENTTYPE(objFactory.createAPPSCUXAPINVOICEPPINTRECPAYMENTTYPE(dto.getPayType()));
            //付汇性质
            appscuxapinvoiceppintrec.setPAYMENTNATURE(objFactory.createAPPSCUXAPINVOICEPPINTRECPAYMENTNATURE(dto.getPayNature()));
            //交易编码
            appscuxapinvoiceppintrec.setEXCHANGECODE(objFactory.createAPPSCUXAPINVOICEPPINTRECEXCHANGECODE(dto.getTranCode()));
            //交易附言
            appscuxapinvoiceppintrec.setEXCHANGEDESC(objFactory.createAPPSCUXAPINVOICEPPINTRECEXCHANGEDESC(dto.getTranMessage()));
            //费用承担方式
            appscuxapinvoiceppintrec.setCOMMITMENTMETHOD(objFactory.createAPPSCUXAPINVOICEPPINTRECCOMMITMENTMETHOD(dto.getExpenseWay()));
            //合同号
            appscuxapinvoiceppintrec.setCONTRACTNUMBER(objFactory.createAPPSCUXAPINVOICEPPINTRECCONTRACTNUMBER(dto.getContractCode()));
            //发票号
            appscuxapinvoiceppintrec.setINVNUMBER(objFactory.createAPPSCUXAPINVOICEPPINTRECINVNUMBER(dto.getInvoiceCode()));
            //外汇局批件/备案表号/业务编号
            appscuxapinvoiceppintrec.setEXCHANGEOFFICECOMMITS(objFactory.createAPPSCUXAPINVOICEPPINTRECEXCHANGEOFFICECOMMITS(dto.getBusinessRecordCode()));
            //是否保税
            appscuxapinvoiceppintrec.setBONDEDFLAG(objFactory.createAPPSCUXAPINVOICEPPINTRECBONDEDFLAG(dto.getFreeTaxGoods()));
            //银行SWIFTCODE
            appscuxapinvoiceppintrec.setSWIFTCODE(objFactory.createAPPSCUXAPINVOICEPPINTRECSWIFTCODE(dto.getSwiftCode()));
            appscuxapinvoiceppinttbl.getPLINETBLITEM().add(appscuxapinvoiceppintrec);

            //发票集业务字段
            List<PaymentPlanInvoiceErpDto> invoiceErpDtos = dto.getInvoiceErpDtos();
            if (ListUtils.isNotEmpty(invoiceErpDtos)) {
                for (PaymentPlanInvoiceErpDto invoiceErpDto : invoiceErpDtos) {
                    APPSCUXAPINVOICEPIINTREC appscuxapinvoicepiintrec = objFactory.createAPPSCUXAPINVOICEPIINTREC();
                    appscuxapinvoicepiintrec.setPAYPLANNUM(objFactory.createAPPSCUXAPINVOICEPAYPGINTRECPAYPLANNUM(dto.getPayPlanNum()));
                    appscuxapinvoicepiintrec.setVENDORSITEID(objFactory.createAPPSCUXAPINVOICEPIINTRECVENDORSITEID(dto.getVendorSiteId()));
                    appscuxapinvoicepiintrec.setINVOICEID(objFactory.createAPPSCUXAPINVOICEPIINTRECINVOICEID(invoiceErpDto.getInvoiceId()));
                    appscuxapinvoicepiintrec.setINVOICENUM(objFactory.createAPPSCUXAPINVOICEPIINTRECINVOICENUM(invoiceErpDto.getInvoiceNum()));
                    appscuxapinvoicepiintrec.setAPPLYAMT(objFactory.createAPPSCUXAPINVOICEPIINTRECAPPLYAMT(invoiceErpDto.getApplyAmt()));
                    appscuxapinvoicepiintrec.setDUEDATE(objFactory.createAPPSCUXAPINVOICEPIINTRECDUEDATE(invoiceErpDto.getDueDate()));
                    appscuxapinvoicepiinttbl.getPDISTTBLITEM().add(appscuxapinvoicepiintrec);
                }
            }
        }

        param.setPDISTTBL(objFactory.createInputParametersPDISTTBL(appscuxapinvoicepiinttbl));
        param.setPHEADERTBL(objFactory.createInputParametersPHEADERTBL(appscuxapinvoicepaypginttbl));
        param.setPLINETBL(objFactory.createInputParametersPLINETBL(appscuxapinvoiceppinttbl));

        try {
            cuxappaymentplanapipkgPortType.doINSERT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payment_plan_api_pkg.ISGServiceFaultMessage e) {
            LOGGER.error("付款计划写入报错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }

    }

    /**
     * PAM-ERP-025 预付款核销
     */
    @Override
    public EsbResponse callAPPSCUXAPPREPAYService(List<PrepayErpDto> dtoList, String businessType) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_prepay_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getPrepaySoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_prepay_api_pkg.do_insert.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_prepay_api_pkg.do_insert.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_prepay_api_pkg.do_insert.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_prepay_api_pkg.do_insert.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        APPSCUXAPPREPAYSTBL appscuxapprepaystbl = objFactory.createAPPSCUXAPPREPAYSTBL();

        for (PrepayErpDto dto : dtoList) {
            APPSCUXAPPREPAYSREC appscuxapprepaysrec = objFactory.createAPPSCUXAPPREPAYSREC();
            appscuxapprepaysrec.setSOURCECODE(objFactory.createAPPSCUXAPPREPAYSRECSOURCECODE(headerRequest.getRequestId()));
            appscuxapprepaysrec.setSOURCENUM(objFactory.createAPPSCUXAPPREPAYSRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));
            appscuxapprepaysrec.setESBSERIALNUM(objFactory.createAPPSCUXAPPREPAYSRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxapprepaysrec.setESBDATASOURCE(objFactory.createAPPSCUXAPPREPAYSRECESBDATASOURCE(businessType));
            appscuxapprepaysrec.setORGID(objFactory.createAPPSCUXAPPREPAYSRECORGID(dto.getOrgId()));
            appscuxapprepaysrec.setAPPLYDATE(objFactory.createAPPSCUXAPPREPAYSRECAPPLYDATE(dto.getApplyDate()));
            appscuxapprepaysrec.setPREPAYINVOICEID(objFactory.createAPPSCUXAPPREPAYSRECPREPAYINVOICEID(dto.getPrepayInvoiceId()));
            appscuxapprepaysrec.setAMOUNT(objFactory.createAPPSCUXAPPREPAYSRECAMOUNT(dto.getAmount()));
            appscuxapprepaysrec.setINVOICEID(objFactory.createAPPSCUXAPPREPAYSRECINVOICEID(dto.getInvoiceId()));
            appscuxapprepaysrec.setCURRENCYCODE(objFactory.createAPPSCUXAPPREPAYSRECCURRENCYCODE(Optional.ofNullable(dto.getCurrencyCode()).orElse("CNY")));

            appscuxapprepaystbl.getPHEADERTBLITEM().add(appscuxapprepaysrec);
        }
        param.setPHEADERTBL(objFactory.createInputParametersPHEADERTBL(appscuxapprepaystbl));

        try {
            cuxapprepayapipkgPortType.doINSERT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_prepay_api_pkg.ISGServiceFaultMessage e) {
            LOGGER.error("预付款核销", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }

    }

    /**
     * PAM-ERP-033 领料/退料写入
     */
    @Override
    public EsbResponse callCUXINVTRANSACTIONSAPIPKGPortTypeMaterialGet(MaterialSyncErpDto dto) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getInvTrxSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.ObjectFactory objFactory =
                new
                        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        APPSCUXINVTRANSACTIONREC appscuxinvtransactionrec = objFactory.createAPPSCUXINVTRANSACTIONREC();
        appscuxinvtransactionrec.setESBDATASOURCE(objFactory.createAPPSCUXINVTRANSACTIONRECESBDATASOURCE(dto.getEsbDataSource()));//PAM-ERP-033-1 or PAM-ERP-033-2
        appscuxinvtransactionrec.setSOURCECODE(objFactory.createAPPSCUXINVTRANSACTIONRECSOURCECODE(headerRequest.getRequestId()));
        appscuxinvtransactionrec.setESBSERIALNUM(objFactory.createAPPSCUXINVTRANSACTIONRECESBSERIALNUM(headerRequest.getSerialNo()));
        appscuxinvtransactionrec.setSOURCENUM(objFactory.createAPPSCUXINVTRANSACTIONRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));//异步回调唯一标识
        //业务字段
        appscuxinvtransactionrec.setSOURCEHEADERID(objFactory.createAPPSCUXINVTRANSACTIONRECSOURCEHEADERID(BigDecimal.valueOf(dto.getHeaderId())));//头表ID
        appscuxinvtransactionrec.setORGANIZATIONID(objFactory.createAPPSCUXINVTRANSACTIONRECORGANIZATIONID(BigDecimal.valueOf(dto.getOrganizationId())));//库存组织ID
        appscuxinvtransactionrec.setORGANIZATIONCODE(objFactory.createAPPSCUXINVTRANSACTIONRECORGANIZATIONCODE(dto.getOrganizationCode()));//库存组织编码
        appscuxinvtransactionrec.setITEMNUMBER(objFactory.createAPPSCUXINVTRANSACTIONRECITEMNUMBER(dto.getMaterialCode()));//物料ERP ID
        appscuxinvtransactionrec.setSUBINVENTORY(objFactory.createAPPSCUXINVTRANSACTIONRECSUBINVENTORY(dto.getInventoryCode()));//仓库编码
        appscuxinvtransactionrec.setTRANSACTIONTYPE(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONTYPE(dto.getTransactionType()));
        appscuxinvtransactionrec.setTRANSACTIONQUANTITY(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONQUANTITY(dto.getAmount()));//数量
        appscuxinvtransactionrec.setTRANSACTIONUOM(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONUOM(dto.getUnit()));//单位
        appscuxinvtransactionrec.setTRANSACTIONDATE(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONDATE(StringUtils.isNotBlank(dto.getTransactionDate()) ? dto.getTransactionDate() : DateUtils.getNow()));
        appscuxinvtransactionrec.setACCOUNTALIAS(objFactory.createAPPSCUXINVTRANSACTIONRECACCOUNTALIAS(dto.getAliasId()));//账户别名ID
        appscuxinvtransactionrec.setLOCATOR(objFactory.createAPPSCUXINVTRANSACTIONRECLOCATOR(dto.getLocation()));//货架
        appscuxinvtransactionrec.setATTRIBUTE13(objFactory.createAPPSCUXINVTRANSACTIONRECATTRIBUTE13(dto.getSourceNumber()));
        appscuxinvtransactionrec.setATTRIBUTE6(objFactory.createAPPSCUXINVTRANSACTIONRECATTRIBUTE6(dto.getProjectNumber()));
        appscuxinvtransactionrec.setTRANSACTIONREFERENCE(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONREFERENCE(dto.getCode()));//单号

        //科目组合格式需用"."分隔为7段，则"."为6个,不足则用0补充
        String concat = dto.getAliasConcat();
        if (concat.endsWith(".")) {
            concat = concat.substring(0, concat.length() - 1);//去除最后一个"."
        }
        int count = StringUtils.countMatches(concat, ".");//计算"."个数
        for (int i = 1; i <= (6 - count); i++) {
            concat = concat + ".0";
        }
        appscuxinvtransactionrec.setATTRIBUTE15(objFactory.createAPPSCUXINVTRANSACTIONRECATTRIBUTE15(concat));//科目组合

        APPSCUXINVTRANSACTIONTBL appscuxinvtransactiontbl = objFactory.createAPPSCUXINVTRANSACTIONTBL();
        appscuxinvtransactiontbl.getPIFACETBLITEM().add(appscuxinvtransactionrec);
        param.setPIFACETBL(objFactory.createInputParametersPIFACETBL(appscuxinvtransactiontbl));
        try {
            cuxinvtransactionsapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.ISGServiceFaultMessage e) {
            LOGGER.error("领料/退料单写入报错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }

    }

    /**
     * PAM-ERP-055-获取关联交易号
     */
    @Override
    public EsbResponse callCUXICPGETSEQPKGPortType() {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        javax.xml.ws.Holder<com.midea.esb.ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_get_seq_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getIcpGetSeqSOAHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_get_seq_pkg.get_seq_id.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_get_seq_pkg.get_seq_id.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_get_seq_pkg.get_seq_id.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_get_seq_pkg.get_seq_id.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        param.setPSOURCENUMBER(objFactory.createInputParametersPSOURCENUMBER(headerRequest.getRequestId()));
        try {
            cuxicpgetseqpkgPortType.getSEQID(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(outParam.value.getXICPSEQID().getValue());
            return esbResponse;
        } catch (com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_get_seq_pkg.ISGServiceFaultMessage e) {
            LOGGER.error("获取关联交易号报错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }

    }

    /**
     * PAM-ERP-033 领料/退料写入
     */
    @Override
    public EsbResponse callCUXINVTRANSACTIONSAPIPKGPortTypeMaterial(List<MaterialSyncErpDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getInvTrxSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.ObjectFactory objFactory =
                new
                        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        APPSCUXINVTRANSACTIONTBL appscuxinvtransactiontbl = objFactory.createAPPSCUXINVTRANSACTIONTBL();
        for (MaterialSyncErpDto dto : dtoList) {
            APPSCUXINVTRANSACTIONREC appscuxinvtransactionrec = objFactory.createAPPSCUXINVTRANSACTIONREC();
            appscuxinvtransactionrec.setESBDATASOURCE(objFactory.createAPPSCUXINVTRANSACTIONRECESBDATASOURCE(dto.getEsbDataSource()));//PAM-ERP-033-1 or PAM-ERP-033-2
            appscuxinvtransactionrec.setSOURCECODE(objFactory.createAPPSCUXINVTRANSACTIONRECSOURCECODE(headerRequest.getRequestId()));
            appscuxinvtransactionrec.setESBSERIALNUM(objFactory.createAPPSCUXINVTRANSACTIONRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxinvtransactionrec.setSOURCENUM(objFactory.createAPPSCUXINVTRANSACTIONRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));//异步回调唯一标识
            //业务字段
            appscuxinvtransactionrec.setSOURCEHEADERID(objFactory.createAPPSCUXINVTRANSACTIONRECSOURCEHEADERID(BigDecimal.valueOf(dto.getHeaderId())));//头表ID
            appscuxinvtransactionrec.setORGANIZATIONID(objFactory.createAPPSCUXINVTRANSACTIONRECORGANIZATIONID(BigDecimal.valueOf(dto.getOrganizationId())));//库存组织ID
            appscuxinvtransactionrec.setORGANIZATIONCODE(objFactory.createAPPSCUXINVTRANSACTIONRECORGANIZATIONCODE(dto.getOrganizationCode()));//库存组织编码
            appscuxinvtransactionrec.setITEMNUMBER(objFactory.createAPPSCUXINVTRANSACTIONRECITEMNUMBER(dto.getMaterialCode()));//物料ERP ID
            appscuxinvtransactionrec.setSUBINVENTORY(objFactory.createAPPSCUXINVTRANSACTIONRECSUBINVENTORY(dto.getInventoryCode()));//仓库编码
            appscuxinvtransactionrec.setTRANSACTIONTYPE(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONTYPE(dto.getTransactionType()));
            appscuxinvtransactionrec.setTRANSACTIONQUANTITY(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONQUANTITY(dto.getAmount()));//数量
            appscuxinvtransactionrec.setTRANSACTIONUOM(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONUOM(dto.getUnit()));//单位
            appscuxinvtransactionrec.setTRANSACTIONDATE(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONDATE(StringUtils.isNotBlank(dto.getTransactionDate()) ? dto.getTransactionDate() : DateUtils.getNow()));
            appscuxinvtransactionrec.setACCOUNTALIAS(objFactory.createAPPSCUXINVTRANSACTIONRECACCOUNTALIAS(dto.getAliasId()));//账户别名ID
            appscuxinvtransactionrec.setLOCATOR(objFactory.createAPPSCUXINVTRANSACTIONRECLOCATOR(dto.getLocation()));
            //货架
            appscuxinvtransactionrec.setATTRIBUTE13(objFactory.createAPPSCUXINVTRANSACTIONRECATTRIBUTE13(dto.getSourceNumber()));
            appscuxinvtransactionrec.setATTRIBUTE6(objFactory.createAPPSCUXINVTRANSACTIONRECATTRIBUTE6(dto.getProjectNumber()));
            //领料单推送erp需要带上工单任务号，退料单暂时不需要
            if (dto.getTicket_task_code() != null) {
                appscuxinvtransactionrec.setTRANSACTIONREFERENCE(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONREFERENCE(dto.getCode() + "_" + dto.getTicket_task_code()));//单号
            } else {
                appscuxinvtransactionrec.setTRANSACTIONREFERENCE(objFactory.createAPPSCUXINVTRANSACTIONRECTRANSACTIONREFERENCE(dto.getCode()));//单号
            }

            //科目组合格式需用"."分隔为7段，则"."为6个,不足则用0补充
            String concat = dto.getAliasConcat();
            if (concat.endsWith(".")) {
                concat = concat.substring(0, concat.length() - 1);//去除最后一个"."
            }
            int count = StringUtils.countMatches(concat, ".");//计算"."个数
            for (int i = 1; i <= (6 - count); i++) {
                concat = concat + ".0";
            }
            appscuxinvtransactionrec.setATTRIBUTE15(objFactory.createAPPSCUXINVTRANSACTIONRECATTRIBUTE15(concat));//科目组合
            //领料单或退料单来源于成本转移单时,这个字段会赋值
            if (!Objects.isNull(dto.getActualCost())) {
                appscuxinvtransactionrec.setATTRIBUTE14(objFactory.createAPPSCUXINVTRANSACTIONRECATTRIBUTE14(dto.getActualCost().toPlainString()));
            }
            appscuxinvtransactiontbl.getPIFACETBLITEM().add(appscuxinvtransactionrec);
        }
        param.setPIFACETBL(objFactory.createInputParametersPIFACETBL(appscuxinvtransactiontbl));
        try {
            cuxinvtransactionsapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_inv_transactions_api_pkg.ISGServiceFaultMessage e) {
            LOGGER.error("领料/退料单写入报错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-072 客户间转款写入
     */
    @Override
    public EsbResponse callCUXICPNONRELATEREQAPIPKGPortType(List<CustomerTransferDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getIcpNonSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.do_import.ObjectFactory objFactory =
                new com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.do_import.OutputParameters> outParam =
                new javax.xml.ws.Holder<>();
        APPSCUXICPINVOICEREQHDRTBL appscuxicpinvoicereqhdrtbl = objFactory.createAPPSCUXICPINVOICEREQHDRTBL();
        APPSCUXICPINVOICEARREQLNETBL appscuxicpinvoicearreqlnetbl = objFactory.createAPPSCUXICPINVOICEARREQLNETBL();
        APPSCUXICPINVOICEAPREQLNETBL appscuxicpinvoiceapreqlnetbl = objFactory.createAPPSCUXICPINVOICEAPREQLNETBL();

        for (CustomerTransferDto dto : dtoList) {
            /** 头表 */
            APPSCUXICPINVOICEREQHDRREC appscuxicpinvoicereqhdrrec = objFactory.createAPPSCUXICPINVOICEREQHDRREC();
            appscuxicpinvoicereqhdrrec.setESBDATASOURCE(objFactory.createAPPSCUXICPINVOICEREQHDRRECESBDATASOURCE(BusinessTypeEnums.CUSTOMER_TRANSFER.getCode()));
            appscuxicpinvoicereqhdrrec.setSOURCECODE(objFactory.createAPPSCUXICPINVOICEREQHDRRECSOURCECODE(headerRequest.getRequestId()));
            appscuxicpinvoicereqhdrrec.setESBSERIALNUM(objFactory.createAPPSCUXICPINVOICEREQHDRRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxicpinvoicereqhdrrec.setSOURCENUM(objFactory.createAPPSCUXICPINVOICEREQHDRRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));//异步回调唯一标识
            //关联交易号
            appscuxicpinvoicereqhdrrec.setREQHEADERID(objFactory.createAPPSCUXICPINVOICEREQHDRRECREQHEADERID(new BigDecimal(dto.getSeqId())));
            //转账单号
            appscuxicpinvoicereqhdrrec.setSOURCEORDERNUM(objFactory.createAPPSCUXICPINVOICEREQHDRRECSOURCEORDERNUM(dto.getTransferCode()));
            //关联交易类型编码，默认为：ICP001 跨组织客户转款
            appscuxicpinvoicereqhdrrec.setNORELATETYPESCODE(objFactory.createAPPSCUXICPINVOICEREQHDRRECNORELATETYPESCODE("ICP001"));
            //应收方OU
            appscuxicpinvoicereqhdrrec.setSUPPLIERORGID(objFactory.createAPPSCUXICPINVOICEREQHDRRECSUPPLIERORGID(new BigDecimal(dto.getTransferInOuId())));
            //应付方OU
            appscuxicpinvoicereqhdrrec.setREQUIREMENTORGID(objFactory.createAPPSCUXICPINVOICEREQHDRRECREQUIREMENTORGID(new BigDecimal(dto.getTransferOutOuId())));
            //币种
            appscuxicpinvoicereqhdrrec.setCURRENCYCODE(objFactory.createAPPSCUXICPINVOICEREQHDRRECCURRENCYCODE(dto.getCurrency()));
            //汇率
            appscuxicpinvoicereqhdrrec.setCONVERSIONRATE(objFactory.createAPPSCUXICPINVOICEREQHDRRECCONVERSIONRATE(dto.getConversionRate()));
            //汇率类型
            appscuxicpinvoicereqhdrrec.setCONVERSIONTYPE(objFactory.createAPPSCUXICPINVOICEREQHDRRECCONVERSIONTYPE(dto.getConversionType()));
            //GL日期
            appscuxicpinvoicereqhdrrec.setGLDATE(objFactory.createAPPSCUXICPINVOICEREQHDRRECGLDATE(DateUtils.format(dto.getAuditDate(), DateUtils.FORMAT_SHORT)));
            //事务处理类型，指定为“组织间客户转款”
            appscuxicpinvoicereqhdrrec.setSARINVOICETYPE(objFactory.createAPPSCUXICPINVOICEREQHDRRECSARINVOICETYPE("ICP组织间转款"));
            //摘要，默认：组织间客户转款
            appscuxicpinvoicereqhdrrec.setDESCRIPTION(objFactory.createAPPSCUXICPINVOICEAPREQLNERECDESCRIPTION("ICP组织间转款"));
            //应收方系统, AR发票写入的系统，G-ERP
            appscuxicpinvoicereqhdrrec.setREQUIREMENTSYSTEM(objFactory.createAPPSCUXICPINVOICEREQHDRRECREQUIREMENTSYSTEM("GERP"));
            //应付方系统, AR发票写入的系统，G-ERP
            appscuxicpinvoicereqhdrrec.setSUPPLIERSYSTEM(objFactory.createAPPSCUXICPINVOICEREQHDRRECSUPPLIERSYSTEM("GERP"));

            /** 应收方行表(收款) */
            APPSCUXICPINVOICEARREQLNEREC appscuxicpinvoicearreqlnerec = objFactory.createAPPSCUXICPINVOICEARREQLNEREC();
            //关联交易号
            appscuxicpinvoicearreqlnerec.setREQHEADERID(objFactory.createAPPSCUXICPINVOICEARREQLNERECREQHEADERID(new BigDecimal(dto.getSeqId())));
            //应收单据号
            appscuxicpinvoicearreqlnerec.setSOURCEORDERNUM(objFactory.createAPPSCUXICPINVOICEARREQLNERECSOURCEORDERNUM(dto.getTransferCode()));
            //行号，默认1
            appscuxicpinvoicearreqlnerec.setREQLINENUM(objFactory.createAPPSCUXICPINVOICEARREQLNERECREQLINENUM(BigDecimal.ONE));
            //摘要，同头表
            appscuxicpinvoicearreqlnerec.setDESCRIPTION(objFactory.createAPPSCUXICPINVOICEARREQLNERECDESCRIPTION("ICP组织间转款"));
            //开票数量，默认1
            appscuxicpinvoicearreqlnerec.setREQUESTQUANTITY(objFactory.createAPPSCUXICPINVOICEARREQLNERECREQUESTQUANTITY(BigDecimal.ONE));
            //单价，等于金额
            appscuxicpinvoicearreqlnerec.setSETTLEMENTPRICE(objFactory.createAPPSCUXICPINVOICEARREQLNERECSETTLEMENTPRICE(dto.getTransferAmount()));
            //原币金额
            appscuxicpinvoicearreqlnerec.setAMOUNT(objFactory.createAPPSCUXICPINVOICEARREQLNERECAMOUNT(dto.getTransferAmount()));
            //本位币金额
//            if (null != dto.getTransferAmount() && null != dto.getConversionRate()) {
//                appscuxicpinvoicearreqlnerec.setBASEAMOUNT(objFactory.createAPPSCUXICPINVOICEARREQLNERECBASEAMOUNT(dto.getTransferAmount().multiply(dto.getConversionRate()).setScale(2, BigDecimal.ROUND_HALF_UP)));
//            }

            /** 应付方行表(发票) */
            APPSCUXICPINVOICEAPREQLNEREC appscuxicpinvoiceapreqlnerec = objFactory.createAPPSCUXICPINVOICEAPREQLNEREC();
            //关联交易号
            appscuxicpinvoiceapreqlnerec.setREQHEADERID(objFactory.createAPPSCUXICPINVOICEARREQLNERECREQHEADERID(new BigDecimal(dto.getSeqId())));
            //应付单据号
            appscuxicpinvoiceapreqlnerec.setSOURCEORDERNUM(objFactory.createAPPSCUXICPINVOICEARREQLNERECSOURCEORDERNUM(dto.getTransferCode()));
            //行号，默认1
            appscuxicpinvoiceapreqlnerec.setREQLINENUM(objFactory.createAPPSCUXICPINVOICEARREQLNERECREQLINENUM(BigDecimal.ONE));
            //摘要，同头表
            appscuxicpinvoiceapreqlnerec.setDESCRIPTION(objFactory.createAPPSCUXICPINVOICEARREQLNERECDESCRIPTION("ICP组织间转款"));
            //原币金额
            appscuxicpinvoiceapreqlnerec.setAMOUNT(objFactory.createAPPSCUXICPINVOICEARREQLNERECAMOUNT(dto.getTransferAmount()));
            //本位币金额
//            if (null != dto.getTransferAmount() && null != dto.getConversionRate()) {
//                appscuxicpinvoiceapreqlnerec.setBASEAMOUNT(objFactory.createAPPSCUXICPINVOICEARREQLNERECBASEAMOUNT(dto.getTransferAmount().multiply(dto.getConversionRate()).setScale(2, BigDecimal.ROUND_HALF_UP)));
//            }

            appscuxicpinvoicereqhdrtbl.getPIFACEHEADERSTBLITEM().add(appscuxicpinvoicereqhdrrec);
            appscuxicpinvoicearreqlnetbl.getPIFACEARLINESTBLITEM().add(appscuxicpinvoicearreqlnerec);
            appscuxicpinvoiceapreqlnetbl.getPIFACEAPLINESTBLITEM().add(appscuxicpinvoiceapreqlnerec);
        }
        param.setPIFACEHEADERSTBL(objFactory.createInputParametersPIFACEHEADERSTBL(appscuxicpinvoicereqhdrtbl));
        param.setPIFACEARLINESTBL(objFactory.createInputParametersPIFACEARLINESTBL(appscuxicpinvoicearreqlnetbl));
        param.setPIFACEAPLINESTBL(objFactory.createInputParametersPIFACEAPLINESTBL(appscuxicpinvoiceapreqlnetbl));
        try {
            cuxicpnonrelatereqapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_icp_nonrelate_req_api_pkg.ISGServiceFaultMessage e) {
            LOGGER.error("客户间转款写入报错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-073 应付付款（预付款付款,迪链票据接口卡）
     */
    @Override
    public EsbResponse<String> callCUXAPPAYMENTSAPIPKGPortType(List<PaymentApplyDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getApPaySoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.ObjectFactory objFactory =
                new com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.OutputParameters> outParam =
                new javax.xml.ws.Holder<>();
        APPSCUXAPCHECKTBL appscuxapchecktbl = objFactory.createAPPSCUXAPCHECKTBL();
        APPSCUXAPPAYMENTTBL appscuxappaymenttbl = objFactory.createAPPSCUXAPPAYMENTTBL();
        for (PaymentApplyDto paymentApplyDto : dtoList) {
            // 头信息
            APPSCUXAPCHECKREC appscuxapcheckrec = objFactory.createAPPSCUXAPCHECKREC();
            // OU组织ID
            appscuxapcheckrec.setORGID(objFactory.createAPPSCUXAPCHECKRECORGID(new BigDecimal(paymentApplyDto.getOuId())));
            // source_code
            appscuxapcheckrec.setSOURCECODE(objFactory.createAPPSCUXAPCHECKRECSOURCECODE(headerRequest.getRequestId()));
            // source_num
            appscuxapcheckrec.setSOURCENUM(objFactory.createAPPSCUXAPCHECKRECSOURCENUM(headerRequest.getSerialNo() + "_" + paymentApplyDto.getId()));
            // esb_serial_num
            appscuxapcheckrec.setESBSERIALNUM(objFactory.createAPPSCUXAPCHECKRECESBSERIALNUM(headerRequest.getSerialNo()));
            // esb_data_source ：PAM-ERP-073
            appscuxapcheckrec.setESBDATASOURCE(objFactory.createAPPSCUXAPCHECKRECESBDATASOURCE(BusinessTypeEnums.APPAYMENTS_PAYMENT_APPLY.getCode()));
            // fs_check_number ：付款申请编号
            appscuxapcheckrec.setFSCHECKNUMBER(objFactory.createAPPSCUXAPCHECKRECFSCHECKNUMBER(String.valueOf(paymentApplyDto.getPaymentApplyCode())));
            // payment_type_flag ：Q
            appscuxapcheckrec.setPAYMENTTYPEFLAG(objFactory.createAPPSCUXAPCHECKRECPAYMENTTYPEFLAG("Q"));
            // vendor_id ：erp供应商 id
            appscuxapcheckrec.setVENDORID(objFactory.createAPPSCUXAPCHECKRECVENDORID(new BigDecimal(paymentApplyDto.getVendorErpId())));
            // vendor_site_id ：erp供应商地址id
            appscuxapcheckrec.setVENDORSITEID(objFactory.createAPPSCUXAPCHECKRECVENDORSITEID(new BigDecimal(paymentApplyDto.getErpVendorSiteId())));
            // check_date ：审批通过日期
            appscuxapcheckrec.setCHECKDATE(objFactory.createAPPSCUXAPCHECKRECCHECKDATE(DateUtils.formatDate(paymentApplyDto.getAuditDate())));
            // bank_account_id ：银行账号id
            appscuxapcheckrec.setBANKACCOUNTID(objFactory.createAPPSCUXAPCHECKRECBANKACCOUNTID(new BigDecimal(paymentApplyDto.getBankAccountId())));
            // bank_account_name ：银行账号名称
            appscuxapcheckrec.setBANKACCOUNTNAME(objFactory.createAPPSCUXAPCHECKRECBANKACCOUNTNAME(paymentApplyDto.getBankAccountName()));
            // payment_method_lookup_code：DL
            appscuxapcheckrec.setPAYMENTMETHODLOOKUPCODE(objFactory.createAPPSCUXAPCHECKRECPAYMENTMETHODLOOKUPCODE(paymentApplyDto.getPaymentMethodCode()));
            // currency_code ：币种
            appscuxapcheckrec.setCURRENCYCODE(objFactory.createAPPSCUXAPCHECKRECCURRENCYCODE(paymentApplyDto.getCurrency()));
            // amount ：金额
            appscuxapcheckrec.setAMOUNT(objFactory.createAPPSCUXAPCHECKRECAMOUNT(paymentApplyDto.getTaxPayIncludedPrice()));
            // remark ：备注
            appscuxapcheckrec.setREMARK(objFactory.createAPPSCUXAPCHECKRECREMARK(paymentApplyDto.getRemark()));
            // 行信息
            APPSCUXAPPAYMENTREC appscuxappaymentrec = objFactory.createAPPSCUXAPPAYMENTREC();

            // fs_check_number ：付款申请编号
            appscuxappaymentrec.setFSCHECKNUMBER(objFactory.createAPPSCUXAPPAYMENTRECFSCHECKNUMBER(paymentApplyDto.getPaymentApplyCode()));
            // invoice_num ："PAM-" + 付款申请编号
            appscuxappaymentrec.setINVOICENUM(objFactory.createAPPSCUXAPPAYMENTRECINVOICENUM("PAM-" + paymentApplyDto.getPaymentApplyCode()));
            // payment_num ：1
            appscuxappaymentrec.setPAYMENTNUM(objFactory.createAPPSCUXAPPAYMENTRECPAYMENTNUM(BigDecimal.valueOf(1)));
            // amount ：本次付款金额(含税)
            appscuxappaymentrec.setAMOUNT(objFactory.createAPPSCUXAPPAYMENTRECAMOUNT(paymentApplyDto.getTaxPayIncludedPrice()));
            // gl_date ：审批通过日期
            appscuxappaymentrec.setGLDATE(objFactory.createAPPSCUXAPPAYMENTRECGLDATE(DateUtils.formatDate(paymentApplyDto.getAuditDate())));
            // source_line_num ：1
            appscuxappaymentrec.setSOURCELINENUM(objFactory.createAPPSCUXAPPAYMENTRECSOURCELINENUM("1"));
            // attribute1 ：付款申请编号
            appscuxappaymentrec.setATTRIBUTE1(objFactory.createAPPSCUXAPPAYMENTRECATTRIBUTE1(paymentApplyDto.getPaymentApplyCode()));

            appscuxapchecktbl.getPHEADERTBLITEM().add(appscuxapcheckrec);
            appscuxappaymenttbl.getPLINETBLITEM().add(appscuxappaymentrec);
        }

        param.setPHEADERTBL(objFactory.createInputParametersPHEADERTBL(appscuxapchecktbl));
        param.setPLINETBL(objFactory.createInputParametersPLINETBL(appscuxappaymenttbl));
        try {
            cuxappaymentsapipkgPortType.doINSERT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse<String> esbResponse = new EsbResponse<>();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.ISGServiceFaultMessage e) {
            LOGGER.error("写入预付款付款迪链票据接口报错", e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-034 采购订单写入
     */
    @Override
    public EsbResponse callCUXPOSTANDARDPOAPIPKGPortType(List<PurchaseOrderDto> dtos) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_standard_po_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getStandardSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_standard_po_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_standard_po_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_standard_po_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_standard_po_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        try {
            APPSCUXPOSTANDARDHEADERTBL appscuxpostandardheadertbl = objFactory.createAPPSCUXPOSTANDARDHEADERTBL();
            APPSCUXPOSTANDARDLINETBL appscuxpostandardlinetbl = objFactory.createAPPSCUXPOSTANDARDLINETBL();
            BigDecimal batchId = new BigDecimal(DateUtils.getCurrentDate().getTime());
            for (PurchaseOrderDto dto : dtos) {
                //header
                APPSCUXPOSTANDARDHEADERREC appscuxpostandardheaderrec = objFactory.createAPPSCUXPOSTANDARDHEADERREC();

                //是否急件
                appscuxpostandardheaderrec.setATTRIBUTE13(objFactory.createAPPSCUXPOSTANDARDHEADERRECATTRIBUTE13("N"));
                if (dto.getDispatchIs() != null) {
                    if (dto.getDispatchIs()) {
                        appscuxpostandardheaderrec.setATTRIBUTE13(objFactory.createAPPSCUXPOSTANDARDHEADERRECATTRIBUTE13("Y"));
                    } else {
                        appscuxpostandardheaderrec.setATTRIBUTE13(objFactory.createAPPSCUXPOSTANDARDHEADERRECATTRIBUTE13("N"));
                    }
                }
                //comments 备注 头;
                appscuxpostandardheaderrec.setCOMMENTS(objFactory.createAPPSCUXPOSTANDARDHEADERRECCOMMENTS("#"));
                if (dto.getRemark() != null) {
                    appscuxpostandardheaderrec.setCOMMENTS(objFactory.createAPPSCUXPOSTANDARDHEADERRECCOMMENTS("#" + dto.getRemark()));
                }

                appscuxpostandardheaderrec.setESBDATASOURCE(objFactory.createAPPSCUXPOSTANDARDHEADERRECESBDATASOURCE(dto.getEsbDataSource()));
                appscuxpostandardheaderrec.setSOURCECODE(objFactory.createAPPSCUXPOSTANDARDHEADERRECSOURCECODE(headerRequest.getRequestId()));
                appscuxpostandardheaderrec.setESBSERIALNUM(objFactory.createAPPSCUXPOSTANDARDHEADERRECESBSERIALNUM(headerRequest.getSerialNo()));
                appscuxpostandardheaderrec.setSOURCENUM(objFactory.createAPPSCUXPOSTANDARDHEADERRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));//todo 异步回调唯一标识

                appscuxpostandardheaderrec.setBATCHID(objFactory.createAPPSCUXPOSTANDARDHEADERRECBATCHID(batchId));
                if (dto.getNum() != null && StringUtils.isNotEmpty(dto.getNum().replace(CodePrefix.PURCHASE_ORDER_1.code(), "")
                        .replace(CodePrefix.PURCHASE_ORDER_3.code(), ""))) {
                    appscuxpostandardheaderrec.setHEADERINTERFACEID(objFactory.createAPPSCUXPOSTANDARDHEADERRECHEADERINTERFACEID
                            (new BigDecimal(dto.getNum().replace(CodePrefix.PURCHASE_ORDER_1.code(), "")
                                    .replace(CodePrefix.PURCHASE_ORDER_3.code(), ""))));
                }
                if (dto.getOuId() != null) {
                    appscuxpostandardheaderrec.setORGID(objFactory.createAPPSCUXPOSTANDARDHEADERRECORGID(new BigDecimal(dto.getOuId())));
                }
                appscuxpostandardheaderrec.setVENDORNUM(objFactory.createAPPSCUXPOSTANDARDHEADERRECVENDORNUM(dto.getVendorNum()));
                appscuxpostandardheaderrec.setVENDORNAME(objFactory.createAPPSCUXPOSTANDARDHEADERRECVENDORNAME(dto.getVendorName()));
                appscuxpostandardheaderrec.setVENDORSITECODE(objFactory.createAPPSCUXPOSTANDARDHEADERRECVENDORSITECODE(dto.getVendorSiteCode()));
                appscuxpostandardheaderrec.setAGENTID(objFactory.createAPPSCUXPOSTANDARDHEADERRECAGENTID(new BigDecimal(dto.getErpBuyerId())));
                appscuxpostandardheaderrec.setCURRENCYCODE(objFactory.createAPPSCUXPOSTANDARDHEADERRECCURRENCYCODE(Optional.ofNullable(dto.getCurrency()).orElse("CNY")));
                appscuxpostandardheaderrec.setRATE(objFactory.createAPPSCUXPOSTANDARDHEADERRECRATE(BigDecimal.valueOf(1)));
                if (dto.getConversionRate() != null) {
                    appscuxpostandardheaderrec.setRATE(objFactory.createAPPSCUXPOSTANDARDHEADERRECRATE(dto.getConversionRate()));
                }
                appscuxpostandardheaderrec.setRATEDATE(objFactory.createAPPSCUXPOSTANDARDHEADERRECRATEDATE(DateUtils.format(new Date(), DateUtils.FORMAT_SHORT)));
                if (dto.getConversionDate() != null) {
                    appscuxpostandardheaderrec.setRATEDATE(objFactory.createAPPSCUXPOSTANDARDHEADERRECRATEDATE(DateUtils.format(dto.getConversionDate(), DateUtils.FORMAT_SHORT)));
                }
                String type = dto.getConversionType();
                if (type != null) {
                    appscuxpostandardheaderrec.setRATETYPE(objFactory.createAPPSCUXPOSTANDARDHEADERRECRATETYPE(type));
                }
                if (type != null && Objects.equals(CorUserStatus.Corporate.getCode(), type)) {
                    appscuxpostandardheaderrec.setRATETYPE(objFactory.createAPPSCUXPOSTANDARDHEADERRECRATETYPE(CorUserStatus.Corporate.getName()));
                } else if (type != null && Objects.equals(CorUserStatus.User.getCode(), type)) {
                    appscuxpostandardheaderrec.setRATETYPE(objFactory.createAPPSCUXPOSTANDARDHEADERRECRATETYPE(CorUserStatus.User.getName()));
                }
                appscuxpostandardheaderrec.setSEGMENT1(objFactory.createAPPSCUXPOSTANDARDHEADERRECSEGMENT1(dto.getNum()));
                appscuxpostandardheaderrec.setATTRIBUTE1(objFactory.createAPPSCUXPOSTANDARDHEADERRECATTRIBUTE1(dto.getOrderType()));
                appscuxpostandardheaderrec.setATTRIBUTE2(objFactory.createAPPSCUXPOSTANDARDHEADERRECATTRIBUTE2("BPA"));
                //wbs
                if (dto.getPricingType() != null) {
                    // 一价一单, 一揽子协议
                    appscuxpostandardheaderrec.setATTRIBUTE1(objFactory.createAPPSCUXPOSTANDARDHEADERRECATTRIBUTE1("DJ01"));
                    appscuxpostandardheaderrec.setATTRIBUTE2(objFactory.createAPPSCUXPOSTANDARDHEADERRECATTRIBUTE2("PO"));
                }

                appscuxpostandardheadertbl.getPIFACEHEADERSTBLITEM().add(appscuxpostandardheaderrec);

                //line
                for (PurchaseOrderDetailDto orderDetailDto : dto.getPurchaseOrderDetailDtos()) {
                    APPSCUXPOSTANDARDLINEREC appscuxpostandardlinerec = objFactory.createAPPSCUXPOSTANDARDLINEREC();
                    if (dto.getNum() != null && StringUtils.isNotEmpty(dto.getNum().replace(CodePrefix.PURCHASE_ORDER_1.code(), "")
                            .replace(CodePrefix.PURCHASE_ORDER_3.code(), ""))) {
                        appscuxpostandardlinerec.setHEADERINTERFACEID(objFactory.createAPPSCUXPOSTANDARDLINERECHEADERINTERFACEID
                                (new BigDecimal(dto.getNum().replace(CodePrefix.PURCHASE_ORDER_1.code(), "")
                                        .replace(CodePrefix.PURCHASE_ORDER_3.code(), ""))));
                    }
                    appscuxpostandardlinerec.setLINENUM(objFactory.createAPPSCUXPOSTANDARDLINERECLINENUM(BigDecimal.valueOf(1)));
                    if (orderDetailDto.getLineNumber() != null) {
                        appscuxpostandardlinerec.setLINENUM(objFactory.createAPPSCUXPOSTANDARDLINERECLINENUM(new BigDecimal(orderDetailDto.getLineNumber())));
                    }
                    appscuxpostandardlinerec.setLINEATTRIBUTE7(objFactory.createAPPSCUXPOSTANDARDLINERECLINEATTRIBUTE7(this.line_attribute7(orderDetailDto)));

                    if (orderDetailDto.getWbsSummaryCode() != null) {
                        appscuxpostandardlinerec.setLINEATTRIBUTE6(objFactory.createAPPSCUXPOSTANDARDLINERECLINEATTRIBUTE6(orderDetailDto.getProjectNum()));
                        appscuxpostandardlinerec.setLINEATTRIBUTE8(objFactory.createAPPSCUXPOSTANDARDLINERECLINEATTRIBUTE8(orderDetailDto.getWbsSummaryCode()));
                    } else {
                        appscuxpostandardlinerec.setLINEATTRIBUTE6(objFactory.createAPPSCUXPOSTANDARDLINERECLINEATTRIBUTE6(orderDetailDto.getProjectNum()));
                    }
                    appscuxpostandardlinerec.setUNITPRICE(objFactory.createAPPSCUXPOSTANDARDLINERECUNITPRICE(new BigDecimal("0")));
                    if (orderDetailDto.getDiscountPrice() != null) {
                        appscuxpostandardlinerec.setUNITPRICE(objFactory.createAPPSCUXPOSTANDARDLINERECUNITPRICE(orderDetailDto.getDiscountPrice()));
                    }

                    appscuxpostandardlinerec.setITEM(objFactory.createAPPSCUXPOSTANDARDLINERECITEM(orderDetailDto.getErpCode()));
                    appscuxpostandardlinerec.setQUANTITY(objFactory.createAPPSCUXPOSTANDARDLINERECQUANTITY(orderDetailDto.getOrderNum()));
                    appscuxpostandardlinerec.setUOMCODE(objFactory.createAPPSCUXPOSTANDARDLINERECUOMCODE(orderDetailDto.getUnitCode()));
                    appscuxpostandardlinerec.setNEEDBYDATE(objFactory.createAPPSCUXPOSTANDARDLINERECNEEDBYDATE(DateUtils.format(orderDetailDto.getDeliveryTime(), DateUtils.FORMAT_SHORT)));
                    appscuxpostandardlinerec.setSHIPTOORGANIZATIONID(objFactory.createAPPSCUXPOSTANDARDLINERECSHIPTOORGANIZATIONID(new BigDecimal(orderDetailDto.getOrganizationId())));
                    appscuxpostandardlinerec.setLINEATTRIBUTE1(objFactory.createAPPSCUXPOSTANDARDLINERECLINEATTRIBUTE1("0"));
                    appscuxpostandardlinerec.setLINEATTRIBUTE5(objFactory.createAPPSCUXPOSTANDARDLINERECLINEATTRIBUTE5(orderDetailDto.getSecondaryInventoryName()));
                    appscuxpostandardlinetbl.getPIFACELINESTBLITEM().add(appscuxpostandardlinerec);
                }
            }
            param.setPIFACELINESTBL(objFactory.createInputParametersPIFACELINESTBL(appscuxpostandardlinetbl));
            param.setPIFACEHEADERSTBL(objFactory.createInputParametersPIFACEHEADERSTBL(appscuxpostandardheadertbl));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
        try {
            cuxpostandardpoapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_standard_po_api_pkg.ISGServiceFaultMessage e) {
            LOGGER.error("采购订单写入报错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }

    }

    private String line_attribute7(PurchaseOrderDetailDto purchaseOrderDetailDto) {
        if (null == purchaseOrderDetailDto) {
            return "#";
        }

        StringBuffer sb = new StringBuffer("#");
        // #批次号#PO行图纸版本号#跟踪日期#单价#折后#图号/型号#需求发布日期#承诺日期
        String designReleaseLotNumber = purchaseOrderDetailDto.getDesignReleaseLotNumber();
        String chartVersion = purchaseOrderDetailDto.getChartVersion();
        Date trackDate = purchaseOrderDetailDto.getTrackDate();
        BigDecimal unitPrice = purchaseOrderDetailDto.getUnitPrice();
        BigDecimal discount = purchaseOrderDetailDto.getDiscount();
        String model = purchaseOrderDetailDto.getModel();
        Date publishTime = purchaseOrderDetailDto.getPublishTime();
        Date contractAppointDate = purchaseOrderDetailDto.getContractAppointDate();
        if (StringUtils.isNotEmpty(designReleaseLotNumber)) {
            sb.append(designReleaseLotNumber);
        }
        sb.append("#");
        if (StringUtils.isNotEmpty(chartVersion)) {
            sb.append(chartVersion);
        }
        sb.append("#");
        if (Objects.nonNull(trackDate)) {
            sb.append(DateUtil.dateToString("yyyy-MM-dd", trackDate));
        }
        sb.append("#");
        if (Objects.nonNull(unitPrice)) {
            sb.append(unitPrice);
        }
        sb.append("#");
        if (Objects.nonNull(discount)) {
            sb.append(discount);
        }
        sb.append("#");
        if (StringUtils.isNotEmpty(model)) {
            sb.append(model);
        }
        sb.append("#");
        if (Objects.nonNull(publishTime)) {
            sb.append(DateUtil.dateToString("yyyy-MM-dd", publishTime));
        }
        sb.append("#");
        if (Objects.nonNull(contractAppointDate)) {
            sb.append(DateUtil.dateToString("yyyy-MM-dd", contractAppointDate));
        }

        return sb.toString();
    }

    /**
     * PAM-ERP-047 GSC触发ERP请求
     */
    @Override
    public EsbResponse callCUXPOCOMMONREQAPIPKGPortType(DifferenceShareEsbSyncDTO dto) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_common_req_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getPoCommonSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_common_req_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_common_req_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_common_req_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_common_req_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        try {
            APPSCUXPOCOMMONREQTBL appscuxpocommonreqtbl = buildAPPSCUXPOCOMMONREQTBL(dto, headerRequest, objFactory);
            param.setPIFACEHEADERSTBL(objFactory.createInputParametersPIFACEHEADERSTBL(appscuxpocommonreqtbl));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
        try {
            cuxpocommonreqapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse esbResponse = new EsbResponse();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_common_req_api_pkg.ISGServiceFaultMessage e) {
            LOGGER.error("GSC触发ERP请求报错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    private APPSCUXPOCOMMONREQTBL buildAPPSCUXPOCOMMONREQTBL(DifferenceShareEsbSyncDTO dto, RequestHeader headerRequest,
                                                             com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_po_common_req_api_pkg.do_import.ObjectFactory objFactory) {
        // 判断不同的接口
        String interfaceCode = dto.getInterfaceCode();

        APPSCUXPOCOMMONREQTBL appscuxpocommonreqtbl = objFactory.createAPPSCUXPOCOMMONREQTBL();
        //header
        APPSCUXPOCOMMONREQREC appscuxpocommonreqrec = objFactory.createAPPSCUXPOCOMMONREQREC();
        appscuxpocommonreqrec.setESBDATASOURCE(objFactory.createAPPSCUXPOCOMMONREQRECESBDATASOURCE(dto.getInterfaceCode()));
        appscuxpocommonreqrec.setSOURCECODE(objFactory.createAPPSCUXPOCOMMONREQRECSOURCECODE(headerRequest.getRequestId()));
        appscuxpocommonreqrec.setESBSERIALNUM(objFactory.createAPPSCUXPOCOMMONREQRECESBSERIALNUM(headerRequest.getSerialNo()));
        appscuxpocommonreqrec.setSOURCENUM(objFactory.createAPPSCUXPOCOMMONREQRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));//todo 异步回调唯一标识
        appscuxpocommonreqrec.setAPPSHOTNAME(objFactory.createAPPSCUXPOCOMMONREQRECAPPSHOTNAME(DifferenceShareEsb.APP_SHOT_NAME));
        appscuxpocommonreqrec.setCONCURRENTPROGRAMNAME(objFactory.createAPPSCUXPOCOMMONREQRECCONCURRENTPROGRAMNAME(dto.getInterfaceName()));

        if (DifferenceShareEsb.InvoicePriceDifferenceRecord.ESB_DATA_SOURCE.equals(interfaceCode)) {
            appscuxpocommonreqrec.setARGUMENT1(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT1(String.valueOf(dto.getOuId())));
            appscuxpocommonreqrec.setARGUMENT2(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT2(dto.getGlStartDate()));
            appscuxpocommonreqrec.setARGUMENT3(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT3(dto.getGlEndDate()));
            appscuxpocommonreqrec.setARGUMENT8(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT8("明细"));
            appscuxpocommonreqrec.setARGUMENT11(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT11("PAM"));
        } else if (DifferenceShareEsb.MaterialUpdateDifferenceRecord.ESB_DATA_SOURCE.equals(interfaceCode)) {
            appscuxpocommonreqrec.setARGUMENT1(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT1(String.valueOf(dto.getOrganizationId())));
            appscuxpocommonreqrec.setARGUMENT2(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT2(dto.getGlStartDate()));
            appscuxpocommonreqrec.setARGUMENT3(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT3(dto.getGlEndDate()));
        } else if (DifferenceShareEsb.PurchasePriceDifferenceRecord.ESB_DATA_SOURCE.equals(interfaceCode)) {
            appscuxpocommonreqrec.setARGUMENT1(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT1(String.valueOf(dto.getOuId())));
            appscuxpocommonreqrec.setARGUMENT2(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT2(String.valueOf(dto.getOrganizationId())));
            appscuxpocommonreqrec.setARGUMENT3(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT3(dto.getGlStartDate()));
            appscuxpocommonreqrec.setARGUMENT4(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT4(dto.getGlEndDate()));
            appscuxpocommonreqrec.setARGUMENT11(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT11("PAM"));
        } else if (DifferenceShareEsb.SubjectBalanceRecord.ESB_DATA_SOURCE.equals(interfaceCode)) {
            appscuxpocommonreqrec.setARGUMENT1(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT1(String.valueOf(dto.getLedgerId()))); // 帐套ID
            appscuxpocommonreqrec.setARGUMENT2(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT2("50428"));
            appscuxpocommonreqrec.setARGUMENT3(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT3(dto.getCompanyCode()));
            appscuxpocommonreqrec.setARGUMENT4(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT4(dto.getGlPeriod()));
            appscuxpocommonreqrec.setARGUMENT5(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT5(dto.getGlPeriod()));
            appscuxpocommonreqrec.setARGUMENT8(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT8("N"));// 是否显示0数据（Y/N）
            appscuxpocommonreqrec.setARGUMENT9(objFactory.createAPPSCUXPOCOMMONREQRECARGUMENT9("PAM"));
        }

        appscuxpocommonreqtbl.getPIFACEHEADERSTBLITEM().add(appscuxpocommonreqrec);

        return appscuxpocommonreqtbl;
    }

    /**
     * PAM-ERP-007 项目号写入
     */
    @Override
    public EsbResponse callCUXESBCOMMONINAPIPKGPortType(ProjectDto dto) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getCommonInSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        APPSCUXESBCOMMONINREC appscuxesbcommoninrec = objFactory.createAPPSCUXESBCOMMONINREC();
        appscuxesbcommoninrec.setESBDATASOURCE(objFactory.createAPPSCUXESBCOMMONINRECESBDATASOURCE(BusinessTypeEnums.CREATE_PROJECT_CODE_ERP.getCode()));
        appscuxesbcommoninrec.setSOURCECODE(objFactory.createAPPSCUXESBCOMMONINRECSOURCECODE(headerRequest.getRequestId()));
        appscuxesbcommoninrec.setESBSERIALNUM(objFactory.createAPPSCUXESBCOMMONINRECESBSERIALNUM(headerRequest.getSerialNo()));
        appscuxesbcommoninrec.setSOURCENUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCENUM(headerRequest.getSerialNo() + dto.getId()));
        appscuxesbcommoninrec.setSOURCEDTLNUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCEDTLNUM(""));//todo
        appscuxesbcommoninrec.setSOURCELINENUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCELINENUM(""));//todo
        appscuxesbcommoninrec.setCOL1(objFactory.createAPPSCUXESBCOMMONINRECCOL1(String.valueOf(dto.getOuId())));
        appscuxesbcommoninrec.setCOL2(objFactory.createAPPSCUXESBCOMMONINRECCOL2(dto.getOuName()));
        appscuxesbcommoninrec.setCOL3(objFactory.createAPPSCUXESBCOMMONINRECCOL3(dto.getCode()));
        appscuxesbcommoninrec.setCOL4(objFactory.createAPPSCUXESBCOMMONINRECCOL4(dto.getName()));
        appscuxesbcommoninrec.setCOL5(objFactory.createAPPSCUXESBCOMMONINRECCOL5(dto.getManagerMip()));
        appscuxesbcommoninrec.setCOL6(objFactory.createAPPSCUXESBCOMMONINRECCOL6(dto.getManagerName()));
        appscuxesbcommoninrec.setCOL7(objFactory.createAPPSCUXESBCOMMONINRECCOL7(dto.getFinancialMip()));
        appscuxesbcommoninrec.setCOL8(objFactory.createAPPSCUXESBCOMMONINRECCOL8(dto.getFinancialName()));
        appscuxesbcommoninrec.setCOL9(objFactory.createAPPSCUXESBCOMMONINRECCOL9(String.valueOf(dto.getStatus())));


        APPSCUXESBCOMMONINTBL appscuxesbcommonintbl = objFactory.createAPPSCUXESBCOMMONINTBL();
        appscuxesbcommonintbl.getPESBCOMMTBLITEM().add(appscuxesbcommoninrec);
        param.setPESBCOMMTBL(objFactory.createInputParametersPESBCOMMTBL(appscuxesbcommonintbl));

        try {
            EsbResponse esbResponse = new EsbResponse();
            cuxesbcommoninapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (Exception e) {
            LOGGER.error("写入项目号到ERP报错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }

    }

    /**
     * PAM-ERP-037 子库货位创建
     */
    @Override
    public EsbResponse callCUXESBCOMMONINAPIPKGPortType(ProjectProfitDto dto) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getCommonInSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();
        APPSCUXESBCOMMONINTBL appscuxesbcommonintbl = objFactory.createAPPSCUXESBCOMMONINTBL();
        for (StorageInventory inventory : dto.getStorageInventories()) {
            APPSCUXESBCOMMONINREC storge = objFactory.createAPPSCUXESBCOMMONINREC();
            storge.setESBDATASOURCE(objFactory.createAPPSCUXESBCOMMONINRECESBDATASOURCE(BusinessTypeEnums.CREATE_STORAGE_LOCATOR.getCode()));
            storge.setSOURCECODE(objFactory.createAPPSCUXESBCOMMONINRECSOURCECODE(headerRequest.getRequestId()));
            storge.setESBSERIALNUM(objFactory.createAPPSCUXESBCOMMONINRECESBSERIALNUM(headerRequest.getSerialNo()));
            storge.setSOURCENUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCENUM(headerRequest.getSerialNo() + dto.getId()));
            storge.setSOURCEDTLNUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCEDTLNUM(""));//todo
            storge.setSOURCELINENUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCELINENUM(""));//todo
            storge.setCOL1(objFactory.createAPPSCUXESBCOMMONINRECCOL1(String.valueOf(dto.getStorageId())));//库存组织ID
            // todo
            storge.setCOL2(objFactory.createAPPSCUXESBCOMMONINRECCOL2(inventory.getSecondaryInventoryName()));//子库存代码
            // todo
            storge.setCOL3(objFactory.createAPPSCUXESBCOMMONINRECCOL3(dto.getProjectCode()));//货位代码 - 项目号
            storge.setCOL4(objFactory.createAPPSCUXESBCOMMONINRECCOL4(dto.getProjectName()));//货位描述 - 项目名称
            appscuxesbcommonintbl.getPESBCOMMTBLITEM().add(storge);
        }
        param.setPESBCOMMTBL(objFactory.createInputParametersPESBCOMMTBL(appscuxesbcommonintbl));
        try {
            EsbResponse esbResponse = new EsbResponse();
            cuxesbcommoninapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (Exception e) {
            LOGGER.error("创建子库货位出错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-057 wbs号写入
     *
     * @param dtoList
     * @return
     */
    @Override
    public EsbResponse callCUXESBCOMMONINAPIPKGPortType(List<CommonByErpDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.SOAHeader soaHeader = EsbCtcUtil.getCommonInSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.ObjectFactory objFactory = new com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.InputParameters param = objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        List<APPSCUXESBCOMMONINREC> headList = new ArrayList<>();
        List<APPSCUXESBCOMMONINREC> detailList = new ArrayList<>();
        for (CommonByErpDto dto : dtoList) {
            // 头数据
            APPSCUXESBCOMMONINREC appscuxesbcommoninrec = objFactory.createAPPSCUXESBCOMMONINREC();
            appscuxesbcommoninrec.setESBDATASOURCE(objFactory.createAPPSCUXESBCOMMONINRECESBDATASOURCE(BusinessTypeEnums.WBS_CODE.getCode()));
            appscuxesbcommoninrec.setSOURCECODE(objFactory.createAPPSCUXESBCOMMONINRECSOURCECODE(headerRequest.getRequestId()));
            appscuxesbcommoninrec.setESBSERIALNUM(objFactory.createAPPSCUXESBCOMMONINRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxesbcommoninrec.setSOURCENUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));
            appscuxesbcommoninrec.setSOURCEDTLNUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCEDTLNUM(""));
            appscuxesbcommoninrec.setSOURCELINENUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCELINENUM(""));
            appscuxesbcommoninrec.setRANK(objFactory.createAPPSCUXESBCOMMONINRECRANK(BigDecimal.valueOf(1)));
            appscuxesbcommoninrec.setCOL1(objFactory.createAPPSCUXESBCOMMONINRECCOL1(dto.getWbsCode()));
            appscuxesbcommoninrec.setCOL2(objFactory.createAPPSCUXESBCOMMONINRECCOL2(dto.getInstructions()));
            appscuxesbcommoninrec.setCOL3(objFactory.createAPPSCUXESBCOMMONINRECCOL3(dto.getWbsType()));
            appscuxesbcommoninrec.setCOL4(objFactory.createAPPSCUXESBCOMMONINRECCOL4(String.valueOf(dto.getLedgerId())));
            appscuxesbcommoninrec.setCOL5(objFactory.createAPPSCUXESBCOMMONINRECCOL5(String.valueOf(dto.getOuId())));
            appscuxesbcommoninrec.setCOL6(objFactory.createAPPSCUXESBCOMMONINRECCOL6(dto.getProfitCenter()));
            appscuxesbcommoninrec.setCOL7(objFactory.createAPPSCUXESBCOMMONINRECCOL7(dto.getProductGroup()));
            appscuxesbcommoninrec.setCOL9(objFactory.createAPPSCUXESBCOMMONINRECCOL9(dto.getStartDate()));
            appscuxesbcommoninrec.setCOL10(objFactory.createAPPSCUXESBCOMMONINRECCOL10(dto.getEndDate()));
            headList.add(appscuxesbcommoninrec);

            // 行数据
            APPSCUXESBCOMMONINREC appscuxesbcommoninrec1 = objFactory.createAPPSCUXESBCOMMONINREC();
            appscuxesbcommoninrec1.setESBDATASOURCE(objFactory.createAPPSCUXESBCOMMONINRECESBDATASOURCE(BusinessTypeEnums.WBS_CODE.getCode()));
            appscuxesbcommoninrec1.setSOURCECODE(objFactory.createAPPSCUXESBCOMMONINRECSOURCECODE(headerRequest.getRequestId()));
            appscuxesbcommoninrec1.setESBSERIALNUM(objFactory.createAPPSCUXESBCOMMONINRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxesbcommoninrec1.setSOURCENUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));
            appscuxesbcommoninrec1.setSOURCEDTLNUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCEDTLNUM(""));
            appscuxesbcommoninrec1.setSOURCELINENUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCELINENUM(""));
            appscuxesbcommoninrec1.setRANK(objFactory.createAPPSCUXESBCOMMONINRECRANK(BigDecimal.valueOf(2)));
            appscuxesbcommoninrec1.setCOL1(objFactory.createAPPSCUXESBCOMMONINRECCOL1(dto.getWbsCode()));
            appscuxesbcommoninrec1.setCOL2(objFactory.createAPPSCUXESBCOMMONINRECCOL2(dto.getWbsType()));
            appscuxesbcommoninrec1.setCOL3(objFactory.createAPPSCUXESBCOMMONINRECCOL3(dto.getProfitCenter()));
            appscuxesbcommoninrec1.setCOL4(objFactory.createAPPSCUXESBCOMMONINRECCOL4(dto.getProductGroup()));
            appscuxesbcommoninrec1.setCOL5(objFactory.createAPPSCUXESBCOMMONINRECCOL5(dto.getPercent()));
            appscuxesbcommoninrec1.setCOL7(objFactory.createAPPSCUXESBCOMMONINRECCOL7(dto.getStartDate()));
            appscuxesbcommoninrec1.setCOL8(objFactory.createAPPSCUXESBCOMMONINRECCOL8(dto.getEndDate()));
            appscuxesbcommoninrec1.setCOL10(objFactory.createAPPSCUXESBCOMMONINRECCOL10(String.valueOf(dto.getOuId())));
            detailList.add(appscuxesbcommoninrec1);
        }

        APPSCUXESBCOMMONINTBL appscuxesbcommonintbl = objFactory.createAPPSCUXESBCOMMONINTBL();
        appscuxesbcommonintbl.getPESBCOMMTBLITEM().addAll(headList);//头
        appscuxesbcommonintbl.getPESBCOMMTBLITEM().addAll(detailList);//行
        param.setPESBCOMMTBL(objFactory.createInputParametersPESBCOMMTBL(appscuxesbcommonintbl));

        try {
            EsbResponse esbResponse = new EsbResponse();
            cuxesbcommoninapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (Exception e) {
            LOGGER.error("写入WBS号到ERP报错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    //PAM-ERP-074 应收发票到期日变更推送
    @Override
    public EsbResponse callCUXESBCOMMONINAPIPKGDDPortType(CommonByErpDto dto) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getCommonInSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_common_in_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        //头数据
        APPSCUXESBCOMMONINREC appscuxesbcommoninrec = objFactory.createAPPSCUXESBCOMMONINREC();
        appscuxesbcommoninrec.setESBDATASOURCE(objFactory.createAPPSCUXESBCOMMONINRECESBDATASOURCE(dto.getEsbDataSource()));
        appscuxesbcommoninrec.setSOURCECODE(objFactory.createAPPSCUXESBCOMMONINRECSOURCECODE(headerRequest.getRequestId()));
        appscuxesbcommoninrec.setESBSERIALNUM(objFactory.createAPPSCUXESBCOMMONINRECESBSERIALNUM(headerRequest.getSerialNo()));
        appscuxesbcommoninrec.setSOURCENUM(objFactory.createAPPSCUXESBCOMMONINRECSOURCENUM(headerRequest.getSerialNo() + "_" + dto.getId()));
        appscuxesbcommoninrec.setCOL1(objFactory.createAPPSCUXESBCOMMONINRECCOL1(dto.getCol1()));
        appscuxesbcommoninrec.setCOL2(objFactory.createAPPSCUXESBCOMMONINRECCOL2(dto.getCol2()));
        appscuxesbcommoninrec.setCOL3(objFactory.createAPPSCUXESBCOMMONINRECCOL3(dto.getCol3()));
        appscuxesbcommoninrec.setCOL4(objFactory.createAPPSCUXESBCOMMONINRECCOL4(dto.getCol4()));

        APPSCUXESBCOMMONINTBL appscuxesbcommonintbl = objFactory.createAPPSCUXESBCOMMONINTBL();
        appscuxesbcommonintbl.getPESBCOMMTBLITEM().add(appscuxesbcommoninrec);//头
        param.setPESBCOMMTBL(objFactory.createInputParametersPESBCOMMTBL(appscuxesbcommonintbl));

        try {
            EsbResponse esbResponse = new EsbResponse();
            cuxesbcommoninapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (Exception e) {
            LOGGER.error("应收发票到期日变更推送报错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    @Override
    public List<ERPMassQueryReturnVo> callEsbMassQuery(String pifacedoe, Map<String, String> paramMap) {
        // 记录接口日志
        EsbMassQueryRecord esbMassQueryRecord = new EsbMassQueryRecord();
        esbMassQueryRecord.setBusinessType(pifacedoe);
        if (paramMap != null && paramMap.size() > 0) {
            esbMassQueryRecord.setErpIpP01(paramMap.get(EsbConstant.ERP_IP_P01));
            esbMassQueryRecord.setErpIpP02(paramMap.get(EsbConstant.ERP_IP_P02));
            esbMassQueryRecord.setErpIpP03(paramMap.get(EsbConstant.ERP_IP_P03));
            esbMassQueryRecord.setErpIpP04(paramMap.get(EsbConstant.ERP_IP_P04));
        }
        esbMassQueryRecord.setStatus(1);
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        try {
            List<ERPMassQueryReturnVo> list = erpMassQueryStrategyHelper.main(headerRequest, pifacedoe, paramMap);
            esbMassQueryRecord.setNum(list != null ? list.size() : 0L);
            return list;
        } catch (
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_esb_mass_query_pkg.ISGServiceFaultMessage isgServiceFaultMessage) {
            LOGGER.info("ERP通用查询异常, 流水号:{}", headerRequest.getSerialNo(), isgServiceFaultMessage);
            esbMassQueryRecord.setStatus(2);
            throw new BizException(Code.ERROR);
        } finally {
            esbMassQueryRecord.setEsbSerialNo(headerRequest.getSerialNo());
            esbMassQueryRecordFeignClient.add(esbMassQueryRecord);
        }
    }

    /**
     * PAM-ERP-029 总账日记账写入
     */
    @Override
    public EsbResponse callCUXGLJOURNALSIMPORTAPIPKGPortType(List<CostAccountingErpDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_gl_journals_import_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getGlJournalsSoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_gl_journals_import_api_pkg.do_import.ObjectFactory objFactory = new
                com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_gl_journals_import_api_pkg.do_import.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_gl_journals_import_api_pkg.do_import.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_gl_journals_import_api_pkg.do_import.OutputParameters> outParam = new javax.xml.ws.Holder<>();

        APPSCUXGLJOURNALTBL appscuxgljournaltbl = objFactory.createAPPSCUXGLJOURNALTBL();
        for (CostAccountingErpDto dto : dtoList) {
            APPSCUXGLJOURNALREC appscuxgljournalrec = objFactory.createAPPSCUXGLJOURNALREC();
            appscuxgljournalrec.setESBDATASOURCE(objFactory.createAPPSCUXGLJOURNALRECESBDATASOURCE(dto.getEsbDataSource()));
            appscuxgljournalrec.setSOURCECODE(objFactory.createAPPSCUXGLJOURNALRECSOURCECODE(headerRequest.getRequestId()));
            // erp 查询优化,配合调整sourcenum顺序
            appscuxgljournalrec.setSOURCENUM(objFactory.createAPPSCUXGLJOURNALRECSOURCENUM(dto.getId() + "_" + headerRequest.getSerialNo() + dto.getLineNumber()));
            appscuxgljournalrec.setESBSERIALNUM(objFactory.createAPPSCUXGLJOURNALRECESBSERIALNUM(headerRequest.getSerialNo()));
            appscuxgljournalrec.setLEDGERID(objFactory.createAPPSCUXGLJOURNALRECLEDGERID(dto.getLedgerId()));
            appscuxgljournalrec.setSTATUS(objFactory.createAPPSCUXGLJOURNALRECSTATUS(dto.getStatus()));
            appscuxgljournalrec.setACCOUNTINGDATE(objFactory.createAPPSCUXGLJOURNALRECACCOUNTINGDATE(dto.getAccountingDate()));
            appscuxgljournalrec.setACTUALFLAG(objFactory.createAPPSCUXGLJOURNALRECACTUALFLAG(dto.getActualFlag()));
            appscuxgljournalrec.setUSERJESOURCENAME(objFactory.createAPPSCUXGLJOURNALRECUSERJESOURCENAME(headerRequest.getRequestId()));
            appscuxgljournalrec.setUSERJECATEGORYNAME(objFactory.createAPPSCUXGLJOURNALRECUSERJECATEGORYNAME(dto.getUserJeCategoryName()));
            appscuxgljournalrec.setCURRENCYCODE(objFactory.createAPPSCUXGLJOURNALRECCURRENCYCODE(dto.getCurrencyCode()));
            appscuxgljournalrec.setUSERCURRENCYCONVERSIONTYPE(objFactory.createAPPSCUXGLJOURNALRECUSERCURRENCYCONVERSIONTYPE(dto.getUserCurrencyConversionType()));
            appscuxgljournalrec.setCURRENCYCONVERSIONDATE(objFactory.createAPPSCUXGLJOURNALRECCURRENCYCONVERSIONDATE(dto.getCurrencyConversionDate()));
            appscuxgljournalrec.setCURRENCYCONVERSIONRATE(objFactory.createAPPSCUXGLJOURNALRECCURRENCYCONVERSIONRATE(dto.getCurrencyConversionRate()));
            appscuxgljournalrec.setBATHNAME(objFactory.createAPPSCUXGLJOURNALRECBATHNAME(dto.getBathName()));//erp头id
            // ，用于区分不同单据
            appscuxgljournalrec.setJOURNALNAME(objFactory.createAPPSCUXGLJOURNALRECJOURNALNAME(dto.getJournalName()));
            appscuxgljournalrec.setENTEREDDR(objFactory.createAPPSCUXGLJOURNALRECENTEREDDR(dto.getEnterEddr()));
            appscuxgljournalrec.setENTEREDCR(objFactory.createAPPSCUXGLJOURNALRECENTEREDCR(dto.getEnterEdcr()));
            appscuxgljournalrec.setACCOUNTEDDR(objFactory.createAPPSCUXGLJOURNALRECACCOUNTEDDR(dto.getAccountEddr()));
            appscuxgljournalrec.setACCOUNTEDCR(objFactory.createAPPSCUXGLJOURNALRECACCOUNTEDCR(dto.getAccountEdcr()));
            appscuxgljournalrec.setREFERENCE5(objFactory.createAPPSCUXGLJOURNALRECREFERENCE5(dto.getReference5()));
            appscuxgljournalrec.setREFERENCE10(objFactory.createAPPSCUXGLJOURNALRECREFERENCE10(dto.getReference10()));
            appscuxgljournalrec.setJGZZRECONREF(objFactory.createAPPSCUXGLJOURNALRECJGZZRECONREF(dto.getJgzzreconref()));
            appscuxgljournalrec.setOPERATIONTYPE(objFactory.createAPPSCUXGLJOURNALRECOPERATIONTYPE(dto.getOperationType()));
            appscuxgljournalrec.setSEGMENT1(objFactory.createAPPSCUXGLJOURNALRECSEGMENT1(dto.getSegment1()));
            appscuxgljournalrec.setSEGMENT2(objFactory.createAPPSCUXGLJOURNALRECSEGMENT2(dto.getSegment2()));
            appscuxgljournalrec.setSEGMENT3(objFactory.createAPPSCUXGLJOURNALRECSEGMENT3(dto.getSegment3()));
            appscuxgljournalrec.setSEGMENT4(objFactory.createAPPSCUXGLJOURNALRECSEGMENT4(dto.getSegment4()));
            appscuxgljournalrec.setSEGMENT5(objFactory.createAPPSCUXGLJOURNALRECSEGMENT5(dto.getSegment5()));
            appscuxgljournalrec.setSEGMENT6(objFactory.createAPPSCUXGLJOURNALRECSEGMENT6(dto.getSegment6()));
            appscuxgljournalrec.setSEGMENT7(objFactory.createAPPSCUXGLJOURNALRECSEGMENT7(dto.getSegment7()));
            appscuxgljournaltbl.getPIFACETBLITEM().add(appscuxgljournalrec);
        }
        param.setPIFACETBL(objFactory.createInputParametersPIFACETBL(appscuxgljournaltbl));
        try {
            EsbResponse esbResponse = new EsbResponse();
            cuxgljournalsimportapipkgPortType.doIMPORT(headerRequest, soaHeader, param, headerReply, outParam);
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (Exception e) {
            LOGGER.error("总账日记账写入出错", e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    @Override
    public Boolean changeMassQueryInterface(int i) {
        erpMassQueryStrategyHelper.setStrategy(i);
        return Boolean.TRUE;
    }

    @Override
    public EsbResponse callGemsBusinessNumberService(BusinessDto businessDto) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        GemsProjectNumber.BodyInput bodyInput = new GemsProjectNumber.BodyInput();
        GemsProjectNumber.BodyInput.ItemDetail itemDetail = new GemsProjectNumber.BodyInput.ItemDetail();
        itemDetail.setOuId(String.valueOf(businessDto.getOperatingUnitId()));
        itemDetail.setOuCode(businessDto.getOuCode());
        itemDetail.setOuName(businessDto.getOperatingUnitName());
        itemDetail.setProjectNumber(businessDto.getBusinessCode());
        itemDetail.setProjectName(businessDto.getName());
        if (Objects.equals(1, businessDto.getStatus())) { //审批通过/关闭(放弃)后重新打开
            itemDetail.setStatus("Y");
        } else if (Objects.equals(0, businessDto.getStatus()) || Objects.equals(3, businessDto.getStatus())) { // 关闭
            // (放弃)/商机赢单（后45天）
            itemDetail.setStatus("N");
        }
        itemDetail.setType("business");
        bodyInput.getItemDetail().add(itemDetail);
        try {
            final GemsProjectNumberResponse.Result res = gemsProjectNumberService.gemsProjectNumber(bodyInput,
                    headerRequest);
            return new EsbResponse(res.getResponseType(), res.getRespnoseCode(), res.getResponseMessage(),
                    headerRequest.getSerialNo());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return new EsbResponse("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

    /**
     * PAM-ERP-073
     * 正负发票核销
     *
     * @param dtoList
     * @return
     */
    @Override
    public EsbResponse<String> callCUXPOSITIVEANDNEGATIVEINCOICESAPIPKGPortType(List<PositiveAndNegativeInvoiceRecordDto> dtoList) {
        RequestHeader headerRequest = EsbCtcUtil.getPamRequestHeader();
        Holder<ReplyInformation> headerReply = new Holder<>();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.SOAHeader soaHeader =
                EsbCtcUtil.getApPaySoaHeader();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.ObjectFactory objFactory =
                new com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.ObjectFactory();
        com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.InputParameters param =
                objFactory.createInputParameters();
        javax.xml.ws.Holder<com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.do_insert.OutputParameters> outParam =
                new javax.xml.ws.Holder<>();
        APPSCUXAPCHECKTBL appscuxapchecktbl = objFactory.createAPPSCUXAPCHECKTBL();
        APPSCUXAPPAYMENTTBL appscuxappaymenttbl = objFactory.createAPPSCUXAPPAYMENTTBL();
        for (PositiveAndNegativeInvoiceRecordDto recordDto : dtoList) {
            // 头信息
            APPSCUXAPCHECKREC appscuxapcheckrec = objFactory.createAPPSCUXAPCHECKREC();
            // OU组织ID
            appscuxapcheckrec.setORGID(objFactory.createAPPSCUXAPCHECKRECORGID(new BigDecimal(recordDto.getOrgId())));
            // source_code
            appscuxapcheckrec.setSOURCECODE(objFactory.createAPPSCUXAPCHECKRECSOURCECODE(recordDto.getSourceCode()));
            // source_num
            appscuxapcheckrec.setSOURCENUM(objFactory.createAPPSCUXAPCHECKRECSOURCENUM(headerRequest.getSerialNo() + "_" + recordDto.getSourceNum()));
            // esb_serial_num
            appscuxapcheckrec.setESBSERIALNUM(objFactory.createAPPSCUXAPCHECKRECESBSERIALNUM(headerRequest.getSerialNo()));
            // esb_data_source ：PAM-ERP-073
            appscuxapcheckrec.setESBDATASOURCE(objFactory.createAPPSCUXAPCHECKRECESBDATASOURCE(BusinessTypeEnums.POSITIVE_AND_NEGATIVE_RECORD.getCode()));
            // fs_check_number ：付款申请编号
            appscuxapcheckrec.setFSCHECKNUMBER(objFactory.createAPPSCUXAPCHECKRECFSCHECKNUMBER(recordDto.getApplyNo()));
            // payment_type_flag ：Q
            appscuxapcheckrec.setPAYMENTTYPEFLAG(objFactory.createAPPSCUXAPCHECKRECPAYMENTTYPEFLAG(recordDto.getTypeFlag()));
            // vendor_id ：erp供应商 id
            appscuxapcheckrec.setVENDORID(objFactory.createAPPSCUXAPCHECKRECVENDORID(new BigDecimal(recordDto.getVendorId())));
            // vendor_site_id ：erp供应商地址id
            appscuxapcheckrec.setVENDORSITEID(objFactory.createAPPSCUXAPCHECKRECVENDORSITEID(new BigDecimal(recordDto.getVendorSitId())));
            // check_date ：审批通过日期
            appscuxapcheckrec.setCHECKDATE(objFactory.createAPPSCUXAPCHECKRECCHECKDATE(recordDto.getCheckDate()));
            // bank_account_id ：银行账号id
            appscuxapcheckrec.setBANKACCOUNTID(objFactory.createAPPSCUXAPCHECKRECBANKACCOUNTID(new BigDecimal(recordDto.getBankAccountId())));
            // bank_account_name ：银行账号名称
            appscuxapcheckrec.setBANKACCOUNTNAME(objFactory.createAPPSCUXAPCHECKRECBANKACCOUNTNAME(recordDto.getBankAccountName()));
            // payment_method_lookup_code：DL
            appscuxapcheckrec.setPAYMENTMETHODLOOKUPCODE(objFactory.createAPPSCUXAPCHECKRECPAYMENTMETHODLOOKUPCODE(recordDto.getPaymentMethodCode()));
            // currency_code ：币种
            appscuxapcheckrec.setCURRENCYCODE(objFactory.createAPPSCUXAPCHECKRECCURRENCYCODE(recordDto.getCurrencyCode()));
            // amount ：金额
            appscuxapcheckrec.setAMOUNT(objFactory.createAPPSCUXAPCHECKRECAMOUNT(new BigDecimal(recordDto.getAmount())));
            // remark ：备注
            appscuxapcheckrec.setREMARK(objFactory.createAPPSCUXAPCHECKRECREMARK(recordDto.getRemark()));
            // 行信息
            List<PositiveAndNegativeInvoiceRecordDetails> lins = recordDto.getLins();
            List<APPSCUXAPPAYMENTREC> details = new ArrayList<>();
            for (PositiveAndNegativeInvoiceRecordDetails lin : lins) {
                APPSCUXAPPAYMENTREC appscuxappaymentrec = objFactory.createAPPSCUXAPPAYMENTREC();
                // fs_check_number ：付款申请编号
                appscuxappaymentrec.setFSCHECKNUMBER(objFactory.createAPPSCUXAPPAYMENTRECFSCHECKNUMBER(lin.getFsCheckNum()));
                // invoice_num ："PAM-" + 付款申请编号
                appscuxappaymentrec.setINVOICENUM(objFactory.createAPPSCUXAPPAYMENTRECINVOICENUM(lin.getInvoiceNum()));
                //appscuxappaymentrec.setINVOICEID(objFactory.createAPPSCUXAPPAYMENTRECINVOICEID(new BigDecimal(lin.getInvoiceId())));
                // payment_num ：1
                appscuxappaymentrec.setPAYMENTNUM(objFactory.createAPPSCUXAPPAYMENTRECPAYMENTNUM(new BigDecimal(lin.getPaymentNum())));
                // amount ：本次付款金额(含税)
                appscuxappaymentrec.setAMOUNT(objFactory.createAPPSCUXAPPAYMENTRECAMOUNT(new BigDecimal(lin.getAmount())));
                // gl_date ：审批通过日期
                appscuxappaymentrec.setGLDATE(objFactory.createAPPSCUXAPPAYMENTRECGLDATE(lin.getGlDate()));
                // source_line_num ：1
                appscuxappaymentrec.setSOURCELINENUM(objFactory.createAPPSCUXAPPAYMENTRECSOURCELINENUM(lin.getLineNum()));
                // attribute1 ：付款申请编号
                appscuxappaymentrec.setATTRIBUTE1(objFactory.createAPPSCUXAPPAYMENTRECATTRIBUTE1(lin.getPaymentApplyId()));
                details.add(appscuxappaymentrec);
            }
            appscuxapchecktbl.getPHEADERTBLITEM().add(appscuxapcheckrec);
            appscuxappaymenttbl.getPLINETBLITEM().addAll(details);

        }

        param.setPHEADERTBL(objFactory.createInputParametersPHEADERTBL(appscuxapchecktbl));
        param.setPLINETBL(objFactory.createInputParametersPLINETBL(appscuxappaymenttbl));
        try {
            cuxappaymentsapipkgPortType.doINSERT(headerRequest, soaHeader, param, headerReply, outParam);
            EsbResponse<String> esbResponse = new EsbResponse<>();
            esbResponse.setResponsecode(outParam.value.getRESPONSECODE().getValue());
            esbResponse.setResponsetype(outParam.value.getRESPONSETYPE().getValue());
            esbResponse.setResponsemessage(outParam.value.getRESPONSEMESSAGE().getValue());
            esbResponse.setData(headerRequest.getSerialNo());
            return esbResponse;
        } catch (com.oracle.xmlns.apps.cux.soaprovider.plsql.cux_ap_payments_api_pkg.ISGServiceFaultMessage e) {
            LOGGER.error("写入正负发票核销报错", e);
            return new EsbResponse<>("", ResponseCodeEnums.FAULT.getCode(), e.getMessage(), headerRequest.getSerialNo());
        }
    }

}
