package com.midea.pam.crm.web;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.TeamUserDto;
import com.midea.pam.common.basedata.dto.TeamUsersDto;
import com.midea.pam.common.basedata.query.CustomerQuery;
import com.midea.pam.common.basedata.query.ExportCustomerQuery;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.crm.dto.CustomerContactDto;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.crm.entity.Customer;
import com.midea.pam.common.crm.entity.CustomerBankAccount;
import com.midea.pam.common.crm.entity.CustomerModifyHistory;
import com.midea.pam.common.crm.entity.CustomerUnitRel;
import com.midea.pam.common.crm.excelVo.CustomerExcelVo;
import com.midea.pam.common.crm.excelVo.CustomerImportExcelVo;
import com.midea.pam.common.crm.excelVo.CustomerUnitRelImportExcelVo;
import com.midea.pam.common.crm.query.BusinessQuery;
import com.midea.pam.common.ctc.dto.WorkflowCallbackCommonDto;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.crm.excel.DataImportService;
import com.midea.pam.crm.service.BusinessService;
import com.midea.pam.crm.service.CustomerContactService;
import com.midea.pam.crm.service.CustomerService;
import com.midea.pam.crm.service.CustomerUnitRelService;
import com.midea.pam.crm.service.CustomerWorkflowCallbackService;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;


@Api("客户模块")
@RestController
@RequestMapping("customer")
public class CustomerController {

    @Resource
    private CustomerService customerService;

    @Resource
    private CustomerContactService customerContactService;

    @Resource
    private BusinessService businessService;

    @Resource
    private DataImportService dataImportService;

    @Resource
    private CustomerUnitRelService customerUnitRelService;

    @Resource
    private CustomerWorkflowCallbackService customerWorkflowCallbackService;

    @ApiOperation(value = "发起审批")
    @PutMapping("updateStatusApproval/skipSecurityInterceptor")
    public String draftSubmitCallback(@RequestParam(required = false) Long formInstanceId,
                                      @RequestParam(required = false) String fdInstanceId,
                                      @RequestParam(required = false) String formUrl,
                                      @RequestParam(required = false) String eventName,
                                      @RequestParam(required = false) String handlerId,
                                      @RequestParam(required = false) Long companyId,
                                      @RequestParam(required = false) Long createUserId) {
        WorkflowCallbackCommonDto workflowCallbackCommonDto = new WorkflowCallbackCommonDto();
        workflowCallbackCommonDto.setFormInstanceId(formInstanceId);
        workflowCallbackCommonDto.setFdInstanceId(fdInstanceId);
        workflowCallbackCommonDto.setFormUrl(formUrl);
        workflowCallbackCommonDto.setEventName(eventName);
        workflowCallbackCommonDto.setHandlerId(handlerId);
        workflowCallbackCommonDto.setCompanyId(companyId);
        workflowCallbackCommonDto.setCreateUserId(createUserId);
        customerWorkflowCallbackService.draftSubmit(workflowCallbackCommonDto);
        return "1";
    }

    @ApiOperation(value = "通过")
    @PutMapping("updateStatusEnable/skipSecurityInterceptor")
    public String passCallback(@RequestParam(required = false) Long formInstanceId,
                               @RequestParam(required = false) String fdInstanceId,
                               @RequestParam(required = false) String formUrl,
                               @RequestParam(required = false) String eventName,
                               @RequestParam(required = false) String handlerId,
                               @RequestParam(required = false) Long companyId,
                               @RequestParam(required = false) Long createUserId,
                               @RequestParam(required = false) String timestamp) {
        WorkflowCallbackCommonDto workflowCallbackCommonDto = new WorkflowCallbackCommonDto();
        workflowCallbackCommonDto.setFormInstanceId(formInstanceId);
        workflowCallbackCommonDto.setFdInstanceId(fdInstanceId);
        workflowCallbackCommonDto.setFormUrl(formUrl);
        workflowCallbackCommonDto.setEventName(eventName);
        workflowCallbackCommonDto.setHandlerId(handlerId);
        workflowCallbackCommonDto.setCompanyId(companyId);
        workflowCallbackCommonDto.setCreateUserId(createUserId);
        workflowCallbackCommonDto.setTimestamp(timestamp);
        customerWorkflowCallbackService.pass(workflowCallbackCommonDto);
        return "1";
    }


    @ApiOperation(value = "驳回")
    @PutMapping("updateStatusRefuse/skipSecurityInterceptor")
    public String refuseCallback(@RequestParam(required = false) Long formInstanceId,
                                 @RequestParam(required = false) String fdInstanceId,
                                 @RequestParam(required = false) String formUrl,
                                 @RequestParam(required = false) String eventName,
                                 @RequestParam(required = false) String handlerId,
                                 @RequestParam(required = false) Long companyId,
                                 @RequestParam(required = false) Long createUserId) {
        WorkflowCallbackCommonDto workflowCallbackCommonDto = new WorkflowCallbackCommonDto();
        workflowCallbackCommonDto.setFormInstanceId(formInstanceId);
        workflowCallbackCommonDto.setFdInstanceId(fdInstanceId);
        workflowCallbackCommonDto.setFormUrl(formUrl);
        workflowCallbackCommonDto.setEventName(eventName);
        workflowCallbackCommonDto.setHandlerId(handlerId);
        workflowCallbackCommonDto.setCompanyId(companyId);
        workflowCallbackCommonDto.setCreateUserId(createUserId);
        customerWorkflowCallbackService.refuse(workflowCallbackCommonDto);
        return "1";
    }

    @ApiOperation(value = "撤回")
    @PutMapping("updateStatusReturn/skipSecurityInterceptor")
    public String draftReturnCallback(@RequestParam(required = false) Long formInstanceId,
                                      @RequestParam(required = false) String fdInstanceId,
                                      @RequestParam(required = false) String formUrl,
                                      @RequestParam(required = false) String eventName,
                                      @RequestParam(required = false) String handlerId,
                                      @RequestParam(required = false) Long companyId,
                                      @RequestParam(required = false) Long createUserId) {
        WorkflowCallbackCommonDto workflowCallbackCommonDto = new WorkflowCallbackCommonDto();
        workflowCallbackCommonDto.setFormInstanceId(formInstanceId);
        workflowCallbackCommonDto.setFdInstanceId(fdInstanceId);
        workflowCallbackCommonDto.setFormUrl(formUrl);
        workflowCallbackCommonDto.setEventName(eventName);
        workflowCallbackCommonDto.setHandlerId(handlerId);
        workflowCallbackCommonDto.setCompanyId(companyId);
        workflowCallbackCommonDto.setCreateUserId(createUserId);
        customerWorkflowCallbackService.draftReturn(workflowCallbackCommonDto);
        return "1";
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandonChange/skipSecurityInterceptor")
    public String abandon(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        WorkflowCallbackCommonDto workflowCallbackCommonDto = new WorkflowCallbackCommonDto();
        workflowCallbackCommonDto.setFormInstanceId(formInstanceId);
        workflowCallbackCommonDto.setFdInstanceId(fdInstanceId);
        workflowCallbackCommonDto.setFormUrl(formUrl);
        workflowCallbackCommonDto.setEventName(eventName);
        workflowCallbackCommonDto.setHandlerId(handlerId);
        workflowCallbackCommonDto.setCompanyId(companyId);
        workflowCallbackCommonDto.setCreateUserId(createUserId);
        customerWorkflowCallbackService.abandon(workflowCallbackCommonDto);
        return "1";
    }

    @ApiOperation(value = "删除")
    @PutMapping("deleteChange/skipSecurityInterceptor")
    public String delete(@RequestParam(required = false) Long formInstanceId,
                         @RequestParam(required = false) String fdInstanceId,
                         @RequestParam(required = false) String formUrl,
                         @RequestParam(required = false) String eventName,
                         @RequestParam(required = false) String handlerId,
                         @RequestParam(required = false) Long companyId,
                         @RequestParam(required = false) Long createUserId) {
        WorkflowCallbackCommonDto workflowCallbackCommonDto = new WorkflowCallbackCommonDto();
        workflowCallbackCommonDto.setFormInstanceId(formInstanceId);
        workflowCallbackCommonDto.setFdInstanceId(fdInstanceId);
        workflowCallbackCommonDto.setFormUrl(formUrl);
        workflowCallbackCommonDto.setEventName(eventName);
        workflowCallbackCommonDto.setHandlerId(handlerId);
        workflowCallbackCommonDto.setCompanyId(companyId);
        workflowCallbackCommonDto.setCreateUserId(createUserId);
        customerWorkflowCallbackService.delete(workflowCallbackCommonDto);
        return "1";
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public String agree(@RequestParam(required = false) Long formInstanceId,
                        @RequestParam(required = false) String fdInstanceId,
                        @RequestParam(required = false) String formUrl,
                        @RequestParam(required = false) String eventName,
                        @RequestParam(required = false) String handlerId,
                        @RequestParam(required = false) Long companyId,
                        @RequestParam(required = false) Long createUserId) {
        WorkflowCallbackCommonDto workflowCallbackCommonDto = new WorkflowCallbackCommonDto();
        workflowCallbackCommonDto.setFormInstanceId(formInstanceId);
        workflowCallbackCommonDto.setFdInstanceId(fdInstanceId);
        workflowCallbackCommonDto.setFormUrl(formUrl);
        workflowCallbackCommonDto.setEventName(eventName);
        workflowCallbackCommonDto.setHandlerId(handlerId);
        workflowCallbackCommonDto.setCompanyId(companyId);
        workflowCallbackCommonDto.setCreateUserId(createUserId);
        return "1";
    }


    @ApiOperation(value = "根据客户ID判断当前登录用户是否创建人,true是 false否")
    @GetMapping("/isCreater")
    public Response isCreater(@RequestParam(required = true) Long customerId) {
        return new DataResponse().setData(customerService.isCreater(customerId));
    }

    @ApiOperation(value = "根据客户ID判断当前登录用户是否所有者,true是 false否")
    @GetMapping("/isOwner")
    public Response isOwner(@RequestParam(required = true) Long customerId) {
        return new DataResponse().setData(customerService.isOwner(customerId));
    }

    @ApiOperation(value = "根据客户ID判断当前登录用户是否团队成员,true是 false否")
    @GetMapping("/isMember")
    public Response isMember(@RequestParam(required = true) Long customerId) {
        return new DataResponse().setData(customerService.isMember(customerId));
    }

    @ApiOperation(value = "根据客户ID判断当前登录用户是否有数据权限,true是 false否")
    @GetMapping("/isDataAuthor")
    public Response isAuthor(@RequestParam(required = true) Long customerId) {
        return new DataResponse().setData(customerService.isDataAuthor(customerId));
    }

    @ApiOperation(value = "通过ID查客户信息")
    @GetMapping("/view")
    public Response getCustomerById(@RequestParam(required = false) Long customerId, @RequestParam(required = false) Long unitId) {
        return new DataResponse().setData(customerService.getCustomerById(customerId, unitId));
    }

    @ApiOperation(value = "通过ID查客户信息")
    @GetMapping("/viewByOu")
    public Response viewByOu(@RequestParam(required = false) Long customerId, @RequestParam Long ouId) {
        return new DataResponse().setData(customerService.getCustomerByOuId(customerId, ouId, SystemContext.getUnitId()));
    }

    @ApiOperation(value = "客户新增审批详情")
    @GetMapping("/viewByUnitRel")
    public Response getCustomerByUnitRel(@RequestParam(required = false) Long unitRelId) {
        return new DataResponse().setData(customerService.getCustomerByUnitRel(unitRelId, SystemContext.getUnitId()));
    }

    @ApiOperation(value = "通过ID集合查客户信息")
    @PostMapping("/viewList")
    public Response getCustomerByIds(@RequestBody ExportCustomerQuery param) {
        return new DataResponse().setData(customerService.getCustomerByIds(param.getCustomerIdList(), param.getUnitId()));
    }

    @ApiOperation(value = "移动端通过ID查客户信息")
    @GetMapping("/getCustomerApp")
    public Response getCustomerApp(@RequestParam(required = true) Long unitRelId) {
        DataResponse<ResponseMap> response = Response.dataResponse();
        return response.setData(customerService.getCustomerApp(unitRelId));
    }

    @ApiOperation(value = "移动端通过ID查客户财务信息完善")
    @GetMapping("/getCustomerModifyApp")
    public Response getCustomerModifyApp(@RequestParam(required = true) Long customerId) {
        DataResponse<ResponseMap> response = Response.dataResponse();
        return response.setData(customerService.getCustomerModifyApp(customerId));
    }

    @ApiOperation(value = "移动端通过ID查客户业务信息变更")
    @GetMapping("/getCustomerBusinessModifyApp")
    public Response getCustomerBusinessModifyApp(@RequestParam(required = true) Long customerModifyId) {
        DataResponse<ResponseMap> response = Response.dataResponse();
        return response.setData(customerService.getCustomerBusinessModifyApp(customerModifyId));
    }

    @ApiOperation(value = "移动端通过ID查客户基础信息变更")
    @GetMapping("/getCustomerBaseInfoModifyApp")
    public Response getCustomerBaseInfoModifyApp(@RequestParam(required = true) Long customerModifyId) {
        DataResponse<ResponseMap> response = Response.dataResponse();
        return response.setData(customerService.getCustomerBaseInfoModifyApp(customerModifyId));
    }

    @ApiOperation(value = "通过name查客户信息")
    @GetMapping("/getCustomerByName")
    public Response getCustomerByName(@RequestParam(required = false) String customerName, @RequestParam(required = false) Long unitId) {
        return new DataResponse().setData(customerService.getCustomerByName(customerName, unitId));
    }

    @ApiOperation(value = "分页查询客户信息")
    @GetMapping("/list")
    public Response getCustomerById(CustomerQuery query) {
        DataResponse<PageInfo<CustomerDto>> response = Response.dataResponse();
        query.setList(true);
        query.setPackage(Boolean.FALSE);
        if (query.getUnitId() == null) {
            query.setUnitId(SystemContext.getUnitId());
        }
        return response.setData(customerService.list(query, true));
    }


    @ApiOperation(value = "我的客户信息")
    @GetMapping("/mylist")
    public Response mylist(CustomerQuery query) {
        DataResponse<PageInfo<CustomerDto>> response = Response.dataResponse();
        query.setMe(true);//查询我的
        return response.setData(customerService.list(query, true));
    }

    @ApiOperation(value = "客户信息列表")
    @PostMapping("/listByIds")
    public Response listByIds(@RequestBody CustomerQuery query) {
        query.setIsAll(true);
        DataResponse<List<CustomerDto>> response = Response.dataResponse();
        return response.setData(customerService.listByIds(query, SystemContext.getUid()));
    }

    @ApiOperation(value = "根据业务主体分页查询客户信息")
    @GetMapping("/listByOu")
    public Response getCustomerByOu(CustomerQuery query) {
        DataResponse<PageInfo<CustomerDto>> response = Response.dataResponse();
        return response.setData(customerService.listByOu(query, SystemContext.getUid(), false, true));
    }

    @ApiOperation(value = "根据客户名称和crm编号查询客户信息")
    @GetMapping("/listByNameOrCrmCode")
    public Response getCustomerByNameOrPamCode(CustomerQuery query) {
        DataResponse<PageInfo<CustomerDto>> response = Response.dataResponse();
        return response.setData(customerService.listByNameOrCrmCode(query, SystemContext.getUid(), false, true));
    }

    @ApiOperation(value = "根据客户名称查询客户信息(无需过滤数据)")
    @GetMapping("/listCustomerByName")
    public Response listCustomerByName(CustomerQuery query) {
        DataResponse<List<CustomerDto>> response = Response.dataResponse();
        query.setUnitId(SystemContext.getUnitId());
        return response.setData(customerService.listCustomerByName(query));
    }

    @ApiOperation(value = "根据客户查询业务实体")
    @GetMapping("/queryOuByCustomer")
    public Response queryOuByCustomer(CustomerQuery query) {
        DataResponse<Set<OperatingUnitDto>> response = Response.dataResponse();
        return response.setData(customerService.queryOuByCustomer(query));
    }

    @ApiOperation(value = "根据使用单位查询业务实体")
    @GetMapping("/queryOuByUnitId")
    public Response queryOuByUnitId(CustomerQuery query) {
        DataResponse<Set<OperatingUnitDto>> response = Response.dataResponse();
        return response.setData(customerService.queryOuByUnitId(query));
    }

    @ApiOperation(value = "新增客戶")
    @PostMapping("add")
    public Response add(@RequestBody CustomerDto customerDto) {
        DataResponse<CustomerDto> response = Response.dataResponse();
        return response.setData(customerService.add(customerDto, SystemContext.getUid()));
    }

    @ApiOperation(value = "编辑客户")
    @PostMapping("update")
    public Response update(@RequestBody CustomerDto customerDto) {
        return new DataResponse().setData(customerService.update(customerDto, SystemContext.getUid()));
    }

    @ApiOperation(value = "团队成员列表")
    @RequestMapping(value = {"/listTeamUsers"}, method = {RequestMethod.GET})
    public Response listTeamUsersByCustomerId(CustomerQuery customerQuery) {
        DataResponse<List<TeamUserDto>> response = Response.dataResponse();
        return response.setData(customerService.listTeamUsersByCustomerId(customerQuery, SystemContext.getUid()));
    }

    @ApiOperation(value = "增加团队成员")
    @RequestMapping(value = {"/saveTeamUser"}, method = {RequestMethod.POST})
    @ResponseBody
    public Response saveTeamUser(@RequestBody TeamUsersDto teamUsersDto) {
        DataResponse<TeamUsersDto> response = Response.dataResponse();
        return response.setData(customerService.saveTeamUser(teamUsersDto, SystemContext.getUid()));
    }

    @ApiOperation(value = "删除团队成员")
    @RequestMapping(value = {"/deleteTeamUser"}, method = {RequestMethod.GET})
    public Response deleteTeamUser(@RequestParam("teamUserId") Long teamUserId) {
        DataResponse<Integer> response = Response.dataResponse();
        return response.setData(customerService.deleteTeamUser(teamUserId, SystemContext.getUid()));
    }


    @ApiOperation(value = "转让")
    @RequestMapping(value = "/changeOwner", method = {RequestMethod.POST})
    @ResponseBody
    public Response changeOwner(@RequestBody Map<String, Object> params) {
        DataResponse<Integer> response = Response.dataResponse();
        return response.setData(customerService.changeOwner(Long.valueOf(params.get("id").toString()), params.get("uid").toString(), params.get("userName").toString()));
    }

    @ApiOperation(value = "作废")
    @RequestMapping(value = "/disableCustomer", method = {RequestMethod.GET})
    public Response disableCustomer(@RequestParam("id") Long id) {
        DataResponse<Integer> response = Response.dataResponse();
        return response.setData(customerService.disableCustomer(id, SystemContext.getUid()));
    }

    @ApiOperation(value = "客户导出")
    @GetMapping("/excelList")
    public Response excelList(CustomerQuery query) {
        query.setPageNum(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setUid(SystemContext.getUid());
        query.setList(true);
        return new DataResponse().setData(customerService.list(query, true));
    }

    @ApiOperation(value = "我的客户导出")
    @RequestMapping(value = "/excel/myexport", method = {RequestMethod.GET})
    public void myexport(CustomerQuery query, HttpServletResponse response) {
        query.setMe(true);//查询我的
        List<CustomerExcelVo> dataResult = getDataResult(query);
        ExportExcelUtil.exportExcel(dataResult, null, "Sheet1", CustomerExcelVo.class, "客户信息导出.xls", response);
    }

    @ApiOperation(value = "录入小助手查询")
    @GetMapping("/assistant/query")
    public Response assistantQuery(CustomerQuery query) {
        DataResponse<CustomerDto> response = Response.dataResponse();
        return response.setData(customerService.assistantQuery(query.getName()));
    }

    @ApiOperation(value = "校验客户是否已引用")
    @GetMapping("/checkCustomerExisting")
    public DataResponse checkCustomerExisting(@RequestParam @ApiParam("客户名称") Long customerId) {
        DataResponse<Integer> response = Response.dataResponse();
        final int i = customerService.checkCustomerExisting(customerId);
        return response.setData(i);
    }

    @ApiOperation(value = "查询对客户信息操作的分类类型")
    @GetMapping("/checkCustomerModifyType")
    public DataResponse checkCustomerModifyType(@RequestParam @ApiParam("客户id") Long customerId) {
        DataResponse<Integer> response = Response.dataResponse();
        final int i = customerService.checkCustomerModifyType(customerId);
        return response.setData(i);
    }

    @ApiOperation(value = "校验客户名称或统一信用代码")
    @GetMapping("/checkCustomerNameOrRn")
    public DataResponse checkCustomerNameOrRn(@RequestParam(required = false) @ApiParam("客户名称") String name,
                                              @RequestParam(required = false) @ApiParam("客户名称") Long customerId,
                                              @RequestParam(required = false) @ApiParam("统一信用代码") String registrationNumber) {
        DataResponse<Integer> response = Response.dataResponse();
        final int i = customerService.checkCustomerNameOrRn(name, registrationNumber, customerId);
        return response.setData(i);
    }

    @Deprecated // BUG2022080248221 CRM推客户过来PAM时，不需要校验注册地址唯一
    @ApiOperation(value = "校验客户注册地址")
    @GetMapping("/checkCustomerAddress")
    public DataResponse checkCustomerAddress(@RequestParam(required = false) @ApiParam("客户名称") Long customerId,
                                             @RequestParam(required = false) @ApiParam("注册地址") String registeredAddress) {
        DataResponse<Integer> response = Response.dataResponse();
        final int i = customerService.checkCustomerAddress(customerId, registeredAddress);
        return response.setData(i);
    }

    @ApiOperation(value = "校验客户三证")
    @GetMapping("/checkCustomerBusinessLicenseNumber")
    public DataResponse checkCustomerBusinessLicenseNumber(@RequestParam(required = false) @ApiParam("客户名称") Long customerId,
                                                           @RequestParam(required = false) @ApiParam("营业执照号") String businessLicenseNumber,
                                                           @RequestParam(required = false) @ApiParam("组织机构代码证") String organizationCode,
                                                           @RequestParam(required = false) @ApiParam("纳税人登记号") String registrationMark) {
        DataResponse<Integer> response = Response.dataResponse();
        final int i = customerService.checkCustomerBusinessLicenseNumber(customerId, businessLicenseNumber, organizationCode, registrationMark);
        return response.setData(i);
    }

    @ApiOperation(value = "新增或更新联系人")
    @PostMapping("/contact/saveOrUpdate")
    public DataResponse saveOrUpdateContact(@RequestBody CustomerContactDto customerContactDto) {
        DataResponse<CustomerContactDto> response = Response.dataResponse();
        final CustomerContactDto customerContact = customerContactService.save(customerContactDto);
        return response.setData(customerContact);
    }

    @ApiOperation(value = "删除联系人")
    @DeleteMapping("/contact/delete")
    public DataResponse deleteContact(@RequestParam @ApiParam("联系人ID") Long id) {
        DataResponse<Integer> response = Response.dataResponse();
        customerContactService.deleteById(id);
        return response.setData(1);
    }

    @ApiOperation(value = "pushCustomerToCrm")
    @GetMapping("/pushCustomerToCrm")
    public Response page(@RequestParam(required = false) final Long customerId) {
        CustomerDto custDto = new CustomerDto();
        custDto.setId(customerId);
        customerService.pushCustomerToCrm(custDto);
        DataResponse<String> response = Response.dataResponse();
        return response.setData("0");
    }

    @ApiOperation(value = "客户导出")
    @RequestMapping(value = "/excel/export", method = {RequestMethod.GET})
    public void export(CustomerQuery query, HttpServletResponse response) {
        query.setList(true);
        List<CustomerExcelVo> dataResult = getDataResult(query);
        ExportExcelUtil.exportExcel(dataResult, null, "Sheet1", CustomerExcelVo.class, "客户信息导出.xls", response);
    }

    protected List<CustomerExcelVo> getDataResult(CustomerQuery customerQuery) {
        List<CustomerExcelVo> contentList = new ArrayList<>();
        customerQuery.setPageNum(1);
        customerQuery.setPageSize(Integer.MAX_VALUE);
        customerQuery.setUid(SystemContext.getUid());
        final PageInfo<CustomerDto> pageInfo = customerService.list(customerQuery, true);
        final List<CustomerDto> list = pageInfo.getList();

        if (ListUtils.isNotEmpty(list)) {
            for (CustomerDto customerDto : list) {
                CustomerExcelVo excelVo = new CustomerExcelVo();
                BeanUtils.copyProperties(customerDto, excelVo);
                BusinessQuery businessQuery = new BusinessQuery();
                businessQuery.setCustomerId(customerDto.getId());
                businessQuery.setPageSize(Integer.MAX_VALUE);
                businessQuery.setStatus(Integer.valueOf("1"));
                businessQuery.setOnlyList(Boolean.TRUE);
                businessQuery.setUid(SystemContext.getUid());
                final PageInfo<BusinessDto> businessPage =
                        businessService.list(businessQuery, false, false, false, false);
                excelVo.setBusinessTotal(businessPage.getTotal()); //有效商机数量

                businessQuery.setStatus(Integer.valueOf("2"));
                businessQuery.setUid(SystemContext.getUid());
                final PageInfo<BusinessDto> businessPage2 =
                        businessService.list(businessQuery, false, false, false, false);
                excelVo.setBusinessWinTotal(businessPage2.getTotal()); //已赢单商机数量
                contentList.add(excelVo);
            }
        }
        return contentList;
    }

    @GetMapping("/getByCrmCode")
    public Response getByCrmCode(@RequestParam(required = true) final String crmCode) {
        List<Customer> customers = customerService.getByCrmCode(crmCode);
        DataResponse<List<Customer>> response = Response.dataResponse();
        return response.setData(customers);
    }

    @GetMapping("/getCustomerModifyHistories")
    public Response getCustomerModifyHistories(@RequestParam final Long customerId) {
        CustomerModifyHistory customerModifyHistories = customerService.getCustomerModifyHistories(customerId);
        DataResponse<CustomerModifyHistory> response = Response.dataResponse();
        return response.setData(customerModifyHistories);
    }

    @ApiOperation(value = "通过crmCode获取客户银行信息")
    @GetMapping("/getCustomerBankInfo")
    public Response getCustomerBankInfo(@RequestParam(required = true) String crmCode,
                                        @RequestParam(required = true) Long ouId,
                                        @RequestParam(required = false) Long unitId) {
        final CustomerBankAccount customerBankInfo = customerService.getCustomerBankInfo(crmCode, ouId, Objects.isNull(unitId) ? SystemContext.getUnitId() : unitId);
        DataResponse<CustomerBankAccount> response = Response.dataResponse();
        return response.setData(customerBankInfo);
    }

    @ApiOperation(value = "通过crmCodes获取客户银行信息")
    @PostMapping("/getCustomerBankInfoByCrmCodes")
    public Response getCustomerBankInfoByCrmCodes(@RequestBody CustomerDto customerDto) {
        Map<String, CustomerBankAccount> customerBankAccountMap = customerService.getCustomerBankInfoByCrmCodeList(customerDto.getCrmCodeList(), SystemContext.getUnitId());
        DataResponse<Map<String, CustomerBankAccount>> response = Response.dataResponse();
        return response.setData(customerBankAccountMap);
    }

    @ApiOperation(value = "通过id获取客户信息")
    @GetMapping("/getCustomerId")
    public Response getCustomerId(@RequestParam(required = true) final Long customerId) {
        CustomerDto customerDto = customerService.getCustomerId(customerId);
        DataResponse<CustomerDto> response = Response.dataResponse();
        return response.setData(customerDto);
    }

    @GetMapping("/find/{name}/{code}")
    public DataResponse<Customer> findByNameAndCode(@PathVariable("name") String name, @PathVariable("code") String code) {
        return Response.dataResponse(customerService.findByNameAndCode(name, code));
    }

    @ApiOperation(value = "导入未生效的客户信息")
    @PostMapping("importUnsyncExcel")
    public Response importUnsyncExcel(@RequestBody List<CustomerImportExcelVo> importExcelVos,
                                      @RequestParam(required = false) Long unitId) {
        return Response.dataResponse(dataImportService.importCustomerImportExcelVo(importExcelVos, unitId));
    }


    @ApiOperation(value = "导入客户与虚拟部门关系信息")
    @PostMapping("importCustomerUnitRel")
    public Response importCustomerUnitRel(@RequestBody List<CustomerUnitRelImportExcelVo> importExcelVos,
                                          @RequestParam(required = false) Long unitId) {
        return Response.dataResponse(dataImportService.importCustomerUnitRel(importExcelVos, unitId));
    }

    @ApiOperation(value = "根据客户编码查询客户信息")
    @GetMapping("/getCustomerDetail")
    public Response getCustomerDetail(@RequestParam(required = true) String customerNum) {
        List<Customer> customerDetail = customerService.getCustomerDetail(customerNum);
        DataResponse<List<Customer>> response = Response.dataResponse();
        return response.setData(customerDetail);
    }

    @ApiOperation(value = "根据客户编码批量查询客户信息")
    @PostMapping("/getCustomerByCodes")
    public List<CustomerDto> getCustomerByCodes(@RequestBody List<String> crmCodes) {
        return customerService.getCustomerByCodes(crmCodes);
    }

    @ApiOperation(value = "根据客户id查询区域名称")
    @GetMapping("/getRegionNameByCustomerId")
    public Response getRegionNameByCustomerId(@RequestParam(required = true) Long unitId,
                                              @RequestParam(required = true) Long customerId) {
        CustomerUnitRel customerUnitRel = customerUnitRelService.getRegionNameByCustomerId(unitId, customerId);
        DataResponse<CustomerUnitRel> response = Response.dataResponse();
        return response.setData(customerUnitRel);
    }

    @ApiOperation(value = "根据业务实体id查询客户(支持模糊搜索)")
    @GetMapping("/selectByOuIdAndName")
    public Response selectByOuIdAndName(CustomerQuery query) {
        DataResponse<List<CustomerDto>> response = Response.dataResponse();
        return response.setData(customerService.selectByOuIdAndName(query));
    }

    @ApiOperation("批量查询客户信息")
    @PostMapping("getCustomerInfoBatch")
    public Response getCustomerInfoBatch(@RequestBody CustomerDto customerDto) {
        return Response.dataResponse(customerService.getCustomerInfoBatch(customerDto));
    }

    @ApiOperation(value = "客户企业性质初始化")
    @GetMapping("init/customerEnterpriseNature")
    public Response initCustomerEnterpriseNature() {
        DataResponse response = Response.dataResponse();
        return response.setData(customerService.initCustomerEnterpriseNature());
    }

}
