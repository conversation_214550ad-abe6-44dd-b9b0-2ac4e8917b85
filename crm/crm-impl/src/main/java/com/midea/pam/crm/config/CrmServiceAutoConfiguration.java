package com.midea.pam.crm.config;


import com.midea.pam.common.util.ApplicationContextProvider;
import com.midea.pam.crm.RedisValueCache;
import com.midea.pam.crm.customer.service.CustomerBaseInfoModifyCallbackService;
import com.midea.pam.crm.customer.service.CustomerBusinessModifyCallbackService;
import com.midea.pam.crm.customer.service.CustomerModifyHistoryService;
import com.midea.pam.crm.customer.service.CustomerModifyWfCallbackService;
import com.midea.pam.crm.customer.service.impl.CustomerBaseInfoModifyCallbackServiceImpl;
import com.midea.pam.crm.customer.service.impl.CustomerBusinessModifyCallbackServiceImpl;
import com.midea.pam.crm.customer.service.impl.CustomerModifyHistoryServiceImpl;
import com.midea.pam.crm.customer.service.impl.CustomerModifyWfCallbackServiceImpl;
import com.midea.pam.crm.excel.DataImportService;
import com.midea.pam.crm.ext.service.ContractExtService;
import com.midea.pam.crm.ext.service.impl.ContractExtServiceImpl;
import com.midea.pam.crm.sdp.mq.service.SdpDataOutStrategySpi;
import com.midea.pam.crm.sdp.mq.service.SdpMessageConsumer;
import com.midea.pam.crm.sdp.mq.service.impl.ItemSdpCrmCustomerTopicDataOutStrategySpi;
import com.midea.pam.crm.service.AchievementGoalService;
import com.midea.pam.crm.service.AtUserService;
import com.midea.pam.crm.service.AttributionUnitService;
import com.midea.pam.crm.service.BasedataExtService;
import com.midea.pam.crm.service.BusinessAttachmentRelationService;
import com.midea.pam.crm.service.BusinessChangeRecordService;
import com.midea.pam.crm.service.BusinessCompetitionService;
import com.midea.pam.crm.service.BusinessContactRelService;
import com.midea.pam.crm.service.BusinessDocumentLibraryService;
import com.midea.pam.crm.service.BusinessExtraService;
import com.midea.pam.crm.service.BusinessFollowRecordService;
import com.midea.pam.crm.service.BusinessProductModuleService;
import com.midea.pam.crm.service.BusinessProductService;
import com.midea.pam.crm.service.BusinessPromptService;
import com.midea.pam.crm.service.BusinessRecordCommentService;
import com.midea.pam.crm.service.BusinessRoleDeptRelService;
import com.midea.pam.crm.service.BusinessService;
import com.midea.pam.crm.service.CacheDataService;
import com.midea.pam.crm.service.CompetitionExtraService;
import com.midea.pam.crm.service.CompetitionService;
import com.midea.pam.crm.service.ContactService;
import com.midea.pam.crm.service.CrmAttachmentService;
import com.midea.pam.crm.service.CtcExtService;
import com.midea.pam.crm.service.CustomerBankAccountService;
import com.midea.pam.crm.service.CustomerContactService;
import com.midea.pam.crm.service.CustomerOuRelService;
import com.midea.pam.crm.service.CustomerService;
import com.midea.pam.crm.service.CustomerUnitRelService;
import com.midea.pam.crm.service.CustomerWorkflowCallbackService;
import com.midea.pam.crm.service.LeadService;
import com.midea.pam.crm.service.MailExtService;
import com.midea.pam.crm.service.MemberAchievementGoalService;
import com.midea.pam.crm.service.MessageCenterService;
import com.midea.pam.crm.service.OtcConvertService;
import com.midea.pam.crm.service.ParaDicService;
import com.midea.pam.crm.service.PlanHumanCostService;
import com.midea.pam.crm.service.PlanOtherCostService;
import com.midea.pam.crm.service.PlanProductCostService;
import com.midea.pam.crm.service.PlanService;
import com.midea.pam.crm.service.PlanSummaryService;
import com.midea.pam.crm.service.PlanTripCostService;
import com.midea.pam.crm.service.ProductAttributeService;
import com.midea.pam.crm.service.ProductOrganizationService;
import com.midea.pam.crm.service.ProductService;
import com.midea.pam.crm.service.QuotationDetailService;
import com.midea.pam.crm.service.QuotationService;
import com.midea.pam.crm.service.RoleDeptService;
import com.midea.pam.crm.service.SdpService;
import com.midea.pam.crm.service.TaxRateConfigurationService;
import com.midea.pam.crm.service.TaxRateRelService;
import com.midea.pam.crm.service.TeamService;
import com.midea.pam.crm.service.TeamUserService;
import com.midea.pam.crm.service.TianYanChaService;
import com.midea.pam.crm.service.ValueCache;
import com.midea.pam.crm.service.excel.DataImportServiceImpl;
import com.midea.pam.crm.service.impl.AchievementGoalServiceImpl;
import com.midea.pam.crm.service.impl.AtUserServiceImpl;
import com.midea.pam.crm.service.impl.AttributionUnitServiceImpl;
import com.midea.pam.crm.service.impl.BasedataExtServiceImpl;
import com.midea.pam.crm.service.impl.BusinessAttachmentRelationServiceImpl;
import com.midea.pam.crm.service.impl.BusinessChangeRecordServiceImpl;
import com.midea.pam.crm.service.impl.BusinessCompetitionServiceImpl;
import com.midea.pam.crm.service.impl.BusinessContactRelServiceImpl;
import com.midea.pam.crm.service.impl.BusinessDocumentLibraryServiceImpl;
import com.midea.pam.crm.service.impl.BusinessExtraServiceImpl;
import com.midea.pam.crm.service.impl.BusinessFollowRecordServiceImpl;
import com.midea.pam.crm.service.impl.BusinessProductModuleServiceImpl;
import com.midea.pam.crm.service.impl.BusinessProductServiceImpl;
import com.midea.pam.crm.service.impl.BusinessPromptServiceImpl;
import com.midea.pam.crm.service.impl.BusinessRecordCommentServiceImpl;
import com.midea.pam.crm.service.impl.BusinessRoleDeptRelServiceImpl;
import com.midea.pam.crm.service.impl.BusinessServiceImpl;
import com.midea.pam.crm.service.impl.CacheDataServiceImpl;
import com.midea.pam.crm.service.impl.CompetitionExtraServiceImpl;
import com.midea.pam.crm.service.impl.CompetitionServiceImpl;
import com.midea.pam.crm.service.impl.ContactServiceImpl;
import com.midea.pam.crm.service.impl.CrmAttachmentServiceImpl;
import com.midea.pam.crm.service.impl.CrmAuditUserLocalProvider;
import com.midea.pam.crm.service.impl.CtcExtServiceImpl;
import com.midea.pam.crm.service.impl.CustomerBankAccountServiceImpl;
import com.midea.pam.crm.service.impl.CustomerContactServiceImpl;
import com.midea.pam.crm.service.impl.CustomerOuRelServiceImpl;
import com.midea.pam.crm.service.impl.CustomerServiceImpl;
import com.midea.pam.crm.service.impl.CustomerUnitRelServiceImpl;
import com.midea.pam.crm.service.impl.CustomerWorkflowCallbackServiceImpl;
import com.midea.pam.crm.service.impl.LeadServiceImpl;
import com.midea.pam.crm.service.impl.MailExtServiceImpl;
import com.midea.pam.crm.service.impl.MemberAchievementGoalServiceImpl;
import com.midea.pam.crm.service.impl.MessageCenterServiceImpl;
import com.midea.pam.crm.service.impl.OtcConvertServiceImpl;
import com.midea.pam.crm.service.impl.ParaDicServiceImpl;
import com.midea.pam.crm.service.impl.PlanHumanCostServiceImpl;
import com.midea.pam.crm.service.impl.PlanOtherCostServiceImpl;
import com.midea.pam.crm.service.impl.PlanProductCostServiceImpl;
import com.midea.pam.crm.service.impl.PlanServiceImpl;
import com.midea.pam.crm.service.impl.PlanSummaryServiceImpl;
import com.midea.pam.crm.service.impl.PlanTripCostServiceImpl;
import com.midea.pam.crm.service.impl.ProductAttributeServiceImpl;
import com.midea.pam.crm.service.impl.ProductOrganizationServiceImpl;
import com.midea.pam.crm.service.impl.ProductServiceImpl;
import com.midea.pam.crm.service.impl.QuotationDetailServiceImpl;
import com.midea.pam.crm.service.impl.QuotationServiceImpl;
import com.midea.pam.crm.service.impl.RoleDeptServiceImpl;
import com.midea.pam.crm.service.impl.SdpServiceImpl;
import com.midea.pam.crm.service.impl.TaxRateConfigurationServiceImpl;
import com.midea.pam.crm.service.impl.TaxRateRelServiceImpl;
import com.midea.pam.crm.service.impl.TeamServiceImpl;
import com.midea.pam.crm.service.impl.TeamUserServiceImpl;
import com.midea.pam.crm.service.impl.TianYanChaServiceImpl;
import com.midea.pam.crm.service.listener.BusinessSynchroListener;
import com.midea.pam.crm.service.listener.CustomerModifyWfCallbackApprovalListener;
import com.midea.pam.crm.service.listener.CustomerSyncListener;
import com.midea.pam.crm.service.listener.CustomerWorkflowCallbackApprovalListener;
import com.midea.pam.crm.workflow.service.WorkflowCallbackService;
import com.midea.pam.crm.workflow.service.impl.WorkflowCallbackServiceImpl;
import com.midea.pam.support.AuditUserLocalProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CrmServiceAutoConfiguration {

    @Bean
    public LeadService leadService() {
        return new LeadServiceImpl();
    }

    @Bean
    public ContactService contactService() {
        return new ContactServiceImpl();
    }

    @Bean
    public ParaDicService paraDicService() {
        return new ParaDicServiceImpl();
    }

    @Bean
    public CustomerService customerService() {
        return new CustomerServiceImpl();
    }

    @Bean
    public CustomerSyncListener customerSyncListener() {
        return new CustomerSyncListener();
    }

    @Bean
    public TeamService teamService() {
        return new TeamServiceImpl();
    }

    @Bean
    public TeamUserService teamUserService() {
        return new TeamUserServiceImpl();
    }

    @Bean
    public CustomerContactService customerContactService() {
        return new CustomerContactServiceImpl();
    }

    @Bean
    public CustomerBankAccountService customerBankAccountService() {
        return new CustomerBankAccountServiceImpl();
    }

    @Bean
    public BusinessCompetitionService businessCompetitionService() {
        return new BusinessCompetitionServiceImpl();
    }


    @Bean
    public CompetitionExtraService competitionExtraService() {
        return new CompetitionExtraServiceImpl();
    }

    @Bean
    public CompetitionService competitionService() {
        return new CompetitionServiceImpl();
    }

    @Bean
    public BusinessContactRelService businessContactRelService() {
        return new BusinessContactRelServiceImpl();
    }

    @Bean
    public BusinessService businessService() {
        return new BusinessServiceImpl();
    }

    @Bean
    public BusinessChangeRecordService businessChangeRecordService() {
        return new BusinessChangeRecordServiceImpl();
    }

    @Bean
    public BusinessExtraService businessExtraService() {
        return new BusinessExtraServiceImpl();
    }

    @Bean
    public BusinessFollowRecordService businessFollowRecordService() {
        return new BusinessFollowRecordServiceImpl();
    }

    @Bean
    public BusinessProductModuleService businessProductModuleService() {
        return new BusinessProductModuleServiceImpl();
    }

    @Bean
    public BusinessProductService businessProductService() {
        return new BusinessProductServiceImpl();
    }

    @Bean
    public BusinessPromptService businessPromptService() {
        return new BusinessPromptServiceImpl();
    }

    @Bean
    public BusinessRoleDeptRelService businessRoleDeptRelService() {
        return new BusinessRoleDeptRelServiceImpl();
    }

    @Bean
    public ApplicationContextProvider applicationContextProvider() {
        return new ApplicationContextProvider();
    }

    @Bean
    public AchievementGoalService achievementGoalService() {
        return new AchievementGoalServiceImpl();
    }

    @Bean
    public MemberAchievementGoalService memberAchievementGoalService() {
        return new MemberAchievementGoalServiceImpl();
    }

    @Bean
    public OtcConvertService otcConvertService() {
        return new OtcConvertServiceImpl();
    }

    @Bean
    public RoleDeptService roleDeptService() {
        return new RoleDeptServiceImpl();
    }

    @Bean
    public AuditUserLocalProvider auditUserLocalProvider() {
        return new CrmAuditUserLocalProvider();
    }

    @Bean
    public AttributionUnitService attributionUnitService() {
        return new AttributionUnitServiceImpl();
    }

    @Bean
    public ProductAttributeService productAttributeService() {
        return new ProductAttributeServiceImpl();
    }

    @Bean
    public ProductOrganizationService productOrganizationService() {
        return new ProductOrganizationServiceImpl();
    }

    @Bean
    public ProductService productService() {
        return new ProductServiceImpl();
    }

    @Bean
    public TaxRateRelService taxRateRelService() {
        return new TaxRateRelServiceImpl();
    }

    @Bean
    public TaxRateConfigurationService taxRateConfigurationService() {
        return new TaxRateConfigurationServiceImpl();
    }

    @Bean
    public BusinessAttachmentRelationService businessAttachmentRelationService() {
        return new BusinessAttachmentRelationServiceImpl();
    }

    @Bean
    public PlanOtherCostService planOtherCostService() {
        return new PlanOtherCostServiceImpl();
    }

    @Bean
    public PlanTripCostService planTripCostService() {
        return new PlanTripCostServiceImpl();
    }

    @Bean
    public PlanHumanCostService planHumanCostService() {
        return new PlanHumanCostServiceImpl();
    }

    @Bean
    public PlanProductCostService planProductCostService() {
        return new PlanProductCostServiceImpl();
    }

    @Bean
    public PlanSummaryService planSummaryService() {
        return new PlanSummaryServiceImpl();
    }

    @Bean
    public PlanService planService() {
        return new PlanServiceImpl();
    }

    @Bean
    public AtUserService atUserService() {
        return new AtUserServiceImpl();
    }

    @Bean
    public BusinessRecordCommentService businessRecordCommentService() {
        return new BusinessRecordCommentServiceImpl();
    }

    @Bean
    public MessageCenterService messageCenterService() {
        return new MessageCenterServiceImpl();
    }

    @Bean
    public CrmAttachmentService crmAttachmentService() {
        return new CrmAttachmentServiceImpl();
    }

    @Bean
    public QuotationService quotationService() {
        return new QuotationServiceImpl();
    }

    @Bean
    public QuotationDetailService quotationDetailService() {
        return new QuotationDetailServiceImpl();
    }

    @Bean
    public TianYanChaService tianYanChaService() {
        return new TianYanChaServiceImpl();
    }

    @Bean
    public CacheDataService cacheDataService() {
        return new CacheDataServiceImpl();
    }

    @Bean
    public ValueCache valueCache() {
        return new RedisValueCache();
    }

    @Bean
    public MailExtService mailExtService() {
        return new MailExtServiceImpl();
    }

    @Bean
    public CustomerModifyHistoryService customerModifyHistoryService() {
        return new CustomerModifyHistoryServiceImpl();
    }

    @Bean
    public CustomerModifyWfCallbackService customerModifyWfCallbackService() {
        return new CustomerModifyWfCallbackServiceImpl();
    }

    @Bean
    public CustomerBusinessModifyCallbackService customerBusinessModifyCallbackService() {
        return new CustomerBusinessModifyCallbackServiceImpl();
    }

    @Bean
    public CustomerBaseInfoModifyCallbackService customerBaseInfoModifyCallbackService() {
        return new CustomerBaseInfoModifyCallbackServiceImpl();
    }

    @Bean
    public BasedataExtService basedataExtService() {
        return new BasedataExtServiceImpl();
    }

    @Bean
    public CustomerOuRelService customerOuRelService() {
        return new CustomerOuRelServiceImpl();
    }

    @Bean
    public CtcExtService ctcExtService() {
        return new CtcExtServiceImpl();
    }

    @Bean
    public CustomerUnitRelService customerUnitRelService() {
        return new CustomerUnitRelServiceImpl();
    }

    @Bean
    public ContractExtService contractExtService() {
        return new ContractExtServiceImpl();
    }

    @Bean
    public DataImportService dataImportService() {
        return new DataImportServiceImpl();
    }

    @Bean
    public BusinessSynchroListener businessSynchroListener() {
        return new BusinessSynchroListener();
    }

    @Bean
    public BusinessDocumentLibraryService businessDocumentLibraryService() {
        return new BusinessDocumentLibraryServiceImpl();
    }

    @Bean
    public WorkflowCallbackService workflowCallbackService() {
        return new WorkflowCallbackServiceImpl();
    }

    @Bean
    public CustomerWorkflowCallbackService customerWorkflowCallbackService() {
        return new CustomerWorkflowCallbackServiceImpl();
    }

    @Bean
    public CustomerWorkflowCallbackApprovalListener customerWorkflowCallbackApprovalListener() {
        return new CustomerWorkflowCallbackApprovalListener();
    }

    @Bean
    public CustomerModifyWfCallbackApprovalListener customerModifyWfCallbackApprovalListener() {
        return new CustomerModifyWfCallbackApprovalListener();
    }

    @Bean
    public SdpService sdpService() {
        return new SdpServiceImpl();
    }

    @Bean
    public SdpMessageConsumer sdpMessageConsumer(){
        return new SdpMessageConsumer();
    }

    @Bean(name = "sdpDataOutStrategy.PAM_CUSTOMER_CRM")
    public SdpDataOutStrategySpi itemSdpCrmCustomerTopicDataOutStrategySpi() {
        return new ItemSdpCrmCustomerTopicDataOutStrategySpi();
    }

}
