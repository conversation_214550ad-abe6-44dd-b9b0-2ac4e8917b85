package com.midea.pam.crm.service;

import com.github.pagehelper.PageInfo;
import com.midea.pam.common.crm.dto.BusinessProductDto;
import com.midea.pam.common.crm.dto.BusinessProductModuleDto;
import com.midea.pam.common.crm.query.BusinessProductModuleQuery;
import com.midea.pam.common.crm.query.BusinessProductQuery;

import java.util.List;

/**
 * 创建时间 2017-11-14 14:35
 *
 * <AUTHOR>
 */
public interface BusinessProductService {

    Integer deleteBusinessProductById(Long id);

    List<BusinessProductDto> saveByProductId(List<BusinessProductDto> businessProducts);

    PageInfo<BusinessProductDto> list(BusinessProductQuery query, String uid);

    List<BusinessProductModuleDto> listBusinessProductModule(BusinessProductModuleQuery query);

    BusinessProductModuleDto saveBusinessProductModule(BusinessProductModuleDto businessProductModule, String uid);

    Integer deleteBusinessProductModuleById(Long id);

    List<BusinessProductDto> getByBusinessId(Long id);
}
