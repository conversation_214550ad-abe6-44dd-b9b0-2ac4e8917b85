business:
  detail:
    url: https://pamsit.midea.com/#/business-detail
esb:
  gfp:
    securitycode: 123456
esb_client:
  JMSConfiguration:
    CCSID: 1208
    channel: PAM.DEF.SVRCONN
    connectionNameList: gw1esbjysit1.midea.com(11001),gw2esbjysit1.midea.com(12001)
    transportType: 1
  longReceiveTimeout: 600000
  receiveTimeout: 120000
  replyDestination: LOCALQ.PAM.RSP
  targetDestination: LOCALQ.C.PAM.REQ
esb_server:
  JMSConfiguration:
    CCSID: 1208
    channel: PAM.DEF.SVRCONN
    connectionNameList1: gw1esbsubsit1.midea.com(11101)
    connectionNameList2: gw2esbsubsit1.midea.com(12101)
    transportType: 1
  concurrentConsumers: 5
  sessionCacheSize: 100
  targetDestination: LOCALQ.SUB.PAM.REQ
eureka:
  client:
    serviceUrl:
      defaultZone: http://127.0.0.1:8761/eureka/,http://127.0.0.1:8761/eureka/,http://127.0.0.1:8761/eureka/
  instance:
    prefer-ip-address: true
expression:
  aop:
    pointcut: '@within(org.springframework.web.bind.annotation.RestController)'
logger:
  parameter:
    enable: false
    level: DEBUG
    mdc-storage:
      enable: false
    name: PARAMETER_LOG
  trace-code:
    enable: true
    mdc-trace-code-key: traceCode
mframework-provider:
  ribbon:
    MaxAutoRetriesNextServer: 2
    NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
    OkToRetryOnAllOperations: true
    listOfServers: ************:61001
mgp:
  carrier:
    discovery:
      env: sit
      system: pam
      version: 1.0
  apiurl: https://apisit.midea.com/
route:
  businessUrl: https://pamsit.midea.com/#/business/detail/
  contractUrl: https://pamsit.midea.com/#/contract/detail/
  leadUrl: https://pamsit.midea.com/#/lead/detail/
  milepostNoticeUrl: https://pamsit.midea.com/#/project/milestone-warn-list?batchId=
  milepostUrl: https://pamsit.midea.com/#/project/detail/%?active=milestone
  projectUrl: https://pamsit.midea.com/#/project/detail/
  subContractUrl: https://pamsit.midea.com/#/contract/subcontract-detail/
  workingHourUrl: https://pamsit.midea.com/#/workhour/my?active=audit&userId=
sdk:
  mform:
    appID: SRMFILE
    appKey: f1d82e0b-26ba-40c3-8be8-14ded9fff336
    bgHost: https://iflowsit.midea.com/formEngine
    downLoadServiceName: /mip-document/document/docTranmission2/public/download
    forwardTo: mip4cdev5.midea.com:60050
    getInfoServiceName: /mip-document/document/sys/docMain/getById
    moduleName: SRMGSC
    serviceName: /document/sys/docTransmission/download
    uploadRestUrlHeader: https://imipsit.midea.com
    uploadServiceName: /mip-document/document/sys/docTranmission/upload
sdp:
  appKey: PAM
  appSecret: l442bis73hjttfvc2lc1xjw0fgp7e58y
  rabbitmq:
    address: *************:5672,*************:5672,*************:5672
    enabled: true
    password: "apps#0820\t"
    username: apps
    virtual-host: /
  callbackUrl: 111
  customer:
    key: 111
    value: 111
spring:
  cluster:
    redis:
      blockWhenExhausted: false
      database: 0
      expire: 1800
      host: *************
      masterName: cluster6379
      maxIdle: 50
      maxTotal: 200
      maxWaitMillis: 1500
      numTestsPerEvictionRun: 1024
      password: ygwHPik57
      port: 6379
      type: singleton
  datasource:
    driverClassName: com.mysql.jdbc.Driver
    filters: stat,wall,log4j
    initialSize: 5
    logAbandoned: true
    maxActive: 50
    maxPoolPreparedStatementPerConnectionSize: 20
    maxWait: 60000
    minEvictableIdleTimeMillis: 25200000
    minIdle: 5
    password: tqVhr1zM5
    platform: mysql
    poolPreparedStatements: true
    removeAbandoned: true
    removeAbandonedTimeout: 1800
    testOnBorrow: false
    testOnReturn: false
    testWhileIdle: true
    timeBetweenEvictionRunsMillis: 60000
    type: com.alibaba.druid.pool.DruidDataSource
    url: ******************************************************************************************************************************************
    useGlobalDataSourceStat: true
    username: pam_sit
    validationQuery: SELECT 1
swagger:
  enable: false
xxl:
  job:
    accessToken: c1edbeaa96684656a69dfd3911925cea
    admin:
      addresses: https://apiuat1.midea.com/djs
    enabled: true
    executor:
      accessKeyId: vtYA2DSLDLpJnunayh3SZivf
      accessKeySecret: 18kM1A09xXX9Hei06ThZhc3KgDCTDca2
      appName: PAMJOB-SIT-CRM
      idmTokenUrl: https://apiuat.midea.com/iam/v1/security/getAccessToken
      logPath: /apps/pam/logs/xxl-job/jobhandler
      logretentiondays: 7
      port: 9992
      version: 1.0
