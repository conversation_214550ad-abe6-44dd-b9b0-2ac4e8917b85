package com.midea.pam.gateway.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.enums.EmailStatus;
import com.midea.pam.common.enums.NoticeBusinessType;
import com.midea.pam.common.gateway.entity.Email;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import com.midea.pam.gateway.service.EmailService;
import com.midea.pam.gateway.service.MipWorkflowService;
import com.midea.pam.gateway.service.ProjectWbsReceiptsEmailService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public class ProjectWbsReceiptsEmailServiceImpl implements ProjectWbsReceiptsEmailService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private EmailService emailService;

    @Resource
    private MipWorkflowService mipWorkflowService;

    @Override
    public void sendPendingEmail(List<ProjectWbsReceiptsDto> projectWbsReceiptsDtoList) {
        // 1. 按Leader分组
        Map<String, List<ProjectWbsReceiptsDto>> leaderToDtosMap = projectWbsReceiptsDtoList.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getProjectLeaderListStr()))
                .collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getProjectLeaderListStr));

        // 2. 遍历分组后的数据，构建邮件内容
        List<Email> emails = convertToEmail(leaderToDtosMap);
        for (Email email : emails) {
            emailService.insert(email);
        }
    }

    @Override
    public List<ProjectWbsReceiptsDto> senApprovalEmail(List<ProjectWbsReceiptsDto> projectWbsReceiptsDtoList,int hours) {
        //如果通过leaderMIP查询不到对应信息，则删除对应数据
        projectWbsReceiptsDtoList.removeIf(dto -> {
            String projectLeaderListStr = dto.getProjectLeaderListStr();
            UserInfo user = CacheDataUtils.findUserByMip(projectLeaderListStr);
            return user == null;
        });
        Date now = new Date();

        projectWbsReceiptsDtoList.removeIf(dto -> {
            String formTemplateId = "mdpChangeTheChangeApp";
            Long formInstanceId = dto.getFormInstanceIdChange();
            String fdInstanceId = dto.getFdInstanceId();
            JSONObject currentApproveNode = mipWorkflowService.getCurrentApproveNode(formTemplateId, formInstanceId, fdInstanceId);

            //如果节点为null，直接过滤
            if (currentApproveNode == null) {
                logger.error("流程信息获取失败，formTemplateId:{},formInstanceId:{},fdInstanceId:{}",
                        formTemplateId, formInstanceId, fdInstanceId);
                return true;
            }

            //检查 previousNodeTime 是否为空
            Object previousNodeTimeObj = currentApproveNode.get("previousNodeTime");
            if (previousNodeTimeObj == null || String.valueOf(previousNodeTimeObj).trim().isEmpty()) {
                logger.error("previousNodeTime为空，formTemplateId:{},formInstanceId:{},fdInstanceId:{}",
                        formTemplateId, formInstanceId, fdInstanceId);
                return true;
            }

            //检查 currentHandlerIds 是否为空
            Object currentHandlerIds = currentApproveNode.get("currentHandlerIds");
            if (currentHandlerIds == null || String.valueOf(currentHandlerIds).trim().isEmpty()) {
                logger.error("currentHandlerIds为空，formTemplateId:{},formInstanceId:{},fdInstanceId:{}",
                        formTemplateId, formInstanceId, fdInstanceId);
                return true;
            }

            try {
                // 处理人信息
                String handlerIdsStr = String.valueOf(currentHandlerIds);
                List<String> handlerList = Arrays.stream(handlerIdsStr.split(";"))
                        .filter(id -> !id.trim().isEmpty())
                        .collect(Collectors.toList());
                String names = handlerList.stream()
                        .map(handleMip -> {
                            UserInfo userByMip = CacheDataUtils.findUserByMip(handleMip);
                            return (userByMip != null) ? userByMip.getName() : "";
                        })
                        .filter(name -> !name.isEmpty())
                        .collect(Collectors.joining(","));

                dto.setHandleName(names);
                dto.setHandleMip(handlerIdsStr.replace(";", ","));

                // 处理时间信息
                String timeStr = String.valueOf(previousNodeTimeObj).trim();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime localDateTime = LocalDateTime.parse(timeStr, formatter);
                Date previousNodeTime = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());

                long diffInMillis = now.getTime() - previousNodeTime.getTime();
                double days = (double) diffInMillis / (24.0 * 60 * 60 * 1000);
                DecimalFormat df = new DecimalFormat("#.#");
                String stayDayStr = df.format(days).replace(".0", "");

                dto.setStayDay(stayDayStr);
                dto.setApprovalStayTime(previousNodeTime);

            } catch (Exception e) {
                logger.error("数据处理异常", e);
                return true;
            }
            return false; // 保留有效数据
        });

        //过滤停留时间超过hours小时的数据
        List<ProjectWbsReceiptsDto> filteredList = projectWbsReceiptsDtoList.stream()
                .filter(dto -> dto.getApprovalStayTime() != null)
                .filter(dto -> (now.getTime() - dto.getApprovalStayTime().getTime()) > hours * 60 * 60 * 1000L)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(filteredList)){
            Map<String, List<ProjectWbsReceiptsDto>> leaderToDtosMap = filteredList.stream()
                    .filter(dto -> StringUtils.isNotBlank(dto.getProjectLeaderListStr()))
                    .collect(Collectors.groupingBy(ProjectWbsReceiptsDto::getProjectLeaderListStr));


            List<Email> emails = convertToEmailForApproval(leaderToDtosMap);
            for (Email email : emails) {
                emailService.insert(email);
            }
            return filteredList;
        }else {
            logger.error("过滤时间后没有需要发送的信息");
        }
        return new ArrayList<>();

    }

    private List<Email> convertToEmailForApproval(Map<String, List<ProjectWbsReceiptsDto>> projectMap) {

        List<Email> emails = new ArrayList<>();

        Set<Map.Entry<String, List<ProjectWbsReceiptsDto>>> entries = projectMap.entrySet();
        for (Map.Entry<String, List<ProjectWbsReceiptsDto>> entry : entries) {
            String username = entry.getKey();
            List<ProjectWbsReceiptsDto> value = entry.getValue();

            Set<String> uniqueHandleMips = value.stream()
                    .map(ProjectWbsReceiptsDto::getHandleMip)
                    .filter(Objects::nonNull)
                    .flatMap(handleMip -> Arrays.stream(handleMip.split(",")))
                    .filter(mip -> !mip.trim().isEmpty())
                    .filter(mip -> !mip.equals(username))
                    .collect(Collectors.toCollection(LinkedHashSet::new));

            String finalReceivers = username;
            if (!uniqueHandleMips.isEmpty()) {
                finalReceivers += "," + String.join(",", uniqueHandleMips);
            }

            Email email = new Email();

            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = now.format(formatter);
            String subject = "PAM停留超2天“审批中”状态需求发布后变更单（取数时点：YYYY-MM-DD 08:00:00）";
            subject = subject.replace("YYYY-MM-DD 08:00:00", formattedDateTime);

            email.setSubject(subject);
            email.setLanguage("zh-CN");
            email.setReceiver(finalReceivers);

            String content = buildContentForApproval(value);
            email.setContent(content);
            email.setBusinessType(NoticeBusinessType.PROJECT_WBS_RECEIPTS_APPROVAL.getType());
            email.setDeletedFlag(Boolean.FALSE);
            email.setStatus(EmailStatus.TO_DO.getCode());
            email.setFromAddress("pam");

            emails.add(email);

        }
        return emails;
    }

    private List<Email> convertToEmail(Map<String, List<ProjectWbsReceiptsDto>> projectMap) {

        List<Email> emails = new ArrayList<>();

        Set<Map.Entry<String, List<ProjectWbsReceiptsDto>>> entries = projectMap.entrySet();
        for (Map.Entry<String, List<ProjectWbsReceiptsDto>> entry : entries) {
            String username = entry.getKey();
            List<ProjectWbsReceiptsDto> value = entry.getValue();

            //提取所有handleMip并去重
            Set<String> uniqueHandleMips = value.stream()
                    .map(ProjectWbsReceiptsDto::getHandleMip)
                    .filter(Objects::nonNull)
                    .filter(handleMip -> !handleMip.equals(username))
                    .collect(Collectors.toCollection(LinkedHashSet::new));

            String finalReceivers = username;
            if (!uniqueHandleMips.isEmpty()) {
                finalReceivers += "," + String.join(",", uniqueHandleMips);
            }

            Email email = new Email();

            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedDateTime = now.format(formatter);
            String subject = "PAM停留超2天“未处理”状态需求发布后变更单（取数时点：YYYY-MM-DD 08:00:00）";
            subject = subject.replace("YYYY-MM-DD 08:00:00", formattedDateTime);
            email.setSubject(subject);
            email.setLanguage("zh-CN");
            email.setReceiver(finalReceivers);

            String content = buildContent(value);
            email.setContent(content);
            email.setBusinessType(NoticeBusinessType.PROJECT_WBS_RECEIPTS_PENDING.getType());
            email.setDeletedFlag(Boolean.FALSE);
            email.setStatus(EmailStatus.TO_DO.getCode());
            email.setFromAddress("pam");

            emails.add(email);

        }

        return emails;
    }

    private String buildContentForApproval(List<ProjectWbsReceiptsDto> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        String header = "以下是PAM“审批中”状态需求发布后详设变更单，停留时间已超2天，请跟进人尽快处理；";
        header = "<div style='font-size: 12px;'>" + header + "</div><br/>";

        //审批中标志
        String flag = "1";
        String table = buildTable(list,flag);
        sb.append(header);

        sb.append(table);
        sb.append("</html>");
        return sb.toString();
    }

    private String buildContent(List<ProjectWbsReceiptsDto> list) {
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        String header = "以下是PAM“未处理”状态需求发布后详设变更单，停留时间已超2天，请跟进人尽快处理；";
        header = "<div style='font-size: 12px;'>" + header + "</div><br/>";

        //待处理标志
        String flag = "0";
        String table = buildTable(list,flag);
        sb.append(header);

        sb.append(table);
        sb.append("</html>");
        return sb.toString();
    }


    private String buildTable(List<ProjectWbsReceiptsDto> list,String flag) {
        StringBuilder sb = new StringBuilder();
        sb.append("<table style='font-size: 12px;border-collapse:collapse;table-layout: fixed;word-wrap:break-word;width: 1000px;'>");
        sb.append("<tr>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:15%;'>序号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:80%;'>单据类型</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:80%;'>单据号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:60%;'>跟进人</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:30%;'>停留天数（单位：天）</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:30%;'>单据状态</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:50%;'>项目号</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:80%;'>项目名称</td>");
        sb.append("<td style='border: #999 1px solid;background-color:#D2D5DD;align:center;width:60%;'>项目业务分类</td>");
        sb.append("</tr>");
        int n = 1;
        for (ProjectWbsReceiptsDto projectWbsReceiptsDto : list) {
            sb.append("<tr>");
            String requirementCode = projectWbsReceiptsDto.getRequirementCode();
            String handleName = projectWbsReceiptsDto.getHandleName();
            String stayDay = projectWbsReceiptsDto.getStayDay();
            String projectCode = projectWbsReceiptsDto.getProjectCode();
            String projectName = projectWbsReceiptsDto.getProjectName();
            String unitName = projectWbsReceiptsDto.getUnitName();
            sb.append(buildTd(String.valueOf(n++)));
            sb.append(buildTd("需求发布后变更"));//单据类型
            sb.append(buildTd(requirementCode));//单据号
            sb.append(buildTd(handleName));//跟进人
            sb.append(buildTd(stayDay));//停留天数
            if ("1".equals(flag)){
                sb.append(buildTd("审批中"));//单据状态
            }else {
                sb.append(buildTd("待处理"));//单据状态
            }
            sb.append(buildTd(projectCode));//项目号
            sb.append(buildTd(projectName));//项目名称
            sb.append(buildTd(unitName));//项目业务分类
            sb.append("</tr>");
        }
        sb.append("</table>");
        return sb.toString();
    }

    private String buildTd(String value) {
        return "<td style='border: #999 1px solid; align:center;'>" + (value == null ? "" : value) + "</td>";
    }

    private String buildTdHref(String value, String url) {
        String urlStr = " <a href='" + url + "' target='_blank'>" + value + "</a>";
        return "<td style='border: #999 1px solid; align:center;'>" + urlStr + "</td>";
    }


}
