package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.CostCollectionDto;
import com.midea.pam.common.ctc.vo.DifferenceShareResultDetailDTOExcelVO;
import com.midea.pam.common.ctc.vo.VendorPenaltyCostDetailExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.excelVo.CarryoverBillOutsourcingCostCollectionExcelVO;
import com.midea.pam.common.statistics.excelVo.CostCollectionStatExcelVO;
import com.midea.pam.common.statistics.excelVo.FeeCostDetailExcelVO;
import com.midea.pam.common.statistics.excelVo.LaborCostDetailStatExcelVO;
import com.midea.pam.common.statistics.excelVo.MaterialActualCostDetailExcelVO;
import com.midea.pam.common.statistics.excelVo.ProgressOutsourcingCostCollectionExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectAssetDeprnCostDetailExcelVO;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @program: common-module
 * @description: 实际成本归集导出
 * @author:zhongpeng
 * @create:2020-03-23 15:20
 **/
@Api("实际成本归集导出")
@RestController
@RequestMapping("statistics/costCollection")
public class CostCollectionStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @Value("${business.kukaRouxingUnitId}")
    private String kukaRouxingUnitId;


    @ApiOperation(value = "实际成本归集导出分页查询")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) final Long id,
                         @ApiParam(name = "projectCode", value = "项目编码", required = false) @RequestParam(required = false) String projectCode,
                         @ApiParam(name = "ouId", value = "实体ID", required = false) @RequestParam(required = false) String ouId,
                         @ApiParam(name = "costMethodMain", value = "收入/成本方法", required = false) @RequestParam(required = false) Integer costMethodMain,
                         @ApiParam(name = "projectName", value = "项目名称", required = false) @RequestParam(required = false) String projectName,
                         @ApiParam(name = "startTime", value = "归集开始时间", required = false) @RequestParam(required = false) String startTime,
                         @ApiParam(name = "endTime", value = "归集结束时间", required = false) @RequestParam(required = false) String endTime,
                         @ApiParam(name = "costDateStart", value = "成本发生开始日期", required = false) @RequestParam(required = false) String costDateStart,
                         @ApiParam(name = "costDateEnd", value = "成本发生结束日期", required = false) @RequestParam(required = false) String costDateEnd,
                         @ApiParam(name = "carryStatus", value = "结转状态", required = false) @RequestParam(required = false) Integer carryStatus,
                         @ApiParam(name = "glPeriod", value = "结转期间", required = false) @RequestParam(required = false) String glPeriod,
                         @ApiParam(name = "projectType", value = "项目类型", required = false) @RequestParam(required = false) String projectType,
                         @ApiParam(name = "currency", value = "币种", required = false) @RequestParam(required = false) String currency,
                         @RequestParam(required = true, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = true, defaultValue = "10") final Integer pageSize) {

        final Map<String, Object> param = new HashMap<>();
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);
        param.put("id", id);
        param.put("projectCode", projectCode);
        param.put("ouId", ouId);
        param.put("projectName", projectName);
        param.put("type", costMethodMain);
        param.put("startTime", startTime);
        param.put("endTime", endTime);

        param.put("costDateStart", costDateStart);
        param.put("costDateEnd", costDateEnd);
        param.put("carryStatus", carryStatus);
        param.put("glPeriod", glPeriod);
        param.put("projectType", projectType);
        param.put("currency", currency);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/costCollection/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CostCollectionDto>>>() {
        });
    }


    @ApiOperation(value = "实际成本归集导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void listExport(HttpServletResponse response, @RequestParam(required = false) final Long id,
                           @ApiParam(name = "projectCode", value = "项目编码", required = false) @RequestParam(required = false) String projectCode,
                           @ApiParam(name = "ouId", value = "实体ID", required = false) @RequestParam(required = false) String ouId,
                           @ApiParam(name = "costMethodMain", value = "收入/成本方法", required = false) @RequestParam(required = false) Integer costMethodMain,
                           @ApiParam(name = "projectName", value = "项目名称", required = false) @RequestParam(required = false) String projectName,
                           @ApiParam(name = "startTime", value = "归集开始时间", required = false) @RequestParam(required = false) String startTime,
                           @ApiParam(name = "endTime", value = "归集结束时间", required = false) @RequestParam(required = false) String endTime,
                           @ApiParam(name = "costDateStart", value = "成本发生开始日期", required = false) @RequestParam(required = false) String costDateStart,
                           @ApiParam(name = "costDateEnd", value = "成本发生结束日期", required = false) @RequestParam(required = false) String costDateEnd,
                           @ApiParam(name = "carryStatus", value = "结转状态", required = false) @RequestParam(required = false) Integer carryStatus,
                           @ApiParam(name = "glPeriod", value = "结转期间", required = false) @RequestParam(required = false) String glPeriod,
                           @ApiParam(name = "projectType", value = "项目类型", required = false) @RequestParam(required = false) String projectType,
                           @ApiParam(name = "currency", value = "币种", required = false) @RequestParam(required = false) String currency) {
        final Map<String, Object> param = new HashMap<>();

        param.put("id", id);
        param.put("projectCode", projectCode);
        param.put("ouId", ouId);
        param.put("projectName", projectName);
        param.put("type", costMethodMain);
        param.put("startTime", startTime);
        param.put("endTime", endTime);

        param.put("costDateStart", costDateStart);
        param.put("costDateEnd", costDateEnd);
        param.put("carryStatus", carryStatus);
        param.put("glPeriod", glPeriod);
        param.put("projectType", projectType);
        param.put("currency", currency);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/costCollection/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        com.midea.pam.gateway.common.base.DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<com.midea.pam.gateway.common.base.DataResponse<Map<String, Object>>>() {
        });
        if (dataResponse.getData() == null
                && dataResponse.getCode() == 999) {
            throw new MipException(dataResponse.getMsg());
        }

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("实际成本归集_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");
        if (resultMap != null) {
            JSONArray costCollectionDtosArr = (JSONArray) resultMap.get("costCollectionDtos");
            JSONArray materialActualCostDetailDtosArr = (JSONArray) resultMap.get("materialActualCostDetailDtos");
            JSONArray differenceShareResultDetailDtos = (JSONArray) resultMap.get("differenceShareResultDetailDtos");
            JSONArray carryoverBillOutsourcingCostCollectionDtosArr = (JSONArray) resultMap.get("carryoverBillOutsourcingCostCollectionDtos");
            JSONArray laborCostDetailDtosArr = (JSONArray) resultMap.get("laborCostDetailDtos");
            JSONArray feeCostDetailDtosArr = (JSONArray) resultMap.get("feeCostDetailDtos");
            JSONArray vendorPenaltyCostDetailsArr = (JSONArray) resultMap.get("vendorPenaltyCostDetails");
            JSONArray projectAssetDeprnCostDetailsArr = (JSONArray) resultMap.get("projectAssetDeprnCostDetails");

            List<CostCollectionStatExcelVO> costCollectionStatExcelVOS = JSONObject.parseArray(costCollectionDtosArr.toJSONString(), CostCollectionStatExcelVO.class);
            List<MaterialActualCostDetailExcelVO> materialActualCostDetailExcelVOS = JSONObject.parseArray(materialActualCostDetailDtosArr.toJSONString(), MaterialActualCostDetailExcelVO.class);
            List<DifferenceShareResultDetailDTOExcelVO> differenceShareResultDetailDTOExcelVOS = JSONObject.parseArray(differenceShareResultDetailDtos.toJSONString(), DifferenceShareResultDetailDTOExcelVO.class);
            List<CarryoverBillOutsourcingCostCollectionExcelVO> carryoverBillOutsourcingCostCollectionExcelVOS = null;
            List<ProgressOutsourcingCostCollectionExcelVO> progressOutsourcingCostCollectionExcelVOS = null;
            if (StringUtils.hasText(kukaRouxingUnitId) && Objects.equals(SystemContext.getUnitId(), Long.valueOf(kukaRouxingUnitId))) {
                progressOutsourcingCostCollectionExcelVOS = JSONObject.parseArray(carryoverBillOutsourcingCostCollectionDtosArr.toJSONString(), ProgressOutsourcingCostCollectionExcelVO.class); //柔性
            } else {
                carryoverBillOutsourcingCostCollectionExcelVOS = JSONObject.parseArray(carryoverBillOutsourcingCostCollectionDtosArr.toJSONString(), CarryoverBillOutsourcingCostCollectionExcelVO.class); //非柔性
            }
            List<LaborCostDetailStatExcelVO> laborCostDetailStatExcelVOS = JSONObject.parseArray(laborCostDetailDtosArr.toJSONString(), LaborCostDetailStatExcelVO.class);
            List<FeeCostDetailExcelVO> feeCostDetailExcelVOS = JSONObject.parseArray(feeCostDetailDtosArr.toJSONString(), FeeCostDetailExcelVO.class);
            List<VendorPenaltyCostDetailExcelVo> vendorPenaltyCostDetailExcelVos = JSONObject.parseArray(vendorPenaltyCostDetailsArr.toJSONString(), VendorPenaltyCostDetailExcelVo.class);
            List<ProjectAssetDeprnCostDetailExcelVO> projectAssetDeprnCostDetailExcelVOS = JSONObject.parseArray(projectAssetDeprnCostDetailsArr.toJSONString(), ProjectAssetDeprnCostDetailExcelVO.class);

            if (CollectionUtils.isNotEmpty(costCollectionStatExcelVOS)) {
                for (int i = 0; i < costCollectionStatExcelVOS.size(); i++) {
                    CostCollectionStatExcelVO costCollectionStatExcelVO = costCollectionStatExcelVOS.get(i);
                    costCollectionStatExcelVO.setNum(i + 1);
                }
            }

            if (CollectionUtils.isNotEmpty(materialActualCostDetailExcelVOS)) {
                for (int i = 0; i < materialActualCostDetailExcelVOS.size(); i++) {
                    MaterialActualCostDetailExcelVO materialActualCostDetailExcelVO = materialActualCostDetailExcelVOS.get(i);
                    materialActualCostDetailExcelVO.setNum(i + 1);
                }
            }

            if (CollectionUtils.isNotEmpty(differenceShareResultDetailDTOExcelVOS)) {
                for (int i = 0; i < differenceShareResultDetailDTOExcelVOS.size(); i++) {
                    DifferenceShareResultDetailDTOExcelVO differenceShareResultDetailDTOExcelVO = differenceShareResultDetailDTOExcelVOS.get(i);
                    differenceShareResultDetailDTOExcelVO.setNum(i + 1);
                }
            }

            if (CollectionUtils.isNotEmpty(carryoverBillOutsourcingCostCollectionExcelVOS)) {
                for (int i = 0; i < carryoverBillOutsourcingCostCollectionExcelVOS.size(); i++) {
                    CarryoverBillOutsourcingCostCollectionExcelVO excelVO = carryoverBillOutsourcingCostCollectionExcelVOS.get(i);
                    excelVO.setNum(i + 1);
                }
            }

            if (CollectionUtils.isNotEmpty(progressOutsourcingCostCollectionExcelVOS)) {
                for (int i = 0; i < progressOutsourcingCostCollectionExcelVOS.size(); i++) {
                    ProgressOutsourcingCostCollectionExcelVO excelVO = progressOutsourcingCostCollectionExcelVOS.get(i);
                    excelVO.setNum(i + 1);
                }
            }

            if (CollectionUtils.isNotEmpty(laborCostDetailStatExcelVOS)) {
                for (int i = 0; i < laborCostDetailStatExcelVOS.size(); i++) {
                    LaborCostDetailStatExcelVO laborCostDetailStatExcelVO = laborCostDetailStatExcelVOS.get(i);
                    laborCostDetailStatExcelVO.setNum(i + 1);
                }
            }


            if (CollectionUtils.isNotEmpty(feeCostDetailExcelVOS)) {
                for (int i = 0; i < feeCostDetailExcelVOS.size(); i++) {
                    FeeCostDetailExcelVO feeCostDetailExcelVO = feeCostDetailExcelVOS.get(i);
                    feeCostDetailExcelVO.setNum(i + 1);
                }
            }

            if (CollectionUtils.isNotEmpty(vendorPenaltyCostDetailExcelVos)) {
                for (int i = 0; i < vendorPenaltyCostDetailExcelVos.size(); i++) {
                    VendorPenaltyCostDetailExcelVo vendorPenaltyCostDetailExcelVo = vendorPenaltyCostDetailExcelVos.get(i);
                    vendorPenaltyCostDetailExcelVo.setNum(i + 1);
                }
            }

            if (CollectionUtils.isNotEmpty(projectAssetDeprnCostDetailExcelVOS)) {
                for (int i = 0; i < projectAssetDeprnCostDetailExcelVOS.size(); i++) {
                    ProjectAssetDeprnCostDetailExcelVO projectAssetDeprnCostDetailExcelVO = projectAssetDeprnCostDetailExcelVOS.get(i);
                    projectAssetDeprnCostDetailExcelVO.setNum(i + 1);
                }
            }

            Workbook workbook = ExportExcelUtil.buildDefaultSheet(costCollectionStatExcelVOS, CostCollectionStatExcelVO.class, null, "汇总", Boolean.TRUE);

            ExportExcelUtil.addSheet(workbook, materialActualCostDetailExcelVOS, MaterialActualCostDetailExcelVO.class, null, "物料成本（直接）明细", Boolean.TRUE);
            if (StringUtils.hasText(kukaRouxingUnitId) && Objects.equals(SystemContext.getUnitId(), Long.valueOf(kukaRouxingUnitId))) {
                ExportExcelUtil.addSheet(workbook, progressOutsourcingCostCollectionExcelVOS, ProgressOutsourcingCostCollectionExcelVO.class, null, "物料成本（外包）明细", Boolean.TRUE); //柔性
            } else {
                ExportExcelUtil.addSheet(workbook, carryoverBillOutsourcingCostCollectionExcelVOS, CarryoverBillOutsourcingCostCollectionExcelVO.class, null, "物料成本（外包）明细", Boolean.TRUE); //非柔性
            }
            ExportExcelUtil.addSheet(workbook, vendorPenaltyCostDetailExcelVos, VendorPenaltyCostDetailExcelVo.class, null, "材料罚扣", Boolean.TRUE);
            ExportExcelUtil.addSheet(workbook, differenceShareResultDetailDTOExcelVOS, DifferenceShareResultDetailDTOExcelVO.class, null, "物料成本（差异分摊）明细", Boolean.TRUE);
            ExportExcelUtil.addSheet(workbook, laborCostDetailStatExcelVOS, LaborCostDetailStatExcelVO.class, null, "人力成本明细", Boolean.TRUE);
            ExportExcelUtil.addSheet(workbook, feeCostDetailExcelVOS, FeeCostDetailExcelVO.class, null, "费用成本明细", Boolean.TRUE);
            ExportExcelUtil.addSheet(workbook, projectAssetDeprnCostDetailExcelVOS, ProjectAssetDeprnCostDetailExcelVO.class, null, "资产折旧成本明细", Boolean.TRUE);

            ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
        }

    }
}
