package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.MaterialGetDto;
import com.midea.pam.common.ctc.vo.MaterialGetExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtil;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/1
 * @description
 */
@Api("统计-领料单")
@RestController
@RequestMapping("statistics/materialGet")
public class MaterialGetStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "查询领料单列表")
    @GetMapping("page")
    public Response list(MaterialGetDto materialGetDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false) final String applyDateStart,
                         @RequestParam(required = false) final String applyDateEnd,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(materialGetDto);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("applyDateStart", applyDateStart);
        params.put("applyDateEnd", applyDateEnd);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/materialGet/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<MaterialGetDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<MaterialGetDto>>>() {
        });

        return response;
    }

    private Map buildParam(MaterialGetDto materialGetDto) {
        final Map<String, Object> params = new HashMap<>();
        //领料单编号 单据状态 制单人 领料人申请日期 领料子库 领料货位 项目号 同步状态
        params.put("getCode", materialGetDto.getGetCode());
        params.put("statusStr", materialGetDto.getStatusStr());
        params.put("fillUserId", materialGetDto.getFillUserId());
        params.put("fillUserName", materialGetDto.getFillUserName());
        params.put("getUserId", materialGetDto.getGetUserId());
        params.put("getUserName", materialGetDto.getGetUserName());
        params.put("inventoryCode", materialGetDto.getInventoryCode());
        params.put("locationCode", materialGetDto.getLocationCode());
        params.put("projectCode", materialGetDto.getProjectCode());
        params.put("managerName", materialGetDto.getManagerName());
        params.put("erpCodeStr", materialGetDto.getErpCodeStr());
        params.put("resouce", materialGetDto.getResouce());
        params.put("operatingUnitIdsStr", materialGetDto.getOperatingUnitIdsStr());
        params.put("organizationCodesStr", materialGetDto.getOrganizationCodesStr());
        params.put("materialGetTypeStr", materialGetDto.getMaterialGetTypeStr());
        return params;
    }


    @ApiOperation(value = "领料单列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response,
                           MaterialGetDto materialGetDto,
                           @RequestParam(required = false) final String applyDateStart,
                           @RequestParam(required = false) final String applyDateEnd) {
        final Map<String, Object> params = buildParam(materialGetDto);
        params.put("applyDateStart", applyDateStart);
        params.put("applyDateEnd", applyDateEnd);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/materialGet/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<MaterialGetExcelVO>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<List<MaterialGetExcelVO>>>() {
        });
        if (dataResponse.getData() == null
                && dataResponse.getCode() == 999) {
            throw new MipException(dataResponse.getMsg());
        }
        List<MaterialGetExcelVO> materialGetExcelVOS = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("领料单_"+ DateUtils.format(new Date(),"yyyyMMddHHmmss"));
        fileName.append(".xls");

        for (int i = 0; i < materialGetExcelVOS.size(); i++) {
            MaterialGetExcelVO materialGetExcelVO = materialGetExcelVOS.get(i);
            materialGetExcelVO.setNum(i + 1);
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(materialGetExcelVOS, MaterialGetExcelVO.class, null, "项目领料", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

}
