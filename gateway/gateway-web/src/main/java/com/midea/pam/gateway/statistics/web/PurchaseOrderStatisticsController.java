package com.midea.pam.gateway.statistics.web;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.entity.Menu;
import com.midea.pam.common.ctc.dto.PurchaseOrderDetailDto;
import com.midea.pam.common.ctc.dto.PurchaseOrderDto;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderExcelVo;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderExportDetailVO;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderImportDetailVO;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderListExcelVO;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderListWbsExcelVO;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderProgressExcelVo;
import com.midea.pam.common.ctc.excelVo.PurchaseOrderWbsExcelVo;
import com.midea.pam.common.enums.ErpOrderStatus;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.OrderLineStatusEnum;
import com.midea.pam.common.enums.OrderStatusEnum;
import com.midea.pam.common.enums.PricingTypeEnum;
import com.midea.pam.common.util.BeanMapTool;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.common.utils.ListUtil;
import com.midea.pam.gateway.request.PurchaseOrderDetailRequest;
import com.midea.pam.gateway.request.PurchaserOrderRequest;
import com.midea.pam.gateway.request.PurchaserOrderWbsRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.assertj.core.util.Lists;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@Api("物料采购订单")
@RestController
@RequestMapping("statistics/purchaseOrder")
public class PurchaseOrderStatisticsController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "采购订单列表导出", response = PurchaseOrderDto.class)
    @GetMapping({"export"})
    public void exportOrder(HttpServletResponse response,
                            @RequestParam(required = false) @ApiParam(value = "采购订单号") String fuzzyOrderNum,
                            @RequestParam(required = false) @ApiParam(value = "供应商名称") String fuzzyVendorName,
                            @RequestParam(required = false) @ApiParam(value = "供应商编码") String fuzzyVendorNum,
                            @RequestParam(required = false) @ApiParam(value = "采购员") String buyerName,
                            @RequestParam(required = false) @ApiParam(value = "ERP同步状态_多选(0：未同步， 1：已同步， 2：同步失败)") String manySyncStatus,
                            @RequestParam(required = false) @ApiParam(value = "订单状态订单状态——1：生效，2：已取消，3：关闭，4：冻结，5：暂挂(多选)") String manyStatus,
                            @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                            @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                            @RequestParam(required = false) @ApiParam(value = "来源") String resouce,
                            @RequestParam(required = false) @ApiParam(value = "业务实体") String manyProjectOuName,
                            @RequestParam(required = false) @ApiParam(value = "创建日期开始") Date orderCreateAtBegin,
                            @RequestParam(required = false) @ApiParam(value = "创建日期结束") Date orderCreateAtEnd,
                            @RequestParam(required = false) @ApiParam(value = "供方承诺交期开始时间") Date promisedDateStart,
                            @RequestParam(required = false) @ApiParam(value = "供方承诺交期结束时间") Date promisedDateEnd,
                            @RequestParam(required = false) @ApiParam(value = "跟踪日期开始时间") Date trackDateStart,
                            @RequestParam(required = false) @ApiParam(value = "跟踪日期结束时间") Date trackDateEnd) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("fuzzyOrderNum", fuzzyOrderNum);
        param.put("fuzzyVendorName", fuzzyVendorName);
        param.put("fuzzyVendorNum", fuzzyVendorNum);
        param.put("buyerName", buyerName);
        param.put("manyStatus", manyStatus);
        param.put("manySyncStatus", manySyncStatus);
        param.put("resouce", resouce);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("manyProjectOuName", manyProjectOuName);
        param.put("orderCreateAtBegin", orderCreateAtBegin == null ? "" : orderCreateAtBegin.getTime());
        param.put("orderCreateAtEnd", orderCreateAtEnd == null ? "" : orderCreateAtEnd.getTime());
        param.put("promisedDateStart", promisedDateStart == null ? "" : promisedDateStart.getTime());
        param.put("promisedDateEnd", promisedDateEnd == null ? "" : promisedDateEnd.getTime());
        param.put("trackDateStart", trackDateStart == null ? "" : trackDateStart.getTime());
        param.put("trackDateEnd", trackDateEnd == null ? "" : trackDateEnd.getTime());
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/purchaseOrder/export", param);
        String responseEntity = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<PurchaseOrderDetailDto>> dataResponse = JSON.parseObject(responseEntity,
                new TypeReference<DataResponse<List<PurchaseOrderDetailDto>>>() {
                });

        List<PurchaseOrderDetailDto> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        List<PurchaseOrderExcelVo> excelVos = ListUtil.map(dataList, new ListUtil.IteratorTask<PurchaseOrderExcelVo, PurchaseOrderDetailDto>() {
            @Override
            public PurchaseOrderExcelVo getValue(PurchaseOrderDetailDto item) {
                PurchaseOrderExcelVo wifi = new PurchaseOrderExcelVo();
                BeanUtils.copyProperties(item, wifi);
                return wifi;
            }
        });
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseOrderExcelVo.class, "采购订单管理_" + DateUtils.format(new Date(),
                "yyyyMMddHHmmss") + ".xls", response);
    }

    @ApiOperation(value = "采购订单列表(WBS)导出", response = PurchaseOrderDto.class)
    @GetMapping({"exportWbs"})
    public void exportWbs(HttpServletResponse response,
                          @RequestParam(required = false) @ApiParam(value = "采购订单号") String fuzzyOrderNum,
                          @RequestParam(required = false) @ApiParam(value = "供应商名称") String fuzzyVendorName,
                          @RequestParam(required = false) @ApiParam(value = "供应商编码") String fuzzyVendorNum,
                          @RequestParam(required = false) @ApiParam(value = "采购员") String buyerName,
                          @RequestParam(required = false) @ApiParam(value = "同步状态(多选)") String manySyncStatus,
                          @RequestParam(required = false) @ApiParam(value = "订单状态(多选)") String manyStatus,
                          @RequestParam(required = false) @ApiParam(value = "业务实体") String manyProjectOuName,
                          @RequestParam(required = false) @ApiParam(value = "创建日期开始") Date orderCreateAtBegin,
                          @RequestParam(required = false) @ApiParam(value = "创建日期结束") Date orderCreateAtEnd) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("fuzzyOrderNum", fuzzyOrderNum);
        param.put("fuzzyVendorName", fuzzyVendorName);
        param.put("fuzzyVendorNum", fuzzyVendorNum);
        param.put("buyerName", buyerName);
        param.put("manyStatus", manyStatus);
        param.put("manySyncStatus", manySyncStatus);
        param.put("manyProjectOuName", manyProjectOuName);
        param.put("orderCreateAtBegin", orderCreateAtBegin == null ? "" : orderCreateAtBegin.getTime());
        param.put("orderCreateAtEnd", orderCreateAtEnd == null ? "" : orderCreateAtEnd.getTime());
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/purchaseOrder/exportWbs", param);
        String responseEntity = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<PurchaseOrderDto>> dataResponse = JSON.parseObject(responseEntity,
                new TypeReference<DataResponse<List<PurchaseOrderDto>>>() {
                });

        List<PurchaseOrderDto> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        List<PurchaseOrderWbsExcelVo> excelVos = ListUtil.map(dataList, new ListUtil.IteratorTask<PurchaseOrderWbsExcelVo, PurchaseOrderDto>() {
            @Override
            public PurchaseOrderWbsExcelVo getValue(PurchaseOrderDto item) {
                PurchaseOrderWbsExcelVo wifi = new PurchaseOrderWbsExcelVo();
                BeanUtils.copyProperties(item, wifi);
                return wifi;
            }
        });
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseOrderWbsExcelVo.class, "采购订单管理(WBS)_" + DateUtils.format(new Date(),
                "yyyyMMddHHmmss") + ".xls", response);
    }

    @ApiOperation(value = "采购订单列表", response = PurchaseOrderDto.class)
    @GetMapping({"selectPage"})
    public Response selectPage(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "采购订单号") String fuzzyOrderNum,
            @RequestParam(required = false) @ApiParam(value = "供应商名称") String fuzzyVendorName,
            @RequestParam(required = false) @ApiParam(value = "供应商编码") String fuzzyVendorNum,
            @RequestParam(required = false) @ApiParam(value = "采购员") String buyerName,
            @RequestParam(required = false) @ApiParam(value = "ERP同步状态_多选(0：未同步， 1：已同步， 2：同步失败，3：同步中)") String manySyncStatus,
            @RequestParam(required = false) @ApiParam(value = "订单状态订单状态——1：生效，2：已取消，3：关闭，4：冻结，5：暂挂(多选)") String manyStatus,
            @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
            @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
            @RequestParam(required = false) @ApiParam(value = "业务实体") String manyProjectOuName,
            @RequestParam(required = false) @ApiParam(value = "创建日期开始") Date orderCreateAtBegin,
            @RequestParam(required = false) @ApiParam("来源") String resouce,
            @RequestParam(required = false) @ApiParam(value = "创建日期结束") Date orderCreateAtEnd,
            @RequestParam(required = false) @ApiParam(value = "供方承诺交期开始时间") Date promisedDateStart,
            @RequestParam(required = false) @ApiParam(value = "供方承诺交期结束时间") Date promisedDateEnd,
            @RequestParam(required = false) @ApiParam(value = "跟踪日期开始时间") Date trackDateStart,
            @RequestParam(required = false) @ApiParam(value = "跟踪日期结束时间") Date trackDateEnd) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyOrderNum", fuzzyOrderNum);
        param.put("fuzzyVendorName", fuzzyVendorName);
        param.put("fuzzyVendorNum", fuzzyVendorNum);
        param.put("buyerName", buyerName);
        param.put("manyStatus", manyStatus);
        param.put("manySyncStatus", manySyncStatus);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("resouce", resouce);
        param.put("manyProjectOuName", manyProjectOuName);
        param.put("orderCreateAtBegin", orderCreateAtBegin == null ? "" : orderCreateAtBegin.getTime());
        param.put("orderCreateAtEnd", orderCreateAtEnd == null ? "" : orderCreateAtEnd.getTime());
        param.put("promisedDateStart", promisedDateStart == null ? "" : promisedDateStart.getTime());
        param.put("promisedDateEnd", promisedDateEnd == null ? "" : promisedDateEnd.getTime());
        param.put("trackDateStart", trackDateStart == null ? "" : trackDateStart.getTime());
        param.put("trackDateEnd", trackDateEnd == null ? "" : trackDateEnd.getTime());
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/purchaseOrder/selectPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<PurchaseOrderDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<PurchaseOrderDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "采购订单列表(WBS)", response = PurchaseOrderDto.class)
    @GetMapping({"selectWbsPage"})
    public Response selectWbsPage(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "采购订单号") String fuzzyOrderNum,
            @RequestParam(required = false) @ApiParam(value = "供应商名称") String fuzzyVendorName,
            @RequestParam(required = false) @ApiParam(value = "供应商编码") String fuzzyVendorNum,
            @RequestParam(required = false) @ApiParam(value = "采购员") String buyerName,
            @RequestParam(required = false) @ApiParam(value = "ERP同步状态_多选(0：未同步， 1：已同步， 2：同步失败，3：同步中)") String manySyncStatus,
            @RequestParam(required = false) @ApiParam(value = "订单状态订单状态——1：生效，2：已取消，3：关闭，4：冻结，5：暂挂(多选)") String manyStatus,
            @RequestParam(required = false) @ApiParam(value = "ERP订单状态_多选(1-未发布 2-已发布 3-已回签)") String erpOrderStatusStr,
            @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
            @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
            @RequestParam(required = false) @ApiParam(value = "业务实体") String manyProjectOuName,
            @RequestParam(required = false) @ApiParam(value = "创建日期开始") Date orderCreateAtBegin,
            @RequestParam(required = false) @ApiParam("来源") String resouce,
            @RequestParam(required = false) @ApiParam(value = "创建日期结束") Date orderCreateAtEnd) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyOrderNum", fuzzyOrderNum);
        param.put("fuzzyVendorName", fuzzyVendorName);
        param.put("fuzzyVendorNum", fuzzyVendorNum);
        param.put("buyerName", buyerName);
        param.put("manyStatus", manyStatus);
        param.put("erpOrderStatusStr", erpOrderStatusStr);
        param.put("manySyncStatus", manySyncStatus);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("resouce", resouce);
        param.put("manyProjectOuName", manyProjectOuName);
        param.put("orderCreateAtBegin", orderCreateAtBegin == null ? "" : orderCreateAtBegin.getTime());
        param.put("orderCreateAtEnd", orderCreateAtEnd == null ? "" : orderCreateAtEnd.getTime());
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/purchaseOrder/selectWbsPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<PurchaseOrderDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<PurchaseOrderDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "采购订单进展导出", response = PurchaseOrderDto.class)
    @GetMapping({"exportProgress"})
    public void exportProgress(HttpServletResponse response,
                               @RequestParam(required = false) @ApiParam(value = "采购订单号") String fuzzyOrderNum,
                               @RequestParam(required = false) @ApiParam(value = "供应商名称") String fuzzyVendorName,
                               @RequestParam(required = false) @ApiParam(value = "供应商编码") String fuzzyVendorNum,
                               @RequestParam(required = false) @ApiParam(value = "采购员") String buyerName,
                               @RequestParam(required = false) @ApiParam(value = "物料描述") String materielDescr,
                               @RequestParam(required = false) @ApiParam(value = "ERP物料编码") String erpCode,
                               @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                               @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                               @RequestParam(required = false) @ApiParam(value = "业务实体") String manyProjectOuName,
                               @RequestParam(required = false) @ApiParam(value = "订单状态") String manyStatus,
                               @RequestParam(required = false) @ApiParam(value = "订单行创建人") String orderDetailCreateByName,
                               @RequestParam(required = false) @ApiParam(value = "订单行创建日期开始") Date orderDetailCreateAtBegin,
                               @RequestParam(required = false) @ApiParam(value = "订单行创建日期结束") Date orderDetailCreateAtEnd,
                               @RequestParam(required = false) @ApiParam(value = "创建日期开始") Date orderCreateAtBegin,
                               @RequestParam(required = false) @ApiParam(value = "创建日期结束") Date orderCreateAtEnd,
                               @RequestParam(required = false) @ApiParam(value = "计划交货日期开始") Date deliveryTimeBegin,
                               @RequestParam(required = false) @ApiParam(value = "计划交货日期结束") Date deliveryTimeEnd,
                               @RequestParam(required = false) @ApiParam(value = "供方承诺交期开始时间") Date promisedDateStart,
                               @RequestParam(required = false) @ApiParam(value = "供方承诺交期结束时间") Date promisedDateEnd,
                               @RequestParam(required = false) @ApiParam(value = "跟踪日期开始时间") Date trackDateStart,
                               @RequestParam(required = false) @ApiParam(value = "跟踪日期结束时间") Date trackDateEnd) throws Exception {
        Map<String, Object> param = new HashMap<>();
        param.put("fuzzyOrderNum", fuzzyOrderNum);
        param.put("fuzzyVendorName", fuzzyVendorName);
        param.put("fuzzyVendorNum", fuzzyVendorNum);
        param.put("buyerName", buyerName);
        param.put("erpCode", erpCode);
        param.put("materielDescr", materielDescr);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("manyProjectOuName", manyProjectOuName);
        param.put("deliveryTimeBegin", deliveryTimeBegin);
        param.put("deliveryTimeEnd", deliveryTimeEnd);
        param.put("orderCreateAtBegin", orderCreateAtBegin);
        param.put("orderCreateAtEnd", orderCreateAtEnd);
        param.put("orderDetailCreateAtBegin", orderDetailCreateAtBegin);
        param.put("orderDetailCreateAtEnd", orderDetailCreateAtEnd);
        param.put("orderDetailCreateByName", orderDetailCreateByName);
        param.put("manyStatus", manyStatus);
        param.put("promisedDateStart", promisedDateStart);
        param.put("promisedDateEnd", promisedDateEnd);
        param.put("trackDateStart", trackDateStart);
        param.put("trackDateEnd", trackDateEnd);
        PurchaseOrderDto query = BeanMapTool.mapToBean(param, PurchaseOrderDto.class);
        String url = String.format("%sstatistics/purchaseOrder/exportPurchaseOrderProgress", ModelsEnum.STATISTICS.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        DataResponse<List<PurchaseOrderDetailDto>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<PurchaseOrderDetailDto>>>() {
                });

        List<PurchaseOrderDetailDto> dataList = dataResponse.getData();
        List<PurchaseOrderProgressExcelVo> excelVos = ListUtil.map(dataList, new ListUtil.IteratorTask<PurchaseOrderProgressExcelVo,
                PurchaseOrderDetailDto>() {
            @Override
            public PurchaseOrderProgressExcelVo getValue(PurchaseOrderDetailDto item) {
                PurchaseOrderProgressExcelVo wifi = new PurchaseOrderProgressExcelVo();
                BeanUtils.copyProperties(item, wifi);
                return wifi;
            }
        });
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseOrderProgressExcelVo.class, "采购订单进展_" + DateUtils.format(new Date(),
                "yyyyMMddHHmmss") + ".xls", response);
    }

    @ApiOperation(value = "采购订单进展", response = PurchaseOrderDto.class)
    @GetMapping({"selectProgressPage"})
    public Response selectProgressPage(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "采购订单号") String fuzzyOrderNum,
            @RequestParam(required = false) @ApiParam(value = "供应商名称") String fuzzyVendorName,
            @RequestParam(required = false) @ApiParam(value = "供应商编码") String fuzzyVendorNum,
            @RequestParam(required = false) @ApiParam(value = "采购员") String buyerName,
            @RequestParam(required = false) @ApiParam(value = "物料描述") String materielDescr,
            @RequestParam(required = false) @ApiParam(value = "ERP物料编码") String erpCode,
            @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
            @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
            @RequestParam(required = false) @ApiParam(value = "业务实体") String manyProjectOuName,
            @RequestParam(required = false) @ApiParam(value = "订单状态") String manyStatus,
            @RequestParam(required = false) @ApiParam(value = "订单行创建人") String orderDetailCreateByName,
            @RequestParam(required = false) @ApiParam(value = "订单行创建日期开始") Date orderDetailCreateAtBegin,
            @RequestParam(required = false) @ApiParam(value = "订单行创建日期结束") Date orderDetailCreateAtEnd,
            @RequestParam(required = false) @ApiParam(value = "创建日期开始") Date orderCreateAtBegin,
            @RequestParam(required = false) @ApiParam(value = "创建日期结束") Date orderCreateAtEnd,
            @RequestParam(required = false) @ApiParam(value = "计划交货日期开始") Date deliveryTimeBegin,
            @RequestParam(required = false) @ApiParam(value = "计划交货日期结束") Date deliveryTimeEnd,
            @RequestParam(required = false) @ApiParam(value = "供方承诺交期开始时间") Date promisedDateStart,
            @RequestParam(required = false) @ApiParam(value = "供方承诺交期结束时间") Date promisedDateEnd,
            @RequestParam(required = false) @ApiParam(value = "跟踪日期开始时间") Date trackDateStart,
            @RequestParam(required = false) @ApiParam(value = "跟踪日期结束时间") Date trackDateEnd) throws Exception {
        Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyOrderNum", fuzzyOrderNum);
        param.put("fuzzyVendorName", fuzzyVendorName);
        param.put("fuzzyVendorNum", fuzzyVendorNum);
        param.put("buyerName", buyerName);
        param.put("erpCode", erpCode);
        param.put("materielDescr", materielDescr);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("manyProjectOuName", manyProjectOuName);
        param.put("deliveryTimeBegin", deliveryTimeBegin);
        param.put("deliveryTimeEnd", deliveryTimeEnd);
        param.put("orderCreateAtBegin", orderCreateAtBegin);
        param.put("orderCreateAtEnd", orderCreateAtEnd);
        param.put("orderDetailCreateAtBegin", orderDetailCreateAtBegin);
        param.put("orderDetailCreateAtEnd", orderDetailCreateAtEnd);
        param.put("orderDetailCreateByName", orderDetailCreateByName);
        param.put("manyStatus", manyStatus);
        param.put("promisedDateStart", promisedDateStart);
        param.put("promisedDateEnd", promisedDateEnd);
        param.put("trackDateStart", trackDateStart);
        param.put("trackDateEnd", trackDateEnd);
        PurchaseOrderDto query = BeanMapTool.mapToBean(param, PurchaseOrderDto.class);
        String url = String.format("%sstatistics/purchaseOrder/selectProgress", ModelsEnum.STATISTICS.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        return JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<PurchaseOrderDetailDto>>>() {
                });
    }

    @ApiOperation(value = "采购订单查询", response = PurchaseOrderDto.class)
    @GetMapping({"selectRecordPage"})
    public Response selectRecordPage(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "采购订单号") String fuzzyOrderNum,
            @RequestParam(required = false) @ApiParam(value = "供应商名称") String fuzzyVendorName,
            @RequestParam(required = false) @ApiParam(value = "供应商编码") String fuzzyVendorNum,
            @RequestParam(required = false) @ApiParam(value = "采购员") String buyerName,
            @RequestParam(required = false) @ApiParam(value = "物料描述") String materielDescr,
            @RequestParam(required = false) @ApiParam(value = "ERP物料编码") String erpCode,
            @RequestParam(required = false) @ApiParam(value = "PAM物料编码") String pamCode,
            @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
            @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
            @RequestParam(required = false) @ApiParam(value = "业务实体") String manyProjectOuName,
            @RequestParam(required = false) @ApiParam(value = "类型：0=已创建；1=待下达；2=待变更") String manyType,
            @RequestParam(required = false) @ApiParam(value = "订单状态") String manyStatus,
            @RequestParam(required = false) @ApiParam(value = "订单行创建人") String orderDetailCreateByName,
            @RequestParam(required = false) @ApiParam(value = "订单行创建日期开始") Date orderDetailCreateAtBegin,
            @RequestParam(required = false) @ApiParam(value = "订单行创建日期结束") Date orderDetailCreateAtEnd,
            @RequestParam(required = false) @ApiParam(value = "创建日期开始") Date orderCreateAtBegin,
            @RequestParam(required = false) @ApiParam(value = "创建日期结束") Date orderCreateAtEnd,
            @RequestParam(required = false) @ApiParam(value = "计划交货日期开始") Date deliveryTimeBegin,
            @RequestParam(required = false) @ApiParam(value = "计划交货日期结束") Date deliveryTimeEnd,
            @RequestParam(required = false) @ApiParam(value = "供方承诺交期开始时间") Date promisedDateStart,
            @RequestParam(required = false) @ApiParam(value = "供方承诺交期结束时间") Date promisedDateEnd,
            @RequestParam(required = false) @ApiParam(value = "跟踪日期开始时间") Date trackDateStart,
            @RequestParam(required = false) @ApiParam(value = "跟踪日期结束时间") Date trackDateEnd) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyOrderNum", fuzzyOrderNum);
        param.put("fuzzyVendorName", fuzzyVendorName);
        param.put("fuzzyVendorNum", fuzzyVendorNum);
        param.put("buyerName", buyerName);
        param.put("erpCode", erpCode);
        param.put("pamCode", pamCode);
        param.put("materielDescr", materielDescr);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("manyProjectOuName", manyProjectOuName);
        param.put("deliveryTimeBegin", deliveryTimeBegin);
        param.put("deliveryTimeEnd", deliveryTimeEnd);
        param.put("orderCreateAtBegin", orderCreateAtBegin);
        param.put("orderCreateAtEnd", orderCreateAtEnd);
        param.put("orderDetailCreateAtBegin", orderDetailCreateAtBegin);
        param.put("orderDetailCreateAtEnd", orderDetailCreateAtEnd);
        param.put("orderDetailCreateByName", orderDetailCreateByName);
        param.put("manyStatus", manyStatus);
        param.put("manyType", manyType);
        param.put("promisedDateStart", promisedDateStart);
        param.put("promisedDateEnd", promisedDateEnd);
        param.put("trackDateStart", trackDateStart);
        param.put("trackDateEnd", trackDateEnd);
        PurchaseOrderDto query = BeanMapTool.mapToBean(param, PurchaseOrderDto.class);
        String url = String.format("%sstatistics/purchaseOrder/selectRecordPage", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        DataResponse<PageInfo<PurchaseOrderDetailDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<PurchaseOrderDetailDto>>>() {
                });
        //脱敏
        desensitize(response.getData().getList());
        return response;
    }

    @ApiOperation(value = "采购订单查询(WBS)", response = PurchaseOrderDto.class)
    @GetMapping({"selectRecordWbsPage"})
    public Response selectRecordWbsPage(
            @RequestParam(required = false) @ApiParam(value = "详细设计单据编号") String requirementCode,
            @RequestParam(required = false) @ApiParam(value = "设计发布批次号") String designReleaseLotNumber,
            @RequestParam(required = false) @ApiParam(value = "发布结束日期") Date publishEndTime,
            @RequestParam(required = false) @ApiParam(value = "发布开始日期") Date publishStartTime,
            @RequestParam(required = false) @ApiParam(value = "图纸版本号") String chartVersion,
            @RequestParam(required = false) @ApiParam(value = "品牌") String brand,
            @RequestParam(required = false) @ApiParam(value = "图号/型号") String model,
            @RequestParam(required = false) @ApiParam(value = "wbs") String wbsSummaryCode,
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
            @RequestParam(required = false) @ApiParam(value = "采购订单号") String fuzzyOrderNum,
            @RequestParam(required = false) @ApiParam(value = "供应商名称") String fuzzyVendorName,
            @RequestParam(required = false) @ApiParam(value = "供应商编码") String fuzzyVendorNum,
            @RequestParam(required = false) @ApiParam(value = "供应商地点") String fuzzyVendorSiteCode,
            @RequestParam(required = false) @ApiParam(value = "采购员") String buyerName,
            @RequestParam(required = false) @ApiParam(value = "物料描述") String materielDescr,
            @RequestParam(required = false) @ApiParam(value = "PAM物料编码") String pamCode,
            @RequestParam(required = false) @ApiParam(value = "ERP物料编码") String erpCode,
            @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
            @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
            @RequestParam(required = false) @ApiParam(value = "业务实体") String manyProjectOuName,
            @RequestParam(required = false) @ApiParam(value = "创建日期开始") Date orderCreateAtBegin,
            @RequestParam(required = false) @ApiParam(value = "创建日期结束") Date orderCreateAtEnd,
            @RequestParam(required = false) @ApiParam(value = "计划交货日期开始") Date deliveryTimeBegin,
            @RequestParam(required = false) @ApiParam(value = "计划交货日期结束") Date deliveryTimeEnd,
            @RequestParam(required = false) @ApiParam(value = "审批通过日期开始") Date approvalStartTime,
            @RequestParam(required = false) @ApiParam(value = "审批通过日期结束") Date approvalEndTime,
            @RequestParam(required = false) @ApiParam(value = "类型：0=已创建；3=创建中") String manyType,
            @RequestParam(required = false) @ApiParam(value = "订单状态") String manyStatus,
            @RequestParam(required = false) @ApiParam(value = "订单行状态") String statusStr,
            @RequestParam(required = false) @ApiParam(value = "是否合并行") String isMerge,
            @RequestParam(required = false) @ApiParam(value = "定价类型:1一单一价;2一揽子") Integer pricingType,
            @RequestParam(required = false) @ApiParam(value = "供方承诺交期开始时间") Date promisedDateStart,
            @RequestParam(required = false) @ApiParam(value = "供方承诺交期结束时间") Date promisedDateEnd,
            @RequestParam(required = false) @ApiParam(value = "跟踪日期开始时间") Date trackDateStart,
            @RequestParam(required = false) @ApiParam(value = "跟踪日期结束时间") Date trackDateEnd) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("requirementCode", requirementCode);
        param.put("designReleaseLotNumber", designReleaseLotNumber);
        param.put("publishEndTime", publishEndTime);
        param.put("publishStartTime", publishStartTime);
        param.put("chartVersion", chartVersion);
        param.put("brand", brand);
        param.put("model", model);
        param.put("wbsSummaryCode", wbsSummaryCode);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyOrderNum", fuzzyOrderNum);
        param.put("fuzzyVendorName", fuzzyVendorName);
        param.put("fuzzyVendorNum", fuzzyVendorNum);
        param.put("fuzzyVendorSiteCode", fuzzyVendorSiteCode);
        param.put("buyerName", buyerName);
        param.put("erpCode", erpCode);
        param.put("pamCode", pamCode);
        param.put("materielDescr", materielDescr);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("manyProjectOuName", manyProjectOuName);
        param.put("deliveryTimeBegin", deliveryTimeBegin);
        param.put("deliveryTimeEnd", deliveryTimeEnd);
        param.put("orderCreateAtBegin", orderCreateAtBegin);
        param.put("orderCreateAtEnd", orderCreateAtEnd);
        param.put("manyType", manyType);
        param.put("manyStatus", manyStatus);
        param.put("statusStr", statusStr);
        param.put("isMerge", isMerge);
        param.put("pricingType", pricingType);
        param.put("approvalStartTime", approvalStartTime);
        param.put("approvalEndTime", approvalEndTime);
        param.put("promisedDateStart", promisedDateStart);
        param.put("promisedDateEnd", promisedDateEnd);
        param.put("trackDateStart", trackDateStart);
        param.put("trackDateEnd", trackDateEnd);
        PurchaseOrderDto query = BeanMapTool.mapToBean(param, PurchaseOrderDto.class);
        String url = String.format("%sstatistics/purchaseOrder/selectRecordWbsPage", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        DataResponse<PageInfo<PurchaseOrderDetailDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<PurchaseOrderDetailDto>>>() {
                });
        //脱敏
        desensitizeWbs(response.getData().getList());
        return response;
    }

    @ApiOperation(value = "采购订单查询导出", response = PurchaseOrderDto.class)
    @GetMapping({"exportPurchaseOrderRecord"})
    public void exportPurchaseOrderRecord(HttpServletResponse response,
                                          @RequestParam(required = false) @ApiParam(value = "详细设计单据编号") String requirementCode,
                                          @RequestParam(required = false) @ApiParam(value = "设计发布批次号") String designReleaseLotNumber,
                                          @RequestParam(required = false) @ApiParam(value = "发布结束日期") Date publishEndTime,
                                          @RequestParam(required = false) @ApiParam(value = "发布开始日期") Date publishStartTime,
                                          @RequestParam(required = false) @ApiParam(value = "图纸版本号") String chartVersion,
                                          @RequestParam(required = false) @ApiParam(value = "品牌") String brand,
                                          @RequestParam(required = false) @ApiParam(value = "图号/型号") String model,
                                          @RequestParam(required = false) @ApiParam(value = "wbs") String wbsSummaryCode,
                                          @RequestParam(required = false) @ApiParam(value = "采购订单号") String fuzzyOrderNum,
                                          @RequestParam(required = false) @ApiParam(value = "供应商名称") String fuzzyVendorName,
                                          @RequestParam(required = false) @ApiParam(value = "供应商编码") String fuzzyVendorNum,
                                          @RequestParam(required = false) @ApiParam(value = "采购员") String buyerName,
                                          @RequestParam(required = false) @ApiParam(value = "物料描述") String materielDescr,
                                          @RequestParam(required = false) @ApiParam(value = "pam物料编码") String pamCode,
                                          @RequestParam(required = false) @ApiParam(value = "ERP物料编码") String erpCode,
                                          @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                                          @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                                          @RequestParam(required = false) @ApiParam(value = "业务实体") String manyProjectOuName,
                                          @RequestParam(required = false) @ApiParam(value = "订单行创建人") String orderDetailCreateByName,
                                          @RequestParam(required = false) @ApiParam(value = "订单行创建日期开始") Date orderDetailCreateAtBegin,
                                          @RequestParam(required = false) @ApiParam(value = "订单行创建日期结束") Date orderDetailCreateAtEnd,
                                          @RequestParam(required = false) @ApiParam(value = "创建日期开始") Date orderCreateAtBegin,
                                          @RequestParam(required = false) @ApiParam(value = "创建日期结束") Date orderCreateAtEnd,
                                          @RequestParam(required = false) @ApiParam(value = "计划交货日期开始") Date deliveryTimeBegin,
                                          @RequestParam(required = false) @ApiParam(value = "计划交货日期结束") Date deliveryTimeEnd,
                                          @RequestParam(required = false) @ApiParam(value = "类型：0=已创建；1=待下达；2=待变更") String manyType,
                                          @RequestParam(required = false) @ApiParam(value = "订单状态") String manyStatus,
                                          @RequestParam(required = false) @ApiParam(value = "订单行状态") String statusStr,
                                          @RequestParam(required = false) @ApiParam(value = "是否合并行") String isMerge,
                                          @RequestParam(required = false) @ApiParam(value = "定价类型:1一单一价;2一揽子") Integer pricingType,
                                          @RequestParam(required = false) @ApiParam(value = "供方承诺交期开始时间") Date promisedDateStart,
                                          @RequestParam(required = false) @ApiParam(value = "供方承诺交期结束时间") Date promisedDateEnd,
                                          @RequestParam(required = false) @ApiParam(value = "跟踪日期开始时间") Date trackDateStart,
                                          @RequestParam(required = false) @ApiParam(value = "跟踪日期结束时间") Date trackDateEnd
    ) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("requirementCode", requirementCode);
        param.put("designReleaseLotNumber", designReleaseLotNumber);
        param.put("publishEndTime", publishEndTime);
        param.put("publishStartTime", publishStartTime);
        param.put("chartVersion", chartVersion);
        param.put("brand", brand);
        param.put("model", model);
        param.put("wbsSummaryCode", wbsSummaryCode);
        param.put("fuzzyOrderNum", fuzzyOrderNum);
        param.put("fuzzyVendorName", fuzzyVendorName);
        param.put("fuzzyVendorNum", fuzzyVendorNum);
        param.put("buyerName", buyerName);
        param.put("erpCode", erpCode);
        param.put("pamCode", pamCode);
        param.put("materielDescr", materielDescr);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("manyProjectOuName", manyProjectOuName);
        param.put("deliveryTimeBegin", deliveryTimeBegin);
        param.put("deliveryTimeEnd", deliveryTimeEnd);
        param.put("orderCreateAtBegin", orderCreateAtBegin);
        param.put("orderCreateAtEnd", orderCreateAtEnd);
        param.put("orderDetailCreateAtBegin", orderDetailCreateAtBegin);
        param.put("orderDetailCreateAtEnd", orderDetailCreateAtEnd);
        param.put("orderDetailCreateByName", orderDetailCreateByName);
        param.put("manyType", manyType);
        param.put("manyStatus", manyStatus);
        param.put("statusStr", statusStr);
        param.put("isMerge", isMerge);
        param.put("pricingType", pricingType);
        param.put("promisedDateStart", promisedDateStart);
        param.put("promisedDateEnd", promisedDateEnd);
        param.put("trackDateStart", trackDateStart);
        param.put("trackDateEnd", trackDateEnd);
        PurchaseOrderDto query = BeanMapTool.mapToBean(param, PurchaseOrderDto.class);
        String url = String.format("%sstatistics/purchaseOrder/exportPurchaseOrderRecord", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        DataResponse<List<PurchaseOrderDetailDto>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<PurchaseOrderDetailDto>>>() {
                });

        List<PurchaseOrderDetailDto> dataList = dataResponse.getData();
        //脱敏
        desensitize(dataList);

        List<PurchaseOrderListExcelVO> excelVos = ListUtil.map(dataList, new ListUtil.IteratorTask<PurchaseOrderListExcelVO,
                PurchaseOrderDetailDto>() {
            @Override
            public PurchaseOrderListExcelVO getValue(PurchaseOrderDetailDto item) {
                PurchaseOrderListExcelVO wifi = new PurchaseOrderListExcelVO();
                BeanUtils.copyProperties(item, wifi);
                return wifi;
            }
        });
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseOrderListExcelVO.class, "采购订单查询_" + DateUtils.format(new Date(),
                "yyyyMMddHHmmss") + ".xls", response);
    }

    @ApiOperation(value = "采购订单查询(WBS)导出", response = PurchaseOrderDto.class)
    @GetMapping({"exportPurchaseOrderRecordWbs"})
    public void exportPurchaseOrderRecordWbs(HttpServletResponse response,
                                             @RequestParam(required = false) @ApiParam(value = "详细设计单据编号") String requirementCode,
                                             @RequestParam(required = false) @ApiParam(value = "设计发布批次号") String designReleaseLotNumber,
                                             @RequestParam(required = false) @ApiParam(value = "发布结束日期") Date publishEndTime,
                                             @RequestParam(required = false) @ApiParam(value = "发布开始日期") Date publishStartTime,
                                             @RequestParam(required = false) @ApiParam(value = "图纸版本号") String chartVersion,
                                             @RequestParam(required = false) @ApiParam(value = "品牌") String brand,
                                             @RequestParam(required = false) @ApiParam(value = "图号/型号") String model,
                                             @RequestParam(required = false) @ApiParam(value = "wbs") String wbsSummaryCode,
                                             @RequestParam(required = false) @ApiParam(value = "采购订单号") String fuzzyOrderNum,
                                             @RequestParam(required = false) @ApiParam(value = "供应商名称") String fuzzyVendorName,
                                             @RequestParam(required = false) @ApiParam(value = "供应商编码") String fuzzyVendorNum,
                                             @RequestParam(required = false) @ApiParam(value = "供应商地点") String fuzzyVendorSiteCode,
                                             @RequestParam(required = false) @ApiParam(value = "采购员") String buyerName,
                                             @RequestParam(required = false) @ApiParam(value = "物料描述") String materielDescr,
                                             @RequestParam(required = false) @ApiParam(value = "pam物料编码") String pamCode,
                                             @RequestParam(required = false) @ApiParam(value = "ERP物料编码") String erpCode,
                                             @RequestParam(required = false) @ApiParam(value = "项目名称") String fuzzyProjectName,
                                             @RequestParam(required = false) @ApiParam(value = "项目编号") String fuzzyProjectNum,
                                             @RequestParam(required = false) @ApiParam(value = "业务实体") String manyProjectOuName,
                                             @RequestParam(required = false) @ApiParam(value = "创建日期开始") Date orderCreateAtBegin,
                                             @RequestParam(required = false) @ApiParam(value = "创建日期结束") Date orderCreateAtEnd,
                                             @RequestParam(required = false) @ApiParam(value = "计划交货日期开始") Date deliveryTimeBegin,
                                             @RequestParam(required = false) @ApiParam(value = "计划交货日期结束") Date deliveryTimeEnd,
                                             @RequestParam(required = false) @ApiParam(value = "审批通过日期开始") Date approvalStartTime,
                                             @RequestParam(required = false) @ApiParam(value = "审批通过日期结束") Date approvalEndTime,
                                             @RequestParam(required = false) @ApiParam(value = "类型：0=已创建；3=创建中") String manyType,
                                             @RequestParam(required = false) @ApiParam(value = "订单状态") String manyStatus,
                                             @RequestParam(required = false) @ApiParam(value = "订单行状态") String statusStr,
                                             @RequestParam(required = false) @ApiParam(value = "是否合并行") String isMerge,
                                             @RequestParam(required = false) @ApiParam(value = "定价类型:1一单一价;2一揽子") Integer pricingType,
                                             @RequestParam(required = false) @ApiParam(value = "供方承诺交期开始时间") Date promisedDateStart,
                                             @RequestParam(required = false) @ApiParam(value = "供方承诺交期结束时间") Date promisedDateEnd,
                                             @RequestParam(required = false) @ApiParam(value = "跟踪日期开始时间") Date trackDateStart,
                                             @RequestParam(required = false) @ApiParam(value = "跟踪日期结束时间") Date trackDateEnd) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("requirementCode", requirementCode);
        param.put("designReleaseLotNumber", designReleaseLotNumber);
        param.put("publishEndTime", publishEndTime);
        param.put("publishStartTime", publishStartTime);
        param.put("chartVersion", chartVersion);
        param.put("brand", brand);
        param.put("model", model);
        param.put("wbsSummaryCode", wbsSummaryCode);
        param.put("fuzzyOrderNum", fuzzyOrderNum);
        param.put("fuzzyVendorName", fuzzyVendorName);
        param.put("fuzzyVendorNum", fuzzyVendorNum);
        param.put("fuzzyVendorSiteCode", fuzzyVendorSiteCode);
        param.put("buyerName", buyerName);
        param.put("erpCode", erpCode);
        param.put("pamCode", pamCode);
        param.put("materielDescr", materielDescr);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("manyProjectOuName", manyProjectOuName);
        param.put("deliveryTimeBegin", deliveryTimeBegin);
        param.put("deliveryTimeEnd", deliveryTimeEnd);
        param.put("orderCreateAtBegin", orderCreateAtBegin);
        param.put("orderCreateAtEnd", orderCreateAtEnd);
        param.put("manyType", manyType);
        param.put("manyStatus", manyStatus);
        param.put("statusStr", statusStr);
        param.put("isMerge", isMerge);
        param.put("pricingType", pricingType);
        param.put("approvalStartTime", approvalStartTime);
        param.put("approvalEndTime", approvalEndTime);
        param.put("promisedDateStart", promisedDateStart);
        param.put("promisedDateEnd", promisedDateEnd);
        param.put("trackDateStart", trackDateStart);
        param.put("trackDateEnd", trackDateEnd);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        PurchaseOrderDto query = BeanMapTool.mapToBean(param, PurchaseOrderDto.class);
        String url = String.format("%sstatistics/purchaseOrder/exportPurchaseOrderRecordWbs", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        DataResponse<List<PurchaseOrderDetailDto>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<PurchaseOrderDetailDto>>>() {
                });
        if (CollectionUtils.isEmpty(dataResponse.getData())
                && dataResponse.getCode() == 999) {
            throw new MipException(dataResponse.getMsg());
        }

        List<PurchaseOrderDetailDto> dataList = dataResponse.getData();
        //脱敏
        desensitizeWbs(dataList);

        List<PurchaseOrderListWbsExcelVO> excelVos = ListUtil.map(dataList, resource -> {
            PurchaseOrderListWbsExcelVO target = new PurchaseOrderListWbsExcelVO();
            BeanUtils.copyProperties(resource, target);
            target.setOrderStatus(OrderStatusEnum.getValue(resource.getOrderStatus()));
            target.setStatusStr(OrderLineStatusEnum.getValue(resource.getStatus()));
            target.setSyncStatus(ErpOrderStatus.getDescribe(resource.getSyncStatus()));
            String pricingTypeStr = resource.getPricingType();
            if (NumberUtil.isNumber(pricingTypeStr)) {
                target.setPricingType(PricingTypeEnum.getValue(Integer.valueOf(pricingTypeStr)));
            }
            target.setMergeRows(Boolean.TRUE.equals(resource.getMergeRows()) ? "是" : "否");
            BigDecimal orderNum = Optional.ofNullable(resource.getOrderNum()).orElse(BigDecimal.ZERO);
            BigDecimal cancelNum = Optional.ofNullable(resource.getCancelNum()).orElse(BigDecimal.ZERO);
            // 实际下单量= orderNum - cancelNum
            target.setActualOrderNum(orderNum.subtract(cancelNum));
            return target;

        });
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseOrderListWbsExcelVO.class, "采购订单查询(WBS)_" + DateUtils.format(new Date(),
                "yyyyMMddHHmmss") + ".xls", response);
    }

    @ApiOperation(value = "有采购订单行无采购订单头的异常数据", response = PurchaseOrderDetailDto.class)
    @GetMapping({"selectErrorPurchaseOrderDetailPage"})
    public Response selectErrorPurchaseOrderDetailPage(
            @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize
    ) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/purchaseOrder/selectErrorPurchaseOrderDetailPage", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<PurchaseOrderDetailDto>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<PageInfo<PurchaseOrderDetailDto>>>() {
                });
        return response;
    }

    @ApiOperation(value = "我的采购订单查询(WBS)", response = PurchaseOrderDto.class)
    @PostMapping("selectMyRecordWbsPage")
    public Response selectMyRecordWbsPage(@RequestBody PurchaserOrderWbsRequest wbsPageRequest) {
        String url = String.format("%sstatistics/purchaseOrder/selectMyRecordWbsPage", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, wbsPageRequest, String.class);
        DataResponse<PageInfo<PurchaseOrderDetailDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<PurchaseOrderDetailDto>>>() {
                });
        //脱敏
        //我的采购订单查询不做脱敏处理
        //desensitizeWbs(response.getData().getList());
        notDesensitizeWbs(response.getData().getList());
        return response;
    }

    @ApiOperation(value = "我的采购订单查询(WBS)导出", response = PurchaseOrderDto.class)
    @GetMapping("exportMyOrderRecordWbs")
    public Response exportMyOrderRecordWbs(HttpServletResponse response, PurchaserOrderWbsRequest wbsPageRequest) {
        // 查询申请日期超过一年报错提示
        if (DateUtils.isOverYear(DateUtil.parse(wbsPageRequest.getOrderCreateAtBegin()),
                DateUtil.parse(wbsPageRequest.getOrderCreateAtEnd()))) {
            throw new MipException("导出数据量过大，请调整创建日期的跨期不大于一年。");
        }
        String url = String.format("%sstatistics/purchaseOrder/exportMyOrderRecordWbs", ModelsEnum.STATISTICS.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, wbsPageRequest, String.class);
        DataResponse<List<PurchaseOrderDetailDto>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<PurchaseOrderDetailDto>>>() {
                });
        List<PurchaseOrderDetailDto> dataList = dataResponse.getData();
        //脱敏
        //我的采购订单查询不做脱敏处理
        //desensitizeWbs(dataList);
        notDesensitizeWbs(dataList);

        List<PurchaseOrderListWbsExcelVO> excelVos = ListUtil.map(dataList, resource -> {
            PurchaseOrderListWbsExcelVO target = new PurchaseOrderListWbsExcelVO();
            BeanUtils.copyProperties(resource, target);
            target.setOrderStatus(OrderStatusEnum.getValue(resource.getOrderStatus()));
            target.setStatusStr(OrderLineStatusEnum.getValue(resource.getStatus()));
            target.setSyncStatus(ErpOrderStatus.getDescribe(resource.getSyncStatus()));
            String pricingTypeStr = resource.getPricingType();
            if (NumberUtil.isNumber(pricingTypeStr)) {
                target.setPricingType(PricingTypeEnum.getValue(Integer.valueOf(pricingTypeStr)));
            }
            target.setMergeRows(BooleanUtil.isTrue(resource.getMergeRows()) ? "是" : "否");
            BigDecimal orderNum = Optional.ofNullable(resource.getOrderNum()).orElse(BigDecimal.ZERO);
            BigDecimal cancelNum = Optional.ofNullable(resource.getCancelNum()).orElse(BigDecimal.ZERO);
            // 实际下单量= orderNum - cancelNum
            target.setActualOrderNum(orderNum.subtract(cancelNum));
            return target;

        });
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseOrderListWbsExcelVO.class, "采购订单查询(WBS)_" + DateUtils.format(new Date(),
                "yyyyMMddHHmmss") + ".xls", response);
        return dataResponse;
    }

    @ApiOperation(value = "我的采购订单查询", response = PurchaseOrderDto.class)
    @PostMapping("selectMyRecordPage")
    public Response selectMyRecordPage(@RequestBody PurchaserOrderRequest request) {
        String url = String.format("%sstatistics/purchaseOrder/selectMyRecordPage", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);
        DataResponse<PageInfo<PurchaseOrderDetailDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<PurchaseOrderDetailDto>>>() {
                });
        //脱敏
        //我的采购订单查询不做脱敏处理
        //desensitize(response.getData().getList());
        notDesensitize(response.getData().getList());
        return response;
    }

    @ApiOperation(value = "我的采购订单查询导出", response = PurchaseOrderDto.class)
    @GetMapping("exportMyOrderRecord")
    public void exportMyOrderRecord(HttpServletResponse response, PurchaserOrderRequest request) {
        String url = String.format("%sstatistics/purchaseOrder/exportMyOrderRecord", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);
        DataResponse<List<PurchaseOrderDetailDto>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<PurchaseOrderDetailDto>>>() {
                });
        List<PurchaseOrderDetailDto> dataList = dataResponse.getData();
        //脱敏
        //我的采购订单查询不做脱敏处理
        //desensitize(dataList);
        notDesensitize(dataList);
        List<PurchaseOrderListExcelVO> excelVos = ListUtil.map(dataList, item -> {
            PurchaseOrderListExcelVO wifi = new PurchaseOrderListExcelVO();
            BeanUtils.copyProperties(item, wifi);
            return wifi;
        });
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseOrderListExcelVO.class, "采购订单查询_" + DateUtils.format(new Date(),
                "yyyyMMddHHmmss") + ".xls", response);
    }

    @ApiOperation(value = "导出订单明细")
    @PostMapping("exportDetail")
    public Response exportDetail(@RequestBody List<PurchaseOrderDetailRequest> orderDetailRequestList, HttpServletResponse response) throws IllegalAccessException, NoSuchFieldException {
        if (CollectionUtils.isEmpty(orderDetailRequestList)) {
            return Response.err("订单明细数据为空!");
        }
        AtomicInteger index = new AtomicInteger(1);
        List<PurchaseOrderExportDetailVO> excelVos = ListUtil.map(orderDetailRequestList, item -> {
            PurchaseOrderExportDetailVO source = new PurchaseOrderExportDetailVO();
            BeanUtils.copyProperties(item, source);
            source.setNumber(index.getAndIncrement());
            //暂存前后,会修改ID的取值,会导致如果保存前导出,保存后再导入,报错:无法匹配采购订单行ID，请重新导出并修改后导入
            //因而需要改变取值,统一处理成取requirementId
            source.setId(item.getRequirementId());
            //处理日期
            if (StringUtils.isNotEmpty(item.getDeliveryTime())){
                source.setDeliveryTime(DateUtils.getShortDate(item.getDeliveryTime()));
            }
            if (StringUtils.isNotEmpty(item.getContractAppointDate())){
                source.setContractAppointDate(DateUtils.getShortDate(item.getContractAppointDate()));
            }
            if (StringUtils.isNotEmpty(item.getTrackDate())){
                source.setTrackDate(DateUtils.getShortDate(item.getTrackDate()));
            }

            return source;
        });

        HSSFWorkbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet("采购订单明细");

        String str = "导入说明：\n" +
                "1、灰色列名的数据不允许编辑；\n" +
                "2、黄色列名的数据允许编辑；\n" +
                "3、每次导入将全量覆盖黄色列名的数据；\n" +
                "4、单价（不含税）填写数字，最多只能输入6位小数；\n" +
                "5、折扣（%）填写数字，最多只能输入2位小数；\n" +
                "6、需求日期、供方承诺交期、跟踪日期必须为时间格式,格式：2022/09/29";

        // 隐藏列
        sheet.setColumnHidden(0, true);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 1, 21));
        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(CellStyle.ALIGN_LEFT);
        cellStyle.setWrapText(true);
        Row row0 = sheet.createRow(0);
        row0.setHeight((short) (1800));
        generateCell(row0, cellStyle, 1, str);

        // 标题行
        Row row1 = sheet.createRow(1);
        Field[] fields = PurchaseOrderExportDetailVO.class.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            Excel excelField = field.getAnnotation(Excel.class);
            if (excelField == null) {
                continue;
            }
            HSSFCellStyle style = workbook.createCellStyle();
            String name = excelField.name();
            generateTitle(row1, name, style, sheet, i);
        }

        // 单元格内容
        for (int j = 0; j < excelVos.size(); j++) {
            PurchaseOrderExportDetailVO detailVO = excelVos.get(j);
            Row row = sheet.createRow(j + 2);
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                Excel excelField = field.getAnnotation(Excel.class);
                if (excelField == null) {
                    continue;
                }
                String fieldName = field.getName();
                Field declaredField = detailVO.getClass().getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                Object obj = declaredField.get(detailVO);
                String objStr = null == obj ? "" : obj.toString();
                if (obj instanceof Date) {
                    Date date = (Date) obj;
                    objStr = DateUtil.format(date, "yyyy/MM/dd");
                }
                generateCell(row, cellStyle, i, objStr);
            }
        }

        String fileName = "采购订单明细_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls";
        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName, response, workbook);
        return Response.response();
    }

    @ApiOperation(value = "导入采购订单明细")
    @PostMapping("importDetail")
    public Response importDetail(@RequestParam(value = "file") MultipartFile file,
                                 @RequestParam(value = "idStr") String idStr) {
        try {
            if (StringUtils.isBlank(idStr)) {
                return Response.err("采购订单id为空!");
            }
            String[] split = idStr.split(",");
            List<String> ids = Arrays.asList(split);
            List<PurchaseOrderImportDetailVO> detailVOS = FileUtil.importExcel(file, PurchaseOrderImportDetailVO.class, 1, 0);
            // 过滤id/编号/型号都是空值的数据
            detailVOS = detailVOS.stream().filter(detailVO ->
                    !(StringUtils.isEmpty(detailVO.getId())
                            && StringUtils.isEmpty(detailVO.getErpCode())
                            && StringUtils.isEmpty(detailVO.getModel()))).collect(Collectors.toList());
            Guard.notNullOrEmpty(detailVOS, "导入采购订单明细模板数据为空");

            DataResponse<List<PurchaseOrderImportDetailVO>> response = Response.dataResponse(detailVOS);
            boolean isValid = validation(detailVOS, ids);
            if (!isValid) {
                response.setMsg("FAIL");
            }
            return response;
        } catch (Exception e) {
            logger.error("导入的采购订单明细数据异常:", e);
            return Response.err("导入的采购订单明细数据异常,请参考规范填写!");
        }
    }

    @ApiOperation(value = "导出订单明细校验结果")
    @PostMapping("exportDetailValidResult")
    public Response exportDetailValidResult(@RequestBody List<PurchaseOrderDetailRequest> requestList, HttpServletResponse response) {
        try {
            if (CollectionUtils.isEmpty(requestList)) {
                return Response.err("校验结果数据为空");
            }
            exportDetail(requestList, response);
            return Response.dataResponse();
        } catch (Exception e) {
            return Response.err("导出校验结果异常");
        }
    }

    private boolean validation(List<PurchaseOrderImportDetailVO> detailVOS, List<String> ids) {
        List<String> idList = Lists.newArrayList();
        AtomicBoolean result = new AtomicBoolean(true);
        StringBuilder sb;
        for (int i = 0; i < detailVOS.size(); i++) {
            PurchaseOrderImportDetailVO detailVO = detailVOS.get(i);
            sb = new StringBuilder();
            if (StringUtils.isEmpty(detailVO.getId())) {
                sb.append("采购订单行不存在，不允许导入;");
            }
            if (StringUtils.isNotEmpty(detailVO.getId())
                    && !ids.contains(detailVO.getId())) {
                sb.append("无法匹配采购订单行ID，请重新导出并修改后导入;");
            }
            if (idList.contains(detailVO.getId())) {
                sb.append("采购订单行ID，不可重复;");
            }
            BigDecimal unitPrice = detailVO.getUnitPrice();
            if (Objects.isNull(unitPrice)) {
                sb.append("单价(不含税)不允许为空;");
            }
            if (Objects.nonNull(unitPrice) && unitPrice.scale() > 6) {
                sb.append("单价（不含税）填写数字，最多只能输入6位小数;");
            }
            BigDecimal discount = detailVO.getDiscount();
            if (Objects.isNull(discount)) {
                sb.append("折扣(%)不允许为空;");
            }
            if (Objects.nonNull(discount) && discount.scale() > 2) {
                sb.append("折扣(%)填写数字，最多只能输入2位小数;");
            }
            Date deliveryTime = detailVO.getDeliveryTime();
            if (Objects.isNull(deliveryTime)) {
                sb.append("请输入有效的需求日期;");
            }
            Date contractAppointDate = detailVO.getContractAppointDate();
            if (Objects.isNull(contractAppointDate)) {
                sb.append("请输入有效的供方承诺日期;");
            }

            boolean valid = Objects.nonNull(contractAppointDate)
                    && DateUtils.isBefore(contractAppointDate, new Date());
            if (valid) {
                sb.append("供方承诺日期必须大于或等于当天日期;");
            }
            if (sb.length() > 0) {
                result.set(false);
            }
            detailVO.setValidResult(sb.toString());
            idList.add(detailVO.getId());
        }
        return result.get();
    }

    private void generateTitle(Row row, String value, HSSFCellStyle cellStyle, Sheet sheet, int index) {
        Cell cell = row.createCell(index);
        if (index < 6) {
            cellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        } else {
            cellStyle.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        }
        sheet.setColumnWidth(index + 1, 12 * 256);
        cellStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cellStyle.setBorderBottom(CellStyle.BORDER_THIN);
        cellStyle.setBorderLeft(CellStyle.BORDER_THIN);
        cellStyle.setBorderTop(CellStyle.BORDER_THIN);
        cellStyle.setBorderRight(CellStyle.BORDER_THIN);
        cell.setCellStyle(cellStyle);
        cell.setCellType(Cell.CELL_TYPE_STRING);
        cell.setCellValue(value);

    }


    /**
     * 在row的index处生成一个单元格,设置样式和值
     *
     * @param row       : 行
     * @param cellStyle : 单元格样式
     * @param index     : 列索引位
     * @param value     : 值
     */
    private void generateCell(Row row, HSSFCellStyle cellStyle, int index, String value) {
        Cell cell = row.createCell(index);
        cell.setCellStyle(cellStyle);
        cell.setCellType(Cell.CELL_TYPE_STRING);
        cell.setCellValue(value);
    }

    /**
     * 根据路由：采购订单查询查看单价权限 脱敏
     *
     * @param list
     */
    public void desensitize(List<PurchaseOrderDetailDto> list) {
        //查询当前用户的菜单权限
        String url1 = String.format("%smenuManage", ModelsEnum.BASEDATA.getBaseUrl());
        String res2 = restTemplate.getForEntity(url1, String.class).getBody();
        List<Menu> menuList = JSON.parseObject(res2, new TypeReference<DataResponse<List<Menu>>>() {
        }).getData();
        boolean ifPermissible = menuList.stream().anyMatch(s -> "采购订单查询查看单价权限".equals(s.getName()));
        for (PurchaseOrderDetailDto dto : list) {
            if (!ifPermissible) {
                dto.setCost(null);
                dto.setCostStr("***");
            } else {
                dto.setCostStr(Optional.ofNullable(dto.getCost()).map(String::valueOf).orElse(null));
            }
        }
    }

    /**
     * 根据路由：采购订单查询查看单价权限 脱敏
     *
     * @param list
     */
    public void desensitizeWbs(List<PurchaseOrderDetailDto> list) {
        //查询当前用户的菜单权限
        String url1 = String.format("%smenuManage", ModelsEnum.BASEDATA.getBaseUrl());
        String res2 = restTemplate.getForEntity(url1, String.class).getBody();
        List<Menu> menuList = JSON.parseObject(res2, new TypeReference<DataResponse<List<Menu>>>() {
        }).getData();
        boolean ifPermissible = menuList.stream().anyMatch(s -> "采购订单查询查看单价权限".equals(s.getName()));
        for (PurchaseOrderDetailDto dto : list) {
            if (!ifPermissible) {
                dto.setDiscount(null);
                dto.setDiscountPrice(null);
                dto.setDiscountMoney(null);
                dto.setUnitPrice(null);
                dto.setDiscountStr("***");
                dto.setDiscountPriceStr("***");
                dto.setDiscountMoneyStr("***");
                dto.setUnitPriceStr("***");
            } else {
                dto.setDiscountStr(Optional.ofNullable(dto.getDiscount()).map(String::valueOf).orElse(null));
                dto.setDiscountPriceStr(Optional.ofNullable(dto.getDiscountPrice()).map(String::valueOf).orElse(null));
                dto.setDiscountMoneyStr(Optional.ofNullable(dto.getDiscountMoney()).map(String::valueOf).orElse(null));
                dto.setUnitPriceStr(Optional.ofNullable(dto.getUnitPrice()).map(String::valueOf).orElse(null));
            }
        }
    }

    /**
     * 查询我的订单信息不做脱敏
     *
     * @param list
     */
    public void notDesensitizeWbs(List<PurchaseOrderDetailDto> list) {
        for (PurchaseOrderDetailDto dto : list) {
            dto.setDiscountStr(Optional.ofNullable(dto.getDiscount()).map(String::valueOf).orElse(null));
            dto.setDiscountPriceStr(Optional.ofNullable(dto.getDiscountPrice()).map(String::valueOf).orElse(null));
            dto.setDiscountMoneyStr(Optional.ofNullable(dto.getDiscountMoney()).map(String::valueOf).orElse(null));
            dto.setUnitPriceStr(Optional.ofNullable(dto.getUnitPrice()).map(String::valueOf).orElse(null));
        }

    }

    public void notDesensitize(List<PurchaseOrderDetailDto> list) {
        for (PurchaseOrderDetailDto dto : list) {
            dto.setCostStr(Optional.ofNullable(dto.getCost()).map(String::valueOf).orElse(null));
        }
    }
}

