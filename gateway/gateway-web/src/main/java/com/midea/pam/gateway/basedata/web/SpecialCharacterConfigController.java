package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.common.basedata.dto.SpecialCharacterConfigDTO;
import com.midea.pam.common.basedata.entity.SpecialCharacterConfig;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 特殊字符配置网关控制器
 * 路由调用basedata模块的特殊字符配置接口
 * 
 * <AUTHOR> Team
 */
@Api(tags = "特殊字符配置管理")
@RestController
@RequestMapping("specialCharacterConfig")
public class SpecialCharacterConfigController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(SpecialCharacterConfigController.class);

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 根据ID查询特殊字符配置
     */
    @ApiOperation(value = "根据ID查询配置")
    @GetMapping("/{id}")
    public Response findById(@ApiParam(value = "配置ID", required = true) @PathVariable Long id) {
        logger.info("[Gateway-特殊字符配置] 查询配置请求，ID: {}", id);
        
        final String url = String.format("%sspecialCharacterConfig/%d", ModelsEnum.BASEDATA.getBaseUrl(), id);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<SpecialCharacterConfig>>(){});
    }

    /**
     * 新增特殊字符配置
     */
    @ApiOperation(value = "新增配置")
    @PostMapping
    public Response save(@ApiParam(value = "配置信息", required = true) @RequestBody @Valid SpecialCharacterConfig config) {
        logger.info("[Gateway-特殊字符配置] 新增配置请求，配置名称: {}, 数据库: {}, 表: {}, 字段: {}", 
                   config.getConfigName(), config.getDatabaseName(), 
                   config.getTableName(), config.getColumnName());
        
        final String url = String.format("%sspecialCharacterConfig", ModelsEnum.BASEDATA.getBaseUrl());
        return restTemplate.postForEntity(url, config, DataResponse.class).getBody();
    }

    /**
     * 更新特殊字符配置
     */
    @ApiOperation(value = "更新配置")
    @PutMapping
    public Response update(@ApiParam(value = "配置信息", required = true) @RequestBody @Valid SpecialCharacterConfig config) {
        logger.info("[Gateway-特殊字符配置] 更新配置请求，ID: {}, 配置名称: {}", 
                   config.getId(), config.getConfigName());
        
        final String url = String.format("%sspecialCharacterConfig", ModelsEnum.BASEDATA.getBaseUrl());
        restTemplate.put(url, config);
        return Response.dataResponse();
    }

    /**
     * 根据ID删除特殊字符配置
     */
    @ApiOperation(value = "删除配置")
    @DeleteMapping("/{id}")
    public Response delete(@ApiParam(value = "配置ID", required = true) @PathVariable Long id) {
        logger.info("[Gateway-特殊字符配置] 删除配置请求，ID: {}", id);
        
        final String url = String.format("%sspecialCharacterConfig/%d", ModelsEnum.BASEDATA.getBaseUrl(), id);
        restTemplate.delete(url);
        return Response.dataResponse();
    }

    /**
     * 分页查询特殊字符配置
     */
    @ApiOperation(value = "分页查询配置")
    @GetMapping("/page")
    public Response page(@ApiParam(value = "查询条件") SpecialCharacterConfigDTO query) {
        logger.info("[Gateway-特殊字符配置] 分页查询请求，页码: {}, 每页大小: {}", 
                   query.getPageNum(), query.getPageSize());
        
        final Map<String, Object> param = new HashMap<>();
        if (query.getPageNum() != null) {
            param.put("pageNum", query.getPageNum());
        }
        if (query.getPageSize() != null) {
            param.put("pageSize", query.getPageSize());
        }
        if (query.getConfigNameLike() != null) {
            param.put("configNameLike", query.getConfigNameLike());
        }
        if (query.getDatabaseNameQuery() != null) {
            param.put("databaseNameQuery", query.getDatabaseNameQuery());
        }
        if (query.getTableNameLike() != null) {
            param.put("tableNameLike", query.getTableNameLike());
        }
        if (query.getColumnNameLike() != null) {
            param.put("columnNameLike", query.getColumnNameLike());
        }
        if (query.getEnabledQuery() != null) {
            param.put("enabledQuery", query.getEnabledQuery());
        }
        if (query.getProcessingRuleQuery() != null) {
            param.put("processingRuleQuery", query.getProcessingRuleQuery());
        }
        
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "specialCharacterConfig/page", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<SpecialCharacterConfigDTO>>>(){});
    }

    /**
     * 根据条件查询特殊字符配置列表
     */
    @ApiOperation(value = "条件查询配置列表")
    @PostMapping("/list")
    public Response list(@ApiParam(value = "查询条件") @RequestBody SpecialCharacterConfigDTO query) {
        logger.info("[Gateway-特殊字符配置] 条件查询请求");
        
        final String url = String.format("%sspecialCharacterConfig/list", ModelsEnum.BASEDATA.getBaseUrl());
        return restTemplate.postForEntity(url, query, DataResponse.class).getBody();
    }

    /**
     * 根据数据库名称查询启用的配置
     */
    @ApiOperation(value = "查询数据库启用配置")
    @GetMapping("/enabled/database/{databaseName}")
    public Response findEnabledByDatabase(@ApiParam(value = "数据库名称", required = true) @PathVariable String databaseName) {
        logger.info("[Gateway-特殊字符配置] 查询数据库启用配置请求，数据库: {}", databaseName);
        
        final String url = String.format("%sspecialCharacterConfig/enabled/database/%s", 
                                        ModelsEnum.BASEDATA.getBaseUrl(), databaseName);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<SpecialCharacterConfig>>>(){});
    }

    /**
     * 根据数据库名称和表名称查询启用的配置
     */
    @ApiOperation(value = "查询表启用配置")
    @GetMapping("/enabled/database/{databaseName}/table/{tableName}")
    public Response findEnabledByDatabaseAndTable(
            @ApiParam(value = "数据库名称", required = true) @PathVariable String databaseName,
            @ApiParam(value = "表名称", required = true) @PathVariable String tableName) {
        logger.info("[Gateway-特殊字符配置] 查询表启用配置请求，数据库: {}, 表: {}", databaseName, tableName);
        
        final String url = String.format("%sspecialCharacterConfig/enabled/database/%s/table/%s", 
                                        ModelsEnum.BASEDATA.getBaseUrl(), databaseName, tableName);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<SpecialCharacterConfig>>>(){});
    }

    /**
     * 批量启用/禁用配置
     */
    @ApiOperation(value = "批量更新启用状态")
    @PutMapping("/batch/enabled")
    public Response batchUpdateEnabled(
            @ApiParam(value = "配置ID列表", required = true) @RequestParam List<Long> ids,
            @ApiParam(value = "启用状态", required = true) @RequestParam Boolean enabled) {
        logger.info("[Gateway-特殊字符配置] 批量更新启用状态请求，ID数量: {}, 启用状态: {}", 
                   ids.size(), enabled);
        
        final Map<String, Object> param = new HashMap<>();
        param.put("ids", ids);
        param.put("enabled", enabled);
        
        final String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "specialCharacterConfig/batch/enabled", param);
        restTemplate.put(url, null);
        return Response.dataResponse();
    }

    /**
     * 验证配置的唯一性
     */
    @ApiOperation(value = "验证配置唯一性")
    @PostMapping("/validate/uniqueness")
    public Response validateUniqueness(@ApiParam(value = "配置信息", required = true) @RequestBody SpecialCharacterConfig config) {
        logger.info("[Gateway-特殊字符配置] 验证唯一性请求，数据库: {}, 表: {}, 字段: {}", 
                   config.getDatabaseName(), config.getTableName(), config.getColumnName());
        
        final String url = String.format("%sspecialCharacterConfig/validate/uniqueness", ModelsEnum.BASEDATA.getBaseUrl());
        return restTemplate.postForEntity(url, config, DataResponse.class).getBody();
    }
}
