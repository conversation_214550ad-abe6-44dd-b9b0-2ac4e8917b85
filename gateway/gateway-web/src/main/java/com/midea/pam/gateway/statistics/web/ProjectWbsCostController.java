package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.dto.ProjectHroWbsSummaryDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostGroupDto;
import com.midea.pam.common.ctc.dto.ProjectWbsSummaryDto;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.entity.HroRequirementContractDetail;
import com.midea.pam.common.statistics.entity.RequirementPoDetail;
import com.midea.pam.common.statistics.entity.RequirementPurchaseContractDetail;
import com.midea.pam.common.statistics.excelVo.HroPurchaseContractCompleteExcelVO;
import com.midea.pam.common.statistics.excelVo.HroPurchaseContractOnTheWayExcelVO;
import com.midea.pam.common.statistics.excelVo.HroRequirementBudgetApprovalExcelVO;
import com.midea.pam.common.statistics.excelVo.HroRequirementBudgetExcelVO;
import com.midea.pam.common.statistics.excelVo.IncurredCostMergeExcelVO;
import com.midea.pam.common.statistics.excelVo.InvoiceReceiptInfoExcelVO;
import com.midea.pam.common.statistics.excelVo.MilepostInfoExcelVO;
import com.midea.pam.common.statistics.excelVo.OnTheWayCostMergeExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectEaDetailExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectEcDetailCompleteVO;
import com.midea.pam.common.statistics.excelVo.ProjectEcDetailExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectMaterialDetailExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectPurchaseContractCompleteExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectPurchaseContractDetailExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectWbsCostExcelVo;
import com.midea.pam.common.statistics.excelVo.ProjectWorkingHourCompleteExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectWorkingHourDetailExcelVO;
import com.midea.pam.common.statistics.excelVo.RequirementBudgetApprovalExcelVO;
import com.midea.pam.common.statistics.excelVo.RequirementBudgetMergeExcelVO;
import com.midea.pam.common.statistics.excelVo.RequirementBudgetOutsourceExcelVO;
import com.midea.pam.common.statistics.excelVo.RequirementBudgetPurchaseExcelVO;
import com.midea.pam.common.statistics.excelVo.RequirementPoDetailExcelVO;
import com.midea.pam.common.statistics.vo.ProjectWbsCostByReceiptsVO;
import com.midea.pam.common.statistics.vo.ProjectWbsCostCompleteVO;
import com.midea.pam.common.statistics.vo.ProjectWbsCostOnTheWayVO;
import com.midea.pam.common.statistics.vo.ProjectWbsCostSystemVO;
import com.midea.pam.common.util.BigDecimalUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExcelUtil;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.support.utils.DateUtil;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/06/21
 * @description 项目Wbs成本
 */
@Api("项目Wbs成本")
@RestController
@RequestMapping(value = {"/statistics/project/wbsCost", "/mobile/app/statistics/project/wbsCost"})
public class ProjectWbsCostController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "System需求预算")
    @PostMapping("/system")
    public Response system(@RequestBody ProjectWbsSummaryDto projectWbsSummaryDto) {
        String url = String.format("%sstatistics/project/wbsCost/system", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectWbsSummaryDto, String.class);
        String res = responseEntity.getBody();
        DataResponse<ProjectWbsCostSystemVO> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsCostSystemVO>>() {
        });
        return response;
    }

    @ApiOperation(value = "需求单据中的物料已下po金额")
    @PostMapping("/amountByPurchaseOrder")
    public Response amountByPurchaseOrder(@RequestBody ProjectWbsSummaryDto projectWbsSummaryDto) {
        projectWbsSummaryDto.setUnitId(SystemContext.getUnitId());
        String url = String.format("%sstatistics/project/wbsCost/amountByPurchaseOrder", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectWbsSummaryDto, String.class);
        String res = responseEntity.getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<RequirementPoDetail>>>() {
        });
    }

    @ApiOperation(value = "人力点工-累计采购合同占用金额")
    @PostMapping("/hroRequirementContractDetail")
    public Response hroRequirementContractDetail(@RequestBody ProjectHroWbsSummaryDto projectWbsSummaryDto) {
        projectWbsSummaryDto.setUnitId(SystemContext.getUnitId());
        String url = String.format("%sstatistics/project/wbsCost/hroRequirementContractDetail", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectWbsSummaryDto, String.class);
        String res = responseEntity.getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<HroRequirementContractDetail>>>() {
        });
    }

    @ApiOperation(value = "需求单据中的物料已下采购合同")
    @PostMapping("/purchaseContract")
    public Response purchaseContract(@RequestBody ProjectWbsSummaryDto projectWbsSummaryDto) {
        projectWbsSummaryDto.setUnitId(SystemContext.getUnitId());
        String url = String.format("%sstatistics/project/wbsCost/purchaseContract", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectWbsSummaryDto, String.class);
        String res = responseEntity.getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<RequirementPurchaseContractDetail>>>() {
        });
    }

    @ApiOperation(value = "非System需求预算")
    @PostMapping("/emsBudgetOccupyDetail")
    public Response emsBudgetOccupyDetail(@RequestBody ProjectWbsSummaryDto projectWbsSummaryDto) {
        projectWbsSummaryDto.setUnitId(SystemContext.getUnitId());
        String url = String.format("%sstatistics/project/wbsCost/emsBudgetOccupyDetail", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectWbsSummaryDto, String.class);
        String res = responseEntity.getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsCostSystemVO>>() {
        });
    }

    /**************************************************************在途成本明细*****************************************************/

    @ApiOperation(value = "在途成本")
    @PostMapping("/onTheWay")
    public Response onTheWay(@RequestBody ProjectWbsSummaryDto projectWbsSummaryDto) {
        projectWbsSummaryDto.setUnitId(SystemContext.getUnitId());
        String url = String.format("%sstatistics/project/wbsCost/onTheWay", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectWbsSummaryDto, String.class);
        String res = responseEntity.getBody();
        DataResponse<ProjectWbsCostOnTheWayVO> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsCostOnTheWayVO>>() {
        });
        return response;
    }

    /**************************************************************已发生成本明细*****************************************************/
    @ApiOperation(value = "已发生")
    @PostMapping("/complete")
    public Response complete(@RequestBody ProjectWbsSummaryDto projectWbsSummaryDto) {
        projectWbsSummaryDto.setUnitId(SystemContext.getUnitId());
        String url = String.format("%sstatistics/project/wbsCost/complete", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectWbsSummaryDto, String.class);
        String res = responseEntity.getBody();
        DataResponse<ProjectWbsCostCompleteVO> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsCostCompleteVO>>() {
        });
        return response;
    }

    /*****************************************************需求发布单据已发生的WBS预算占用**********************************************/
    @ApiOperation(value = "需求发布单据已发生的WBS预算占用")
    @PostMapping("/requirementCodeBudget")
    public Response requirementCodeBudget(@RequestBody ProjectWbsSummaryDto projectWbsSummaryDto) {
        String url = String.format("%sstatistics/project/wbsCost/requirementCodeBudget", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, projectWbsSummaryDto, String.class);
        String res = responseEntity.getBody();
        DataResponse<List<ProjectWbsCostByReceiptsVO>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<ProjectWbsCostByReceiptsVO>>>() {
        });
        return response;
    }

    /**************************************************************项目成本汇总*****************************************************/
    @ApiOperation(value = "项目成本")
    @PostMapping("/summary")
    public Response summary(@RequestBody Map<String, Object> param) {
        String url = String.format("%sstatistics/project/wbsCost/summary", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        String res = responseEntity.getBody();
        DataResponse<ProjectWbsCostExcelVo> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsCostExcelVo>>() {
        });
        return response;
    }

    @ApiOperation(value = "主动调用项目成本定时")
    @GetMapping("/active/{id}")
    public Response active(@PathVariable Long id) {
        final String url = String.format("%sstatistics/project/wbsCost/active/" + id, ModelsEnum.STATISTICS.getBaseUrl());
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "定时测试")
    @GetMapping("/testJob")
    public Response testJob() {
        final Map<String, Object> params = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/project/wbsCost/testJob", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "定时测试-删除")
    @GetMapping("/testDelJob")
    public Response testDelJob() {
        final Map<String, Object> params = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/project/wbsCost/testDelJob", params);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "导出")
    @PostMapping("/excel/export")
    public void excelExport(@RequestBody Map<String, Object> param, HttpServletResponse response) {
        String url = String.format("%sstatistics/project/wbsCost/exportList", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        String res = responseEntity.getBody();
        DataResponse<ProjectWbsCostExcelVo> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsCostExcelVo>>() {
        });
        ProjectWbsCostExcelVo excelVo = dataResponse.getData();

        /**********************************汇总********************************/
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);
        cellLeftStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);

        // 设置背景色
        HSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        titleStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        titleStyle.setFont(font);
        titleStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        titleStyle.setFillForegroundColor(HSSFColor.PALE_BLUE.index);//背景白色
        titleStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);

        HSSFSheet sheet = workbook.createSheet("项目成本汇总");
        this.createSummarySheet(sheet, excelVo, cellLeftStyle, titleStyle);

        List<RequirementBudgetPurchaseExcelVO> purchaseList = BeanConverter.copy(excelVo.getPurchaseList(), RequirementBudgetPurchaseExcelVO.class);
        List<RequirementBudgetOutsourceExcelVO> outsourceList = BeanConverter.copy(excelVo.getOutsourceList(), RequirementBudgetOutsourceExcelVO.class);
        List<RequirementBudgetApprovalExcelVO> approvalList = BeanConverter.copy(excelVo.getApprovalList(), RequirementBudgetApprovalExcelVO.class);
        List<ProjectEaDetailExcelVO> ea = BeanConverter.copy(excelVo.getEa(), ProjectEaDetailExcelVO.class);
        List<HroRequirementBudgetExcelVO> hroRequirementBudgetList = BeanConverter.copy(excelVo.getHroRequirementBudgets(), HroRequirementBudgetExcelVO.class);
        List<HroRequirementBudgetApprovalExcelVO> hroRequirementBudgetApprovalList = BeanConverter.copy(excelVo.getHroRequirementBudgetApprovals(), HroRequirementBudgetApprovalExcelVO.class);
        Integer[] arrA = {1};
        Integer[] arrB = {1};
        Integer[] arrC = {1};
        Integer[] arrD = {1};
        Integer[] arrHro = {1};
        Integer[] arrHroApproval = {1};
        List<RequirementBudgetPurchaseExcelVO> requirementBudgetPurchaseExcelVOS = purchaseList.stream().peek(e -> e.setNumber(arrA[0]++)).collect(Collectors.toList());
        List<RequirementBudgetOutsourceExcelVO> requirementBudgetOutsourceExcelVOS = outsourceList.stream().peek(e -> e.setNumber(arrB[0]++)).collect(Collectors.toList());
        List<RequirementBudgetApprovalExcelVO> requirementBudgetApprovalExcelVOS = approvalList.stream().peek(e -> e.setNumber(arrC[0]++)).collect(Collectors.toList());
        List<ProjectEaDetailExcelVO> projectEaDetailExcelVOS = ea.stream().peek(e -> e.setNumber(arrD[0]++)).collect(Collectors.toList());
        List<HroRequirementBudgetExcelVO> hroRequirementBudgetExcelVOS = hroRequirementBudgetList.stream().peek(e -> e.setNumber(arrHro[0]++)).collect(Collectors.toList());
        List<HroRequirementBudgetApprovalExcelVO> hroRequirementBudgetApprovalExcelVOS = hroRequirementBudgetApprovalList.stream().peek(e -> e.setNumber(arrHroApproval[0]++)).collect(Collectors.toList());

        ExportExcelUtil.addSheet(workbook, requirementBudgetPurchaseExcelVOS, RequirementBudgetPurchaseExcelVO.class, null, "需求预算-物料采购需求预算", true);
        ExportExcelUtil.addSheet(workbook, requirementBudgetOutsourceExcelVOS, RequirementBudgetOutsourceExcelVO.class, null, "需求预算-物料外包（整包）需求预算", true);
        ExportExcelUtil.addSheet(workbook, requirementBudgetApprovalExcelVOS, RequirementBudgetApprovalExcelVO.class, null, "需求预算-审批中的需求发布单据", true);
        ExportExcelUtil.addSheet(workbook, projectEaDetailExcelVOS, ProjectEaDetailExcelVO.class, null, "需求预算-费用申请（EA单）未释放、剩余可用的金额", true);
        ExportExcelUtil.addSheet(workbook, hroRequirementBudgetExcelVOS, HroRequirementBudgetExcelVO.class, null, "需求预算-人力点工需求预算", true);
        ExportExcelUtil.addSheet(workbook, hroRequirementBudgetApprovalExcelVOS, HroRequirementBudgetApprovalExcelVO.class, null, "需求预算-审批中的人力点工需求单据", true);

        List<RequirementPoDetailExcelVO> poOnTheWay = BeanConverter.copy(excelVo.getPoOnTheWay(), RequirementPoDetailExcelVO.class);
        List<ProjectPurchaseContractDetailExcelVO> contractOnTheWay = BeanConverter.copy(excelVo.getContractOnTheWay(), ProjectPurchaseContractDetailExcelVO.class);
        List<ProjectWorkingHourDetailExcelVO> wkOnTheWay = BeanConverter.copy(excelVo.getWkOnTheWay(), ProjectWorkingHourDetailExcelVO.class);
        List<ProjectEcDetailExcelVO> ecOnTheWay = BeanConverter.copy(excelVo.getEcOnTheWay(), ProjectEcDetailExcelVO.class);
        List<HroPurchaseContractOnTheWayExcelVO> hroPurchaseContractOnTheWayList = BeanConverter.copy(excelVo.getHroPurchaseContractOnTheWay(), HroPurchaseContractOnTheWayExcelVO.class);
        Integer[] arrE = {1};
        Integer[] arrF = {1};
        Integer[] arrG = {1};
        Integer[] arrH = {1};
        Integer[] arrhroPurchaseContractOnTheWay = {1};
        List<RequirementPoDetailExcelVO> requirementPoDetailExcelVOS = poOnTheWay.stream().peek(e -> e.setNumber(arrE[0]++)).collect(Collectors.toList());
        List<ProjectPurchaseContractDetailExcelVO> projectPurchaseContractDetailExcelVOS = contractOnTheWay.stream().peek(e -> e.setNumber(arrF[0]++)).collect(Collectors.toList());
        List<ProjectWorkingHourDetailExcelVO> projectWorkingHourDetailExcelVOS = wkOnTheWay.stream().peek(e -> e.setNumber(arrG[0]++)).collect(Collectors.toList());
        List<ProjectEcDetailExcelVO> projectEcDetailExcelVOS = ecOnTheWay.stream().peek(e -> e.setNumber(arrH[0]++)).collect(Collectors.toList());
        List<HroPurchaseContractOnTheWayExcelVO> hroPurchaseContractOnTheWayExcelVOS = hroPurchaseContractOnTheWayList.stream().peek(e -> e.setNumber(arrhroPurchaseContractOnTheWay[0]++)).collect(Collectors.toList());

        ExportExcelUtil.addSheet(workbook, requirementPoDetailExcelVOS, RequirementPoDetailExcelVO.class, null, "在途成本-po的未接收金额", true);
        ExportExcelUtil.addSheet(workbook, projectPurchaseContractDetailExcelVOS, ProjectPurchaseContractDetailExcelVO.class, null, "在途成本-采购合同未进度执行金额", true);
        ExportExcelUtil.addSheet(workbook, projectWorkingHourDetailExcelVOS, ProjectWorkingHourDetailExcelVO.class, null, "在途成本-已填报待审批的工时金额", true);
        ExportExcelUtil.addSheet(workbook, projectEcDetailExcelVOS, ProjectEcDetailExcelVO.class, null, "在途成本-费用报销（EC单）已申请未报销", true);
        ExportExcelUtil.addSheet(workbook, hroPurchaseContractOnTheWayExcelVOS, HroPurchaseContractOnTheWayExcelVO.class, null, "在途成本-点工采购合同分配金额-已对账金额", true);

        List<ProjectMaterialDetailExcelVO> getMaterial = BeanConverter.copy(excelVo.getGetMaterial(), ProjectMaterialDetailExcelVO.class);
        List<ProjectPurchaseContractCompleteExcelVO> contractComplete = BeanConverter.copy(excelVo.getContractComplete(), ProjectPurchaseContractCompleteExcelVO.class);
        List<ProjectWorkingHourCompleteExcelVO> wkComplete = BeanConverter.copy(excelVo.getWkComplete(), ProjectWorkingHourCompleteExcelVO.class);
        List<ProjectEcDetailCompleteVO> ecComplete = BeanConverter.copy(excelVo.getEcComplete(), ProjectEcDetailCompleteVO.class);
        List<HroPurchaseContractCompleteExcelVO> hroPurchaseContractCompleteList = BeanConverter.copy(excelVo.getHroPurchaseContractComplete(), HroPurchaseContractCompleteExcelVO.class);
        Integer[] arrI = {1};
        Integer[] arrJ = {1};
        Integer[] arrK = {1};
        Integer[] arrL = {1};
        Integer[] arrhroPurchaseContractComplete = {1};
        List<ProjectMaterialDetailExcelVO> projectMaterialDetailExcelVOS = getMaterial.stream().peek(e -> e.setNumber(arrI[0]++)).collect(Collectors.toList());
        List<ProjectPurchaseContractCompleteExcelVO> projectPurchaseContractCompleteExcelVOS = contractComplete.stream().peek(e -> e.setNumber(arrJ[0]++)).collect(Collectors.toList());
        List<ProjectWorkingHourCompleteExcelVO> projectWorkingHourCompleteExcelVOS = wkComplete.stream().peek(e -> e.setNumber(arrK[0]++)).collect(Collectors.toList());
        List<ProjectEcDetailCompleteVO> projectEcDetailCompleteVOS = ecComplete.stream().peek(e -> e.setNumber(arrL[0]++)).collect(Collectors.toList());
        List<HroPurchaseContractCompleteExcelVO> hroPurchaseContractCompleteExcelVOS = hroPurchaseContractCompleteList.stream().peek(e -> e.setNumber(arrhroPurchaseContractComplete[0]++)).collect(Collectors.toList());

        ExportExcelUtil.addSheet(workbook, projectMaterialDetailExcelVOS, ProjectMaterialDetailExcelVO.class, null, "已发生成本-领退料成本", true);
        ExportExcelUtil.addSheet(workbook, projectPurchaseContractCompleteExcelVOS, ProjectPurchaseContractCompleteExcelVO.class, null, "已发生成本-采购合同进度执行金额", true);
        ExportExcelUtil.addSheet(workbook, projectWorkingHourCompleteExcelVOS, ProjectWorkingHourCompleteExcelVO.class, null, "已发生成本-审批通过的工时金额", true);
        ExportExcelUtil.addSheet(workbook, projectEcDetailCompleteVOS, ProjectEcDetailCompleteVO.class, null, "已发生成本-费用报销（EC单）审核通过并已入账", true);
        ExportExcelUtil.addSheet(workbook, hroPurchaseContractCompleteExcelVOS, HroPurchaseContractCompleteExcelVO.class, null, "已发生成本-点工采购合同已对账金额", true);

        ExportExcelUtil.downLoadExcel(excelVo.getProjectCode() + "_项目成本明细_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    @ApiOperation(value = "导出v2", notes = "优化需求，合并成6个sheet")
    @PostMapping("v2/excel/export")
    public void excelExportNew(@RequestBody Map<String, Object> param, HttpServletResponse response) {
        String url = String.format("%sstatistics/project/wbsCost/exportList", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        String res = responseEntity.getBody();
        DataResponse<ProjectWbsCostExcelVo> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsCostExcelVo>>() {
        });
        ProjectWbsCostExcelVo excelVo = dataResponse.getData();

        //查询里程碑信息
        String milepostUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/projectDeliveries/selectListWithDetail", param);
        String milepostRes = restTemplate.getForEntity(milepostUrl, String.class).getBody();
        DataResponse<List<ProjectMilepostDto>> milepostDataResponse = JSON.parseObject(milepostRes, new TypeReference<DataResponse<List<ProjectMilepostDto>>>() {
        });
        List<ProjectMilepostDto> projectMilepostDtos = milepostDataResponse.getData();

        /**********************************汇总********************************/
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);
        cellLeftStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);

        // 设置背景色
        HSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        titleStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        titleStyle.setFont(font);
        titleStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        titleStyle.setFillForegroundColor(HSSFColor.PALE_BLUE.index);//背景白色
        titleStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);

        HSSFSheet sheet = workbook.createSheet("项目成本汇总");
        this.createSummarySheet(sheet, excelVo, cellLeftStyle, titleStyle);

        List<InvoiceReceiptInfoExcelVO> invoiceReceiptInfoExcelVOS = BeanConverter.copy(excelVo.getInvoiceReceiptInfoList(), InvoiceReceiptInfoExcelVO.class);
        List<MilepostInfoExcelVO> milepostInfoExcelVOS = dealMilepostInfo(projectMilepostDtos);
        List<RequirementBudgetMergeExcelVO> requirementBudgetMergeExcelVOS = mergeRequirementBudget(excelVo);
        List<OnTheWayCostMergeExcelVO> onTheWayCostMergeExcelVOS = mergeOnTheWayCost(excelVo);
        List<IncurredCostMergeExcelVO> incurredCostMergeExcelVOS = mergeIncurredCost(excelVo);
        ExportExcelUtil.addSheet(workbook, invoiceReceiptInfoExcelVOS, InvoiceReceiptInfoExcelVO.class, null, "开票回款信息", true);
        ExportExcelUtil.addSheet(workbook, milepostInfoExcelVOS, MilepostInfoExcelVO.class, null, "里程碑信息", true);
        ExportExcelUtil.addSheet(workbook, requirementBudgetMergeExcelVOS, RequirementBudgetMergeExcelVO.class, null, "需求预算明细", true);
        ExportExcelUtil.addSheet(workbook, onTheWayCostMergeExcelVOS, OnTheWayCostMergeExcelVO.class, null, "在途成本明细", true);
        ExportExcelUtil.addSheet(workbook, incurredCostMergeExcelVOS, IncurredCostMergeExcelVO.class, null, "已发生成本明细", true);

        ExportExcelUtil.downLoadExcel(excelVo.getProjectCode() + "_项目成本明细_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    private List<IncurredCostMergeExcelVO> mergeIncurredCost(ProjectWbsCostExcelVo excelVo) {
        // 已发生成本-领退料成本
        List<IncurredCostMergeExcelVO> getMaterial = BeanConverter.copy(excelVo.getGetMaterial(), IncurredCostMergeExcelVO.class);
        getMaterial.forEach(s -> {
            s.setType("领退料成本");
            s.setIncurredCost(s.getTotalAmount()); //领退料单*单位成本
        });
        // 已发生成本-采购合同进度执行金额
        List<IncurredCostMergeExcelVO> contractComplete = BeanConverter.copy(excelVo.getContractComplete(), IncurredCostMergeExcelVO.class);
        contractComplete.forEach(s -> {
            s.setType("采购合同进度执行金额");
            s.setBillCode(s.getPurchaseContractCode()); //采购合同编号
            s.setIncurredCost(s.getBudgetExecuteAmountTotal()); //累计进度执行金额（不含税）
            s.setBillCreateTime(s.getContractCreateTime()); //合同进度创建时间
        });
        // 已发生成本-审批通过的工时金额
        List<IncurredCostMergeExcelVO> wkComplete = BeanConverter.copy(excelVo.getWkComplete(), IncurredCostMergeExcelVO.class);
        wkComplete.forEach(s -> {
            s.setType("审批通过的工时金额");
            s.setVendorName(s.getUserMip()); //人员
            s.setPamCode(DateUtils.format(s.getApplyDate())); //时间
            s.setMaterielDescr(s.getLevel()); //角色
            s.setActualAmount(s.getWorkingHours()); //审批通过的工时（h）
            s.setActualCost(s.getCostMoney()); //标准费率（d）
            s.setIncurredCost(s.getTotalAmount()); //审批通过的工时*标准费率/8
        });
        // 已发生成本-费用报销（EC单）审核通过并已入账
        List<IncurredCostMergeExcelVO> ecComplete = BeanConverter.copy(excelVo.getEcComplete(), IncurredCostMergeExcelVO.class);
        ecComplete.forEach(s -> {
            s.setType("费用报销（EC单）审核通过并已入账");
            s.setBillCode(s.getFeeApplyCode()); //EC单号
            s.setIncurredCost(s.getAmount()); //审核通过并已入账金额
        });
        // 已发生成本-点工采购合同已对账金额
        List<IncurredCostMergeExcelVO> hroPurchaseContractCompleteList = BeanConverter.copy(excelVo.getHroPurchaseContractComplete(), IncurredCostMergeExcelVO.class);
        hroPurchaseContractCompleteList.forEach(s -> {
            s.setType("人力点工");
            s.setBillCode(s.getPurchaseContractCode()); //采购合同编号
            s.setMaterielDescr(s.getRoleName()); //角色
            s.setActualAmount(s.getBillMhAmount()); //已对账工时金额（不含税）
            s.setActualCost(s.getBillCostAmount()); //已对账费用（含税）
            s.setIncurredCost(s.getSurplusAmount()); //点工采购合同已对账金额
        });

        // 供应商罚扣项目成本汇总
        List<IncurredCostMergeExcelVO> vendorPenaltyProjectCostSummaryList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(excelVo.getVendorPenaltyProjectCostSummaries())) {
            vendorPenaltyProjectCostSummaryList = excelVo.getVendorPenaltyProjectCostSummaries()
                    .stream().map(e -> {
                        IncurredCostMergeExcelVO vo = new IncurredCostMergeExcelVO();
                        vo.setType("供应商罚扣");
                        vo.setWbsSummaryCode(e.getWbsCode());
                        vo.setActivityCode(e.getActivityCode());
                        vo.setBillCode(e.getPenaltyCode());
                        vo.setVendorName(e.getVendorName());
                        vo.setIncurredCost(e.getOccurCost());
                        vo.setDataTime(e.getDataTime());
                        return vo;
                    }).collect(Collectors.toList());
        }

        List<IncurredCostMergeExcelVO> incurredCostMergeExcelVOS = new ArrayList<>();
        incurredCostMergeExcelVOS.addAll(getMaterial);
        incurredCostMergeExcelVOS.addAll(contractComplete);
        incurredCostMergeExcelVOS.addAll(wkComplete);
        incurredCostMergeExcelVOS.addAll(ecComplete);
        incurredCostMergeExcelVOS.addAll(hroPurchaseContractCompleteList);
        incurredCostMergeExcelVOS.addAll(vendorPenaltyProjectCostSummaryList);

        Integer[] arr = {1};
        return incurredCostMergeExcelVOS.stream().peek(e -> e.setNumber(arr[0]++)).collect(Collectors.toList());
    }

    private List<OnTheWayCostMergeExcelVO> mergeOnTheWayCost(ProjectWbsCostExcelVo excelVo) {
        // 在途成本-po的未接收金额
        List<OnTheWayCostMergeExcelVO> poOnTheWay = BeanConverter.copy(excelVo.getPoOnTheWay(), OnTheWayCostMergeExcelVO.class);
        poOnTheWay.forEach(s -> {
            s.setType("采购成本");
            s.setOnTheWayCost(s.getPoTotalAmount()); //po的未接收数量*po单价金额
        });
        // 在途成本-采购合同未进度执行金额
        List<OnTheWayCostMergeExcelVO> contractOnTheWay = BeanConverter.copy(excelVo.getContractOnTheWay(), OnTheWayCostMergeExcelVO.class);
        contractOnTheWay.forEach(s -> {
            s.setType("采购成本");
            s.setNum(s.getPurchaseContractCode()); //采购合同编号
            s.setOnTheWayCost(s.getAllocationExecuteAmount()); //采购合同分配金额-进度执行金额
        });
        // 在途成本-已填报待审批的工时金额
        List<OnTheWayCostMergeExcelVO> wkOnTheWay = BeanConverter.copy(excelVo.getWkOnTheWay(), OnTheWayCostMergeExcelVO.class);
        wkOnTheWay.forEach(s -> {
            s.setType("已填报待审批的工时金额");
            s.setVendorName(s.getUserMip()); //人员
            s.setPamCode(DateUtils.format(s.getApplyDate())); //时间
            s.setMaterielDescr(s.getLevel()); //角色
            s.setOrderNum(s.getWorkingHours()); //已填报待审批的工时（h）
            s.setStorageCount(s.getCostMoney()); //标准费率（d）
            s.setOnTheWayCost(s.getTotalAmount()); //已填报待审批的工时*标准费率/8
        });
        // 在途成本-费用报销（EC单）已申请未报销
        List<OnTheWayCostMergeExcelVO> ecOnTheWay = BeanConverter.copy(excelVo.getEcOnTheWay(), OnTheWayCostMergeExcelVO.class);
        ecOnTheWay.forEach(s -> {
            s.setType("费用报销（EC单）已申请未报销");
            s.setNum(s.getFeeApplyCode()); //EC单号
            s.setOnTheWayCost(s.getAmount()); //已申请未报销金额
        });
        // 在途成本-点工采购合同分配金额-已对账金额
        List<OnTheWayCostMergeExcelVO> hroPurchaseContractOnTheWayList = BeanConverter.copy(excelVo.getHroPurchaseContractOnTheWay(), OnTheWayCostMergeExcelVO.class);
        hroPurchaseContractOnTheWayList.forEach(s -> {
            s.setType("人力点工");
            s.setNum(s.getPurchaseContractCode()); //采购合同编号
            s.setMaterielDescr(s.getRoleName()); //角色
            s.setTotalPrice(s.getTotalAmount()); //总价（不含税）-本位币
            s.setOnTheWayCost(s.getSurplusAmount()); //点工采购合同分配金额-已对账金额
            s.setDataTime(null); // 和合同对账单对账时间字段重了，清空数据
            s.setDataTime(s.getContractCreateTime()); //合同创建时间
        });

        // 在途成本-供应商罚扣项目成本
        List<OnTheWayCostMergeExcelVO> vendorPenaltyProjectCostOnTheWayList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(excelVo.getVendorPenaltyProjectCostSummaries())) {
            vendorPenaltyProjectCostOnTheWayList = excelVo.getVendorPenaltyProjectCostSummaries()
                    .stream().map(e -> {
                        OnTheWayCostMergeExcelVO vo = new OnTheWayCostMergeExcelVO();
                        vo.setType("供应商罚扣");
                        vo.setWbsSummaryCode(e.getWbsCode());
                        vo.setActivityCode(e.getActivityCode());
                        vo.setNum(e.getPenaltyCode());
                        vo.setVendorName(e.getVendorName());
                        vo.setOnTheWayCost(e.getProjectCost().subtract(e.getOccurCost()));
                        return vo;
                    }).collect(Collectors.toList());
        }

        List<OnTheWayCostMergeExcelVO> onTheWayCostMergeExcelVOS = new ArrayList<>();
        onTheWayCostMergeExcelVOS.addAll(poOnTheWay);
        onTheWayCostMergeExcelVOS.addAll(contractOnTheWay);
        onTheWayCostMergeExcelVOS.addAll(wkOnTheWay);
        onTheWayCostMergeExcelVOS.addAll(ecOnTheWay);
        onTheWayCostMergeExcelVOS.addAll(hroPurchaseContractOnTheWayList);
        onTheWayCostMergeExcelVOS.addAll(vendorPenaltyProjectCostOnTheWayList);

        Integer[] arr = {1};
        return onTheWayCostMergeExcelVOS.stream().peek(e -> e.setNumber(arr[0]++)).collect(Collectors.toList());
    }

    private List<RequirementBudgetMergeExcelVO> mergeRequirementBudget(ProjectWbsCostExcelVo excelVo) {
        // 需求预算-物料采购需求预算
        List<RequirementBudgetMergeExcelVO> purchaseList = BeanConverter.copy(excelVo.getPurchaseList(), RequirementBudgetMergeExcelVO.class);
        purchaseList.forEach(s -> {
            s.setType("采购需求");
            s.setRemainingCost(s.getRemainingCostAmount()); //当前物料采购需求预算金额
        });
        // 需求预算-物料外包（整包）需求预算
        List<RequirementBudgetMergeExcelVO> outsourceList = BeanConverter.copy(excelVo.getOutsourceList(), RequirementBudgetMergeExcelVO.class);
        outsourceList.forEach(s -> {
            s.setType("采购需求");
            s.setRemainingCost(s.getRemainingCostAmount()); //需求预算剩余金额
        });
        // 需求预算-审批中的需求发布单据
        List<RequirementBudgetMergeExcelVO> approvalList = BeanConverter.copy(excelVo.getApprovalList(), RequirementBudgetMergeExcelVO.class);
        approvalList.forEach(s -> {
            s.setType("采购需求");
            s.setReceiptsType("审批中");
            s.setRemainingCost(s.getBudgetOccupiedAmount()); //WBS'预算占用金额'
            s.setBudgetOccupiedAmount(null); // 和需求预算字段重了，清空数据
        });
        // 需求预算-费用申请（EA单）未释放、剩余可用的金额
        List<RequirementBudgetMergeExcelVO> ea = BeanConverter.copy(excelVo.getEa(), RequirementBudgetMergeExcelVO.class);
        ea.forEach(s -> {
            s.setType("费用申请（EA单）未释放、剩余可用的金额");
            s.setRequirementCode(s.getFeeApplyCode()); //EC单号
            s.setRemainingCost(s.getAmount()); //剩余EA可用金额
        });
        // 需求预算-人力点工需求预算
        List<RequirementBudgetMergeExcelVO> hroRequirementBudgetList = BeanConverter.copy(excelVo.getHroRequirementBudgets(), RequirementBudgetMergeExcelVO.class);
        hroRequirementBudgetList.forEach(s -> {
            s.setType("人力点工");
            s.setRemainingCost(s.getRemainingCostAmount()); //当前人力点工需求预算金额
        });
        // 需求预算-审批中的人力点工需求单据
        List<RequirementBudgetMergeExcelVO> hroRequirementBudgetApprovalList = BeanConverter.copy(excelVo.getHroRequirementBudgetApprovals(), RequirementBudgetMergeExcelVO.class);
        hroRequirementBudgetApprovalList.forEach(s -> {
            s.setType("人力点工");
            s.setReceiptsType("审批中");
            s.setRemainingCost(s.getBudgetOccupiedAmount()); //总预算（需求预算）
            s.setBudgetOccupiedAmount(null); // 和需求预算字段重了，清空数据
        });

        List<RequirementBudgetMergeExcelVO> requirementBudgetMergeExcelVOS = new ArrayList<>();
        requirementBudgetMergeExcelVOS.addAll(purchaseList);
        requirementBudgetMergeExcelVOS.addAll(outsourceList);
        requirementBudgetMergeExcelVOS.addAll(approvalList);
        requirementBudgetMergeExcelVOS.addAll(ea);
        requirementBudgetMergeExcelVOS.addAll(hroRequirementBudgetList);
        requirementBudgetMergeExcelVOS.addAll(hroRequirementBudgetApprovalList);

        Integer[] arr = {1};
        return requirementBudgetMergeExcelVOS.stream().peek(e -> e.setNumber(arr[0]++)).collect(Collectors.toList());
    }

    private List<MilepostInfoExcelVO> dealMilepostInfo(List<ProjectMilepostDto> projectMilepostDtos) {
        List<ProjectMilepostDto> milepostInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(projectMilepostDtos)) {
            for (ProjectMilepostDto milepost : projectMilepostDtos) {
                if (CollectionUtils.isNotEmpty(milepost.getGroupList())) {
                    for (ProjectMilepostGroupDto group : milepost.getGroupList()) {
                        if (CollectionUtils.isNotEmpty(group.getProjectMilepostDtoList())) {
                            for (ProjectMilepostDto childMilepost : group.getProjectMilepostDtoList()) {
                                childMilepost.setGroupName(group.getName());
                                childMilepost.setChildOrderNum(childMilepost.getOrderNum());
                                childMilepost.setOrderNum(milepost.getOrderNum());
                                childMilepost.setChildName(childMilepost.getName());
                                childMilepost.setName(milepost.getName());
                                milepostInfoList.add(childMilepost);
                            }
                        }
                    }
                } else {
                    milepostInfoList.add(milepost);
                }
            }
        }
        return BeanConverter.copy(milepostInfoList, MilepostInfoExcelVO.class);
    }

    private void createSummarySheet(HSSFSheet sheet, ProjectWbsCostExcelVo po, HSSFCellStyle cellLeftStyle, HSSFCellStyle titleStyle) {
        List<Map<String, Object>> mapList = po.getMapList();
        sheet.setColumnWidth(0, 10000);
        sheet.setColumnWidth(1, 6000);
        sheet.setColumnWidth(2, 6000);
        sheet.setColumnWidth(3, 6000);
        sheet.setColumnWidth(4, 6000);
        sheet.setColumnWidth(5, 6000);
        sheet.setColumnWidth(6, 6000);
        sheet.setColumnWidth(7, 6000);

        ExportExcelUtil.createCell(sheet, "项目汇总信息", 0, 0, titleStyle);

        HSSFRow row1 = ExportExcelUtil.createCell(sheet, "项目号", 1, 0, titleStyle);
        ExportExcelUtil.createCell(row1, "项目名称", 2, titleStyle);
        ExportExcelUtil.createCell(row1, "更新时间", 4, titleStyle);
        ExportExcelUtil.createCell(row1, "本位币币种", 6, titleStyle);

        ExportExcelUtil.createCell(row1, po.getProjectCode(), 1, cellLeftStyle);
        ExportExcelUtil.createCell(row1, po.getProjectName(), 3, cellLeftStyle);
        ExportExcelUtil.createCell(row1, DateUtil.format(po.getExecuteTime(), DateUtil.TIMESTAMP_PATTERN), 5, cellLeftStyle);
        ExportExcelUtil.createCell(row1, po.getLocalCurrency(), 7, cellLeftStyle);

        HSSFRow row2 = ExportExcelUtil.createCell(sheet, "合同币种", 2, 0, titleStyle);
        ExportExcelUtil.createCell(row2, "项目不含税金额（原币）", 2, titleStyle);
        ExportExcelUtil.createCell(row2, "累计已确认收入比例（原币）", 4, titleStyle);
        ExportExcelUtil.createCell(row2, "汇兑损益（本位币）", 6, titleStyle);

        ExportExcelUtil.createCell(row2, po.getCurrency(), 1, cellLeftStyle);
        ExportExcelUtil.createCell(row2, bigDecimalFormat(po.getAmount()), 3, cellLeftStyle);
        //累计已确认收入比例（原币）= 取结转单中“本期确认收入（原币）”的合计/项目不含税金额（原币）*100
        BigDecimal cumulativeConfirmedIncomeRatio = BigDecimalUtils.divide(BigDecimalUtils.multiply(po.getConfirmedIncome(), new BigDecimal(100)), po.getAmount());
        ExportExcelUtil.createCell(row2, bigDecimalRoundRatioFormat(cumulativeConfirmedIncomeRatio), 5, cellLeftStyle);
        ExportExcelUtil.createCell(row2, bigDecimalFormat(po.getConfirmedExchangeAmount()), 7, cellLeftStyle);

        HSSFRow row3 = ExportExcelUtil.createCell(sheet, "已确认收入总额（本位币）", 3, 0, titleStyle);
        ExportExcelUtil.createCell(row3, "已确认成本总额（本位币）", 2, titleStyle);
        ExportExcelUtil.createCell(row3, "已确认毛利率（本位币）", 4, titleStyle);
        ExportExcelUtil.createCell(row3, "项目总预算（本位币）", 6, titleStyle);

        ExportExcelUtil.createCell(row3, bigDecimalFormat(po.getStandardConfirmedIncome()), 1, cellLeftStyle);
        ExportExcelUtil.createCell(row3, bigDecimalFormat(po.getConfirmedCost()), 3, cellLeftStyle);
        ExportExcelUtil.createCell(row3, bigDecimalRoundRatioFormat(po.getConfirmedRate()), 5, cellLeftStyle);
        ExportExcelUtil.createCell(row3, bigDecimalFormat(po.getProjectTotalBudget()), 7, cellLeftStyle);

        HSSFRow row4 = ExportExcelUtil.createCell(sheet, "项目总需求预算（本位币）", 4, 0, titleStyle);
        ExportExcelUtil.createCell(row4, "在途总成本（本位币）", 2, titleStyle);
        ExportExcelUtil.createCell(row4, "已发生总成本（本位币）", 4, titleStyle);
        ExportExcelUtil.createCell(row4, "剩余总预算（本位币）", 6, titleStyle);

        ExportExcelUtil.createCell(row4, bigDecimalFormat(po.getTotalDemandBudget()), 1, cellLeftStyle);
        ExportExcelUtil.createCell(row4, bigDecimalFormat(po.getTotalCostInTransit()), 3, cellLeftStyle);
        ExportExcelUtil.createCell(row4, bigDecimalFormat(po.getTotalCostIncurred()), 5, cellLeftStyle);
        ExportExcelUtil.createCell(row4, bigDecimalFormat(po.getRemainingTotalBudget()), 7, cellLeftStyle);

        ExportExcelUtil.createCell(sheet, "项目成本明细", 6, 0, titleStyle);
        for (int i = 0; i < mapList.size(); i++) {
            Map<String, Object> dataMap = mapList.get(i);
            if (StringUtils.isNotBlank(MapUtils.getString(dataMap, WbsBudgetFieldConstant.FEE_TYPE_NAME))) {
                // 能匹配到，boolean转字典
                dataMap.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, Boolean.TRUE.equals(MapUtils.getBoolean(dataMap, WbsBudgetFieldConstant.FEE_SYNC_EMS)) ? "是" : "否");
            } else {
                // 匹配不到，不返回内容
                dataMap.remove(WbsBudgetFieldConstant.FEE_SYNC_EMS);
            }
            dataMap.put(WbsBudgetFieldConstant.ORDER_NO, i + 1);
            // 设置WBS全编码
            String projectCode = String.valueOf(dataMap.get(WbsBudgetFieldConstant.PROJECT_CODE));
            String wbsFullCode = String.valueOf(dataMap.get(WbsBudgetFieldConstant.WBS_FULL_CODE));
            dataMap.put(WbsBudgetFieldConstant.WBS_FULL_CODE, projectCode + "-" + wbsFullCode);
        }
        // wbs动态列
        List<WbsTemplateRuleCache> wbsCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(po.getWbsTemplateInfoId());
        /* 导出数据JsonArray格式 */
        JSONArray dataJsonArray = JSONArray.parseArray(JSON.toJSONString(mapList));
        /* 标题 */
        LinkedHashMap<String, String> titleMap = new LinkedHashMap<>();
        titleMap.put(WbsBudgetFieldConstant.ORDER_NO, "序号");
        titleMap.put(WbsBudgetFieldConstant.PROJECT_CODE, "项目");
        if (!org.springframework.util.CollectionUtils.isEmpty(wbsCaches)) {
            for (WbsTemplateRuleCache wbsCacge : wbsCaches) {
                titleMap.put(wbsCacge.getKey(), wbsCacge.getRuleName());
            }
        }
        titleMap.put(WbsBudgetFieldConstant.WBS_FULL_CODE, "WBS号");
        titleMap.put(WbsBudgetFieldConstant.DESCRIPTION, "描述");
        titleMap.put(WbsBudgetFieldConstant.ACTIVITY_CODE, "活动事项编码");
        titleMap.put(WbsBudgetFieldConstant.ACTIVITY_NAME, "类别名称");
        titleMap.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, "类别属性");
        titleMap.put(WbsBudgetFieldConstant.FEE_TYPE_NAME, "经济事项");
        titleMap.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, "是否同步EMS");
        titleMap.put(WbsBudgetFieldConstant.PRICE, "预算金额");
        titleMap.put(WbsBudgetFieldConstant.BASELINE_COST, "预算基线");
        titleMap.put(WbsBudgetFieldConstant.DEMAND_COST, "需求预算");
        titleMap.put(WbsBudgetFieldConstant.ON_THE_WAY_COST, "在途成本");
        titleMap.put(WbsBudgetFieldConstant.INCURRED_COST, "已发生成本");
        titleMap.put(WbsBudgetFieldConstant.REMAINING_COST, "剩余可用预算");
        titleMap.put(WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST, "累计变更金额");

        int rowNum = 7;
        //标题
        LinkedList list = new LinkedList();
        Iterator<String> iterator = titleMap.keySet().iterator();
        Row row = sheet.createRow(rowNum);
        int i = 0;
        while (iterator.hasNext()) {
            String key = iterator.next();
            Cell cell = row.createCell(i);
            cell.setCellValue(titleMap.get(key));
            cell.setCellStyle(titleStyle);
            list.add(key);
            i++;
        }
        rowNum++;
        //数据
        ExcelUtil.setJsonData(0, rowNum, sheet, list, dataJsonArray, cellLeftStyle);
    }


    private String bigDecimalFormat(BigDecimal b) {
        if (b == null) {
            return "-";
        } else {
            return b.toString();
        }
    }

    private String bigDecimalRoundRatioFormat(BigDecimal b) {
        if (b == null) {
            return "-";
        } else {
            return b.setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "%";
        }
    }

    @ApiOperation(value = "wbs预算版本生成-手动调用")
    @GetMapping("/generateWbsBudgetVersion/{id}")
    public Response generateWbsBudgetVersion(@PathVariable Long id) {
        String url = String.format("%sstatistics/project/wbsBudgetVersion/publish/%d", ModelsEnum.STATISTICS.getBaseUrl(), id);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Boolean> response =   JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

}