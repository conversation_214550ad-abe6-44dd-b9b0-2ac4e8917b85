package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.UnitDto;
import com.midea.pam.common.basedata.entity.ProductUnit;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.excelVo.HroRequirementReportExcelVo;
import com.midea.pam.common.ctc.excelVo.ReportBudgetChangeExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.ProjectLevelEnums;
import com.midea.pam.common.enums.ProjectStatus;
import com.midea.pam.common.statistics.dto.ExportResponse;
import com.midea.pam.common.statistics.dto.ReportDepartmentInfoDTO;
import com.midea.pam.common.statistics.dto.ReportExecuteRecordDTO;
import com.midea.pam.common.statistics.dto.ReportGroupFormationDTO;
import com.midea.pam.common.statistics.dto.ReportGroupGrantUserDTO;
import com.midea.pam.common.statistics.dto.ReportGroupInfoDTO;
import com.midea.pam.common.statistics.dto.ReportInfoDTO;
import com.midea.pam.common.statistics.dto.ReportProjectChangeBaseInfoDto;
import com.midea.pam.common.statistics.dto.ReportProjectChangeBudgetDto;
import com.midea.pam.common.statistics.dto.ReportProjectChangeMilepostDto;
import com.midea.pam.common.statistics.dto.ReportProjectChangeTraceSummaryDto;
import com.midea.pam.common.statistics.dto.ReportProjectMarginDto;
import com.midea.pam.common.statistics.dto.ReportProjectMonthlyDto;
import com.midea.pam.common.statistics.dto.ReportProjectProcessProfitDTO;
import com.midea.pam.common.statistics.entity.ReportExecuteParameter;
import com.midea.pam.common.statistics.entity.ReportGroupGrantUser;
import com.midea.pam.common.statistics.entity.ReportInfo;
import com.midea.pam.common.statistics.entity.ReportProjectWbsBudget;
import com.midea.pam.common.statistics.excelVo.ProjectCostDetailExcelVO;
import com.midea.pam.common.statistics.excelVo.ProjectCostTotalFeeRecordExcelVO;
import com.midea.pam.common.statistics.excelVo.ReportIncomeCalculateExcelVO;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExcelUtil;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.common.util.MapUtil;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/7
 * @description
 */
@Api("统计报表")
@RestController
@RequestMapping("statistics/report")
public class ReportController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private RestTemplate restTemplate;

    @ApiOperation("新增或更新事业部关系")
    @PostMapping("department")
    public Response addDepartment(@RequestBody ReportDepartmentInfoDTO reportDepartmentInfoDTO) {
        final String url = String.format("%sstatistics/report/department", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, reportDepartmentInfoDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("事业部关系分页查询")
    @GetMapping("department/page")
    public Response pageDepartment(@RequestParam(required = false) final String name,
                                   @RequestParam(required = false) final String unitNames,
                                   @RequestParam(required = false) final String financialName,
                                   @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("name", name);
        paramMap.put("unitNames", unitNames);
        paramMap.put("financialName", financialName);
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/department/page", paramMap);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class, paramMap);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ReportDepartmentInfoDTO>>>() {
        });
    }

    @ApiOperation(value = "查询可用的虚拟部门")
    @GetMapping("avaiableUnit")
    public Response avaiableUnit() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/avaiableUnit", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<UnitDto>>>() {
        });
    }

    @ApiOperation(value = "查询可用的产品部门")
    @GetMapping("getProductUnit")
    public Response getProductUnit() {
        final Map<String, Object> param = new HashMap<>();
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/getProductUnit", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<ProductUnit>>>() {
        });
    }

    @ApiOperation("报表分页查询")
    @GetMapping("report/page")
    public Response pageReport(@RequestParam(required = false) final String type,
                               @RequestParam(required = false) final String name,
                               @RequestParam(required = false) final String usingObject,
                               @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                               @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("type", type);
        paramMap.put("name", name);
        paramMap.put("usingObject", usingObject);
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/report/page", paramMap);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ReportInfoDTO>>>() {
        });
    }

    @ApiOperation("新增或更新报表组")
    @PostMapping("reportGroup")
    public Response addReportGroup(@RequestBody ReportGroupInfoDTO reportGroupInfoDTO) {
        final String url = String.format("%sstatistics/report/reportGroup", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, reportGroupInfoDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("报表组分页查询")
    @GetMapping("reportGroup/page")
    public Response pageReportGroup(@RequestParam(required = false) final String permissionStr,
                                    @RequestParam(required = false) final String name,
                                    @RequestParam(required = false) final String unitName,
                                    @RequestParam(required = false) final Long reportId,
                                    @RequestParam(required = false) final Long userId,
                                    @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                    @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("permissionStr", permissionStr);
        paramMap.put("name", name);
        paramMap.put("unitName", unitName);
        paramMap.put("reportId", reportId);
        paramMap.put("userId", userId);
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/reportGroup/page", paramMap);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ReportGroupInfoDTO>>>() {
        });
    }

    @ApiOperation("删除报表组")
    @DeleteMapping("reportGroup/{id}")
    public Response removeReportGroup(@PathVariable("id") Long id) {
        String url = String.format("%sstatistics/report/reportGroup/" + id, ModelsEnum.STATISTICS.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.DELETE, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("报表组分配用户分页查询")
    @GetMapping("reportGroup/user/page")
    public Response pageReportGroupAuthUser(@RequestParam(required = false) final Long reportGroupId,
                                            @RequestParam(required = false) final Long userId,
                                            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("reportGroupId", reportGroupId);
        paramMap.put("userId", userId);
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/reportGroup/user/page", paramMap);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ReportGroupGrantUserDTO>>>() {
        });
    }

    @ApiOperation("报表组查询已分配的报表")
    @GetMapping("reportGroup/report/distributed/page")
    public Response pageReportGroupDistributeReport(@RequestParam(required = true) final Long reportGroupId,
                                                    @RequestParam(required = false) final String reportName,
                                                    @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                    @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("reportGroupId", reportGroupId);
        paramMap.put("reportName", reportName);
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/reportGroup/report/distributed/page", paramMap);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ReportGroupFormationDTO>>>() {
        });
    }

    @ApiOperation("报表组查询未分配的报表")
    @GetMapping("reportGroup/report/undistributed/page")
    public Response pageReportGroupUnDistributeReport(@RequestParam(required = true) final Long reportGroupId,
                                                      @RequestParam(required = false) final String reportName,
                                                      @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                                      @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("reportGroupId", reportGroupId);
        paramMap.put("reportName", reportName);
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/reportGroup/report/undistributed/page", paramMap);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ReportInfo>>>() {
        });
    }

    @ApiOperation("报表组新增报表")
    @PostMapping("reportGroupFormation/batchSave")
    public Response addReportGroupFormation(@RequestBody ReportGroupInfoDTO reportGroupInfoDTO) {
        final String url = String.format("%sstatistics/report/reportGroupFormation/batchSave", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, reportGroupInfoDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("报表组移除报表")
    @DeleteMapping("reportGroupFormation/{reportGroupFormationId}")
    public Response deleteReportGroupFormation(@PathVariable final Long reportGroupFormationId) {
        String url = String.format("%sstatistics/report/reportGroupFormation/" + reportGroupFormationId, ModelsEnum.STATISTICS.getBaseUrl());
        final ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.DELETE, null, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("授权报表组权限给用户")
    @PostMapping("reportGroupGrantUser")
    public Response reportGroupGrantUser(@RequestBody ReportGroupGrantUser reportGroupGrantUser) {
        final String url = String.format("%sstatistics/report/reportGroupGrantUser", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, reportGroupGrantUser, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("批量授权报表组权限给用户")
    @PostMapping("reportGroupGrantUser/batchSave")
    public Response reportGroupGrantUserBatchSave(@RequestBody ReportGroupGrantUserDTO reportGroupGrantUser) {
        final String url = String.format("%sstatistics/report/reportGroupGrantUser/batchSave", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, reportGroupGrantUser, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("批量更新授权报表组权限给用户")
    @PostMapping("reportGroupGrantUser/batchUpdate")
    public Response reportGroupGrantUserBatchUpdate(@RequestBody ReportGroupGrantUserDTO reportGroupGrantUser) {
        final String url = String.format("%sstatistics/report/reportGroupGrantUser/batchUpdate", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, reportGroupGrantUser, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("用户执行报表清单")
    @GetMapping("pageReportExecuteSummary")
    public Response pageReportExecuteSummary(@ApiParam("报表分类，多个用,隔开") @RequestParam(required = false) final String reportType,
                                             @ApiParam("报表名称") @RequestParam(required = false) final String reportName,
                                             @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                             @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("reportType", reportType);
        paramMap.put("reportName", reportName);
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/pageReportExecuteSummary", paramMap);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ReportExecuteRecordDTO>>>() {
        });
    }

    @ApiOperation("新增报表执行记录")
    @PostMapping("reportExecuteRecord")
    public Response addReportExecuteRecord(@RequestBody ReportExecuteRecordDTO reportExecuteRecordDTO) {
        final String url = String.format("%sstatistics/report/reportExecuteRecord", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, reportExecuteRecordDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ReportExecuteRecordDTO>>() {
        });
    }

    @ApiOperation("取消报表执行")
    @PostMapping("cancelReportExecuteRecord")
    public Response cancelReportExecuteRecord(@RequestBody ReportExecuteRecordDTO reportExecuteRecordDTO) {
        final String url = String.format("%sstatistics/report/cancelReportExecuteRecord", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, reportExecuteRecordDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("执行报表调度")
    @PostMapping("action")
    public Response action(@RequestBody ReportExecuteRecordDTO reportExecuteRecordDTO) {
        final String url = String.format("%sstatistics/report/action", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, reportExecuteRecordDTO, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Integer>>() {
        });
    }

    @ApiOperation("导出报表执行记录")
    @GetMapping("exportReportExecuteRecord")
    public void exportReportExecuteRecord(HttpServletResponse response, ReportExecuteRecordDTO reportExecuteRecordDTO) {
        final String url = String.format("%sstatistics/report/exportReportExecuteRecord", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, reportExecuteRecordDTO, String.class);
        DataResponse<ExportResponse> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ExportResponse>>() {
        });
        ExportResponse result = dataResponse.getData();
//        logger.info("获取的内容为:{}",JSON.toJSONString(result));
//        dataResponse.getData().getSheetList().get(0).setDataList(new ArrayList<>());
//        logger.info("获取的内容为:{}",JSON.toJSONString(result));
        logger.info("请求参数为:{},响应结果为:{}", JSON.toJSONString(reportExecuteRecordDTO), result.getSheetList().size());

        if (result != null && ListUtils.isNotEmpty(result.getSheetList())) {
            if (StringUtils.isNotEmpty(reportExecuteRecordDTO.getReportCode()) &&
                    "incomeCalculate".equals(reportExecuteRecordDTO.getReportCode())) {


                HSSFWorkbook workbook = new HSSFWorkbook();

                HSSFFont font = workbook.createFont();
                font.setFontName("等线");
                font.setFontHeightInPoints((short) 12);

                HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
                cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
                cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_CENTER);
                cellLeftStyle.setFont(font);

                HSSFSheet sheet1 = workbook.createSheet("收入预测汇总表");
                HSSFSheet sheet2 = workbook.createSheet("自动计算类收入汇总");
                HSSFSheet sheet3 = workbook.createSheet("项目经理反馈类收入汇总");
                HSSFSheet sheet4 = workbook.createSheet("产品总监反馈类收入汇总");


                List<ExportResponse.Sheet> sheetList = result.getSheetList();

                List<ReportIncomeCalculateExcelVO> summaryReportList =
                        JSONObject.parseArray(sheetList.get(0).getDataList().toString(), ReportIncomeCalculateExcelVO.class);

                List<ReportIncomeCalculateExcelVO> calculateReportList =
                        JSONObject.parseArray(sheetList.get(1).getDataList().toString(), ReportIncomeCalculateExcelVO.class);

                List<ReportIncomeCalculateExcelVO> projectReportList =
                        JSONObject.parseArray(sheetList.get(2).getDataList().toString(), ReportIncomeCalculateExcelVO.class);

                List<ReportIncomeCalculateExcelVO> productReportList =
                        JSONObject.parseArray(sheetList.get(3).getDataList().toString(), ReportIncomeCalculateExcelVO.class);

                String monthStr = productReportList.get(0).getMonthStr();


                generateForm(cellLeftStyle, sheet1, monthStr, summaryReportList);
                generateForm(cellLeftStyle, sheet2, monthStr, calculateReportList);
                generateForm(cellLeftStyle, sheet3, monthStr, projectReportList);
                generateForm(cellLeftStyle, sheet4, monthStr, productReportList);


                StringBuffer fileName = new StringBuffer();

                fileName.append("收入预测" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
                fileName.append(".xls");
                ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
            } else if (StringUtils.isNotEmpty(reportExecuteRecordDTO.getReportCode()) &&
                    "countSwapData".equals(reportExecuteRecordDTO.getReportCode())) {
                List<ExportResponse.Sheet> sheetList = result.getSheetList();
                JSONArray list = JSONArray.parseArray(sheetList.get(0).getDataList().toString());
                exportCountSwapData(response, list);
            } else if (StringUtils.isNotEmpty(reportExecuteRecordDTO.getReportCode()) &&
                    "reportProjectProcessProfit".equals(reportExecuteRecordDTO.getReportCode())) {
                exportProjectProcessProfit(result, response);
            } else if ("financeAgesReceivableTerm".equals(reportExecuteRecordDTO.getReportCode()) ||
                    "contractReceivableAges".equals(reportExecuteRecordDTO.getReportCode())) {
                exportContractReceivableAges(result, response);
            } else if (Objects.equals("reportPurchaseContractDebtAges", reportExecuteRecordDTO.getReportCode())) {
                exportPurchaseContractDebtAges(result, response);
            } else if (Objects.equals("reportProjectContractReceivable", reportExecuteRecordDTO.getReportCode())) {
                exportProjectContractReceivable(result, response);
            } else if (Objects.equals("reportProjectChangeTraceSummary", reportExecuteRecordDTO.getReportCode())) {
                exportProjectChangeSummary(result, response);
            } else if (Objects.equals("reportPurchaseProgress", reportExecuteRecordDTO.getReportCode())) {
                exportPurchaseProgress(result, response);
            } else if ("projectMonthly".equals(reportExecuteRecordDTO.getReportCode())) {
                exportProjectMonthly(result, response);
            } else if ("budgetChange".equals(reportExecuteRecordDTO.getReportCode())) {
                exportBudgetChange(result, response);
            } else if ("hroOverspendWarn".equals(reportExecuteRecordDTO.getReportCode())) {
                exportHroOverspendWarn(result, response);
            } else if ("projectCost".equals(reportExecuteRecordDTO.getReportCode())) {
                List<ExportResponse.Sheet> sheetList = result.getSheetList();
                List<ProjectCostDetailExcelVO> detailExcelVOList =
                        JSONObject.parseArray(sheetList.get(0).getDataList().toString(), ProjectCostDetailExcelVO.class);
                List<ProjectCostTotalFeeRecordExcelVO> totalFeeRecordExcelVOList =
                        JSONObject.parseArray(sheetList.get(1).getDataList().toString(), ProjectCostTotalFeeRecordExcelVO.class);
                //导出操作
                StringBuilder fileName = new StringBuilder();
                // 创建新的Excel 工作簿
                HSSFWorkbook workbook = new HSSFWorkbook();

                ExportExcelUtil.addSheet(workbook, detailExcelVOList, ProjectCostDetailExcelVO.class, null, "项目成本明细表", true);
                ExportExcelUtil.addSheet(workbook, totalFeeRecordExcelVOList, ProjectCostTotalFeeRecordExcelVO.class, null, "项目费用成本汇总", true);

                fileName.append("项目成本" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
                fileName.append(".xls");
                ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
            } else if ("reportProjectWbsCost".equals(reportExecuteRecordDTO.getReportCode())) {
                exportProjectWbsCost(result, response);
            } else if ("projectMargin".equals(reportExecuteRecordDTO.getReportCode())) {
                exportProjectMargin(result, response);
            } else {
                Workbook workbook = null;
                for (int i = 0; i < result.getSheetList().size(); i++) {
                    ExportResponse.Sheet sheet = result.getSheetList().get(i);
                    List<Object> sheetData = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(sheet.getDataList())) {
                        for (JSONObject jsonObject : (List<JSONObject>) sheet.getDataList()) {
                            sheetData.add(JSONObject.toJavaObject(jsonObject, sheet.getEntityClass()));
                        }
                    }
                    if (i == 0) {
                        workbook = ExportExcelUtil.buildDefaultSheet(sheetData, sheet.getEntityClass(), sheet.getTitle(), sheet.getSheetName(), sheet.getCreateHeader());
                    } else {
                        ExportExcelUtil.addSheet(workbook, sheetData, sheet.getEntityClass(), sheet.getTitle(), sheet.getSheetName(), sheet.getCreateHeader());
                    }
                }
                ExportExcelUtil.downLoadExcel(result.getFileName(), response, workbook);
            }
        }
    }

    public void exportProjectWbsCost(ExportResponse result, HttpServletResponse response) {
        List<ExportResponse.Sheet> sheetList = result.getSheetList();
        logger.info("获取的数据为:{}", sheetList.size());
        //设置表格样式
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);
        cellLeftStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);

        // 设置背景色
        HSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        titleStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        titleStyle.setFont(font);
        titleStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        titleStyle.setFillForegroundColor(HSSFColor.PALE_BLUE.index);//背景白色
        titleStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        for (int k = 0; k < sheetList.size(); k++) {
            ExportResponse.Sheet sheetData = sheetList.get(k);
            if (Objects.equals("项目成本汇总", sheetData.getSheetName())) {
                List<ReportProjectWbsBudget> budgetList = JSONObject.parseArray(sheetData.getDataList().toString(), ReportProjectWbsBudget.class);
                Map<Long, List<ReportProjectWbsBudget>> listMap = budgetList.stream().collect(Collectors.groupingBy(ReportProjectWbsBudget::getWbsTemplateInfoId));
                for (Long wbsTemplateInfoId : listMap.keySet()) {
                    List<ReportProjectWbsBudget> budgets = listMap.get(wbsTemplateInfoId).stream().sorted(Comparator.comparing(ReportProjectWbsBudget::getProjectCode).thenComparing(ReportProjectWbsBudget::getWbsFullCode)).collect(Collectors.toList());
                    List<WbsTemplateRuleCache> wbsCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);
                    //标题
                    LinkedHashMap<String, String> titleMap = new LinkedHashMap<>();
                    titleMap.put(WbsBudgetFieldConstant.ORDER_NO, "序号");
                    titleMap.put(WbsBudgetFieldConstant.PROJECT_CODE, "项目");
                    if (!org.springframework.util.CollectionUtils.isEmpty(wbsCaches)) {
                        for (WbsTemplateRuleCache wbsCacge : wbsCaches) {
                            titleMap.put(wbsCacge.getKey(), wbsCacge.getRuleName());
                        }
                    }
                    titleMap.put(WbsBudgetFieldConstant.WBS_FULL_CODE, "WBS号");
                    titleMap.put(WbsBudgetFieldConstant.DESCRIPTION, "描述");
                    titleMap.put(WbsBudgetFieldConstant.ACTIVITY_CODE, "活动事项编码");
                    titleMap.put(WbsBudgetFieldConstant.ACTIVITY_NAME, "类别名称");
                    titleMap.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, "类别属性");
                    titleMap.put(WbsBudgetFieldConstant.FEE_TYPE_NAME, "经济事项");
                    titleMap.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, "是否同步EMS");
                    titleMap.put(WbsBudgetFieldConstant.PRICE, "预算金额");
                    titleMap.put(WbsBudgetFieldConstant.BASELINE_COST, "预算基线");
                    titleMap.put(WbsBudgetFieldConstant.DEMAND_COST, "需求预算");
                    titleMap.put(WbsBudgetFieldConstant.ON_THE_WAY_COST, "在途成本");
                    titleMap.put(WbsBudgetFieldConstant.INCURRED_COST, "已发生成本");
                    titleMap.put(WbsBudgetFieldConstant.REMAINING_COST, "剩余可用预算");
                    titleMap.put(WbsBudgetFieldConstant.CHANGE_ACCUMULATE_COST, "累计变更金额");
                    titleMap.put(WbsBudgetFieldConstant.EXECUTE_TIME, "项目成本更新时间");
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    List<Map<String, Object>> mapList = new ArrayList<>();
                    for (int i = 0; i < budgets.size(); i++) {
                        ReportProjectWbsBudget budget = budgets.get(i);
                        Map map = JSON.parseObject(JSON.toJSONString(budget), Map.class);
                        if (map.containsKey(WbsBudgetFieldConstant.EXECUTE_TIME)) {
                            //日期处理,导出excel时显示的是Long类型的数据,转成字符串
                            Date executeTime = budget.getExecuteTime();
                            map.put(WbsBudgetFieldConstant.EXECUTE_TIME, sdf.format(executeTime));
                        }


                        //动态行的值处理
                        String[] dynamicFieldArr = budget.getDynamicFields().split(",");
                        String[] dynamicValueArr = budget.getDynamicValues().split(",");
                        for (int j = 0; j < dynamicFieldArr.length; j++) {
                            map.put(dynamicFieldArr[j], dynamicValueArr[j]);
                        }
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(MapUtils.getString(map, WbsBudgetFieldConstant.FEE_TYPE_NAME))) {
                            map.put(WbsBudgetFieldConstant.FEE_SYNC_EMS, Boolean.TRUE.equals(MapUtils.getBoolean(map, WbsBudgetFieldConstant.FEE_SYNC_EMS)) ? "是" : "否");
                        } else {
                            map.remove(WbsBudgetFieldConstant.FEE_SYNC_EMS);
                        }
                        map.put(WbsBudgetFieldConstant.ORDER_NO, i + 1);
                        mapList.add(map);
                    }
                    /* 导出数据JsonArray格式 */
                    JSONArray dataJsonArray = JSONArray.parseArray(JSON.toJSONString(mapList));
                    HSSFSheet sheet = workbook.createSheet("项目成本汇总-(" + budgets.get(0).getWbsTemplateName() + ")");
                    int rowNum = 0;
                    LinkedList list = new LinkedList();
                    Iterator<String> iterator = titleMap.keySet().iterator();
                    Row row = sheet.createRow(rowNum);
                    int i = 0;
                    while (iterator.hasNext()) {
                        String key = iterator.next();
                        Cell cell = row.createCell(i);
                        cell.setCellValue(titleMap.get(key));
                        cell.setCellStyle(titleStyle);
                        list.add(key);
                        i++;
                    }
                    rowNum++;
                    //数据
                    ExcelUtil.setJsonData(0, rowNum, sheet, list, dataJsonArray, cellLeftStyle);

                    //设置列宽
                    this.setReportProjectWbsBudgetSheetWidth(sheet);
                }
            } else {
                //其他类型的表数据导出
                String sheetName = sheetData.getSheetName();
                logger.info("待生成表格的数据名称为:{}", sheetName);
                List dataList = sheetData.getDataList();
                JSONArray jsonArray = new JSONArray();
                jsonArray.addAll(dataList);
                List<Object> detailExcelVOList = jsonArray.toJavaList(sheetData.getEntityClass());
                //如果数据超过5万行分sheet
                if (detailExcelVOList.size() > 50000) {
                    List<List<Object>> lists = ListUtils.splistList(detailExcelVOList, 50000);
                    logger.info("单个sheet表的数据超过5万行,进行拆sheet表处理:{}", lists.size());
                    //ExportExcelUtil.addSheet(workbook, detailExcelVOList, sheetData.getEntityClass(), null, sheetName, true);
                    //ExportExcelUtil.addSheet(workbook, detailExcelVOList, sheetData.getEntityClass(), null, "2", true);
                    //logger.info("单个sheet表的数据超过5万行,进行拆sheet表处理:{}",lists.size());
                    int num = 0;
                    for (List<Object> list : lists) {
                        num++;
                        ExportExcelUtil.addSheet(workbook, new ArrayList<>(list), sheetData.getEntityClass(), null, sheetName + "_" + num, true);
                    }
                } else {
                    ExportExcelUtil.addSheet(workbook, detailExcelVOList, sheetData.getEntityClass(), null, sheetName, true);
                }
            }
        }
        ExportExcelUtil.downLoadExcel("项目成本汇总表（WBS)-" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    private void setReportProjectWbsBudgetSheetWidth(HSSFSheet sheet) {
        sheet.setColumnWidth(0, 6 * 256);
        sheet.setColumnWidth(1, 15 * 256);
        sheet.setColumnWidth(2, 11 * 256);
        sheet.setColumnWidth(3, 11 * 256);
        sheet.setColumnWidth(4, 11 * 256);
        sheet.setColumnWidth(5, 25 * 256);
        sheet.setColumnWidth(6, 35 * 256);
        sheet.setColumnWidth(7, 15 * 256);
        sheet.setColumnWidth(8, 35 * 256);
        sheet.setColumnWidth(9, 11 * 256);
        sheet.setColumnWidth(10, 15 * 256);
        sheet.setColumnWidth(11, 11 * 256);
        sheet.setColumnWidth(12, 20 * 256);
        sheet.setColumnWidth(13, 20 * 256);
        sheet.setColumnWidth(14, 20 * 256);
        sheet.setColumnWidth(15, 20 * 256);
        sheet.setColumnWidth(16, 20 * 256);
        sheet.setColumnWidth(17, 20 * 256);
        sheet.setColumnWidth(18, 20 * 256);
        sheet.setColumnWidth(19, 30 * 256);
    }

    private HSSFCellStyle getCellStyle(HSSFWorkbook workbook, short alignment, boolean bold, boolean underColor, boolean head) {
        // 设置字体
        HSSFFont boldFont = workbook.createFont();
        boldFont.setFontName("宋体");
        if (bold) {
            boldFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//粗体显示
        }
        boldFont.setFontHeightInPoints((short) 10);
        if (head) {
            boldFont.setFontHeightInPoints((short) 20);
        }

        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        cellStyle.setFillForegroundColor(HSSFColor.WHITE.index);//背景白色，不设置默认黑底
        if (underColor) {
            cellStyle.setFillForegroundColor(HSSFColor.GREY_25_PERCENT.index);
        }
        cellStyle.setAlignment(alignment); //HSSFCellStyle.ALIGN_CENTER
        cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
        cellStyle.setFont(boldFont);
        cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        cellStyle.setRightBorderColor(HSSFColor.BLACK.index);
        cellStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        cellStyle.setTopBorderColor(HSSFColor.BLACK.index);
        return cellStyle;
    }

    private void createSheet(Map<Long, List<ReportProjectMarginDto>> map, HSSFWorkbook workbook, int num) {
        HSSFSheet sheet = workbook.createSheet("页码" + num);

        HSSFCellStyle headerStyle0 = getCellStyle(workbook, HSSFCellStyle.ALIGN_CENTER, true, false, true);
        HSSFCellStyle headerStyle1 = getCellStyle(workbook, HSSFCellStyle.ALIGN_LEFT, true, false, false);
        HSSFCellStyle headerStyle2 = getCellStyle(workbook, HSSFCellStyle.ALIGN_LEFT, false, false, false);
        HSSFCellStyle headerStyle3 = getCellStyle(workbook, HSSFCellStyle.ALIGN_LEFT, true, true, false);
        HSSFCellStyle headerStyle4 = getCellStyle(workbook, HSSFCellStyle.ALIGN_RIGHT, true, false, false);
        HSSFCellStyle detailStyle1 = getCellStyle(workbook, HSSFCellStyle.ALIGN_CENTER, true, false, false);
        HSSFCellStyle detailStyle2 = getCellStyle(workbook, HSSFCellStyle.ALIGN_CENTER, true, true, false);

        sheet.setColumnWidth(0, 5000);
        sheet.setColumnWidth(1, 5000);
        sheet.setColumnWidth(2, 7500);
        sheet.setColumnWidth(3, 7500);
        sheet.setColumnWidth(4, 7500);
        sheet.setColumnWidth(5, 7500);
        sheet.setColumnWidth(6, 7500);
        sheet.setColumnWidth(7, 8100);

        int row = 0;
        for (List<ReportProjectMarginDto> list : map.values()) {
            HSSFRow row1 = sheet.createRow(row);
            ExportExcelUtil.createCell(row1, "项目利润表", 0, headerStyle0);
            ExportExcelUtil.createCell(row1, null, 1, headerStyle0);
            ExportExcelUtil.createCell(row1, null, 2, headerStyle0);
            ExportExcelUtil.createCell(row1, null, 3, headerStyle0);
            ExportExcelUtil.createCell(row1, null, 4, headerStyle0);
            ExportExcelUtil.createCell(row1, null, 5, headerStyle0);
            ExportExcelUtil.createCell(row1, null, 6, headerStyle0);
            ExportExcelUtil.createCell(row1, null, 7, headerStyle0);
            sheet.addMergedRegion(new CellRangeAddress(row, row, 0, 7));

            HSSFRow row2 = sheet.createRow(++row);
            ExportExcelUtil.createCell(row2, null, 0, headerStyle2);
            ExportExcelUtil.createCell(row2, null, 1, headerStyle2);
            ExportExcelUtil.createCell(row2, null, 2, headerStyle2);
            ExportExcelUtil.createCell(row2, null, 3, headerStyle2);
            ExportExcelUtil.createCell(row2, null, 4, headerStyle2);
            ExportExcelUtil.createCell(row2, null, 5, headerStyle2);
            ExportExcelUtil.createCell(row2, null, 6, headerStyle2);
            ExportExcelUtil.createCell(row2, null, 7, headerStyle2);
            sheet.addMergedRegion(new CellRangeAddress(row, row, 0, 7));

            ReportProjectMarginDto item = list.get(0);

            HSSFRow row3 = sheet.createRow(++row);
            sheet.addMergedRegion(new CellRangeAddress(row, row, 0, 1));
            sheet.addMergedRegion(new CellRangeAddress(row, row, 2, 3));
            ExportExcelUtil.createCell(row3, "项目号：" + item.getProjectCode(), 0, headerStyle1);
            ExportExcelUtil.createCell(row3, null, 1, headerStyle1);
            ExportExcelUtil.createCell(row3, "项目名称：" + item.getProjectName(), 2, headerStyle1);
            ExportExcelUtil.createCell(row3, null, 3, headerStyle1);
            ExportExcelUtil.createCell(row3, null, 4, headerStyle1);
            ExportExcelUtil.createCell(row3, null, 5, headerStyle1);
            ExportExcelUtil.createCell(row3, null, 6, headerStyle1);
            ExportExcelUtil.createCell(row3, "更新时间：" + DateUtils.format(item.getLastUpdateTime()), 7, headerStyle1);

            HSSFRow row4 = sheet.createRow(++row);
            ExportExcelUtil.createCell(row4, "期间：" + item.getDeadlinePeriod(), 0, headerStyle1);
            ExportExcelUtil.createCell(row4, "Curent Month（本月）", 2, detailStyle1);
            ExportExcelUtil.createCell(row4, "YTD（当年）", 3, detailStyle1);
            ExportExcelUtil.createCell(row4, "以前年度累计", 4, detailStyle1);
            ExportExcelUtil.createCell(row4, "YTD Project（累计）", 5, detailStyle1);
            ExportExcelUtil.createCell(row4, "基线预算", 6, detailStyle1);
            ExportExcelUtil.createCell(row4, "当前预算", 7, detailStyle1);

            for (ReportProjectMarginDto dto : list) {
                HSSFRow detailRow = sheet.createRow(++row);
                sheet.addMergedRegion(new CellRangeAddress(row, row, 0, 1));
                if (StringUtils.isEmpty(dto.getParentCode())) {
                    ExportExcelUtil.createCell(detailRow, dto.getName(), 0, headerStyle3);
                    ExportExcelUtil.createCell(detailRow, null, 1, headerStyle3);
                    if (Objects.equals(dto.getCode(), "700")) {
                        ExportExcelUtil.createCell(detailRow, bigDecimalRoundRatioFormat(dto.getCurrentMonthCost()), 2, detailStyle2);
                        ExportExcelUtil.createCell(detailRow, bigDecimalRoundRatioFormat(dto.getCurrentYearCost()), 3, detailStyle2);
                        ExportExcelUtil.createCell(detailRow, bigDecimalRoundRatioFormat(dto.getPreviousYearCost()), 4, detailStyle2);
                        ExportExcelUtil.createCell(detailRow, bigDecimalRoundRatioFormat(dto.getTotalCost()), 5, detailStyle2);
                        ExportExcelUtil.createCell(detailRow, bigDecimalRoundRatioFormat(dto.getBaselineCost()), 6, detailStyle2);
                        ExportExcelUtil.createCell(detailRow, bigDecimalRoundRatioFormat(dto.getBudgetCost()), 7, detailStyle2);
                    } else {
                        ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getCurrentMonthCost()), 2, detailStyle2);
                        ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getCurrentYearCost()), 3, detailStyle2);
                        ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getPreviousYearCost()), 4, detailStyle2);
                        ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getTotalCost()), 5, detailStyle2);
                        ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getBaselineCost()), 6, detailStyle2);
                        ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getBudgetCost()), 7, detailStyle2);
                    }
                } else {
                    ExportExcelUtil.createCell(detailRow, dto.getName(), 0, headerStyle2);
                    ExportExcelUtil.createCell(detailRow, null, 1, headerStyle2);
                    ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getCurrentMonthCost()), 2, detailStyle1);
                    ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getCurrentYearCost()), 3, detailStyle1);
                    ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getPreviousYearCost()), 4, detailStyle1);
                    ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getTotalCost()), 5, detailStyle1);
                    ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getBaselineCost()), 6, detailStyle1);
                    ExportExcelUtil.createCell(detailRow, bigDecimalFormat(dto.getBudgetCost()), 7, detailStyle1);
                }
                //计算POC
                if (Objects.equals(dto.getCode(), "500")) {
                    BigDecimal POC = BigDecimal.ZERO;
                    BigDecimal totalCost = dto.getTotalCost();
                    BigDecimal budgetCost = dto.getBudgetCost();
                    if (totalCost != null && budgetCost != null && budgetCost.compareTo(BigDecimal.ZERO) != 0) {
                        POC = totalCost.divide(budgetCost, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                    }
                    ExportExcelUtil.createCell(row4, "POC：" + bigDecimalRoundRatioFormat(POC), 1, headerStyle4);
                }
            }
            row = row + 6;
        }
    }

    private void exportProjectMargin(ExportResponse result, HttpServletResponse response) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        ExportResponse.Sheet sheet = result.getSheetList().get(0);

        if (ListUtils.isNotEmpty(sheet.getDataList())) {
            List<ReportProjectMarginDto> excelVOList = JSONObject.parseArray(sheet.getDataList().toString(), ReportProjectMarginDto.class);
            Map<Long, List<ReportProjectMarginDto>> excelVOMap = excelVOList.stream().collect(Collectors.groupingBy(ReportProjectMarginDto::getProjectId));
            //每1000个项目切割一个Sheet
            Map<Long, List<ReportProjectMarginDto>>[] maps = MapUtil.splitMap(excelVOMap, 1000);
            int num = 1;
            for (Map<Long, List<ReportProjectMarginDto>> map : maps) {
                createSheet(map, workbook, num++);
            }
        }
        ExportExcelUtil.downLoadExcel(result.getFileName(), response, workbook);
    }

    private void exportHroOverspendWarn(ExportResponse result, HttpServletResponse response) {
        List<ExportResponse.Sheet> sheetList = result.getSheetList();
        ExportResponse.Sheet dataSheet = sheetList.get(0);
        List<HroRequirementReportExcelVo> excelVOList = JSONObject.parseArray(dataSheet.getDataList().toString(), HroRequirementReportExcelVo.class);
        int index = 1;
        for (HroRequirementReportExcelVo vo : excelVOList) {
            vo.setIndex(index++);
        }
        ExportExcelUtil.exportExcel(excelVOList, null, "Sheet1", HroRequirementReportExcelVo.class, dataSheet.getSheetName() + ".xls", response);
    }

    private void exportBudgetChange(ExportResponse result, HttpServletResponse response) {
        List<ExportResponse.Sheet> sheetList = result.getSheetList();
        ExportResponse.Sheet dataSheet = sheetList.get(0);
        List<ReportBudgetChangeExcelVo> excelVOList = JSONObject.parseArray(dataSheet.getDataList().toString(), ReportBudgetChangeExcelVo.class);
        ExportExcelUtil.exportExcel(excelVOList, null, "Sheet1", ReportBudgetChangeExcelVo.class, dataSheet.getSheetName() + ".xls", response);
    }

    private void exportPurchaseProgress(ExportResponse result, HttpServletResponse response) {
        Workbook workbook = new HSSFWorkbook();
        for (int i = 0; i < result.getSheetList().size(); i++) {
            ExportResponse.Sheet sheet = result.getSheetList().get(i);
            List<Object> sheetData = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(sheet.getDataList())) {
                for (JSONObject jsonObject : (List<JSONObject>) sheet.getDataList()) {
                    sheetData.add(JSONObject.toJavaObject(jsonObject, sheet.getEntityClass()));
                }
            }
            if (i == 0) {
                workbook = ExportExcelUtil.buildDefaultSheet(sheetData, sheet.getEntityClass(), sheet.getTitle(), sheet.getSheetName(), sheet.getCreateHeader());
            } else {
                ExportExcelUtil.addSheet(workbook, sheetData, sheet.getEntityClass(), sheet.getTitle(), sheet.getSheetName(), sheet.getCreateHeader());
            }
        }
        // 副表描述
        Sheet sheet = workbook.createSheet("计算逻辑说明");
        this.createExplainSheet((HSSFWorkbook) workbook, (HSSFSheet) sheet);
        ExportExcelUtil.downLoadExcel(result.getFileName(), response, workbook);
    }


    private void exportProjectChangeSummary(ExportResponse result, HttpServletResponse response) {
        StringBuffer fileName = new StringBuffer();
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);

        HSSFCellStyle cellRightStyleWithBorder = workbook.createCellStyle();
        cellRightStyleWithBorder.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellRightStyleWithBorder.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellRightStyleWithBorder.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
        cellRightStyleWithBorder.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
        cellRightStyleWithBorder.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
        cellRightStyleWithBorder.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
        cellRightStyleWithBorder.setFont(font);

        HSSFCellStyle cs = workbook.createCellStyle();
        HSSFFont f = workbook.createFont();
        f.setFontName("宋体");
        f.setFontHeightInPoints((short) 10);
        f.setFontHeight((short) 100);
        cs.setFont(f);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        cs.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cs.setWrapText(true);

        List<ExportResponse.Sheet> sheetList = result.getSheetList();
        ExportResponse.Sheet dataSheet = sheetList.get(0);
        if (ListUtils.isNotEmpty(sheetList.get(0).getDataList())) {

            HSSFSheet sheet = workbook.createSheet("变更汇总");
            List<ReportProjectChangeTraceSummaryDto> excelVOList =
                    JSONObject.parseArray(dataSheet.getDataList().toString(), ReportProjectChangeTraceSummaryDto.class);
            this.createProjectChangeSummarySheet(workbook, sheet, excelVOList, cellLeftStyle);


        }
        ExportResponse.Sheet dataSheet1 = sheetList.get(1);
        if (ListUtils.isNotEmpty(sheetList.get(1).getDataList())) {

            HSSFSheet sheet = workbook.createSheet("项目基本信息变更");
            List<ReportProjectChangeBaseInfoDto> baseInfoVOList =
                    JSONObject.parseArray(dataSheet1.getDataList().toString(), ReportProjectChangeBaseInfoDto.class);
            this.createProjectBaseInfoSheet(workbook, sheet, baseInfoVOList, cellLeftStyle);


        }
        ExportResponse.Sheet dataSheet2 = sheetList.get(2);
        if (ListUtils.isNotEmpty(sheetList.get(2).getDataList())) {

            HSSFSheet sheet = workbook.createSheet("里程碑变更");
            List<ReportProjectChangeMilepostDto> milepostVOList =
                    JSONObject.parseArray(dataSheet2.getDataList().toString(), ReportProjectChangeMilepostDto.class);
            this.createProjectChangeMilepostSheet(workbook, sheet, milepostVOList, cellLeftStyle);

        }
        ExportResponse.Sheet dataSheet3 = sheetList.get(3);
        if (ListUtils.isNotEmpty(sheetList.get(3).getDataList())) {

            HSSFSheet sheet = workbook.createSheet("预算变更");
            List<ReportProjectChangeBudgetDto> budgetVOList =
                    JSONObject.parseArray(dataSheet3.getDataList().toString(), ReportProjectChangeBudgetDto.class);
            this.createProjectChangeBudgetSheet(workbook, sheet, budgetVOList, cellLeftStyle);

        }

        fileName.append(dataSheet.getSheetName());
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private void createProjectChangeSummarySheet(HSSFWorkbook workbook, HSSFSheet sheet,
                                                 List<ReportProjectChangeTraceSummaryDto> excelVOList, HSSFCellStyle cellLeftStyle) {

        HSSFCellStyle headerStyle0 = getHeaderCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        HSSFCellStyle headerStyle1 = getHeaderCellStyle(workbook, (short) 11, (byte) 220, (byte) 230, (byte) 241);
        HSSFCellStyle headerStyle2 = getHeaderCellStyle(workbook, (short) 12, (byte) 253, (byte) 233, (byte) 217);
        HSSFCellStyle headerStyle3 = getHeaderCellStyle(workbook, (short) 13, (byte) 235, (byte) 241, (byte) 222);
        HSSFCellStyle headerStyle4 = getHeaderCellStyle(workbook, (short) 14, (byte) 242, (byte) 220, (byte) 219);
        batchSetSummaryColumnWidth(sheet);
        sheet.createFreezePane(0, 2, 0, 2); //固定表头
        // 绘制表头
        HSSFRow row0 = sheet.createRow(0);
        HSSFRow row1 = sheet.createRow(1);

        for (int i = 0; i < 16; i++) {
            ExportExcelUtil.createCell(row0, null, i, headerStyle0);
            ExportExcelUtil.createCell(row1, null, i, headerStyle0);
        }

        int firstCol = 0; // 列开始
        int lastCol = 6; // 列结束
        row0.getCell(firstCol).setCellValue("项目基本信息变更汇总");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("里程碑变更");
        firstCol = lastCol;
        lastCol = lastCol + 3;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("预算变更");
        firstCol = lastCol;
        lastCol = lastCol + 3;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));


        // 绘制表头
        firstCol = 0; // 列开始
        lastCol = 0;//列结束
        row1.getCell(firstCol).setCellValue("项目编号");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目名称");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目经理");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更次数");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更类型");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("最后更新人");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("最后变更审批通过时间");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更次数");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更类型");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("最后更新人");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("最后变更审批通过时间");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更次数");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更类型");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("最后更新人");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("最后变更审批通过时间");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));


        // 绘制数据
        HSSFCellStyle dataStyle0 = getCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        HSSFCellStyle rightDataStyle0 = getCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        rightDataStyle0.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle1 = getCellStyle(workbook, (short) 11, (byte) 220, (byte) 230, (byte) 241);
        HSSFCellStyle dataStyle2 = getCellStyle(workbook, (short) 12, (byte) 253, (byte) 233, (byte) 217);
        dataStyle2.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle3 = getCellStyle(workbook, (short) 13, (byte) 235, (byte) 241, (byte) 222);
        dataStyle3.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle4 = getCellStyle(workbook, (short) 14, (byte) 242, (byte) 220, (byte) 219);
        dataStyle4.setAlignment(XSSFCellStyle.ALIGN_RIGHT);

        int firstRow = 2;
        for (ReportProjectChangeTraceSummaryDto dto : excelVOList) {
            firstCol = 0;
            HSSFRow row = ExportExcelUtil.createCell(sheet, dto.getProjectCode(), firstRow++, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getProjectName(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getProjectManagerName() ? dto.getProjectManagerName() : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getBaseInfoCount() != null ? String.valueOf(dto.getBaseInfoCount()) : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, "0".equals(dto.getBaseInfoType()) ? "基本信息变更" : null, firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, null != dto.getBaseInfoLastName() ? dto.getBaseInfoLastName() : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getBaseInfoLastApprovedTime() != null ?
                    DateUtils.format(dto.getBaseInfoLastApprovedTime(), DateUtils.FORMAT_SHORT) : null, firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, dto.getMilepostCount() != null ? String.valueOf(dto.getMilepostCount()) : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, "1".equals(dto.getMilepostType()) ? "里程碑变更" : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getMilepostLastName() ? dto.getMilepostLastName() : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getMilepostLastApprovedTime() != null ?
                    DateUtils.format(dto.getMilepostLastApprovedTime(), DateUtils.FORMAT_SHORT) : null, firstCol++, dataStyle0);


            ExportExcelUtil.createCell(row, dto.getBudgetCount() != null ? String.valueOf(dto.getBudgetCount()) : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, "2".equals(dto.getBudgetType()) ? "预算变更" : null, firstCol++, rightDataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getBudgetLastName() ? dto.getBudgetLastName() : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getBudgetLastApprovedTime() != null ?
                    DateUtils.format(dto.getBudgetLastApprovedTime(), DateUtils.FORMAT_SHORT) : null, firstCol++, dataStyle0);


        }

    }

    private void createProjectBaseInfoSheet(HSSFWorkbook workbook, HSSFSheet sheet,
                                            List<ReportProjectChangeBaseInfoDto> excelVOList, HSSFCellStyle cellLeftStyle) {

        HSSFCellStyle headerStyle0 = getHeaderCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        HSSFCellStyle headerStyle1 = getHeaderCellStyle(workbook, (short) 11, (byte) 220, (byte) 230, (byte) 241);
        HSSFCellStyle headerStyle2 = getHeaderCellStyle(workbook, (short) 12, (byte) 255, (byte) 255, (byte) 53);
        HSSFCellStyle headerStyle3 = getHeaderCellStyle(workbook, (short) 13, (byte) 235, (byte) 241, (byte) 222);
        HSSFCellStyle headerStyle4 = getHeaderCellStyle(workbook, (short) 14, (byte) 242, (byte) 220, (byte) 219);

        sheet.createFreezePane(0, 2, 0, 2); //固定表头

        batchSetBaseInfoColumnWidth(sheet);
        // 绘制表头
        HSSFRow row0 = sheet.createRow(0);
        HSSFRow row1 = sheet.createRow(1);

        for (int i = 0; i < 25; i++) {
            ExportExcelUtil.createCell(row0, null, i, headerStyle0);
            if (i >= 0 && i < 2) {
                ExportExcelUtil.createCell(row1, null, i, headerStyle0);
            }
            if (i >= 2 && i < 16) {
                ExportExcelUtil.createCell(row1, null, i, headerStyle2);
            }
            if (i >= 16 && i < 25) {
                ExportExcelUtil.createCell(row1, null, i, headerStyle0);
            }

        }
        int firstCol = 0; // 列开始
        int lastCol = 5; // 列结束
        row0.getCell(firstCol).setCellValue("");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("变更后项目基本信息");
        firstCol = lastCol;
        lastCol = lastCol + 9;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("变更前项目基本信息");
        firstCol = lastCol;
        lastCol = lastCol + 8;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));


        // 绘制表头
        firstCol = 0; // 列开始
        lastCol = 0;//列结束
        row1.getCell(firstCol).setCellValue("项目编号");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目名称");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更发起人");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更日期");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更原因");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更原因说明");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后项目名称");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后业务分类");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后项目开始时间");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后项目结束时间");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后项目经理");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后项目财务");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后销售经理");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后方案设计员");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后技术负责人");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后项目概述");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("业务分类");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目开始时间");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目结束时间");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目经理");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目财务");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("销售经理");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("方案设计员");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("技术负责人");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目概述");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));


        // 绘制数据
        HSSFCellStyle dataStyle0 = getCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        HSSFCellStyle rightDataStyle0 = getCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        rightDataStyle0.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle1 = getCellStyle(workbook, (short) 11, (byte) 220, (byte) 230, (byte) 241);
        HSSFCellStyle dataStyle2 = getCellStyle(workbook, (short) 12, (byte) 253, (byte) 233, (byte) 217);
        dataStyle2.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle3 = getCellStyle(workbook, (short) 13, (byte) 235, (byte) 241, (byte) 222);
        dataStyle3.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle4 = getCellStyle(workbook, (short) 14, (byte) 242, (byte) 220, (byte) 219);
        dataStyle4.setAlignment(XSSFCellStyle.ALIGN_RIGHT);

        int firstRow = 2;
        for (ReportProjectChangeBaseInfoDto dto : excelVOList) {
            firstCol = 0;
            HSSFRow row = ExportExcelUtil.createCell(sheet, dto.getProjectCode(), firstRow++, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getProjectName(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, String.valueOf(dto.getChangeSubmitBy()), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getChangeTime() != null ?
                    DateUtils.format(dto.getChangeTime(), DateUtils.FORMAT_SHORT) : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getChangeReason(), firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, dto.getChangeReasonDescribe(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getAfterName() ? dto.getAfterName() : "--", firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, null !=
                    dto.getAfterBusinessType() ? dto.getAfterBusinessType() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getAfterStartTime() ? DateUtils.format(dto.getAfterStartTime(), DateUtils.FORMAT_SHORT) : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getAfterStartTime() ? DateUtils.format(dto.getAfterEndTime(), DateUtils.FORMAT_SHORT) : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getAfterManagerName() ? dto.getAfterManagerName() : "--", firstCol++, dataStyle0);


            ExportExcelUtil.createCell(row, null !=
                    dto.getAfterFinancialName() ? dto.getAfterFinancialName() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getAfterSaleManagerName() ? dto.getAfterSaleManagerName() : "--", firstCol++, rightDataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getAfterDesignerName() ? dto.getAfterDesignerName() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getAfterTechnologyName() ? dto.getAfterTechnologyName() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getAfterProjectDescribe() ? dto.getAfterProjectDescribe() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getBeforeBusinessType() ? dto.getBeforeBusinessType() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getBeforeStartTime() ? DateUtils.format(dto.getBeforeStartTime(), DateUtils.FORMAT_SHORT) : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getBeforeEndTime() ? DateUtils.format(dto.getBeforeEndTime(), DateUtils.FORMAT_SHORT) : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getBeforeManagerName() ? dto.getBeforeManagerName() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getBeforeFinancialName() ? dto.getBeforeFinancialName() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getBeforeSaleManagerName() ? dto.getBeforeSaleManagerName() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getBeforeDesignerName() ? dto.getBeforeDesignerName() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getBeforeTechnologyName() ? dto.getBeforeTechnologyName() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getBeforeProjectDescribe() ? dto.getBeforeProjectDescribe() : "--", firstCol++, dataStyle0);


        }

    }

    private void createProjectChangeMilepostSheet(HSSFWorkbook workbook, HSSFSheet sheet,
                                                  List<ReportProjectChangeMilepostDto> excelVOList, HSSFCellStyle cellLeftStyle) {

        HSSFCellStyle headerStyle0 = getHeaderCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        HSSFCellStyle headerStyle1 = getHeaderCellStyle(workbook, (short) 11, (byte) 220, (byte) 230, (byte) 241);
        HSSFCellStyle headerStyle2 = getHeaderCellStyle(workbook, (short) 12, (byte) 255, (byte) 255, (byte) 0);
        HSSFCellStyle headerStyle3 = getHeaderCellStyle(workbook, (short) 13, (byte) 235, (byte) 241, (byte) 222);
        HSSFCellStyle headerStyle4 = getHeaderCellStyle(workbook, (short) 14, (byte) 242, (byte) 220, (byte) 219);

        sheet.createFreezePane(0, 2, 0, 2); //固定表头

        batchSetMilepostColumnWidth(sheet);
        // 绘制表头
        HSSFRow row0 = sheet.createRow(0);
        HSSFRow row1 = sheet.createRow(1);

        for (int i = 0; i < 16; i++) {
            ExportExcelUtil.createCell(row0, null, i, headerStyle0);

            if (i >= 0 && i < 5) {
                ExportExcelUtil.createCell(row1, null, i, headerStyle0);
            }

            if (i >= 5 && i < 12) {
                ExportExcelUtil.createCell(row1, null, i, headerStyle2);
            }
            if (i >= 12 && i < 16) {
                ExportExcelUtil.createCell(row1, null, i, headerStyle0);
            }

        }


        int firstCol = 0; // 列开始
        int lastCol = 3; // 列结束
        row0.getCell(firstCol).setCellValue("");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("变更后项目里程碑信息");
        firstCol = lastCol;
        lastCol = lastCol + 7;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("变更前项目里程碑信息");
        firstCol = lastCol;
        lastCol = lastCol + 3;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));


        // 绘制表头
        firstCol = 0; // 列开始
        lastCol = 0;//列结束
        row1.getCell(firstCol).setCellValue("项目编号");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目名称");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目经理");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("操作类型");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更发起人");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更日期");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更原因");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("原因说明");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后里程碑名称");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后计划开始日期");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后计划结束日期");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后责任人");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("里程碑名称");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("计划开始日期");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("计划结束日期");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("责任人");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));


        // 绘制数据
        HSSFCellStyle dataStyle0 = getCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        HSSFCellStyle rightDataStyle0 = getCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        rightDataStyle0.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle1 = getCellStyle(workbook, (short) 11, (byte) 220, (byte) 230, (byte) 241);
        HSSFCellStyle dataStyle2 = getCellStyle(workbook, (short) 12, (byte) 253, (byte) 233, (byte) 217);
        dataStyle2.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle3 = getCellStyle(workbook, (short) 13, (byte) 235, (byte) 241, (byte) 222);
        dataStyle3.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle4 = getCellStyle(workbook, (short) 14, (byte) 242, (byte) 220, (byte) 219);
        dataStyle4.setAlignment(XSSFCellStyle.ALIGN_RIGHT);

        int firstRow = 2;
        for (ReportProjectChangeMilepostDto dto : excelVOList) {
            firstCol = 0;
            HSSFRow row = ExportExcelUtil.createCell(sheet, dto.getProjectCode(), firstRow++, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getProjectName(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getProjectManager(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getOperationType(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getChangeSubmitBy(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getChangeTime() != null ?
                    DateUtils.format(dto.getChangeTime(), DateUtils.FORMAT_SHORT) : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getChangeReason(), firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, dto.getChangeReasonDescribe(), firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, null != dto.getAfterName() ? dto.getAfterName() : "--", firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, null != dto.getAfterPlanStartTime() ?
                    DateUtils.format(dto.getAfterPlanStartTime(), DateUtils.FORMAT_SHORT) : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getAfterPlanEndTime() ?
                    DateUtils.format(dto.getAfterPlanEndTime(), DateUtils.FORMAT_SHORT) : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getAfterResponsible() ? String.valueOf(dto.getAfterResponsible()) : "--", firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, null != dto.getBeforeName() ? dto.getBeforeName() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getBeforePlanStartTime() ?
                    DateUtils.format(dto.getBeforePlanStartTime(), DateUtils.FORMAT_SHORT) : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getBeforePlanEndTime() ?
                    DateUtils.format(dto.getBeforePlanEndTime(), DateUtils.FORMAT_SHORT) : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null !=
                    dto.getBeforeResponsible() ? String.valueOf(dto.getBeforeResponsible()) : "--", firstCol++, dataStyle0);


        }

    }


    private void createProjectChangeBudgetSheet(HSSFWorkbook workbook, HSSFSheet sheet,
                                                List<ReportProjectChangeBudgetDto> excelVOList, HSSFCellStyle cellLeftStyle) {

        HSSFCellStyle headerStyle0 = getHeaderCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        HSSFCellStyle headerStyle1 = getHeaderCellStyle(workbook, (short) 11, (byte) 220, (byte) 230, (byte) 241);
        HSSFCellStyle headerStyle2 = getHeaderCellStyle(workbook, (short) 12, (byte) 255, (byte) 255, (byte) 0);
        HSSFCellStyle headerStyle3 = getHeaderCellStyle(workbook, (short) 13, (byte) 235, (byte) 241, (byte) 222);
        HSSFCellStyle headerStyle4 = getHeaderCellStyle(workbook, (short) 14, (byte) 242, (byte) 220, (byte) 219);

        sheet.createFreezePane(0, 2, 0, 2); //固定表头
        batchSetBudgetColumnWidth(sheet);
        // 绘制表头
        HSSFRow row0 = sheet.createRow(0);
        HSSFRow row1 = sheet.createRow(1);

        for (int i = 0; i < 15; i++) {
            ExportExcelUtil.createCell(row0, null, i, headerStyle0);
            if (i >= 0 && i < 3) {
                ExportExcelUtil.createCell(row1, null, i, headerStyle0);
            }
            if (i >= 3 && i < 11) {
                ExportExcelUtil.createCell(row1, null, i, headerStyle2);
            }
            if (i >= 11 && i < 15) {
                ExportExcelUtil.createCell(row1, null, i, headerStyle0);
            }

        }

        int firstCol = 0; // 列开始
        int lastCol = 2; // 列结束
        row0.getCell(firstCol).setCellValue("");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("变更后预算");
        firstCol = lastCol;
        lastCol = lastCol + 7;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("变更前预算");
        firstCol = lastCol;
        lastCol = lastCol + 3;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));


        // 绘制表头
        firstCol = 0; // 列开始
        lastCol = 0;//列结束
        row1.getCell(firstCol).setCellValue("项目编号");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目名称");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目经理");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更发起人");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更日期");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更原因");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("原因说明");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后物料成本");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后人力成本");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后差旅成本");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("变更后非差旅成本");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("物料成本");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("人力成本");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("差旅成本");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("非差旅成本");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol++, lastCol++));


        // 绘制数据
        HSSFCellStyle dataStyle0 = getCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        HSSFCellStyle rightDataStyle0 = getCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        rightDataStyle0.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle1 = getCellStyle(workbook, (short) 11, (byte) 220, (byte) 230, (byte) 241);
        HSSFCellStyle dataStyle2 = getCellStyle(workbook, (short) 12, (byte) 253, (byte) 233, (byte) 217);
        dataStyle2.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle3 = getCellStyle(workbook, (short) 13, (byte) 235, (byte) 241, (byte) 222);
        dataStyle3.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle4 = getCellStyle(workbook, (short) 14, (byte) 242, (byte) 220, (byte) 219);
        dataStyle4.setAlignment(XSSFCellStyle.ALIGN_RIGHT);

        int firstRow = 2;
        for (ReportProjectChangeBudgetDto dto : excelVOList) {
            firstCol = 0;
            HSSFRow row = ExportExcelUtil.createCell(sheet, dto.getProjectCode(), firstRow++, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getProjectName(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getProjectManager(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getChangeSubmitBy(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getChangeTime() != null ?
                    DateUtils.format(dto.getChangeTime(), DateUtils.FORMAT_SHORT) : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, dto.getChangeReason(), firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, dto.getChangeReasonDescribe(), firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, null !=
                    dto.getAfterMaterialCost() ? String.valueOf(dto.getAfterMaterialCost()) : "--", firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, null != dto.getAfterHumanCost() ? dto.getAfterHumanCost().toString() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getAfterTravelCost() ? dto.getAfterTravelCost().toString() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getAfterNotTravelCost() ? dto.getAfterNotTravelCost().toString() : "--", firstCol++, dataStyle0);

            ExportExcelUtil.createCell(row, null != dto.getMaterialCost() ? dto.getMaterialCost().toString() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getHumanCost() ? dto.getHumanCost().toString() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getTravelCost() ? dto.getTravelCost().toString() : "--", firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, null != dto.getNotTravelCost() ? dto.getNotTravelCost().toString() : "--", firstCol++, dataStyle0);

        }

    }

    private void exportProjectMonthly(ExportResponse result, HttpServletResponse response) {
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 11);

        // 设置样式
        HSSFCellStyle baseCellStyle = workbook.createCellStyle();
        baseCellStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        baseCellStyle.setAlignment(XSSFCellStyle.ALIGN_CENTER);
        baseCellStyle.setFont(font);

        List<ExportResponse.Sheet> sheetList = result.getSheetList();
        ExportResponse.Sheet dataSheet = sheetList.get(0);
        HSSFSheet sheet = workbook.createSheet("工程月度报表");
        List<ReportProjectMonthlyDto> excelVOList = JSONObject.parseArray(dataSheet.getDataList().toString(), ReportProjectMonthlyDto.class);
        createProjectMonthlySheet(workbook, sheet, excelVOList, baseCellStyle);
        ExportExcelUtil.downLoadExcel(dataSheet.getSheetName() + ".xls", response, workbook);
    }

    private void createProjectMonthlySheet(HSSFWorkbook workbook, HSSFSheet sheet, List<ReportProjectMonthlyDto> excelVOList, HSSFCellStyle baseCellStyle) {
        String yearMonth = "";
        if (!excelVOList.isEmpty()) {
            yearMonth = excelVOList.get(0).getTheYearMonth();
        }
        fillTitle(workbook, sheet, baseCellStyle, yearMonth);
        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.cloneStyleFrom(baseCellStyle);
        cellStyle.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (int i = 0; i < excelVOList.size(); i++) {
            int col = 0;
            HSSFRow row = sheet.createRow(i + 3);
            ReportProjectMonthlyDto vo = excelVOList.get(i);
            generateCell(row, baseCellStyle, col++, String.valueOf(vo.getNumber()), true);
            generateCell(row, baseCellStyle, col++, vo.getProjectCode(), true);
            generateCell(row, baseCellStyle, col++, vo.getProjectName(), true);
            generateCell(row, baseCellStyle, col++, vo.getCustomerCode(), true);
            generateCell(row, baseCellStyle, col++, vo.getCustomerName(), true);
            generateCell(row, baseCellStyle, col++, vo.getOuName(), true);
            generateCell(row, baseCellStyle, col++, vo.getRegion(), true);
            generateCell(row, baseCellStyle, col++, vo.getProjectType(), true);
            generateCell(row, baseCellStyle, col++, vo.getPriceTypeName(), true);
            generateCell(row, baseCellStyle, col++, vo.getProjectStatusName(), true);
            generateCell(row, baseCellStyle, col++, vo.getContractCode(), true);
            generateCell(row, baseCellStyle, col++, vo.getIndustry(), true);
            generateCell(row, baseCellStyle, col++, vo.getSigningCenter(), true);
            generateCell(row, baseCellStyle, col++, vo.getProjectManager(), true);
            String startTime = null;
            if (vo.getStartTime() != null) {
                startTime = dateFormat.format(vo.getStartTime());
            }
            generateCell(row, baseCellStyle, col++, startTime, true);
            String endTime = null;
            if (vo.getEndTime() != null) {
                endTime = dateFormat.format(vo.getEndTime());
            }
            generateCell(row, baseCellStyle, col++, endTime, true);
            generateCell(row, cellStyle, col++, vo.getContractAmount() == null ? null : vo.getContractAmount().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getInitialProfit() == null ? null : vo.getInitialProfit().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getTotalIncome() == null ? null : vo.getTotalIncome().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getInitialIncome() == null ? null : vo.getInitialIncome().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getAppendIncome() == null ? null : vo.getAppendIncome().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getProjectTotalBudget() == null ? null : vo.getProjectTotalBudget().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getProjectInitialBudget() == null ? null : vo.getProjectInitialBudget().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getProjectChangeBudget() == null ? null : vo.getProjectChangeBudget().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getActualOccurCompletePercent(), true);
            generateCell(row, cellStyle, col++, vo.getActualCarryCompletePercent(), true);
            generateCell(row, cellStyle, col++, vo.getCollectProjectCost() == null ? null : vo.getCollectProjectCost().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getProjectCarryCost() == null ? null : vo.getProjectCarryCost().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthEarlyIncome() == null ? null : vo.getMonthEarlyIncome().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthIncome() == null ? null : vo.getMonthIncome().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getCollectIncome() == null ? null : vo.getCollectIncome().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthEarlyCost() == null ? null : vo.getMonthEarlyCost().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthCost() == null ? null : vo.getMonthCost().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getCollectCost() == null ? null : vo.getCollectCost().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthEarlyGrossProfit() == null ? null : vo.getMonthEarlyGrossProfit().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthGrossProfit() == null ? null : vo.getMonthGrossProfit().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getCollectGrossProfit() == null ? null : vo.getCollectGrossProfit().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthEarlyInvoice() == null ? null : vo.getMonthEarlyInvoice().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthInvoice() == null ? null : vo.getMonthInvoice().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getCollectInvoice() == null ? null : vo.getCollectInvoice().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getCollectInvoicePercent(), true);
            generateCell(row, cellStyle, col++, vo.getNotTaxNotInvoiceReceivable() == null ? null : vo.getNotTaxNotInvoiceReceivable().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getNotTaxInvoiceReceivable() == null ? null : vo.getNotTaxInvoiceReceivable().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthEarlyNotInvoice() == null ? null : vo.getMonthEarlyNotInvoice().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getCollectNotInvoice() == null ? null : vo.getCollectNotInvoice().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthEarlyReceipt() == null ? null : vo.getMonthEarlyReceipt().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthReceipt() == null ? null : vo.getMonthReceipt().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getCollectReceipt() == null ? null : vo.getCollectReceipt().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getCollectReceiptPercent(), true);
            generateCell(row, cellStyle, col++, vo.getMonthEarlyReceivable() == null ? null : vo.getMonthEarlyReceivable().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthEndReceivable() == null ? null : vo.getMonthEndReceivable().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthEarlyPayment() == null ? null : vo.getMonthEarlyPayment().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMonthPayment() == null ? null : vo.getMonthPayment().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getCollectPayment() == null ? null : vo.getCollectPayment().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getLabourContractAmount() == null ? null : vo.getLabourContractAmount().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getMaterialContractAmount() == null ? null : vo.getMaterialContractAmount().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getInnerHrCost() == null ? null : vo.getInnerHrCost().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getProjectCost() == null ? null : vo.getProjectCost().stripTrailingZeros().toPlainString(), true);
            generateCell(row, cellStyle, col++, vo.getProjectNetAmount() == null ? null : vo.getProjectNetAmount().stripTrailingZeros().toPlainString(), true);
        }
    }

    private void fillTitle(HSSFWorkbook workbook, HSSFSheet sheet, HSSFCellStyle baseCellStyle, String yearMonth) {

        HSSFCellStyle yellowCellStyle = generateBgColorCellStyle(workbook, baseCellStyle, (short) 10, (byte) 255, (byte) 255, (byte) 0);
        HSSFCellStyle orangeCellStyle = generateBgColorCellStyle(workbook, baseCellStyle, (short) 11, (byte) 253, (byte) 233, (byte) 217);
        HSSFCellStyle cyanCellStyle = generateBgColorCellStyle(workbook, baseCellStyle, (short) 12, (byte) 235, (byte) 241, (byte) 222);
        HSSFCellStyle pinkCellStyle = generateBgColorCellStyle(workbook, baseCellStyle, (short) 13, (byte) 242, (byte) 220, (byte) 219);

        HSSFRow row0 = sheet.createRow(0);
        HSSFRow row1 = sheet.createRow(1);
        HSSFRow row2 = sheet.createRow(2);

        row0.setHeightInPoints(35);
        row1.setHeightInPoints(16);
        row2.setHeightInPoints(32);

        // 设置列宽
        setCellWidth(sheet);

        HSSFCellStyle cellStyle = yellowCellStyle;
        for (int i = 0; i <= 58; i++) {
            if (i == 1) {
                cellStyle = orangeCellStyle;
            } else if (i == 18) {
                cellStyle = cyanCellStyle;
            } else if (i == 37) {
                cellStyle = pinkCellStyle;
            } else if (i == 51) {
                cellStyle = yellowCellStyle;
            }
            ExportExcelUtil.createCell(row0, null, i, cellStyle);
            ExportExcelUtil.createCell(row1, null, i, cellStyle);
            ExportExcelUtil.createCell(row2, null, i, cellStyle);
        }

        generateCell(row0, null, 0, "月份" + yearMonth, false);
        generateRegionCell(sheet, row0, 0, 0, 1, 17, "项目基本信息");
        generateRegionCell(sheet, row0, 0, 0, 18, 36, "项目营收情况");
        generateRegionCell(sheet, row0, 0, 0, 37, 50, "项目开票收款情况");
        generateRegionCell(sheet, row0, 0, 0, 51, 58, "项目资金使用情况");

        generateRegionCell(sheet, row1, 1, 2, 0, 0, "序号");
        List<String> titles = Arrays.asList("项目编号", "项目名称", "客户编码", "客户名称", "所属主体", "所属区域", "项目类型", "项目属性", "项目状态", "子合同编号", "行业", "签约中心", "项目经理", "开工时间", "竣工时间", "合同金额（含税）", "初始利润额");
        for (int i = 0; i < titles.size(); i++) {
            generateRegionCell(sheet, row1, 1, 2, i + 1, i + 1, titles.get(i));
        }

        generateRegionCell(sheet, row1, 1, 1, 18, 20, "预计合同总收入（不含税）");
        generateRegionCell(sheet, row1, 1, 1, 21, 23, "项目预算");

        titles = Arrays.asList("总收入", "合同初始收入", "变更等追加收入", "当前总预算", "项目初始预算", "项目变更预算");
        for (int i = 0; i < titles.size(); i++) {
            generateCell(row2, null, i + 18, titles.get(i), false);
        }

        titles = Arrays.asList("实际发生完工百分比", "实际已结转完工百分比", "累计实际工程成本", "实际已结转成本");
        for (int i = 0; i < titles.size(); i++) {
            generateRegionCell(sheet, row1, 1, 2, i + 24, i + 24, titles.get(i));
        }

        generateRegionCell(sheet, row1, 1, 1, 28, 30, "已确认合同收入");
        generateRegionCell(sheet, row1, 1, 1, 31, 33, "已确认合同成本");
        generateRegionCell(sheet, row1, 1, 1, 34, 36, "累计已确认毛利");

        titles = Arrays.asList("月初已确认收入", "本月确认收入", "累计确认收入", "月初已确认成本", "本月确认成本", "累计确认成本", "月初已确认毛利", "本月确认毛利", "累计毛利");
        for (int i = 0; i < titles.size(); i++) {
            generateCell(row2, null, i + 28, titles.get(i), false);
        }

        generateRegionCell(sheet, row1, 1, 1, 37, 40, "已开票金额（含税）");
        generateRegionCell(sheet, row1, 1, 1, 43, 44, "未开票情况");
        generateRegionCell(sheet, row1, 1, 1, 45, 47, "累计收取工程款");
        generateRegionCell(sheet, row1, 1, 1, 51, 53, "资金使用");
        generateRegionCell(sheet, row1, 1, 1, 54, 57, "使用资金去向（累计）");

        titles = Arrays.asList("本月初已开票", "本月开票", "月末累计已开票", "月末累计已开票百分比", "未开票应收账款（不含税）", "已开票应收账款（不含税）", "月初未开票", "月末未开票", "月初已收款", "本月收款", "累计收款", "月末累计已收款百分比", "月初应收账款", "月末应收账款", "月初已使用资金", "本月使用资金", "月末使用资金", "劳务分包", "材料设备", "内部工时", "项目费用");
        for (int i = 0; i < titles.size(); i++) {
            generateCell(row2, null, i + 37, titles.get(i), false);
        }
        generateRegionCell(sheet, row1, 1, 2, 58, 58, "项目资金净流量");
    }

    private void setCellWidth(HSSFSheet sheet) {
        sheet.setColumnWidth(0, 8 * 256);
        for (int i = 0; i < 58; i++) {
            sheet.setColumnWidth(i + 1, 16 * 256);
        }
    }

    private HSSFCellStyle generateBgColorCellStyle(HSSFWorkbook workbook, HSSFCellStyle baseCellStyle, short index, byte red, byte green, byte blue) {
        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.cloneStyleFrom(baseCellStyle);
        HSSFPalette palette = workbook.getCustomPalette(); //调色板实例
        palette.setColorAtIndex(index, red, green, blue);
        HSSFColor color = palette.findColor(red, green, blue);
        cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        cellStyle.setFillForegroundColor(color.getIndex());
        cellStyle.setWrapText(true);
        cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        cellStyle.setRightBorderColor(HSSFColor.BLACK.index);
        cellStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        cellStyle.setTopBorderColor(HSSFColor.BLACK.index);
        return cellStyle;
    }

    private void generateCell(Row row, HSSFCellStyle cellStyle, int index, String value, boolean isNew) {
        Cell cell;
        if (isNew) {
            cell = row.createCell(index);
            cell.setCellType(XSSFCell.CELL_TYPE_STRING);
        } else {
            cell = row.getCell(index);
        }
        cell.setCellValue(value);
        if (cellStyle != null) {
            cell.setCellStyle(cellStyle);
        }
    }

    private void generateRegionCell(HSSFSheet sheet, Row row, int firstRow, int lastRow, int firstCol, int lastCol, String value) {
        Cell cell = row.getCell(firstCol);
        cell.setCellValue(value);
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
    }

    private void exportProjectProcessProfit(ExportResponse result, HttpServletResponse response) {
        //导出操作
        StringBuffer fileName = new StringBuffer();
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);

        HSSFCellStyle cellRightStyleWithBorder = workbook.createCellStyle();
        cellRightStyleWithBorder.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellRightStyleWithBorder.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellRightStyleWithBorder.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
        cellRightStyleWithBorder.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
        cellRightStyleWithBorder.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
        cellRightStyleWithBorder.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框
        cellRightStyleWithBorder.setFont(font);

        HSSFCellStyle cs = workbook.createCellStyle();
        HSSFFont f = workbook.createFont();
        f.setFontName("宋体");
        f.setFontHeightInPoints((short) 10);
        f.setFontHeight((short) 100);
        cs.setFont(f);
        cs.setAlignment(CellStyle.ALIGN_CENTER);
        cs.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cs.setWrapText(true);

        List<ExportResponse.Sheet> sheetList = result.getSheetList();
        ExportResponse.Sheet dataSheet = sheetList.get(0);
        List<ReportProjectProcessProfitDTO> excelVOList = new ArrayList<>();
        HSSFSheet sheet = workbook.createSheet("项目进度毛利跟踪表");
        if (ListUtils.isNotEmpty(sheetList.get(0).getDataList())) {
            excelVOList = JSONObject.parseArray(dataSheet.getDataList().toString(), ReportProjectProcessProfitDTO.class);
        }
        this.createSheet(workbook, sheet, excelVOList, cellLeftStyle);
        // 副表描述
        HSSFSheet secondSheet = workbook.createSheet("说明");
        this.createSecondSsheet(workbook, secondSheet, cellLeftStyle);

        fileName.append(dataSheet.getSheetName());
        fileName.append(".xls");
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    private void exportContractReceivableAges(ExportResponse result, HttpServletResponse response) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        for (int i = 0; i < result.getSheetList().size(); i++) {
            ExportResponse.Sheet sheet = result.getSheetList().get(i);
            if (i == 0) {
                List<Map<String, Object>> dataList = sheet.getDataList();
                buildContractReceivableAgesSheet(workbook, dataList, sheet.getSheetName());
            } else {
                List dataList = sheet.getDataList();
                JSONArray jsonArray = new JSONArray();
                jsonArray.addAll(dataList);
                List<Object> detailExcelVOList = jsonArray.toJavaList(sheet.getEntityClass());
                //转List
                ExportExcelUtil.addSheet(workbook, detailExcelVOList, sheet.getEntityClass(), sheet.getTitle(), sheet.getSheetName(), sheet.getCreateHeader());
            }
        }
        // 副表描述
        HSSFSheet sheet = workbook.createSheet("计算逻辑说明");
        this.createRemarkSheet(workbook, sheet);
        ExportExcelUtil.downLoadExcel(result.getFileName(), response, workbook);
    }

    private void buildContractReceivableAgesSheet(HSSFWorkbook workbook, List<Map<String, Object>> dataList, String sheetName) {
        // 设置字体
        HSSFFont font = workbook.createFont();
//        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        titleStyle.setAlignment(CellStyle.ALIGN_CENTER);
        titleStyle.setFont(font);

        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(CellStyle.ALIGN_CENTER);
        cellLeftStyle.setFont(font);

        HSSFSheet newSheet = workbook.createSheet(sheetName);

        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        map.put("code", "子合同编号");
        map.put("name", "子合同名称");
        map.put("parentCode", "主合同编号");
        map.put("parentName", "主合同名称");
        map.put("ouName", "业务实体");

        map.put("unitName", "销售部门");
        map.put("projectType", "业务模式");
        map.put("customerCode", "客户CRM编码");
        map.put("customerName", "客户名称");
        map.put("customerType", "客户属性");

        map.put("projectCode", "项目编号");
        map.put("projectName", "项目名称");
        map.put("projectManager", "项目经理");
        map.put("salesManager", "销售经理");
        map.put("currency", "币种");

        map.put("amount", "子合同含税金额（原币）");
        map.put("excludingTaxAmount", "子合同不含税金额（原币）");
        map.put("conversionRate", "子合同汇率");
        map.put("standardAmount", "子合同含税金额（本位币）");
        map.put("standardExcludingTaxAmount", "子合同不含税金额（本位币）");

        map.put("invoiceAmount", "子合同已开票金额（原币）");
        map.put("receiptAmount", "子合同已收款金额（原币）");
        map.put("receivableAmount", "应收余额（原币）");

        if (CollectionUtils.isNotEmpty(dataList)) {
            String ruleName = (String) dataList.get(0).get("ruleName");
            String[] dynamicFieldArr = ruleName.split("\\|@\\|");
            for (int m = 0; m < dynamicFieldArr.length; m++) {
                map.put(dynamicFieldArr[m], dynamicFieldArr[m]);
            }
        }

        //标题
        LinkedList<String> list = new LinkedList<>();
        Iterator<String> iterator = map.keySet().iterator();
        Row row = newSheet.createRow(0);
        int k = 0;
        while (iterator.hasNext()) {
            String key = iterator.next();
            Cell cell = row.createCell(k);
            cell.setCellValue(map.get(key));
            cell.setCellStyle(titleStyle);
            list.add(key);
            k++;
        }

        JSONArray dataAraay = JSON.parseArray(JSON.toJSONString(dataList));
        if (ListUtils.isNotEmpty(dataList)) {
            ExcelUtil.setJsonData(0, 1, newSheet, list, dataAraay, cellLeftStyle);
        }

        this.setContractReceivableAgesSheetWidth(newSheet);
    }

    private void setContractReceivableAgesSheetWidth(HSSFSheet sheet) {
        // 设置列宽
        sheet.setColumnWidth(0, 15 * 256);
        sheet.setColumnWidth(1, 30 * 256);
        sheet.setColumnWidth(2, 15 * 256);
        sheet.setColumnWidth(3, 30 * 256);
        sheet.setColumnWidth(4, 30 * 256);
        sheet.setColumnWidth(5, 20 * 256);
        sheet.setColumnWidth(6, 20 * 256);
        sheet.setColumnWidth(7, 30 * 256);
        sheet.setColumnWidth(8, 30 * 256);
        sheet.setColumnWidth(9, 10 * 256);
        sheet.setColumnWidth(10, 15 * 256);
        sheet.setColumnWidth(11, 30 * 256);
        sheet.setColumnWidth(12, 15 * 256);
        sheet.setColumnWidth(13, 15 * 256);
        sheet.setColumnWidth(14, 10 * 256);

        sheet.setColumnWidth(15, 20 * 256);
        sheet.setColumnWidth(16, 20 * 256);
        sheet.setColumnWidth(17, 10 * 256);
        sheet.setColumnWidth(18, 20 * 256);
        sheet.setColumnWidth(19, 20 * 256);
        sheet.setColumnWidth(20, 20 * 256);
        sheet.setColumnWidth(21, 20 * 256);
        sheet.setColumnWidth(22, 20 * 256);

        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            HSSFRow row = sheet.getRow(i); //获取单行
            if (row == null) {
                return;
            }
            row.setHeightInPoints(25);
            //遍历列
//            for(int j=0;j<=row.getLastCellNum();j++){
//                HSSFCell cell=row.getCell(j); //获取单列
//                if(cell ==null){
//                    continue;
//                }
//            }
        }

    }

    private void exportPurchaseContractDebtAges(ExportResponse result, HttpServletResponse response) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        ExportResponse.Sheet sheet = result.getSheetList().get(0);
        List<Map<String, Object>> dataList = sheet.getDataList();

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        titleStyle.setAlignment(CellStyle.ALIGN_CENTER);
        titleStyle.setFont(font);

        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(CellStyle.ALIGN_CENTER);
        cellLeftStyle.setFont(font);

        HSSFSheet newSheet = workbook.createSheet(sheet.getSheetName());

        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        map.put("ouName", "业务主体");
        map.put("projectCode", "项目编号");
        map.put("code", "采购合同号");
        map.put("vendorCode", "供应商编码");
        map.put("vendorName", "供应商名称");
        map.put("vendorSiteCode", "供应商地点");
        map.put("currency", "币种");
        map.put("conversionRate", "合同汇率");
        map.put("invoiceAmount", "合同开票不含税金额（原币）");
        map.put("progressAmount", "合同开票进展金额（原币）");
        map.put("standardInvoiceAmount", "合同开票金额（本位币）");
        map.put("standardProgressAmount", "合同开票进展金额（本位币）");
        map.put("remainAmount", "合同进展未开票金额（原币）");
        map.put("standardRemainAmount", "合同进展未开票金额（本位币）");
        map.put("evaluateRemainAmount", "合同进展未开票金额（重估后）");

        if (CollectionUtils.isNotEmpty(dataList)) {
            String ruleName = (String) dataList.get(0).get("ruleName");
            String[] dynamicFieldArr = ruleName.split("\\|@\\|");
            for (int m = 0; m < dynamicFieldArr.length; m++) {
                map.put(dynamicFieldArr[m], dynamicFieldArr[m]);
            }
        }

        //标题
        LinkedList<String> list = new LinkedList<>();
        Iterator<String> iterator = map.keySet().iterator();
        Row row = newSheet.createRow(0);
        int k = 0;
        while (iterator.hasNext()) {
            String key = iterator.next();
            Cell cell = row.createCell(k);
            cell.setCellValue(map.get(key));
            cell.setCellStyle(titleStyle);
            list.add(key);
            k++;
        }

        JSONArray dataAraay = JSON.parseArray(JSON.toJSONString(dataList));
        if (ListUtils.isNotEmpty(dataList)) {
            ExcelUtil.setJsonData(0, 1, newSheet, list, dataAraay, cellLeftStyle);
        }

        this.setPurchaseContractDebtAgesSheetWidth(newSheet);

        ExportExcelUtil.downLoadExcel(result.getFileName(), response, workbook);
    }

    private void setPurchaseContractDebtAgesSheetWidth(HSSFSheet sheet) {
        // 设置列宽
        sheet.setColumnWidth(0, 50 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 20 * 256);
        sheet.setColumnWidth(3, 20 * 256);
        sheet.setColumnWidth(4, 40 * 256);
        sheet.setColumnWidth(5, 20 * 256);
        sheet.setColumnWidth(6, 15 * 256);
        sheet.setColumnWidth(7, 20 * 256);
        sheet.setColumnWidth(8, 30 * 256);
        sheet.setColumnWidth(9, 30 * 256);
        sheet.setColumnWidth(10, 30 * 256);
        sheet.setColumnWidth(11, 30 * 256);
        sheet.setColumnWidth(12, 30 * 256);
        sheet.setColumnWidth(13, 30 * 256);
        sheet.setColumnWidth(14, 30 * 256);

        sheet.setColumnWidth(15, 20 * 256);
        sheet.setColumnWidth(16, 20 * 256);
        sheet.setColumnWidth(17, 20 * 256);
        sheet.setColumnWidth(18, 20 * 256);
        sheet.setColumnWidth(19, 20 * 256);
        sheet.setColumnWidth(20, 20 * 256);
        sheet.setColumnWidth(21, 20 * 256);
        sheet.setColumnWidth(22, 20 * 256);
        sheet.setColumnWidth(23, 20 * 256);
        sheet.setColumnWidth(24, 20 * 256);

        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            HSSFRow row = sheet.getRow(i); //获取单行
            if (row == null) {
                return;
            }
            row.setHeightInPoints(25);
        }
    }

    private void exportProjectContractReceivable(ExportResponse result, HttpServletResponse response) {
        List<ReportExecuteParameter> parameterList = result.getParameterList();
        boolean flag = parameterList.stream()
                .filter(param -> "calculateRule".equals(param.getName()))
                .map(ReportExecuteParameter::getValue)
                .findFirst()
                .map(value -> Objects.equals("1", value))
                .orElse(false);

        HSSFWorkbook workbook = new HSSFWorkbook();
        ExportResponse.Sheet sheet = result.getSheetList().get(0);
        List<Map<String, Object>> dataList = sheet.getDataList();

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        titleStyle.setAlignment(CellStyle.ALIGN_CENTER);
        titleStyle.setFont(font);

        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(CellStyle.ALIGN_CENTER);
        cellLeftStyle.setFont(font);
        HSSFSheet newSheet = workbook.createSheet(sheet.getSheetName());

        List<String[]> titleList = new ArrayList<>();
        titleList.add(new String[]{"number", "序号"});
        titleList.add(new String[]{"projectCode", "项目号"});
        titleList.add(new String[]{"projectName", "项目名称"});
        titleList.add(new String[]{"projectTypeName", "项目类型"});
        titleList.add(new String[]{"projectStatus", "项目状态"});
        titleList.add(new String[]{"customerCode", "CRM客户编码"});
        titleList.add(new String[]{"customerName", "客户名称"});
        titleList.add(new String[]{"businessSegment", "业务分部"});
        titleList.add(new String[]{"ouName", "OU名称"});
        titleList.add(new String[]{"industry", "行业"});
        titleList.add(new String[]{"signingCenter", "签约中心"});
        titleList.add(new String[]{"standardCumulativeRevenue", "项目累计收入-本位币"});
        titleList.add(new String[]{"standardCumulativeTaxInvoicing", "项目累计开票-本位币(含税)"});
        titleList.add(new String[]{"standardCumulativeInvoicing", "项目累计开票-本位币(不含税)"});
        titleList.add(new String[]{"standardCumulativeRepayment", "项目累计回款-本位币(不含税)"});
        titleList.add(new String[]{"standardCumulativeRefund", "累计退款金额-本位币(不含税)"});
        titleList.add(new String[]{"standardBalance", "合同资产(本位币)"});
        titleList.add(new String[]{"currency", "项目合同币种"});
        titleList.add(new String[]{"cumulativeRevenue", "项目累计收入-合同币种"});
        titleList.add(new String[]{"cumulativeTaxInvoicing", "项目累计开票-合同币种(含税)"});
        titleList.add(new String[]{"cumulativeInvoicing", "项目累计开票-合同币种(不含税)"});
        titleList.add(new String[]{"cumulativeRepayment", "项目累计回款-合同币种(不含税)"});
        titleList.add(new String[]{"cumulativeRefund", "累计退款金额-合同币种(不含税)"});
        titleList.add(new String[]{"contractLiabilities", "合同负债-合同币种"});
        titleList.add(new String[]{"projectAccountsTaxReceivable", "项目应收账款-合同币种(含税)"});
        titleList.add(new String[]{"projectAccountsReceivable", "项目应收账款-合同币种(不含税)"});
        titleList.add(new String[]{"balance", "合同资产-合同币种"});

        // 如果flag为true，则移除指定元素
        if (flag) {
            titleList.removeIf(arr -> arr[0].equals("standardCumulativeRepayment"));
            titleList.removeIf(arr -> arr[0].equals("standardCumulativeRefund"));
            titleList.removeIf(arr -> arr[0].equals("cumulativeRepayment"));
            titleList.removeIf(arr -> arr[0].equals("cumulativeRefund"));
            titleList.removeIf(arr -> arr[0].equals("projectAccountsTaxReceivable"));
            titleList.removeIf(arr -> arr[0].equals("projectAccountsReceivable"));
        }
        LinkedHashMap<String, String> map = createMap(titleList);

        if (CollectionUtils.isNotEmpty(dataList)) {
            String ruleName = (String) dataList.get(0).get("ruleName");
            String[] dynamicFieldArr = ruleName.split("\\|@\\|");
            for (int m = 0; m < dynamicFieldArr.length; m++) {
                map.put(dynamicFieldArr[m], dynamicFieldArr[m]);
            }
        }
        //标题
        LinkedList<String> list = new LinkedList<>();
        Iterator<String> iterator = map.keySet().iterator();
        Row row = newSheet.createRow(0);
        int k = 0;
        while (iterator.hasNext()) {
            String key = iterator.next();
            Cell cell = row.createCell(k);
            cell.setCellValue(map.get(key));
            cell.setCellStyle(titleStyle);
            list.add(key);
            k++;
        }
        JSONArray dataAraay = JSON.parseArray(JSON.toJSONString(dataList));
        if (ListUtils.isNotEmpty(dataList)) {
            ExcelUtil.setJsonData(0, 1, newSheet, list, dataAraay, cellLeftStyle);
        }
        this.setProjectContractReceivableSheetWidth(newSheet);
        // 副表描述
        HSSFSheet describeSheet = workbook.createSheet("报表计算公式说明");
        this.createDescSheet(workbook, describeSheet);
        ExportExcelUtil.downLoadExcel(result.getFileName(), response, workbook);
    }

    private static LinkedHashMap<String, String> createMap(List<String[]> list) {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        for (String[] arr : list) {
            map.put(arr[0], arr[1]);
        }
        return map;
    }

    private void setProjectContractReceivableSheetWidth(HSSFSheet sheet) {
        // 设置列宽
        sheet.setColumnWidth(0, 10 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 30 * 256);
        sheet.setColumnWidth(3, 25 * 256);
        sheet.setColumnWidth(4, 20 * 256);
        sheet.setColumnWidth(5, 30 * 256);
        sheet.setColumnWidth(6, 35 * 256);
        sheet.setColumnWidth(7, 30 * 256);
        sheet.setColumnWidth(8, 40 * 256);
        sheet.setColumnWidth(9, 30 * 256);
        sheet.setColumnWidth(10, 25 * 256);
        sheet.setColumnWidth(11, 20 * 256);
        sheet.setColumnWidth(12, 20 * 256);
        sheet.setColumnWidth(13, 20 * 256);
        sheet.setColumnWidth(14, 20 * 256);
        sheet.setColumnWidth(15, 15 * 256);
        sheet.setColumnWidth(16, 25 * 256);
        sheet.setColumnWidth(17, 20 * 256);
        sheet.setColumnWidth(18, 20 * 256);
        sheet.setColumnWidth(19, 20 * 256);
        sheet.setColumnWidth(20, 20 * 256);
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            HSSFRow row = sheet.getRow(i); //获取单行
            if (row == null) {
                return;
            }
            row.setHeightInPoints(25);
        }
    }

    private void createSecondSsheet(HSSFWorkbook workbook, HSSFSheet sheet, HSSFCellStyle cellLeftStyle) {
        int columnIndex = 0;
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 25000);
        cellLeftStyle.setWrapText(true);

        HSSFRow row0 = ExportExcelUtil.createCell(sheet, "字段", 0, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "说明", 1, cellLeftStyle);

        HSSFRow row1 = ExportExcelUtil.createCell(sheet, "里程碑状态", 1, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row1, "完成：里程碑状态为通过\r\n" +
                "预警：里程碑将在15天内超期 \r\n" +
                "超期：已超出计划结束日期\r\n" +
                "进行中：未来15 天不会超期", 1, cellLeftStyle);

        HSSFRow row2 = ExportExcelUtil.createCell(sheet, "目标成本", 2, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row2, "立项或者预立项时的料工费预算", 1, cellLeftStyle);

        HSSFRow row3 = ExportExcelUtil.createCell(sheet, "实际毛利率", 3, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row3, "实际毛利率=【项目金额（不含税）- 已发生成本总额】/ 项目金额（不含税）*100%", 1, cellLeftStyle);

        HSSFRow row4 = ExportExcelUtil.createCell(sheet, "目标毛利率偏差率", 4, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row4, "实际毛利率-目标毛利率", 1, cellLeftStyle);

        HSSFRow row5 = ExportExcelUtil.createCell(sheet, "预算毛利率", 5, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row5, "实际毛利率=【项目金额（不含税）- 当前预算总额】/ 项目金额（不含税）*100%", 1, cellLeftStyle);

        HSSFRow row6 = ExportExcelUtil.createCell(sheet, "预算毛利率偏差", 6, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row6, "实际毛利率-预算毛利率", 1, cellLeftStyle);

        HSSFRow row7 = ExportExcelUtil.createCell(sheet, "项目付款金额", 7, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row7, "项目付款金额=物料（直接采购）+物料（外包）+人工+费用\r\n" +
                "物料（直接采购）=（项目领料已处理成本+差异分摊已处理成本+项目库存待处理成本）*1.13\r\n" +
                "物料（外包）=该项目所关联的已支付成功的付款申请合计的实际付款金额\r\n" +
                "人工=项目成本页面的人工已审核成本\r\n" +
                "费用=项目成本页面的已处理成本", 1, cellLeftStyle);

        HSSFRow row8 = ExportExcelUtil.createCell(sheet, "项目应收余额", 8, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row8, "开票金额-回款金额", 1, cellLeftStyle);

        HSSFRow row9 = ExportExcelUtil.createCell(sheet, "项目逾期金额", 9, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row9, "应收报表中逾期金额汇总", 1, cellLeftStyle);


        HSSFCellStyle redHeaderStyle = getHeaderCellStyle(workbook, HSSFColor.RED.index, (byte) 255, (byte) 0, (byte) 0);
        HSSFRow row10 = ExportExcelUtil.createCell(sheet, "项目及时交付率", 10, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row10, "备注：假设“延误系数”为5%；\r\n" +
                "1、项目及时交付率=（及时交付里程碑数量+Σ延误里程碑数量*延误系数）/计划交付里程碑数量*100%\r\n" +
                "某个被考核里程碑每延误1天，当前里程碑及时率扣除5%，延误20天及以上，扣除至0%，如果提前或者当日完成，则该里程碑及时率为100%。\r\n" +
                "2、计算出延误里程碑数量后，乘以及时率，其他里程碑乘以100%，相加后除以里程碑总数，即加权平均。\r\n" +
                "3、* 单项目交付及时率：某项目考核周期内共考核2个里程碑，其中A里程碑延期6天完成，B里程碑如期完成，则该项目及时率为(1*(1-6*5%)+1*100%)）/2=85%;\r\n" +
                "4、*多项目交付及时率：某项目群考核周期内共考核20个里程碑，其中A里程碑延期10天完成，B里程碑延期16天完成，C里程碑延期6天完成，其余17个里程碑均如期完成，则该项目群及时率为(1*(1-10*5%）+1*(1-16*5%)+1*(1-6*5%)+17*100%)）/20=92%", 1, redHeaderStyle);

        HSSFRow row11 = ExportExcelUtil.createCell(sheet, "考核个数", 11, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row11, "如果里程碑节点的实际/计划结束时间在统计日期区间内，则考核个数+1", 1, cellLeftStyle);
    }

    private void createRemarkSheet(HSSFWorkbook workbook, HSSFSheet sheet) {
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);

        sheet.setColumnWidth(0, 12000);
        cellLeftStyle.setWrapText(true);
        String remark = "1、数据查询逻辑：\r\n" +
                "    a、根据开票申请和退款申请的记账日期，以及回款认领的入账日期进行回款数据统计；\r\n" +
                "    b、逾期账龄，根据“应收到期日”-“截止入账期间”计算出逾期时间区间；\r\n" +
                "2、不支持的数据场景有：\r\n" +
                "    2022年5月认领回款，6月撤回认款，这时候查询“截止入账期间”为2022年5月的数据，\r\n" +
                "    无法查询到5月的回款数据，只能统计到最新的回款数据；";
        sheet.addMergedRegion(new CellRangeAddress(0, 10, 0, 5));
        ExportExcelUtil.createCell(sheet, remark, 0, 0, cellLeftStyle);
    }

    private void createDescSheet(HSSFWorkbook workbook, HSSFSheet sheet) {
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);

        cellLeftStyle.setWrapText(true); // 设置换行
        sheet.setColumnWidth(1, 11000);
        sheet.setColumnWidth(2, 29000);
        HSSFRow row0 = ExportExcelUtil.createCell(sheet, "序号", 0, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "重点字段", 1, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "逻辑说明", 2, cellLeftStyle);

        HSSFRow row1 = ExportExcelUtil.createCell(sheet, "1", 1, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row1, "合同资产-合同币种", 1, cellLeftStyle);
        ExportExcelUtil.createCell(row1, "1、当选择计算逻辑=1时 :收入-开票， 当余额>=0时，按余额显示，当余额<0时按0显示\n" +
                "\n" +
                "2、当选择计算逻辑=2时 : 收入 -max{开票,回款}，按余额显示，当余额<0时按0显示\n" +
                "\n" +
                "收入 = 项目累计收入-合同币种\n" +
                "开票 = 项目累计开票-合同币种（不含税）\n" +
                "回款 = 项目累计回款-合同币种（不含税）- 累计退款金额-合同币种（不含税）", 2, cellLeftStyle);
        row1.setHeight((short) 2400);

        HSSFRow row2 = ExportExcelUtil.createCell(sheet, "2", 2, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row2, "合同负债-合同币种", 1, cellLeftStyle);
        ExportExcelUtil.createCell(row2, "1、当  收入 < 收款  时  ，  合同负债=收款-收入\n" +
                "\n" +
                "2、当  收入 >= 收款  时  ，  合同负债=0   \n" +
                "\n" +
                "收入=“项目累计收入-合同币种”\n" +
                "收款=【项目累计回款-合同币种（不含税）-  累计退款金额-合同币种（不含税）】", 2, cellLeftStyle);
        row2.setHeight((short) 2400);

        HSSFRow row3 = ExportExcelUtil.createCell(sheet, "3", 3, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row3, "项目应收账款-合同币种（含税）", 1, cellLeftStyle);
        ExportExcelUtil.createCell(row3, "1、计算公式：\n" +
                "1）、项目应收账款-合同币种（不含税） *(1+合同税率)，保留2位小数；\n" +
                "2）、合同税率取数逻辑：{（子合同的含税金额-子合同不含税金额）/子合同的不含税金额}  保留2位小数；\n" +
                "2、当“计算逻辑”=“收入-开票”  时    不显示此字段", 2, cellLeftStyle);
        row3.setHeight((short) 1600);

        HSSFRow row4 = ExportExcelUtil.createCell(sheet, "4", 4, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row4, "项目应收账款-合同币种（不含税）", 1, cellLeftStyle);
        ExportExcelUtil.createCell(row4, "1、计算公式：Min(收入，开票）-收款\n" +
                "   当余额>=0时，按余额显示， 当余额<0时按0显示\n" +
                "2、当“计算逻辑”=“收入-开票”  时    不显示此字段\n" +
                "\n" +
                "\n" +
                " 收入=“项目累计收入-合同币种”\n" +
                " 开票=项目累计开票-合同币种(不含税)\n" +
                " 收款=【项目累计回款-合同币种（不含税）-  累计退款金额-合同币种（不含税）】", 2, cellLeftStyle);
        row4.setHeight((short) 2800);

        HSSFRow row5 = ExportExcelUtil.createCell(sheet, "5", 5, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row5, "账龄段：未逾期、账龄段1、账龄段2……", 1, cellLeftStyle);
        ExportExcelUtil.createCell(row5, "本报表的账龄段仅是指“合同资产-合同币种”的账龄", 2, cellLeftStyle);
        row5.setHeight((short) 400);
    }

    private void createExplainSheet(HSSFWorkbook workbook, HSSFSheet sheet) {
        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);

        sheet.setColumnWidth(0, 12000);
        cellLeftStyle.setWrapText(true);
        String remark = "暂无..............................";
        sheet.addMergedRegion(new CellRangeAddress(0, 10, 0, 5));
        ExportExcelUtil.createCell(sheet, remark, 0, 0, cellLeftStyle);
    }

    private void createSheet(HSSFWorkbook workbook, HSSFSheet sheet, List<ReportProjectProcessProfitDTO> excelVOList, HSSFCellStyle cellLeftStyle) {
        HSSFCellStyle headerStyle0 = getHeaderCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        HSSFCellStyle headerStyle1 = getHeaderCellStyle(workbook, (short) 11, (byte) 220, (byte) 230, (byte) 241);
        HSSFCellStyle headerStyle2 = getHeaderCellStyle(workbook, (short) 12, (byte) 253, (byte) 233, (byte) 217);
        HSSFCellStyle headerStyle3 = getHeaderCellStyle(workbook, (short) 13, (byte) 235, (byte) 241, (byte) 222);
        HSSFCellStyle headerStyle4 = getHeaderCellStyle(workbook, (short) 14, (byte) 242, (byte) 220, (byte) 219);
        // 黄色 汇总
        HSSFCellStyle headerStyle5 = getHeaderCellStyle(workbook, (short) 15, (byte) 255, (byte) 255, (byte) 0);

        List<String> milepostNameList = new ArrayList<>();
        int milepostNum = 1;
        if (excelVOList.size() != 0) {
            milepostNameList = Arrays.asList(excelVOList.get(0).getMilepostNameInfo().split("\\|\\|"));
            milepostNum = milepostNameList.size();
        }
        batchSetColumnWidth(sheet, milepostNum);
        sheet.createFreezePane(0, 3, 0, 3); //固定表头
        // 绘制表头
        HSSFRow row0 = sheet.createRow(0);
        HSSFRow row1 = sheet.createRow(1);
        HSSFRow row2 = sheet.createRow(2);
        for (int i = 0; i < 52 + 3 * milepostNum + milepostNum + 2; i++) {
            if (i < 18) {                                                      //项目基本信息（白色）
                ExportExcelUtil.createCell(row0, null, i, headerStyle0);
                ExportExcelUtil.createCell(row1, null, i, headerStyle0);
                ExportExcelUtil.createCell(row2, null, i, headerStyle0);
            } else if (i >= 18 && i < 18 + 3 * milepostNum) {                  //项目执行进度（蓝色）
                ExportExcelUtil.createCell(row0, null, i, headerStyle1);
                ExportExcelUtil.createCell(row1, null, i, headerStyle1);
                ExportExcelUtil.createCell(row2, null, i, headerStyle1);
            } else if (i >= 18 + 3 * milepostNum && i < 34 + 3 * milepostNum) { //成本管控（橙色）
                ExportExcelUtil.createCell(row0, null, i, headerStyle2);
                ExportExcelUtil.createCell(row1, null, i, headerStyle2);
                ExportExcelUtil.createCell(row2, null, i, headerStyle2);
            } else if (i >= 34 + 3 * milepostNum && i < 41 + 3 * milepostNum) { //项目资金概况（绿色）
                ExportExcelUtil.createCell(row0, null, i, headerStyle3);
                ExportExcelUtil.createCell(row1, null, i, headerStyle3);
                ExportExcelUtil.createCell(row2, null, i, headerStyle3);
            } else if (i >= 41 + 3 * milepostNum && i < 52 + 3 * milepostNum) { //项目收入进度（粉色）
                ExportExcelUtil.createCell(row0, null, i, headerStyle4);
                ExportExcelUtil.createCell(row1, null, i, headerStyle4);
                ExportExcelUtil.createCell(row2, null, i, headerStyle4);
            } else {                                                            //交付及时率（黄色）
                ExportExcelUtil.createCell(row0, null, i, headerStyle5);
                ExportExcelUtil.createCell(row1, null, i, headerStyle5);
                ExportExcelUtil.createCell(row2, null, i, headerStyle5);
            }
        }

        // 考核周期
        StringBuilder assessmentCycleStr = new StringBuilder();
        if (excelVOList.size() != 0 && null != excelVOList.get(0).getExecuteId()) {
            String url = String.format("%sstatistics/report/reportExecuteRecord/" + excelVOList.get(0).getExecuteId(), ModelsEnum.STATISTICS.getBaseUrl());
            ReportExecuteRecordDTO executeParameterDto = null;
            try {
                ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
                DataResponse<ReportExecuteRecordDTO> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ReportExecuteRecordDTO>>() {
                });
                if (null != dataResponse.getData()) {
                    executeParameterDto = dataResponse.getData();
                }
            } catch (Exception e) {
                logger.error("获取report_execute_parameter,execute_id=" + excelVOList.get(0).getExecuteId() + "异常", e);
            }
            if (null != executeParameterDto && CollectionUtils.isNotEmpty(executeParameterDto.getExecuteParameters())) {
                ReportExecuteParameter endTimeStartParameter = executeParameterDto.getExecuteParameters().stream().filter(a -> a.getName().equals("endTimeStart")).findFirst().orElse(null);
                ReportExecuteParameter endTimeEndParameter = executeParameterDto.getExecuteParameters().stream().filter(a -> a.getName().equals("endTimeEnd")).findFirst().orElse(null);
                if (null != endTimeEndParameter) {
                    // 拼装例子： 20220901-20220931
                    assessmentCycleStr.append("（");
                    if (null != endTimeStartParameter) {
                        assessmentCycleStr.append(endTimeStartParameter.getValue().replace("-", ""));
                        assessmentCycleStr.append("-");
                    }
                    assessmentCycleStr.append(endTimeEndParameter.getValue().replace("-", ""));
                    assessmentCycleStr.append("）");
                }
            }
        }

        int firstCol = 0; // 列开始
        int lastCol = 17; // 列结束
        row0.getCell(firstCol).setCellValue("项目基本信息");
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("项目执行进度");
        firstCol = lastCol;
        lastCol = lastCol + milepostNum * 3 - 1;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("成本管控");
        firstCol = lastCol;
        lastCol = lastCol + 15;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("项目资金概况");
        firstCol = lastCol;
        lastCol = lastCol + 6;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("项目收入进度");
        firstCol = lastCol;
        lastCol = lastCol + 10;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol++));

        row0.getCell(lastCol).setCellValue("交付及时率" + assessmentCycleStr);
        firstCol = lastCol;
        lastCol = lastCol + milepostNum + 2;
        sheet.addMergedRegion(new CellRangeAddress(0, 0, firstCol, lastCol - 1));

        // 绘制表头
        firstCol = 0; // 列开始
        lastCol = 0;//列结束
        row1.getCell(firstCol).setCellValue("项目编号");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目名称");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("合同编号");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("业务分类");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目类型");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("客户名称");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目等级");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目财务");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目经理");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("销售");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("方案");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("技术负责人");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目状态");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("当前里程碑");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目合同币种");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目金额(不含税)-原币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目金额(不含税)-本位币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        row1.getCell(firstCol).setCellValue("项目开始日期");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        for (String milepostName : milepostNameList) {
            row1.getCell(firstCol).setCellValue(milepostName);

            row2.getCell(firstCol++).setCellValue("状态");
            row2.getCell(firstCol++).setCellValue("计划结束日期");
            row2.getCell(firstCol++).setCellValue("实际结束日期");
            sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol - 3, firstCol - 1));
        }

        row1.getCell(firstCol).setCellValue("目标成本");
        row2.getCell(firstCol++).setCellValue("物料目标成本");
        row2.getCell(firstCol++).setCellValue("人力目标成本");
        row2.getCell(firstCol++).setCellValue("差旅费目标成本");
        row2.getCell(firstCol++).setCellValue("非差旅费目标成本");
        row2.getCell(firstCol++).setCellValue("目标成本总额");
        row2.getCell(firstCol++).setCellValue("目标毛利率");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol - 6, firstCol - 1));

        row1.getCell(firstCol).setCellValue("已发生成本");
        row2.getCell(firstCol++).setCellValue("物料成本");
        row2.getCell(firstCol++).setCellValue("人力成本");
        row2.getCell(firstCol++).setCellValue("差旅成本");
        row2.getCell(firstCol++).setCellValue("非差旅成本");
        row2.getCell(firstCol++).setCellValue("成本总额");
        sheet.addMergedRegion(new CellRangeAddress(1, 1, firstCol - 5, firstCol - 1));

        lastCol = firstCol;
        row1.getCell(firstCol).setCellValue("实际毛利率");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("目标毛利率偏差率");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("当前预算总额");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("预算毛利率");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("预算毛利偏差率");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("收款币种");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("项目收款金额-原币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("项目收款金额-本位币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("项目付款金额");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("项目现金流-本位币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("项目应收金额-原币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("项目逾期金额-原币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("已结转收入(不含税)-原币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("已结转收入比");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("已结转收入(不含税)-本位币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("已结转成本(不含税)");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("已结转毛利额-本位币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("开票币种");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("开票金额(含税）-原币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("开票金额(不含税）-原币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("开票金额(含税）-本位币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        row1.getCell(firstCol).setCellValue("开票金额(不含税）-本位币");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));
        // 交付及时率开始列
        int row4firstCol = firstCol;
        row1.getCell(firstCol).setCellValue("开票进度");
        sheet.addMergedRegion(new CellRangeAddress(1, 2, firstCol++, lastCol++));

        for (String milepostName : milepostNameList) {
            row1.getCell(firstCol).setCellValue(milepostName);
            row2.getCell(firstCol++).setCellValue("延迟天数");
        }
        row1.getCell(firstCol).setCellValue("考核数");
        row2.getCell(firstCol++).setCellValue("个数");
        // 交付及时率结束列行
        int row4lastCol = firstCol;
        row1.getCell(firstCol).setCellValue("项目及时率");
        row2.getCell(firstCol++).setCellValue("完成值");

        // 绘制数据
        HSSFCellStyle dataStyle0 = getCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        HSSFCellStyle rightDataStyle0 = getCellStyle(workbook, (short) 10, (byte) 255, (byte) 255, (byte) 255);
        rightDataStyle0.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle1 = getCellStyle(workbook, (short) 11, (byte) 220, (byte) 230, (byte) 241);
        HSSFCellStyle dataStyle2 = getCellStyle(workbook, (short) 12, (byte) 253, (byte) 233, (byte) 217);
        dataStyle2.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle3 = getCellStyle(workbook, (short) 13, (byte) 235, (byte) 241, (byte) 222);
        dataStyle3.setAlignment(XSSFCellStyle.ALIGN_RIGHT);
        HSSFCellStyle dataStyle4 = getCellStyle(workbook, (short) 14, (byte) 242, (byte) 220, (byte) 219);
        dataStyle4.setAlignment(XSSFCellStyle.ALIGN_RIGHT);

        int firstRow = 3;
        for (ReportProjectProcessProfitDTO profit : excelVOList) {
            firstCol = 0;
            HSSFRow row = ExportExcelUtil.createCell(sheet, profit.getProjectCode(), firstRow++, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getProjectName(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getContractCode(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getUnitName(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getProjectType(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getCustomerName(), firstCol++, dataStyle0);
            String projectLevel = "";
            if (profit.getProjectLevel() != null) { // 项目等级
                projectLevel = ProjectLevelEnums.getCodeByLevel(profit.getProjectLevel());
            }
            ExportExcelUtil.createCell(row, projectLevel, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getFinancial(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getProjectManager(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getSalesManager(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getPlanDesignerName(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getTechnologyLeaderName(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getProjectStatus() != null ? ProjectStatus.getValue(profit.getProjectStatus()) : null, firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getCurrentMilepost(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getCurrency(), firstCol++, dataStyle0);
            ExportExcelUtil.createCell(row, profit.getProjectAmount() != null ? profit.getProjectAmount().stripTrailingZeros().toPlainString() : null, firstCol++, rightDataStyle0);
            ExportExcelUtil.createCell(row, profit.getProjectContractStandardAmount() != null ? profit.getProjectContractStandardAmount().stripTrailingZeros().toPlainString() : null, firstCol++, rightDataStyle0);
            ExportExcelUtil.createCell(row, profit.getStartDate() != null ? DateUtils.format(profit.getStartDate(), DateUtils.FORMAT_SHORT) : null, firstCol++, dataStyle0);
            // 里程碑信息
            for (int i = 0; i < milepostNameList.size(); i++) {
                String milepostStatusInfo = profit.getMilepostStatusInfo() != null ? profit.getMilepostStatusInfo() : "";
                String milepostEndDateInfo = profit.getMilepostEndDateInfo() != null ? profit.getMilepostEndDateInfo() : "";
                String milepostActualEndTimeInfo = profit.getMilepostActualEndTimeInfo() != null ? profit.getMilepostActualEndTimeInfo() : "";

                List<String> milepostStatusList = Arrays.asList(milepostStatusInfo.split("\\|\\|"));
                List<String> milepostEndDateInfoList = Arrays.asList(milepostEndDateInfo.split("\\|\\|"));
                List<String> milepostActualEndTimeInfoList = Arrays.asList(milepostActualEndTimeInfo.split("\\|\\|"));

                ExportExcelUtil.createCell(row, milepostStatusList.size() > i ? milepostStatusList.get(i) : null, firstCol++, dataStyle1);
                ExportExcelUtil.createCell(row, milepostEndDateInfoList.size() > i ? milepostEndDateInfoList.get(i) : null, firstCol++, dataStyle1);
                ExportExcelUtil.createCell(row, milepostActualEndTimeInfoList.size() > i ? milepostActualEndTimeInfoList.get(i) : null, firstCol++, dataStyle1);
            }
            ExportExcelUtil.createCell(row, profit.getInitMaterialTargetCost() != null ? profit.getInitMaterialTargetCost().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getInitHumanTargetCost() != null ? profit.getInitHumanTargetCost().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getInitTravelTargetCost() != null ? profit.getInitTravelTargetCost().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getInitOtherTargetCost() != null ? profit.getInitOtherTargetCost().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getInitTotalTargetCost() != null ? profit.getInitTotalTargetCost().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getTargetCostGrassProfitRatio() != null ?
                    profit.getTargetCostGrassProfitRatio().stripTrailingZeros().toPlainString() + "%" : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getMaterialCost() != null ? profit.getMaterialCost().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getHumanCost() != null ? profit.getHumanCost().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getTravelCost() != null ? profit.getTravelCost().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getFeeCost() != null ? profit.getFeeCost().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getTotalCost() != null ? profit.getTotalCost().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getActualGrassProfitRatio() != null ?
                    profit.getActualGrassProfitRatio().stripTrailingZeros().toPlainString() + "%" : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getTargetCostDeviationRate() != null ? profit.getTargetCostDeviationRate().stripTrailingZeros().toPlainString() + "%" : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getBudgetCost() != null ? profit.getBudgetCost().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getBudgetProfitRatio() != null ?
                    profit.getBudgetProfitRatio().stripTrailingZeros().toPlainString() + "%" : null, firstCol++, dataStyle2);
            ExportExcelUtil.createCell(row, profit.getBudgetDeviationRate() != null ? profit.getBudgetDeviationRate().stripTrailingZeros().toPlainString() + "%" : null, firstCol++, dataStyle2);

            ExportExcelUtil.createCell(row, profit.getReceiptCurrency(), firstCol++, dataStyle3);
            ExportExcelUtil.createCell(row, profit.getReceiptClaimAmount() != null ? profit.getReceiptClaimAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle3);
            ExportExcelUtil.createCell(row, profit.getStandardReceiptClaimAmount() != null ? profit.getStandardReceiptClaimAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle3);
            ExportExcelUtil.createCell(row, profit.getPaymentAmount() != null ? profit.getPaymentAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle3);
            ExportExcelUtil.createCell(row, profit.getProjectCash() != null ? profit.getProjectCash().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle3);
            ExportExcelUtil.createCell(row, profit.getReceivableAmount() != null ? profit.getReceivableAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle3);
            ExportExcelUtil.createCell(row, profit.getPreviewAmount() != null ? profit.getPreviewAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle3);

            ExportExcelUtil.createCell(row, profit.getCarryoverIncomeAmount() != null ? profit.getCarryoverIncomeAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle4);
            ExportExcelUtil.createCell(row, profit.getCarryoverIncomeRatio() != null ?
                    profit.getCarryoverIncomeRatio().stripTrailingZeros().toPlainString() + "%" : null, firstCol++, dataStyle4);
            ExportExcelUtil.createCell(row, profit.getStandardCarryoverIncomeAmount() != null ? profit.getStandardCarryoverIncomeAmount().setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle4);
            ExportExcelUtil.createCell(row, profit.getCarryoverCostAmount() != null ? profit.getCarryoverCostAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle4);
            ExportExcelUtil.createCell(row, profit.getCarryoverCostGrassAmount() != null ? profit.getCarryoverCostGrassAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle4);
            ExportExcelUtil.createCell(row, profit.getInvoiceCurrency(), firstCol++, dataStyle4);
            ExportExcelUtil.createCell(row, profit.getInvoiceAmount() != null ? profit.getInvoiceAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle4);
            ExportExcelUtil.createCell(row, profit.getExclusiveInvoiceAmount() != null ? profit.getExclusiveInvoiceAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle4);
            ExportExcelUtil.createCell(row, profit.getStandardInvoiceAmount() != null ? profit.getStandardInvoiceAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle4);
            ExportExcelUtil.createCell(row, profit.getStandardExclusiveInvoiceAmount() != null ? profit.getStandardExclusiveInvoiceAmount().stripTrailingZeros().toPlainString() : null, firstCol++, dataStyle4);
            ExportExcelUtil.createCell(row, profit.getInvoiceRatio() != null ?
                    profit.getInvoiceRatio().stripTrailingZeros().toPlainString() + "%" : null, firstCol++, dataStyle4);

            // 项目及时率
            for (int i = 0; i < milepostNameList.size(); i++) {
                String delayDayInfo = profit.getDelayDayInfo() != null ? profit.getDelayDayInfo() : "";
                List<String> delayDayInfoList = Arrays.asList(delayDayInfo.split("\\|\\|"));
                ExportExcelUtil.createCell(row, delayDayInfoList.size() > i ? delayDayInfoList.get(i) : null, firstCol++, headerStyle5);
            }
            if (null != profit.getAssessmentNum()) {
                ExportExcelUtil.createCell(row, profit.getAssessmentNum().stripTrailingZeros().toPlainString(), firstCol++, headerStyle5);
            } else {
                ExportExcelUtil.createCell(row, "0", firstCol++, headerStyle5);
            }

            ExportExcelUtil.createCell(row, profit.getAssessmentPercent() != null ?
                    profit.getAssessmentPercent().stripTrailingZeros().toPlainString() + "%" : null, firstCol++, headerStyle5);
        }

        // 汇总
        HSSFRow row4 = sheet.createRow(excelVOList.size() + 3);
        for (int i = row4firstCol; i <= row4lastCol; i++) {
            // 背景色
            ExportExcelUtil.createCell(row4, null, i, headerStyle5);
        }
        ExportExcelUtil.createCell(row4, "汇总", row4firstCol, headerStyle5);
        if (CollectionUtils.isNotEmpty(excelVOList) && null != excelVOList.get(0).getAssessmentNumTotal()) {
            ExportExcelUtil.createCell(row4, excelVOList.get(0).getAssessmentNumTotal().stripTrailingZeros().toPlainString(), row4lastCol - 1, headerStyle5);
            ExportExcelUtil.createCell(row4, excelVOList.get(0).getAssessmentPercentTotal() != null ? excelVOList.get(0).getAssessmentPercentTotal().stripTrailingZeros().toPlainString() + "%" : null, row4lastCol, headerStyle5);
        } else {
            ExportExcelUtil.createCell(row4, "0", row4lastCol - 1, headerStyle5);
        }
    }

    private HSSFCellStyle getHeaderCellStyle(HSSFWorkbook workbook, short index, byte red, byte green, byte blue) {
        HSSFPalette palette = workbook.getCustomPalette(); //调色板实例
        palette.setColorAtIndex(index, red, green, blue);
        HSSFColor color = palette.findColor(red, green, blue);
        // 设置字体
        HSSFFont boldFont = workbook.createFont();
        boldFont.setFontName("宋体");
        boldFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);//粗体显示
        boldFont.setFontHeightInPoints((short) 10);

        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        cellStyle.setFillForegroundColor(color.getIndex());
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中
        cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
        cellStyle.setFont(boldFont);
        cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        cellStyle.setRightBorderColor(HSSFColor.BLACK.index);
        cellStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        cellStyle.setTopBorderColor(HSSFColor.BLACK.index);
        return cellStyle;
    }

    private HSSFCellStyle getCellStyle(HSSFWorkbook workbook, short index, byte red, byte green, byte blue) {
        HSSFPalette palette = workbook.getCustomPalette(); //调色板实例
        palette.setColorAtIndex(index, red, green, blue);
        HSSFColor color = palette.findColor(red, green, blue);

        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        cellStyle.setFillForegroundColor(color.getIndex());
        cellStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellStyle.setLeftBorderColor(HSSFColor.BLACK.index);
        cellStyle.setRightBorderColor(HSSFColor.BLACK.index);
        cellStyle.setBottomBorderColor(HSSFColor.BLACK.index);
        cellStyle.setTopBorderColor(HSSFColor.BLACK.index);

        return cellStyle;
    }

    private void batchSetSummaryColumnWidth(HSSFSheet sheet) {
        int columnIndex = 0;
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 7500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
    }

    private void batchSetBaseInfoColumnWidth(HSSFSheet sheet) {
        int columnIndex = 0;
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 7500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);

        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
    }

    private void batchSetMilepostColumnWidth(HSSFSheet sheet) {
        int columnIndex = 0;
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 7500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 7500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 7500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
    }

    private void batchSetBudgetColumnWidth(HSSFSheet sheet) {
        int columnIndex = 0;
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 7500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 7500);
        sheet.setColumnWidth(columnIndex++, 5000);
        sheet.setColumnWidth(columnIndex++, 5000);
        sheet.setColumnWidth(columnIndex++, 5000);
        sheet.setColumnWidth(columnIndex++, 5000);
        sheet.setColumnWidth(columnIndex++, 4000);
        sheet.setColumnWidth(columnIndex++, 4000);
        sheet.setColumnWidth(columnIndex++, 4000);
        sheet.setColumnWidth(columnIndex++, 4000);
    }

    private void batchSetColumnWidth(HSSFSheet sheet, int milepostNum) {
        int columnIndex = 0;
        //项目基本信息（白色）
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 7500);
        sheet.setColumnWidth(columnIndex++, 3500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 7500);
        sheet.setColumnWidth(columnIndex++, 2500);
        sheet.setColumnWidth(columnIndex++, 2500);
        sheet.setColumnWidth(columnIndex++, 2500);
        sheet.setColumnWidth(columnIndex++, 2500);
        sheet.setColumnWidth(columnIndex++, 3000);
        sheet.setColumnWidth(columnIndex++, 4000);
        sheet.setColumnWidth(columnIndex++, 4000);
        sheet.setColumnWidth(columnIndex++, 5000);
        sheet.setColumnWidth(columnIndex++, 4000);
        sheet.setColumnWidth(columnIndex++, 6500);
        sheet.setColumnWidth(columnIndex++, 6500);
        sheet.setColumnWidth(columnIndex++, 4500);
        //项目执行进度（蓝色）
        for (int i = 0; i < milepostNum; i++) {
            sheet.setColumnWidth(columnIndex++, 2500);
            sheet.setColumnWidth(columnIndex++, 4500);
            sheet.setColumnWidth(columnIndex++, 4500);
        }
        //成本管控（橙色）
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 4500);
        //项目资金概况（绿色）
        sheet.setColumnWidth(columnIndex++, 3000);
        sheet.setColumnWidth(columnIndex++, 5500);
        sheet.setColumnWidth(columnIndex++, 5500);
        sheet.setColumnWidth(columnIndex++, 5500);
        sheet.setColumnWidth(columnIndex++, 5500);
        sheet.setColumnWidth(columnIndex++, 5500);
        sheet.setColumnWidth(columnIndex++, 5500);
        //项目收入进度（粉色）
        sheet.setColumnWidth(columnIndex++, 6500);
        sheet.setColumnWidth(columnIndex++, 4500);
        sheet.setColumnWidth(columnIndex++, 7000);
        sheet.setColumnWidth(columnIndex++, 5500);
        sheet.setColumnWidth(columnIndex++, 6000);
        sheet.setColumnWidth(columnIndex++, 3000);
        sheet.setColumnWidth(columnIndex++, 6500);
        sheet.setColumnWidth(columnIndex++, 6500);
        sheet.setColumnWidth(columnIndex++, 6500);
        sheet.setColumnWidth(columnIndex++, 6500);
        sheet.setColumnWidth(columnIndex++, 3000);
    }

    private void generateForm(HSSFCellStyle cellLeftStyle, HSSFSheet sheet, String monthStr, List<ReportIncomeCalculateExcelVO> ReportList) {

        sheet.setColumnWidth(0, 4500);
        sheet.setColumnWidth(1, 4500);
        sheet.setColumnWidth(2, 4500);
        sheet.setColumnWidth(3, 4500);
        sheet.setColumnWidth(4, 4500);
        sheet.setColumnWidth(5, 4500);
        sheet.setColumnWidth(6, 4500);
        sheet.setColumnWidth(7, 4500);
        sheet.setColumnWidth(8, 4500);
        sheet.setColumnWidth(9, 4500);
        sheet.setColumnWidth(10, 4500);
        sheet.setColumnWidth(11, 4500);
        sheet.setColumnWidth(12, 4500);
        sheet.setColumnWidth(13, 4500);
        sheet.setColumnWidth(14, 4500);
        sheet.setColumnWidth(15, 4500);


        Map<String, List<ReportIncomeCalculateExcelVO>> map =
                ReportList.stream().collect(Collectors.groupingBy(reportDto -> fetchGroupKey(reportDto)));

        Set<Long> productOgrIdList = new HashSet<>();
        ReportList.forEach(calculateReortExcelDto -> {
            productOgrIdList.add(calculateReortExcelDto.getProductOrgId());
        });

        HSSFRow row0 = ExportExcelUtil.createCell(sheet, "事业部", 0, 0, cellLeftStyle);
        ExportExcelUtil.createCell(row0, "产品", 1, cellLeftStyle);
        int index = 2;
        String[] monthArr = monthStr.split(",");
        for (String month : monthArr) {
            ExportExcelUtil.createCell(row0, month + "(内部)", index, cellLeftStyle);
            ExportExcelUtil.createCell(row0, month + "(外部)", index += 1, cellLeftStyle);
            index++;
        }

        int n = 1;
        for (Long productOrgId : productOgrIdList) {
            if (productOrgId == null) {
                continue;
            }
            HSSFRow row_n = null;
            int idx = 2;
            Boolean iscreate = false;
            for (String month : monthArr) {
                String unq = productOrgId + "," + month;
                if (map.get(unq) == null) {
                    idx += 2;
                    continue;
                }
                if (!iscreate) {
                    row_n = ExportExcelUtil.createCell(sheet, map.get(unq).get(0).getDepartmentName(), n, 0, cellLeftStyle);
                    ExportExcelUtil.createCell(row_n, map.get(unq).get(0).getProductOrgName(), 1, cellLeftStyle);
                    iscreate = true;
                }
                if (map.containsKey(unq)) {
                    ExportExcelUtil.createCell(row_n, String.valueOf(map.get(unq).get(0).getInnerIncome()
                            .setScale(2, BigDecimal.ROUND_HALF_UP)), idx, cellLeftStyle);
                    ExportExcelUtil.createCell(row_n, String.valueOf(map.get(unq).get(0).getOutterIncome()
                            .setScale(2, BigDecimal.ROUND_HALF_UP)), idx += 1, cellLeftStyle);
                    idx++;
                }
            }
            n++;
        }
    }

    private String fetchGroupKey(ReportIncomeCalculateExcelVO excelVO) {
        if (StringUtils.isEmpty(excelVO.getKeyword())) {
            return "-1";
        }
        return excelVO.getKeyword();
    }

    @ApiOperation("查询执行信息详情")
    @GetMapping("reportExecuteRecord/{executeId}")
    public Response getReportExecuteRecordById(@PathVariable Long executeId) {
        String url = String.format("%sstatistics/report/reportExecuteRecord/" + executeId, ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<ReportExecuteRecordDTO>>() {
        });
    }

    @ApiOperation("分页查询执行结果（个人）")
    @GetMapping("pageReportExecuteRecord/personal")
    public Response pageReportExecuteRecord(@ApiParam("运行状态，多个用,隔开") @RequestParam(required = false) final String statusesStr,
                                            @ApiParam("报表名称") @RequestParam(required = false) final String reportName,
                                            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("statusesStr", statusesStr);
        paramMap.put("reportName", reportName);
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);

//        CurrentContext currentContext = CacheDataUtils.getContextByMip(PamCurrentUserUtil.getCurrentUserName());
//        final Long companyId = currentContext.getCurrentOrgId();
//        UserInfo userInfo = CacheDataUtils.findUserByMip(currentContext.getLoginUserName());
//        paramMap.put("companyId", companyId);
//        paramMap.put("userId", userInfo.getId());

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/pageReportExecuteRecord/personal", paramMap);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ReportExecuteRecordDTO>>>() {
        });
    }

    @ApiOperation("分页查询执行结果（全局）")
    @GetMapping("pageReportExecuteRecord")
    public Response pageReportExecuteRecord(@ApiParam("运行状态，多个用,隔开") @RequestParam(required = false) final String statusesStr,
                                            @ApiParam("报表名称") @RequestParam(required = false) final String reportName,
                                            @ApiParam("用户mip账号") @RequestParam(required = false) final String userMip,
                                            @ApiParam("运行编号") @RequestParam(required = false) final String code,
                                            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("statusesStr", statusesStr);
        paramMap.put("reportName", reportName);
        paramMap.put("userMip", userMip);
        paramMap.put("code", code);
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/pageReportExecuteRecord", paramMap);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ReportExecuteRecordDTO>>>() {
        });
    }

    @ApiOperation("分页查询用户未授权报表")
    @GetMapping("pageNoGrantReportGroup")
    public Response pageNoGrantReportGroup(@ApiParam("用户ID") @RequestParam final Long userId,
                                           @RequestParam(required = false) final String name,
                                           @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                           @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", userId);
        paramMap.put("reportGroupName", name);
        paramMap.put("pageNum", pageNum);
        paramMap.put("pageSize", pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/report/pageNoGrantReportGroup", paramMap);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<ReportGroupInfoDTO>>>() {
        });
    }

    //汇总报表导出
    public void exportCountSwapData(HttpServletResponse response, JSONArray list) {
        if (list != null) {
            StringBuffer fileName = new StringBuffer();
            fileName.append("内部资源调剂汇总_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
            fileName.append(".xls");

            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            map.put("division", "事业部");
            map.put("produectDepartment", "产品部门");
            map.put("contractAmount", "当月调整");
            map.put("accruedContractAmount", "累计调整");
            map.put("leftSwapReceiptAmount", "当月调整");
            map.put("accruedLeftSwapReceiptAmount", "累计调整");
            map.put("swapExecuteTotalAmount", "当月调整");
            map.put("accruedSwapExecuteTotalAmount", "累计调整");

            map.put("memberSwapCost", "当月调整");
            map.put("accruedMemberSwapCost", "累计调整");
            map.put("feeTotalCost", "当月调整");
            map.put("accruedFeeTotalCost", "累计调整");

            String[] title2 = {"事业部", "产品部门", "合同", "合同", "回款", "回款", "收入", "收入", "人工成本", "人工成本", "费用", "费用"};
            Workbook workbook = ExcelUtil.writeExcel(list, map, "内部资源调剂汇总", "内部资源调剂汇总", null, title2, fileName.toString());
            //ExcelUtil.mergedRegion(workbook,0,0,1,2,null);//合并事业部标题
            ExcelUtil.mergedRegion(workbook, 0, 1, 1, 2, null);//产品部门标题
            ExcelUtil.mergedRegion(workbook, 0, 0, 1, list.size() + 2, null);
            ExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
        }
    }

    private String bigDecimalFormat(BigDecimal b) {
        if (b == null) {
            return "-";
        } else {
            return b.stripTrailingZeros().toPlainString();
        }
    }

    private String bigDecimalRoundFormat(BigDecimal b) {
        if (b == null) {
            return "-";
        } else {
            return b.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        }
    }

    private String bigDecimalRoundRatioFormat(BigDecimal b) {
        if (b == null) {
            return "-";
        } else {
            return b.setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "%";
        }
    }
}
