package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.excelVo.MaterialCrossOrganizationalExcelVO;
import com.midea.pam.common.statistics.excelVo.MaterialExcelVO;
import com.midea.pam.common.statistics.query.MaterialQuery;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.Utils;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/21
 * @description
 */
@Api("统计-物料查询")
@RestController
@RequestMapping("statistics/material")
public class MaterialStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "查询物料列表")
    @PostMapping("page")
    public Response list(@RequestBody MaterialQuery materialQuery) {
        Guard.notNull(materialQuery, "查询物料列表条件为空");
        String url = String.format("%sstatistics/material/v1/list", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialQuery, String.class);
        String res = cleanStr(responseEntity.getBody());
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "物料列表导出", response = ResponseMap.class)
    @PostMapping("export")
    public void listExport(HttpServletResponse response, @RequestBody MaterialQuery materialQuery) {
        Guard.notNull(materialQuery, "物料列表导出条件为空");
        String url = String.format("%sstatistics/material/v1/export", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialQuery, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String,
                Object>>>() {
        });
        if (CollectionUtils.isEmpty(dataResponse.getData())
                && dataResponse.getCode() == 999) {
            throw new MipException(dataResponse.getMsg());
        }

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("物料编码-" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray materialArr = (JSONArray) resultMap.get("materialList");

        List<MaterialExcelVO> materialExcelVOs = new ArrayList<>();
        if (materialArr != null) {
            materialExcelVOs = JSONObject.parseArray(materialArr.toJSONString(), MaterialExcelVO.class);
            for (int i = 0; i < materialExcelVOs.size(); i++) {
                MaterialExcelVO materialExcelVO = materialExcelVOs.get(i);
                materialExcelVO.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(materialExcelVOs, MaterialExcelVO.class, null, "物料列表", true);

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation("查找物料表中已用到的某类型下数据字典")
    @GetMapping("listDistByType")
    public Response listDistByType(@RequestParam String type) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/material/v1/listDistByType", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        List<Map<String, Object>> dataList = Utils.jsonStr2List(res);
        DataResponse<List<Map<String, Object>>> response = Response.dataResponse();
        return response.setData(dataList);
    }

    /**
     * 获取当前使用单位配置的物料编码规则
     *
     * @param unitId 使用单位ID
     * @return
     */
    @ApiOperation("获取当前使用单位配置的物料编码规则")
    @GetMapping("getMaterialCodeRuleConfig")
    public Response getMaterialCodeRuleConfig(@RequestParam(required = false) Long unitId) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/material/v1/getMaterialCodeRuleConfig", param);
        String res = cleanStr(restTemplate.getForObject(url, String.class));
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "查询所有组织有效的物料列表")
    @PostMapping("getAllOrganizationMaterial")
    public Response getAllOrganizationMaterial(@RequestBody MaterialQuery materialQuery) {
        Guard.notNull(materialQuery, "查询物料列表条件为空");
        String url = String.format("%sstatistics/material/getAllOrganizationMaterial", ModelsEnum.STATISTICS.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialQuery, String.class);
        String res = cleanStr(responseEntity.getBody());
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "查询所有组织有效的物料列表")
    @PostMapping("checkOrganizationMaterial")
    public Response checkOrganizationMaterial(@RequestBody MaterialQuery materialQuery) {
        Guard.notNull(materialQuery, "查询物料列表条件为空");
        String url = String.format("%sstatistics/material/checkOrganizationMaterial", ModelsEnum.STATISTICS.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialQuery, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation(value = "跨组织物料列表")
    @PostMapping("pageByCrossOrganizational")
    public Response pageByCrossOrganizational(@RequestBody MaterialQuery materialQuery) {
        Guard.notNull(materialQuery, "查询物料列表条件为空");
        String url = String.format("%sstatistics/material/pageByCrossOrganizational", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialQuery, String.class);
        String res = cleanStr(responseEntity.getBody());
        PageInfo<Map<String, Object>> data = JSON.parseObject(res, new TypeReference<PageInfo<Map<String, Object>>>() {
        });
        PageResponse<Map<String, Object>> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation(value = "跨组织物料列表导出", response = ResponseMap.class)
    @PostMapping("exportByCrossOrganizational")
    public void listExportByCrossOrganizational(HttpServletResponse response, @RequestBody MaterialQuery materialQuery) {
        Guard.notNull(materialQuery, "物料列表导出条件为空");
        String url = String.format("%sstatistics/material/listExportByCrossOrganizational", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialQuery, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String,
                Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        String fileName = "物料编码-" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls";

        JSONArray materialArr = (JSONArray) resultMap.get("materialList");

        List<MaterialCrossOrganizationalExcelVO> excelVOList = new ArrayList<>();
        if (materialArr != null) {
            excelVOList = JSONObject.parseArray(materialArr.toJSONString(), MaterialCrossOrganizationalExcelVO.class);
            for (int i = 0; i < excelVOList.size(); i++) {
                MaterialCrossOrganizationalExcelVO excelVO = excelVOList.get(i);
                excelVO.setNum(i + 1);
            }
        }

        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(excelVOList, MaterialCrossOrganizationalExcelVO.class, null, "物料列表", true);

        ExportExcelUtil.downLoadExcel(fileName, response, workbook);
    }

}
