package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.FormConfigDTO;
import com.midea.pam.common.basedata.entity.Unit;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.crm.dto.WinLossOrderSelectDto;
import com.midea.pam.common.ctc.entity.OrganizationCustomDict;
import com.midea.pam.common.ctc.vo.BusinessContactExcelVo;
import com.midea.pam.common.ctc.vo.BusinessExcVo;
import com.midea.pam.common.ctc.vo.BusinessFollowRecordExcelVo;
import com.midea.pam.common.ctc.vo.BusinessTeamExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.PaymentNumberEnums;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.FormConfigExcelUtil;
import com.midea.pam.gateway.service.BusinessWinLossEmailService;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Api("商机模块")
@RestController
@RequestMapping("statistics/business")
public class BusinessStatisticsController extends ControllerHelper {

    private static final String SENSITIVE_WORD_REPLACER = "******";

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private BusinessWinLossEmailService businessWinLossEmailService;

    @ApiOperation(value = "查询商机列表")
    @GetMapping("page")
    public Response list(BusinessDto businessDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(businessDto);
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/business/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<BusinessDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<BusinessDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "查询我的商机列表")
    @GetMapping("createByMePage")
    public Response createByMePage(BusinessDto businessDto,
                                   @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(businessDto);
        params.put("me", Boolean.TRUE);

        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/business/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<BusinessDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<BusinessDto>>>() {
        });

        return response;
    }

    @ApiOperation(value = "商机列表导出", response = ResponseMap.class)
    @GetMapping("/export")
    public void listExport(HttpServletResponse response, BusinessDto businessDto) {
        final Map<String, Object> param = buildParam(businessDto);
        param.put("list", Boolean.TRUE);
        // param.put("list",customerDto.isList());
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/business/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("商机信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray businessArr = (JSONArray) resultMap.get("business");
        JSONArray businessContactArr = (JSONArray) resultMap.get("businessContactExcelVos");
        JSONArray businessFollowRecordArr = (JSONArray) resultMap.get("businessFollowRecordExcelVos");
        JSONArray businessTeamArr = (JSONArray) resultMap.get("businessTeamExcelVos");

        final Map<String, Object> query = new HashMap<>();
        query.put("orgId", SystemContext.getUnitId());
        query.put("orgFrom", "company");
        query.put("name", "商机等级");
        url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", query);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationCustomDict>> listDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
        });
        List<OrganizationCustomDict> dicts = listDataResponse.getData();
        List<String> values = StringUtils.splitToList(dicts.get(0).getValue(), ",");
        List<BusinessExcVo> businessExcelVOS = JSONObject.parseArray(businessArr.toJSONString(), BusinessExcVo.class);
        for (int i = 0; i < businessExcelVOS.size(); i++) {
            BusinessExcVo businessExcelVo = businessExcelVOS.get(i);
            if (StringUtils.isNotEmpty(businessExcelVo.getProductIntentionName())) {
                businessExcelVo.setProductIntentionName(businessExcelVo.getProductIntentionName());
            } else {
                if (StringUtils.isNotEmpty(businessExcelVo.getProductIntention())) {
                    String productIntention = businessExcelVo.getProductIntention();
                    List<Long> unitIds = Arrays.stream(productIntention.split(","))
                            .map(s -> Long.parseLong(s.trim()))
                            .collect(Collectors.toList());
                    StringBuffer nameBuffer = new StringBuffer();
                    if (CollectionUtils.isNotEmpty(unitIds)) {
                        for (Long unitId : unitIds) {
                            Unit unit = CacheDataUtils.findUnitById(unitId);
                            nameBuffer.append(unit.getUnitName());
                            nameBuffer.append(",");
                        }
                        nameBuffer.delete(nameBuffer.length() - 1, nameBuffer.length());
                        businessExcelVo.setProductIntentionName(nameBuffer.toString());
                    }
                }
            }
            for (String value : values) {
                if (value.contains(businessExcelVo.getLevel() + "_")) {
                    businessExcelVo.setLevelName(value.substring(value.indexOf("_") + 1));
                }
            }
            if (StringUtils.isNotEmpty(businessExcelVo.getPaymentNumber())) {
                businessExcelVo.setPaymentNumber(PaymentNumberEnums.getValue(businessExcelVo.getPaymentNumber()));
            }
            businessExcelVo.setNum(i + 1);
        }

        List<BusinessContactExcelVo> businessContacts = JSONObject.parseArray(businessContactArr.toJSONString(), BusinessContactExcelVo.class);
        for (int i = 0; i < businessContacts.size(); i++) {
            BusinessContactExcelVo businessContactExcelVO = businessContacts.get(i);
            businessContactExcelVO.setNum(i + 1);
            desensitizationExport(businessContactExcelVO);
        }

        List<BusinessFollowRecordExcelVo> businessFollowRecords = JSONObject.parseArray(businessFollowRecordArr.toJSONString(), BusinessFollowRecordExcelVo.class);
        for (int i = 0; i < businessFollowRecords.size(); i++) {
            BusinessFollowRecordExcelVo businessFollowRecordsExcelVO = businessFollowRecords.get(i);
            businessFollowRecordsExcelVO.setNum(i + 1);
        }

        List<BusinessTeamExcelVo> businessTeams = JSONObject.parseArray(businessTeamArr.toJSONString(), BusinessTeamExcelVo.class);
        for (int i = 0; i < businessTeams.size(); i++) {
            BusinessTeamExcelVo businessTeamVo = businessTeams.get(i);
            businessTeamVo.setNum(i + 1);
        }

        final Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(businessExcelVOS, BusinessExcVo.class, null, "基本信息", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, businessFollowRecords, BusinessFollowRecordExcelVo.class, null, "跟进记录", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, businessContacts, BusinessContactExcelVo.class, null, "联系人", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, businessTeams, BusinessTeamExcelVo.class, null, "团队成员", true);

        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("unitId", SystemContext.getUnitId());
        queryParam.put("module", "business");
        queryParam.put("scene", "excel");
        String buildGetUrl = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "formConfig/listByUnitIdAndModule", queryParam);
        ResponseEntity<String> entity = restTemplate.getForEntity(buildGetUrl, String.class);
        DataResponse<List<FormConfigDTO>> resultResponse = JSON.parseObject(entity.getBody(), new TypeReference<DataResponse<List<FormConfigDTO>>>() {
        });
        List<FormConfigDTO> configDTOS = resultResponse.getData()
                .stream().filter(dto -> dto.getFieldType() == 2).collect(Collectors.toList());
        FormConfigExcelUtil.generateData(workbook, configDTOS);//拼接数据
        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "我的商机列表导出", response = ResponseMap.class)
    @GetMapping("/exportMy")
    public void listMyExport(HttpServletResponse response, BusinessDto businessDto) {
        final Map<String, Object> param = buildParam(businessDto);
        param.put("me", Boolean.TRUE);
        // param.put("list",customerDto.isList());
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/business/v1/export", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("商机信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss"));
        fileName.append(".xls");

        JSONArray businessArr = (JSONArray) resultMap.get("business");
        JSONArray businessContactArr = (JSONArray) resultMap.get("businessContactExcelVos");
        JSONArray businessFollowRecordArr = (JSONArray) resultMap.get("businessFollowRecordExcelVos");
        JSONArray businessTeamArr = (JSONArray) resultMap.get("businessTeamExcelVos");

        final Map<String, Object> query = new HashMap<>();
        query.put("orgId", SystemContext.getUnitId());
        query.put("orgFrom", "company");
        query.put("name", "商机等级");
        url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/organizationCustomDict/queryByOrdId", query);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<OrganizationCustomDict>> listDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<OrganizationCustomDict>>>() {
        });
        List<OrganizationCustomDict> dicts = listDataResponse.getData();
        List<String> values = StringUtils.splitToList(dicts.get(0).getValue(), ",");
        List<BusinessExcVo> businessExcelVOS = JSONObject.parseArray(businessArr.toJSONString(), BusinessExcVo.class);
        for (int i = 0; i < businessExcelVOS.size(); i++) {
            BusinessExcVo businessExcelVo = businessExcelVOS.get(i);
            if (StringUtils.isNotEmpty(businessExcelVo.getProductIntentionName())) {
                businessExcelVo.setProductIntentionName(businessExcelVo.getProductIntentionName());
            } else {
                if (StringUtils.isNotEmpty(businessExcelVo.getProductIntention())) {
                    String productIntention = businessExcelVo.getProductIntention();
                    List<Long> unitIds = Arrays.stream(productIntention.split(","))
                            .map(s -> Long.parseLong(s.trim()))
                            .collect(Collectors.toList());
                    StringBuffer nameBuffer = new StringBuffer();
                    if (CollectionUtils.isNotEmpty(unitIds)) {
                        for (Long unitId : unitIds) {
                            Unit unit = CacheDataUtils.findUnitById(unitId);
                            nameBuffer.append(unit.getUnitName());
                            nameBuffer.append(",");
                        }
                        nameBuffer.delete(nameBuffer.length() - 1, nameBuffer.length());
                        businessExcelVo.setProductIntentionName(nameBuffer.toString());
                    }
                }
            }
            for (String value : values) {
                if (value.contains(businessExcelVo.getLevel() + "_")) {
                    businessExcelVo.setLevelName(value.substring(value.indexOf("_") + 1));
                }
            }
            if (StringUtils.isNotEmpty(businessExcelVo.getPaymentNumber())) {
                businessExcelVo.setPaymentNumber(PaymentNumberEnums.getValue(businessExcelVo.getPaymentNumber()));
            }
            businessExcelVo.setNum(i + 1);
        }

        List<BusinessContactExcelVo> businessContacts = JSONObject.parseArray(businessContactArr.toJSONString(), BusinessContactExcelVo.class);
        for (int i = 0; i < businessContacts.size(); i++) {
            BusinessContactExcelVo businessContactExcelVO = businessContacts.get(i);
            businessContactExcelVO.setNum(i + 1);
            desensitizationExport(businessContactExcelVO);
        }

        List<BusinessFollowRecordExcelVo> businessFollowRecords = JSONObject.parseArray(businessFollowRecordArr.toJSONString(), BusinessFollowRecordExcelVo.class);
        for (int i = 0; i < businessFollowRecords.size(); i++) {
            BusinessFollowRecordExcelVo businessFollowRecordsExcelVO = businessFollowRecords.get(i);
            businessFollowRecordsExcelVO.setNum(i + 1);
        }

        List<BusinessTeamExcelVo> businessTeams = JSONObject.parseArray(businessTeamArr.toJSONString(), BusinessTeamExcelVo.class);
        for (int i = 0; i < businessTeams.size(); i++) {
            BusinessTeamExcelVo businessTeamVo = businessTeams.get(i);
            businessTeamVo.setNum(i + 1);
        }

        final Workbook workbook = com.midea.pam.common.util.ExportExcelUtil.buildDefaultSheet(businessExcelVOS, BusinessExcVo.class, null, "基本信息", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, businessFollowRecords, BusinessFollowRecordExcelVo.class, null, "跟进记录", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, businessContacts, BusinessContactExcelVo.class, null, "联系人", true);
        com.midea.pam.common.util.ExportExcelUtil.addSheet(workbook, businessTeams, BusinessTeamExcelVo.class, null, "团队成员", true);

        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("unitId", SystemContext.getUnitId());
        queryParam.put("module", "business");
        queryParam.put("scene", "excel");
        String buildGetUrl = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "formConfig/listByUnitIdAndModule", queryParam);
        ResponseEntity<String> entity = restTemplate.getForEntity(buildGetUrl, String.class);
        DataResponse<List<FormConfigDTO>> resultResponse = JSON.parseObject(entity.getBody(), new TypeReference<DataResponse<List<FormConfigDTO>>>() {
        });
        List<FormConfigDTO> configDTOS = resultResponse.getData()
                .stream().filter(dto -> dto.getFieldType() == 2).collect(Collectors.toList());
        FormConfigExcelUtil.generateData(workbook, configDTOS);//拼接数据

        com.midea.pam.common.util.ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "根据商机ID查询使用单位ID")
    @GetMapping("/getUnitIdByBusinessId")
    public Response getUnitIdByBusinessId(@RequestParam Long businessId) {
        final Map<String, Object> param = new HashMap<>(1);
        param.put("businessId", businessId);
        final String url = StringUtils.buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/business/v1/getUnitIdByBusinessId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Long> response = JSON.parseObject(res, new TypeReference<DataResponse<Long>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询需要商机复盘提醒的数据")
    @GetMapping("/listNeedRemindBusinessReplay")
    public Response listNeedRemindBusinessReplay() {
        final String url = ModelsEnum.STATISTICS.getBaseUrl() + "/statistics/business/v1/listNeedRemindBusinessReplay";
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<BusinessDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<BusinessDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "新建项目关联商机号接口")
    @GetMapping("/listBaseBusinessInfo")
    public Response listBaseBusinessInfo(@RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                                         @RequestParam(required = false) String fuzzyLike) {
        final Map<String, Object> params = new HashMap<>();
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("fuzzyLike", fuzzyLike);
        final String url = StringUtils.buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "/statistics/business/v1/listBaseBusinessInfo", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<PageInfo<BusinessDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<BusinessDto>>>() {
        });
        return response;
    }

    private Map buildParam(BusinessDto businessDto) {

        //商机编号、商机名称、客户名称、客户CRM编码、跟进人使用文本模糊搜索；
        //商机状态、商机阶段、商机等级、来源、销售部门按照下拉搜索；创建日期使用日期搜索
        final Map<String, Object> params = new HashMap<>();
        params.put("businessCode", businessDto.getBusinessCode());
        if (StringUtils.isNotEmpty(businessDto.getName()) && businessDto.getName().contains("+")) {
            String businessName = businessDto.getName().replace("+", "u002B");
            params.put("name", businessName);
        } else if (StringUtils.isNotEmpty(businessDto.getName())) {
            params.put("name", businessDto.getName());
        }
        params.put("replayStatusStr", businessDto.getReplayStatusStr());
        params.put("riskStrategyStatusStr", businessDto.getRiskStrategyStatusStr());
        params.put("customerName", businessDto.getCustomerName());
        params.put("crmCode", businessDto.getCrmCode());
        params.put("saasBusinessFlag", businessDto.getSaasBusinessFlag());
        params.put("ownerName", businessDto.getOwnerName());
        params.put("projectLocation", businessDto.getProjectLocation());

        params.put("statusStr", businessDto.getStatusStr());
        params.put("followStatusStr", businessDto.getFollowStatusStr());
        params.put("stateStr", businessDto.getStateStr());
        params.put("levelStr", businessDto.getLevelStr());
        params.put("sourceStr", businessDto.getSourceStr());
        params.put("unitIdStr", businessDto.getUnitIdStr());
        params.put("roleDeptStr", businessDto.getRoleDeptStr());
        params.put("businessTypeStr", businessDto.getBusinessTypeStr());
        params.put("createBy", businessDto.getCreateBy());

        params.put("createAtStart", businessDto.getCreateAtStart());
        params.put("createAtEnd", businessDto.getCreateAtEnd());
        params.put("followTimeStart", businessDto.getFollowTimeStart());
        params.put("followTimeEnd", businessDto.getFollowTimeEnd());
        params.put("closeClassStr", businessDto.getCloseClassStr());
        params.put("customerGroupStr", businessDto.getCustomerGroupStr());

        params.put("amountMin", businessDto.getAmountMin());
        params.put("amountMax", businessDto.getAmountMax());
        params.put("planBeginNodeStart", businessDto.getPlanBeginNodeStart());
        params.put("planBeginNodeEnd", businessDto.getPlanBeginNodeEnd());
        params.put("estimateBidAtStart", businessDto.getEstimateBidAtStart());
        params.put("estimateBidAtEnd", businessDto.getEstimateBidAtEnd());
        params.put("estimateDeliveryAtStart", businessDto.getEstimateDeliveryAtStart());
        params.put("estimateDeliveryAtEnd", businessDto.getEstimateDeliveryAtEnd());
        params.put("expectedProductionTimeStart", businessDto.getExpectedProductionTimeStart());
        params.put("expectedProductionTimeEnd", businessDto.getExpectedProductionTimeEnd());
        return params;
    }

    /**
     * 脱敏操作
     */
    private void desensitizationExport(BusinessContactExcelVo businessContactExcelVO) {
        if (Objects.isNull(businessContactExcelVO)) {
            return;
        }
        String cellphone = businessContactExcelVO.getCellphone();
        if (StringUtils.isNotEmpty(cellphone)) {
            businessContactExcelVO.setCellphone(SENSITIVE_WORD_REPLACER);
        }
        String email = businessContactExcelVO.getEmail();
        if (StringUtils.isNotEmpty(email)) {
            businessContactExcelVO.setEmail(SENSITIVE_WORD_REPLACER);
        }
        String wechat = businessContactExcelVO.getWechat();
        if (StringUtils.isNotEmpty(wechat)) {
            businessContactExcelVO.setWechat(SENSITIVE_WORD_REPLACER);
        }
        String qq = businessContactExcelVO.getQq();
        if (StringUtils.isNotEmpty(qq)) {
            businessContactExcelVO.setQq(SENSITIVE_WORD_REPLACER);
        }
    }


    @GetMapping("/winlossOrderSelect")
    public Response winlossOrderSelect(@RequestParam String hours) {
        final Map<String, Object> params = new HashMap<>();
        params.put("hours", hours);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/business/winlossOrderSelect", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<WinLossOrderSelectDto> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<WinLossOrderSelectDto>>() {
                });

        WinLossOrderSelectDto data = response.getData();
        if (data != null) {
            businessWinLossEmailService.businessWinLossEmailSave(data);
        }
        return response;
    }

}
