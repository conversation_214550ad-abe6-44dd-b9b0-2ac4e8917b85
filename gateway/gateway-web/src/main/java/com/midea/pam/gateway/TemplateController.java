package com.midea.pam.gateway;

import com.alibaba.fastjson.JSONObject;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.enums.Code;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@Api("模板管理")
@RestController
@RequestMapping("template")
public class TemplateController {


    @ApiOperation(value = "下载错误数据")
    @PostMapping("/common/downloadErrorMsg")
    public void downloadErrorMsg(@RequestPart("file") MultipartFile file, @RequestParam String errMsg, HttpServletResponse response) {
        List<String> errMsgList = null;
        try {
            errMsgList = JSONObject.parseArray(errMsg, String.class);
        } catch (Exception e) {
            throw new MipException("参数异常");
        }
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.createSheet("报错信息");
            for (int i = 0; i < errMsgList.size(); ++i) {
                Row row = sheet.createRow(i);
                row.createCell(0).setCellValue(errMsgList.get(i));
            }
        } catch (Exception e) {
            throw new BizException(Code.ERROR, "模板解析异常");
        }
        //导出
        ExportExcelUtil.downLoadExcel("报错信息_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

}
