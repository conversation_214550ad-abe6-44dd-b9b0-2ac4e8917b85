package com.midea.pam.gateway.ctc.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.MaterialCostDto;
import com.midea.pam.common.basedata.entity.MaterialCost;
import com.midea.pam.common.basedata.excelVo.MaterialCostExportExcelVo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanConfirmRecordDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanExportRequestDto;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanPurchaseRecordDTO;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanSubmitRecordDto;
import com.midea.pam.common.ctc.dto.MrpDto;
import com.midea.pam.common.ctc.dto.ProjectMilepostDto;
import com.midea.pam.common.ctc.dto.UpdateDeliveryTimeDto;
import com.midea.pam.common.ctc.dto.UpdateTickDeliveryTimeDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.entity.DesignPlanDetailMatchResult;
import com.midea.pam.common.ctc.entity.MilepostDesignPlanDetail;
import com.midea.pam.common.ctc.excelVo.ImportMilepostDesignPlanDetailExcelVo;
import com.midea.pam.common.ctc.excelVo.KsMilepostDesignPlanDetailApprovedExportVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanCustomExportVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailApprovedExportVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailExportExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailImportExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailKsImportExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailKsImportWbsExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailPurchaseExportVo;
import com.midea.pam.common.ctc.excelVo.PurchaseInAdvanceExcelVO;
import com.midea.pam.common.ctc.query.DetailByProjectIdAndMilepostIdNewWbsQuery;
import com.midea.pam.common.ctc.vo.MilepostDesignPlanDetailApprovedVO;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.MaterialCodeRuleEnum;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.BeanConverter;
import com.midea.pam.gateway.common.utils.DateUtil;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import com.midea.pam.gateway.common.utils.FileUtil;
import com.midea.pam.gateway.common.utils.ListUtil;
import com.midea.pam.gateway.config.CommonPropertiesConfig;
import com.midea.pam.gateway.service.OssService;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@Api("里程碑详细设计方案")
@RestController
@RequestMapping("milepostDesignPlan")
public class MilepostDesignPlanController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(MilepostDesignPlanController.class);

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private OssService ossService;

    @Resource
    private CommonPropertiesConfig commonPropertiesConfig;

    @ApiOperation(value = "里程碑详细方案交付项目列表", response = ProjectMilepostDto.class)
    @GetMapping({"milestoneDetailedPlanDeliveryProjectList"})
    public Response milestoneDetailedPlanDeliveryProjectList() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan" +
                "/milestoneDetailedPlanDeliveryProjectList", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<ProjectMilepostDto>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<ProjectMilepostDto>>>() {
                });
        return response;
    }

    @ApiOperation(value = "获取里程碑详细设计方案:所有提交记录/设计信息/附件信息", response = MilepostDesignPlanDto.class)
    @GetMapping({"getDetailByProjectIdAndMilepostId"})
    public Response getDetailByProjectIdAndMilepostId(@RequestParam @ApiParam("项目id") Long projectId,
                                                      @RequestParam @ApiParam("里程碑id") Long milepostId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("milepostId", milepostId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getDetailByProjectIdAndMilepostId"
                , param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(res,
                new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
                });
        return response;
    }

    @ApiOperation(value = "获取里程碑详细设计方案:所有提交记录/设计信息/附件信息(柔性)", response = MilepostDesignPlanDto.class)
    @PostMapping({"getDetailByProjectIdAndProjectId"})
    public Response getDetailByProjectIdAndProjectId(@RequestBody DetailByProjectIdAndMilepostIdNewWbsQuery query) {
        String url = String.format("%smilepostDesignPlan/getDetailByProjectIdAndProjectId", ModelsEnum.CTC.getBaseUrl());
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
        });
    }

    @ApiOperation(value = "进度确认/部分确认信息页", response = MilepostDesignPlanDto.class)
    @PostMapping({"getDetailByProjectIdWithWBSEnabled"})
    public Response getDetailByProjectIdWithWBSEnabled(@RequestBody DetailByProjectIdAndMilepostIdNewWbsQuery query
    ) {
        String url = String.format("%smilepostDesignPlan/getDetailByProjectIdWithWBSEnabled",
                ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
                });
        return response;
    }


    @ApiOperation(value = "获取里程碑详细设计方案:所有提交记录/设计信息/附件信息", response = MilepostDesignPlanDto.class)
    @GetMapping({"getDetailByProjectIdAndMilepostIdNew"})
    public Response getDetailByProjectIdAndMilepostIdNew(@RequestParam @ApiParam("项目id") Long projectId,
                                                         @RequestParam @ApiParam("里程碑id") Long milepostId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("milepostId", milepostId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan" +
                "/getDetailByProjectIdAndMilepostIdNew", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(res,
                new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
                });
        return response;
    }

    @ApiOperation(value = "获取里程碑详细设计方案:所有提交记录/设计信息/附件信息(柔性)", response = MilepostDesignPlanDto.class)
    @PostMapping({"getDetailByProjectIdAndMilepostIdNewWbs"})
    public Response getDetailByProjectIdAndMilepostIdNewWbs(@RequestBody DetailByProjectIdAndMilepostIdNewWbsQuery query) {
        String url = String.format("%smilepostDesignPlan/getDetailByProjectIdAndMilepostIdNewWbs",
                ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
                });
        return response;
    }

    @ApiOperation(value = "获取物料状态详情（详细设计方案）")
    @GetMapping({"getMaterialStatusDetailByProjectIdAndMilepostId"})
    public Response getMaterialStatusDetailByProjectIdAndMilepostId(@RequestParam @ApiParam("项目id") Long projectId,
                                                                    @RequestParam @ApiParam("里程碑id") Long milepostId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("milepostId", milepostId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan" +
                "/getMaterialStatusDetailByProjectIdAndMilepostId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Map<String, Object>>>() {
        });
    }

    @ApiOperation(value = "获取物料状态详情（详细设计方案）wbs")
    @GetMapping({"getMaterialStatusDetailByProjectId"})
    public Response getMaterialStatusDetailByProjectId(@RequestParam @ApiParam("项目id") Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan" +
                "/getMaterialStatusDetailByProjectId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<Map<String, Object>>>() {
        });
    }

    @ApiOperation(value = "动态获取获取PAM物料编码或者ERP物料编码")
    @GetMapping({"getDynamicPamCodeOrErpCodeList"})
    public Response getDynamicPamCodeOrErpCodeList(@RequestParam @ApiParam("项目id") Long projectId,
                                                   @RequestParam(required = false) @ApiParam("里程碑id") Long milepostId,
                                                   @RequestParam(required = false, defaultValue = "false") @ApiParam(
                                                           "物料是否同步ERP数") boolean isSynchronizedErp,
                                                   @RequestParam(required = false, defaultValue = "false") @ApiParam(
                                                           "是否生成采购需求数") boolean isGeneratePurchase) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("milepostId", milepostId);
        param.put("isSynchronizedErp", isSynchronizedErp);
        param.put("isGeneratePurchase", isGeneratePurchase);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getDynamicPamCodeOrErpCodeList",
                param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<String>>>() {
        });
    }

    @ApiOperation(value = "获取提交记录以及对应的设计信息/所有附件信息", response = MilepostDesignPlanDto.class)
    @GetMapping("getDetailBySubmitId")
    public Response getDetailBySubmitId(@RequestParam @ApiParam("提交记录id") Long submitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("submitId", submitId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getDetailBySubmitId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
                });

        return response;
    }

    @ApiOperation(value = "获取暂存提交记录以及对应的设计信息/所有附件信息", response = MilepostDesignPlanDto.class)
    @GetMapping("getDetailBySubmitIdForStaging")
    public Response getDetailBySubmitIdForStaging(@RequestParam @ApiParam("提交记录id") Long submitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("submitId", submitId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getDetailBySubmitIdForStaging",
                param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
                });

        return response;
    }


    @ApiOperation(value = "获取确认记录以及对应的设计信息", response = MilepostDesignPlanDto.class)
    @GetMapping("getDetailByConfirmId")
    public Response getDetailByConfirmId(@RequestParam @ApiParam("确认记录id") Long confirmId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("confirmId", confirmId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getDetailByConfirmId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
                });

        return response;
    }

    @ApiOperation(value = "获取提前采购提交记录和对应的采购内容", response = MilepostDesignPlanDto.class)
    @GetMapping("getDetailForPurchase")
    public Response getDetailForPurchase(@RequestParam @ApiParam("确认记录id") Long confirmId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("confirmId", confirmId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getDetailForPurchase", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
                });

        return response;
    }

    @ApiOperation(value = "移动审批获取确认记录以及对应的设计信息")
    @GetMapping("getMilepostDesignPlanConfirmApp")
    public Response getMilepostDesignPlanConfirmApp(@RequestParam @ApiParam("确认记录id") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getMilepostDesignPlanConfirmApp",
                param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<ResponseMap> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<ResponseMap>>() {
                });
        return response;
    }

    @ApiOperation(value = "保存里程碑详细设计方案", response = MilepostDesignPlanDto.class)
    @PostMapping({"savePlanWithDetail"})
    public Response savePlanWithDetail(@RequestBody MilepostDesignPlanDto dto) {
        String url = String.format("%smilepostDesignPlan/savePlanWithDetail", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<MilepostDesignPlanSubmitRecordDto> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanSubmitRecordDto>>() {
                });
        return response;
    }

    @ApiOperation(value = "保存里程碑详细设计方案前的校验")
    @PostMapping({"savePlanWithDetailAsyncBefore"})
    public Response savePlanWithDetailAsyncBefore(@RequestBody MilepostDesignPlanDto dto) {
        String url = String.format("%smilepostDesignPlan/savePlanWithDetailAsyncBefore", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<List<String>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<String>>>() {
                });
        return response;
    }

    @ApiOperation(value = "保存里程碑详细设计方案(异步)", response = AsyncRequestResult.class)
    @PostMapping({"savePlanWithDetailAsync"})
    public Response savePlanWithDetailAsync(@RequestBody MilepostDesignPlanDto dto) {
        String url = String.format("%smilepostDesignPlan/savePlanWithDetailAsync", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<AsyncRequestResult> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<AsyncRequestResult>>() {
                });
        return response;
    }

    @ApiOperation(value = "导入交付物(异步处理)", response = AsyncRequestResult.class)
    @PostMapping("importDeliverableAsync")
    public Response importDeliverableAsync(@RequestParam(value = "file") MultipartFile file, @RequestParam(value =
            "storageId") Long storageId) {
        DataResponse<AsyncRequestResult> response = null;
        List<MilepostDesignPlanDetailImportExcelVo> detailExcelVos = null;
        List<MilepostDesignPlanDetailKsImportExcelVo> ksDetailExcelVos = null;
        String materialCodeRule = getMaterialCodeRule(); //根据当前使用单位查询编码规则
        try {
            //昆山迭代5新增功能，修改上传文件检验逻辑 begin
            if (materialCodeRule.equals(MaterialCodeRuleEnum.KUNSHAN.getCode())) { //如果是‘昆山物料编码‘,走此逻辑。
                ksDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKsImportExcelVo.class, 1, 0);
                Iterator<MilepostDesignPlanDetailKsImportExcelVo> iterator = ksDetailExcelVos.iterator();
                //移除空行的数据
                while (iterator.hasNext()) {
                    MilepostDesignPlanDetailKsImportExcelVo ksDetailExcelVo = iterator.next();
                    if (StringUtils.isBlank(ksDetailExcelVo.getMaterielType())
                            && StringUtils.isBlank(ksDetailExcelVo.getCodingMiddleClass())
                            && StringUtils.isBlank(ksDetailExcelVo.getBrand())
                            && StringUtils.isBlank(ksDetailExcelVo.getName())
                            && StringUtils.isBlank(ksDetailExcelVo.getModel())
                            && StringUtils.isBlank(ksDetailExcelVo.getUnit())
                            && StringUtils.isBlank(ksDetailExcelVo.getFigureNumber())
                            && StringUtils.isBlank(ksDetailExcelVo.getChartVersion())
                            && StringUtils.isBlank(ksDetailExcelVo.getMachiningPartType())
                            && StringUtils.isBlank(ksDetailExcelVo.getMaterial())
                            && ksDetailExcelVo.getUnitWeight() == null
                            && StringUtils.isBlank(ksDetailExcelVo.getMaterialProcessing())
                            && StringUtils.isBlank(ksDetailExcelVo.getBrandMaterialCode())
                            && StringUtils.isBlank(ksDetailExcelVo.getOrSparePartsMask())
                            && StringUtils.isBlank(ksDetailExcelVo.getPamCode())
                            && StringUtils.isBlank(ksDetailExcelVo.getMaterialCategory())
                            && Objects.isNull(ksDetailExcelVo.getNumber())
                    ) {
                        iterator.remove();
                    }
                    Asserts.notEmpty(ksDetailExcelVos, ErrorCode.SYSTEM_FILE_EMPTY);
                    Map<String, Object> param = new HashMap<>(1);
                    param.put("storageId", storageId);
                    String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan" +
                            "/ksMatchingMaterialCodeEstimateAsync", param);
                    String res = restTemplate.postForEntity(url, ksDetailExcelVos, String.class).getBody();
                    response = JSON.parseObject(res, new TypeReference<DataResponse<AsyncRequestResult>>() {
                    });
                }
                //昆山迭代5新增功能，修改上传文件检验逻辑 end
            } else if (materialCodeRule.equals(MaterialCodeRuleEnum.KUKAMIA.getCode())) { //如果是美的机器人编码规则，则走此逻辑。
                detailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailImportExcelVo.class, 1, 0);
                Iterator<MilepostDesignPlanDetailImportExcelVo> iterator = detailExcelVos.iterator();
                while (iterator.hasNext()) {
                    MilepostDesignPlanDetailImportExcelVo detailExcelVo = iterator.next();
                    if (StringUtil.isNull(detailExcelVo.getMaterielType())
                            && StringUtil.isNull(detailExcelVo.getName())
                            && StringUtil.isNull(detailExcelVo.getModel())
                            && StringUtil.isNull(detailExcelVo.getUnit())
                            && StringUtil.isNull(detailExcelVo.getBrand())
                            && Objects.isNull(detailExcelVo.getNumber())
                            && Objects.isNull(detailExcelVo.getDeliveryTime())) {
                        iterator.remove();
                    }
                }
                Asserts.notEmpty(detailExcelVos, ErrorCode.SYSTEM_FILE_EMPTY);
                final String url = String.format("%smilepostDesignPlan/matchingMaterialCodeEstimateAsync",
                        ModelsEnum.CTC.getBaseUrl());
                String res = restTemplate.postForEntity(url, detailExcelVos, String.class).getBody();
                response = JSON.parseObject(res, new TypeReference<DataResponse<AsyncRequestResult>>() {
                });
            } else {
                Asserts.success(ErrorCode.WLBMGZ_ERROR);
            }
        } catch (Exception e) {
            logger.info("详细设计生成文件，上传失败:", e.getMessage());
            Asserts.success(ErrorCode.SYSTEM_FILE_ERROR);
        }
        return response;
    }

    @ApiOperation(value = "详细设计导入交付物数据校验", response = AsyncRequestResult.class)
    @PostMapping("importDeliverable")
    public Response importDeliverable(@RequestParam(value = "file") MultipartFile file, @RequestParam(value =
            "storageId") Long storageId) {
        DataResponse<List<MilepostDesignPlanDetailDto>> response = null;
        List<MilepostDesignPlanDetailImportExcelVo> detailExcelVos = null;
        List<MilepostDesignPlanDetailKsImportExcelVo> ksDetailExcelVos = null;
        String materialCodeRule = getMaterialCodeRule(); //根据当前使用单位查询编码规则
        try {
            //昆山迭代5新增功能，修改上传文件检验逻辑 begin
            //if (materialCodeRule.equals(MaterialCodeRuleEnum.KUNSHAN.getCode())) { //如果是‘昆山物料编码‘,走此逻辑。
            try {
                ksDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKsImportExcelVo.class, 1, 0);
            } catch (Exception e) {
                ksDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKsImportExcelVo.class, 0, 0);
            }
            Iterator<MilepostDesignPlanDetailKsImportExcelVo> iterator = ksDetailExcelVos.iterator();
            //移除空行的数据
            while (iterator.hasNext()) {
                MilepostDesignPlanDetailKsImportExcelVo ksDetailExcelVo = iterator.next();
                if (StringUtils.isBlank(ksDetailExcelVo.getMaterielType())
                        && StringUtils.isBlank(ksDetailExcelVo.getCodingMiddleClass())
                        && StringUtils.isBlank(ksDetailExcelVo.getBrand())
                        && StringUtils.isBlank(ksDetailExcelVo.getName())
                        && StringUtils.isBlank(ksDetailExcelVo.getModel())
                        && StringUtils.isBlank(ksDetailExcelVo.getUnit())
                        && StringUtils.isBlank(ksDetailExcelVo.getFigureNumber())
                        && StringUtils.isBlank(ksDetailExcelVo.getChartVersion())
                        && StringUtils.isBlank(ksDetailExcelVo.getMachiningPartType())
                        && StringUtils.isBlank(ksDetailExcelVo.getMaterial())
                        && ksDetailExcelVo.getUnitWeight() == null
                        && StringUtils.isBlank(ksDetailExcelVo.getMaterialProcessing())
                        && StringUtils.isBlank(ksDetailExcelVo.getBrandMaterialCode())
                        && StringUtils.isBlank(ksDetailExcelVo.getOrSparePartsMask())
                        && StringUtils.isBlank(ksDetailExcelVo.getPamCode())
                        && StringUtils.isBlank(ksDetailExcelVo.getMaterialCategory())
                        && Objects.isNull(ksDetailExcelVo.getNumber())
                ) {
                    iterator.remove();
                }

            }
            Asserts.notEmpty(ksDetailExcelVos, ErrorCode.SYSTEM_FILE_EMPTY);
            Map<String, Object> param = new HashMap<>(1);
            param.put("storageId", storageId);
            String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/ksMatchingMaterialCodeEstimate"
                    , param);
            String res = restTemplate.postForEntity(url, ksDetailExcelVos, String.class).getBody();
            response = JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
            });
            //昆山迭代5新增功能，修改上传文件检验逻辑 end
            //}
                /*else if (materialCodeRule.equals(MaterialCodeRuleEnum.KUKA.getCode())) { //如果是美的机器人编码规则，则走此逻辑。
                try {
                    detailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailImportExcelVo.class, 1, 0);
                } catch (Exception e) {
                    detailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailImportExcelVo.class, 0, 0);
                }
                Iterator<MilepostDesignPlanDetailImportExcelVo> iterator = detailExcelVos.iterator();
                while (iterator.hasNext()) {
                    MilepostDesignPlanDetailImportExcelVo detailExcelVo = iterator.next();
                    if (StringUtil.isNull(detailExcelVo.getMaterielType())
                            && StringUtil.isNull(detailExcelVo.getName())
                            && StringUtil.isNull(detailExcelVo.getModel())
                            && StringUtil.isNull(detailExcelVo.getUnit())
                            && StringUtil.isNull(detailExcelVo.getBrand())
                            && Objects.isNull(detailExcelVo.getNumber())
                            && Objects.isNull(detailExcelVo.getDeliveryTime())) {
                        iterator.remove();
                    }
                }
                Asserts.notEmpty(detailExcelVos, ErrorCode.SYSTEM_FILE_EMPTY);
                final String url = String.format("%smilepostDesignPlan/matchingMaterialCodeEstimate", ModelsEnum.CTC
                .getBaseUrl());
                HttpComponentsClientHttpRequestFactory httpRequestFactory = new
                HttpComponentsClientHttpRequestFactory();
                httpRequestFactory.setConnectionRequestTimeout(600*1000);
                httpRequestFactory.setConnectTimeout(600*1000);
                httpRequestFactory.setReadTimeout(600*1000);
                restTemplate.setRequestFactory(httpRequestFactory);
                String res = restTemplate.postForEntity(url, detailExcelVos, String.class).getBody();
                response = JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
                });
            }*/ /*else {
                Asserts.success(ErrorCode.WLBMGZ_ERROR);
            }*/
        } catch (Exception e) {
            logger.info("详细设计生成文件，上传失败:", e.getMessage());
            Asserts.success(ErrorCode.SYSTEM_FILE_ERROR);
        }
        return response;
    }

    /**
     * 工单工时导入-反馈表导出
     *
     * @param dtoList 导入的Excel包含检查结果的数据
     * @return 校验结果
     */
    @ApiOperation(value = "详细设计导入交付物校验结果下载")
    @PostMapping("exportDeliverableValidResult")
    public void exportDeliverableValidResult(HttpServletResponse response,
                                             @RequestParam @ApiParam(value = "物料编码规则，上一步返回的msg") String materialCodeRule,
                                             @RequestBody List<MilepostDesignPlanDetailDto> dtoList) {
        String fileName = "详细设计导入校验结果_" + DateUtils.format(new Date(), "yyyyMMddHH") + ".xls";
        if (MaterialCodeRuleEnum.KUNSHAN.getCode().equalsIgnoreCase(materialCodeRule)) {
            List<MilepostDesignPlanDetailKsImportExcelVo> excelVoList = BeanConverter.copy(dtoList,
                    MilepostDesignPlanDetailKsImportExcelVo.class);
            ExportExcelUtil.exportExcel(excelVoList, null, "详细设计导入校验结果",
                    MilepostDesignPlanDetailKsImportExcelVo.class, fileName, response);
        } else if (MaterialCodeRuleEnum.KUKAMIA.getCode().equalsIgnoreCase(materialCodeRule)) {
            List<MilepostDesignPlanDetailImportExcelVo> excelVoList = BeanConverter.copy(dtoList,
                    MilepostDesignPlanDetailImportExcelVo.class);
            ExportExcelUtil.exportExcel(excelVoList, null, "详细设计导入校验结果", MilepostDesignPlanDetailImportExcelVo.class,
                    fileName, response);
        } else {
            throw new BizException(1, "编码规则参数错误");
        }
    }

    /**
     * 详细设计导入交付物校验结果下载
     *
     * @param dtoList 导入的Excel包含检查结果的数据
     * @return 校验结果
     */
    @ApiOperation(value = "详细设计导入交付物校验结果下载")
    @PostMapping("exportDeliverableValidResultWbs")
    public void exportDeliverableValidResultWbs(HttpServletResponse response,
                                                @RequestParam @ApiParam(value = "物料编码规则，上一步返回的msg") String materialCodeRule,
                                                @RequestBody List<MilepostDesignPlanDetailDto> dtoList) {
        String fileName = "详细设计导入校验结果_" + DateUtils.format(new Date(), "yyyyMMddHH") + ".xls";
        if (MaterialCodeRuleEnum.KUNSHAN.getCode().equalsIgnoreCase(materialCodeRule)) {
            List<MilepostDesignPlanDetailKsImportWbsExcelVo> excelVoList = BeanConverter.copy(dtoList,
                    MilepostDesignPlanDetailKsImportWbsExcelVo.class);
            ExportExcelUtil.exportExcel(excelVoList, null, "详细设计导入校验结果",
                    MilepostDesignPlanDetailKsImportWbsExcelVo.class, fileName, response);
        } else if (MaterialCodeRuleEnum.KUKAMIA.getCode().equalsIgnoreCase(materialCodeRule)) {
            List<MilepostDesignPlanDetailImportExcelVo> excelVoList = BeanConverter.copy(dtoList,
                    MilepostDesignPlanDetailImportExcelVo.class);
            ExportExcelUtil.exportExcel(excelVoList, null, "详细设计导入校验结果", MilepostDesignPlanDetailImportExcelVo.class,
                    fileName, response);
        } else {
            throw new BizException(1, "编码规则参数错误");
        }
    }

    /**
     * 详细设计导入交付物校验结果下载
     *
     * @param dtoList 导入的Excel包含检查结果的数据
     * @return 校验结果
     */
    @ApiOperation(value = "详细设计导入交付物校验结果下载")
    @PostMapping("exportDesignPlanDetailValidResultWithKuka2022")
    public void exportDesignPlanDetailValidResultWithKuka2022(HttpServletResponse response,
                                                              @RequestParam @ApiParam(value = "物料编码规则，上一步返回的msg") String materialCodeRule,
                                                              @RequestBody List<MilepostDesignPlanDetailDto> dtoList) {
        String fileName = "详细设计导入校验结果_" + DateUtils.format(new Date(), "yyyyMMddHH") + ".xls";
        List<MilepostDesignPlanDetailKsImportWbsExcelVo> excelVoList = BeanConverter.copy(dtoList,
                MilepostDesignPlanDetailKsImportWbsExcelVo.class);
        ExportExcelUtil.exportExcel(excelVoList, null, "详细设计导入校验结果",
                MilepostDesignPlanDetailKsImportWbsExcelVo.class, fileName, response);
    }

    private String getMaterialCodeRule() {
        Long unitId = SystemContext.getUnitId();
        Map<String, Object> param = new HashMap<>(1);
        param.put("unitId", unitId);
        String getMaterialCodeRuleUrl = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/material/v1" +
                "/getMaterialCodeRuleConfig", param);
        String getMaterialCodeRuleRes = cleanStr(restTemplate.getForObject(getMaterialCodeRuleUrl, String.class));
        DataResponse<String> materialCodeRuleResponse = JSON.parseObject(getMaterialCodeRuleRes,
                new TypeReference<DataResponse<String>>() {
                });
        String materialCodeRule = materialCodeRuleResponse.getData(); //当前单位的物料编码规则
        MaterialCodeRuleEnum materialCodeRuleEnum = MaterialCodeRuleEnum.getEnumByCode(materialCodeRule);
        if (materialCodeRuleEnum == null) {
            throw new BizException(100, materialCodeRule);
        }
        return materialCodeRule;
    }

    @ApiOperation(value = "获取导入交付物匹配结果", response = DesignPlanDetailMatchResult.class)
    @GetMapping("designPlanDetailMatchResultList")
    public Response designPlanDetailMatchResultList(@RequestParam(required = false) @ApiParam("异步请求结果id") Long asyncRequestResultId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("asyncRequestResultId", asyncRequestResultId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/designPlanDetailMatchResultList",
                param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<DesignPlanDetailMatchResult>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<DesignPlanDetailMatchResult>>>() {
                });
        return response;
    }

    @ApiOperation(value = "生成物料PAM编码")
    @GetMapping({"generateMaterialPAMCode"})
    public Response generateMaterialPAMCode() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/generateMaterialPAMCode", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<String> response = JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
        return response;
    }

    @ApiOperation(value = "匹配设计成本", response = MaterialCostDto.class)
    @GetMapping({"matchingDesignCost"})
    public Response matchingDesignCost(@RequestParam @ApiParam("PAM编码") String pamCode, @RequestParam(required =
            false) Long organizationId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pamCode", pamCode);
        param.put("organizationId", organizationId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/matchingDesignCost", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MaterialCostDto> response = JSON.parseObject(res,
                new TypeReference<DataResponse<MaterialCostDto>>() {
                });
        return response;
    }

    @ApiOperation(value = "导出提交记录对应上传记录")
    @GetMapping("exportDeliverable")
    public void exportDeliverable(HttpServletResponse response,
                                  @RequestParam @ApiParam("提交记录id") Long submitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("submitId", submitId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan" +
                "/getSubmitRecordWithDetailBySubmitId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<MilepostDesignPlanSubmitRecordDto> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanSubmitRecordDto>>() {
                });

        MilepostDesignPlanSubmitRecordDto data = dataResponse.getData();
        List<MilepostDesignPlanDetailDto> dataList = data.getDesignPlanDetailDtos();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }

        List<MilepostDesignPlanDetailExportExcelVo> excelVos = ListUtil.map(dataList,
                new ListUtil.IteratorTask<MilepostDesignPlanDetailExportExcelVo, MilepostDesignPlanDetailDto>() {
                    @Override
                    public MilepostDesignPlanDetailExportExcelVo getValue(MilepostDesignPlanDetailDto item) {
                        MilepostDesignPlanDetailExportExcelVo wifi = new MilepostDesignPlanDetailExportExcelVo();
                        BeanUtils.copyProperties(item, wifi);
                        return wifi;
                    }
                });

        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", MilepostDesignPlanDetailExportExcelVo.class, "上传记录.xls"
                , response);
    }

    @ApiOperation(value = "取消发布")
    @GetMapping("unpublishBySubmitId")
    public Response unpublishBySubmitId(@RequestParam @ApiParam("提交记录id") Long submitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("submitId", submitId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/unpublishBySubmitId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Boolean> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Boolean>>() {
                });

        return response;
    }

    @ApiOperation(value = "保存里程碑详细设计方案确认记录", response = MilepostDesignPlanConfirmRecordDto.class)
    @PostMapping({"saveConfirmRecordWithRecursiveDetail"})
    public Response saveConfirmRecordWithRecursiveDetail(@RequestBody MilepostDesignPlanConfirmRecordDto dto) {
        String url = String.format("%smilepostDesignPlan/saveConfirmRecordWithRecursiveDetail",
                ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<MilepostDesignPlanConfirmRecordDto> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanConfirmRecordDto>>() {
                });
        return response;
    }

    @ApiOperation(value = "保存里程碑详细设计方案提前采购记录", response = MilepostDesignPlanPurchaseRecordDTO.class)
    @PostMapping({"savePurchaseRecordWithRecursiveDetail"})
    public Response savePurchaseRecordWithRecursiveDetail(@RequestBody MilepostDesignPlanPurchaseRecordDTO dto) {
        String url = String.format("%smilepostDesignPlan/savePurchaseRecordWithRecursiveDetail",
                ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<MilepostDesignPlanPurchaseRecordDTO> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanPurchaseRecordDTO>>() {
                });
        return response;
    }

    @ApiOperation(value = "根据项目生成确认记录流程名", response = String.class)
    @GetMapping("generateConfirmRecordProcessName")
    public Response generateConfirmRecordProcessName(@RequestParam @ApiParam("项目id") Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/generateConfirmRecordProcessName",
                param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<String> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<String>>() {
                });

        return response;
    }

    @ApiOperation(value = "项目物料预算转换详细方案模组")
    @GetMapping({"projectBudgetMaterialToMilepostDesignPlanDetail"})
    public Response projectBudgetMaterialToMilepostDesignPlanDetail(@RequestParam Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan" +
                "/projectBudgetMaterialToMilepostDesignPlanDetail", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "导出物料成本(提交记录)", response = MaterialCostDto.class)
    @GetMapping("exportMaterialCostForSubmitRecord")
    public void exportMaterialCostForSubmitRecord(HttpServletResponse response,
                                                  @RequestParam @ApiParam("提交记录id") Long submitRecordId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("submitRecordId", submitRecordId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "milepostDesignPlan/exportMaterialCostForSubmitRecord",
                param);
        String responseEntity = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialCostDto>> dataResponse = JSON.parseObject(responseEntity,
                new TypeReference<DataResponse<List<MaterialCostDto>>>() {
                });

        List<MaterialCostDto> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }

        List<MaterialCostExportExcelVo> excelVos = ListUtil.map(dataList,
                new ListUtil.IteratorTask<MaterialCostExportExcelVo, MaterialCostDto>() {
                    @Override
                    public MaterialCostExportExcelVo getValue(MaterialCostDto item) {
                        MaterialCostExportExcelVo wifi = new MaterialCostExportExcelVo();
                        BeanUtils.copyProperties(item, wifi);
                        if (wifi.getItemCost() != null) {
                            //已估价
                            wifi.setItemCostIsNull(false);
                        } else {
                            //未估价
                            wifi.setItemCostIsNull(true);
                        }
                        return wifi;
                    }
                });

        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", MaterialCostExportExcelVo.class, "估价明细.xls", response);
    }

    @ApiOperation(value = "导出提前采购的内容", response = MaterialCostDto.class)
    @GetMapping("exportMaterialCostForPurchase")
    public void exportMaterialCostForPurchase(HttpServletResponse response,
                                              @RequestParam @ApiParam("提交记录id") Long submitRecordId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("submitRecordId", submitRecordId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "milepostDesignPlan/exportMaterialCostForPurchase",
                param);
        String responseEntity = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MilepostDesignPlanDetailDto>> dataResponse = JSON.parseObject(responseEntity,
                new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
                });

        List<MilepostDesignPlanDetailDto> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        List<PurchaseInAdvanceExcelVO> excelVos = BeanConverter.copy(dataList, PurchaseInAdvanceExcelVO.class);
        int num = 0;
        for (PurchaseInAdvanceExcelVO excelVo : excelVos) {
            num++;
            excelVo.setSerialNumber(String.valueOf(num));
            excelVo.setTotalNum(excelVo.getTotalNum().setScale(0));
            excelVo.setNumber(excelVo.getNumber().setScale(0));
            excelVo.setRequirementNum(excelVo.getRequirementNum().setScale(0));
            excelVo.setPurchaseNum(excelVo.getPurchaseNum().setScale(0));
            excelVo.setDesignCost(excelVo.getDesignCost() == null ? BigDecimal.ZERO : excelVo.getDesignCost());
        }
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseInAdvanceExcelVO.class, "提前采购内容.xls", response);
    }

    @ApiOperation(value = "导出物料成本(确认记录)", response = MaterialCostDto.class)
    @GetMapping("exportMaterialCostForConfirmRecord")
    public void exportMaterialCostForConfirmRecord(HttpServletResponse response,
                                                   @RequestParam @ApiParam("确认记录id") Long confirmRecordId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("confirmRecordId", confirmRecordId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "milepostDesignPlan/exportMaterialCostForConfirmRecord"
                , param);
        String responseEntity = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MaterialCostDto>> dataResponse = JSON.parseObject(responseEntity,
                new TypeReference<DataResponse<List<MaterialCostDto>>>() {
                });

        List<MaterialCostDto> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }

        List<MaterialCostExportExcelVo> excelVos = ListUtil.map(dataList,
                new ListUtil.IteratorTask<MaterialCostExportExcelVo, MaterialCostDto>() {
                    @Override
                    public MaterialCostExportExcelVo getValue(MaterialCostDto item) {
                        MaterialCostExportExcelVo wifi = new MaterialCostExportExcelVo();
                        BeanUtils.copyProperties(item, wifi);
                        if (wifi.getItemCost() != null) {
                            //已估价
                            wifi.setItemCostIsNull(false);
                        } else {
                            //未估价
                            wifi.setItemCostIsNull(true);
                        }
                        return wifi;
                    }
                });

        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", MaterialCostExportExcelVo.class, "估价明细.xls", response);
    }


    @ApiOperation(value = "确认发布(非采购)")
    @PutMapping("updateStatusPublishForNoPurchase/skipSecurityInterceptor")
    public Response updateStatusPublishForNoPurchase(@RequestParam(required = false) Long formInstanceId,
                                                     @RequestParam(required = false) String fdInstanceId,
                                                     @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                     @RequestParam(required = false) String handlerId,
                                                     @RequestParam(required = false) Long companyId,
                                                     @RequestParam(required = false) Long createUserId) {
        // 取详细设计方案发布(非采购)信息
        final Map<String, Object> param = new HashMap<>();
        param.put("submitId", formInstanceId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getDetailBySubmitId1", param);
        ResponseEntity<String> responseEntity1 = restTemplate.getForEntity(url1, String.class);
        // 暂用MilepostDesignPlanDetailApprovedVO这个属性
        DataResponse<MilepostDesignPlanDetailApprovedVO> response1 = JSON.parseObject(responseEntity1.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDetailApprovedVO>>() {
                });
        //生成附件
        MultipartFile multipartFile = createAnnex(response1.getData());
        String fileId = "";
        String fileName = "";
        String fileSize = "";
        try {
            if (multipartFile != null) {
                JSONArray result = ossService.upload(multipartFile);
                if (!ObjectUtils.isEmpty(result)) {
                    JSONObject jsonObject = result.getJSONObject(0);
                    //提交审批
                    fileId = jsonObject.getString("fileId");
                    fileName = jsonObject.getString("fileName");
                    fileSize = jsonObject.getString("fileSize");
                }
            }
        } catch (Exception e) {
            logger.error("确认发布(非采购)详细设计生成文件，上传失败", e);
        }

        String url = String.format("%smilepostDesignPlan/updateStatusPublishForNoPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" +
                        "&fileId=%s" +
                        "&fileName=%s" +
                        "&fileSize=%s",
                ModelsEnum.CTC.getBaseUrl(),
                formInstanceId,
                fdInstanceId,
                formUrl,
                eventName,
                handlerId,
                companyId,
                createUserId,
                fileId,
                fileName,
                fileSize);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批通过(非采购)")
    @PutMapping("updateStatusPassForNoPurchase/skipSecurityInterceptor")
    public Response updateStatusPassForNoPurchase(@RequestParam(required = false) Long formInstanceId,
                                                  @RequestParam(required = false) String fdInstanceId,
                                                  @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                  @RequestParam(required = false) String handlerId,
                                                  @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateStatusPassForNoPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批驳回(非采购)")
    @PutMapping("updateStatusRejectForNoPurchase/skipSecurityInterceptor")
    public Response updateStatusRejectForNoPurchase(@RequestParam(required = false) Long formInstanceId,
                                                    @RequestParam(required = false) String fdInstanceId,
                                                    @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                    @RequestParam(required = false) String handlerId,
                                                    @RequestParam(required = false) Long companyId,
                                                    @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateStatusRejectForNoPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批撤回(非采购)")
    @PutMapping("updateStatusReturnForNoPurchase/skipSecurityInterceptor")
    public Response updateStatusReturnForNoPurchase(@RequestParam(required = false) Long formInstanceId,
                                                    @RequestParam(required = false) String fdInstanceId,
                                                    @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                    @RequestParam(required = false) String handlerId,
                                                    @RequestParam(required = false) Long companyId,
                                                    @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateStatusReturnForNoPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目详设发布(非提前采购)流程作废")
    @PutMapping("updateStatusAbandonForNoPurchase/skipSecurityInterceptor")
    public Response updateStatusAbandonForNoPurchase(@RequestParam(required = false) Long formInstanceId,
                                                     @RequestParam(required = false) String fdInstanceId,
                                                     @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                     @RequestParam(required = false) String handlerId,
                                                     @RequestParam(required = false) Long companyId,
                                                     @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateStatusAbandonForNoPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目详设发布(非提前采购)流程作废")
    @PutMapping("updateStatusDeleteForNoPurchase/skipSecurityInterceptor")
    public Response updateStatusDeleteForNoPurchase(@RequestParam(required = false) Long formInstanceId,
                                                    @RequestParam(required = false) String fdInstanceId,
                                                    @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                    @RequestParam(required = false) String handlerId,
                                                    @RequestParam(required = false) Long companyId,
                                                    @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateStatusDeleteForNoPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "处理人通过(非采购)")
    @PutMapping("agreeForNoPurchase/skipSecurityInterceptor")
    public Response agreeForNoPurchase(@RequestParam(required = false) Long formInstanceId,
                                       @RequestParam(required = false) String fdInstanceId,
                                       @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                       @RequestParam(required = false) String handlerId,
                                       @RequestParam(required = false) Long companyId,
                                       @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/agreeForNoPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "确认发布(采购)")
    @PutMapping("updateStatusPublishForPurchase/skipSecurityInterceptor")
    public Response updateStatusPublishForPurchase(@RequestParam(required = false) Long formInstanceId,
                                                   @RequestParam(required = false) String fdInstanceId,
                                                   @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                   @RequestParam(required = false) String handlerId,
                                                   @RequestParam(required = false) Long companyId,
                                                   @RequestParam(required = false) Long createUserId) {
        // 取详细设计方案发布(采购)信息
        final Map<String, Object> param = new HashMap<>();
        param.put("submitId", formInstanceId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getDetailBySubmitId1", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url1, String.class);
        DataResponse<MilepostDesignPlanDetailApprovedVO> response1 = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDetailApprovedVO>>() {
                });
        //生成附件
        MultipartFile multipartFile = createAnnex(response1.getData());
        String fileId = "";
        String fileName = "";
        String fileSize = "";
        //上传附件
        try {
            if (multipartFile != null) {
                JSONArray result = ossService.upload(multipartFile);
                if (!ObjectUtils.isEmpty(result)) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject = result.getJSONObject(0);
                    fileId = jsonObject.getString("fileId");
                    fileName = jsonObject.getString("fileName");
                    fileSize = jsonObject.getString("fileSize");
                }
            }
        } catch (Exception e) {
            logger.error("确认发布(采购)详细设计生成文件，上传失败", e);
        }

        String url = String.format("%smilepostDesignPlan/updateStatusPublishForPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" +
                        "&fileId=%s" +
                        "&fileName=%s" +
                        "&fileSize=%s",
                ModelsEnum.CTC.getBaseUrl(),
                formInstanceId,
                fdInstanceId,
                formUrl,
                eventName,
                handlerId,
                companyId,
                createUserId,
                fileId,
                fileName,
                fileSize);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批通过(采购)")
    @PutMapping("updateStatusPassForPurchase/skipSecurityInterceptor")
    public Response updateStatusPassForPurchase(@RequestParam(required = false) Long formInstanceId,
                                                @RequestParam(required = false) String fdInstanceId,
                                                @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                @RequestParam(required = false) String handlerId,
                                                @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateStatusPassForPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批驳回(采购)")
    @PutMapping("updateStatusRejectForPurchase/skipSecurityInterceptor")
    public Response updateStatusRejectForPurchase(@RequestParam(required = false) Long formInstanceId,
                                                  @RequestParam(required = false) String fdInstanceId,
                                                  @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                  @RequestParam(required = false) String handlerId,
                                                  @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateStatusRejectForPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "审批撤回(采购)")
    @PutMapping("updateStatusReturnForPurchase/skipSecurityInterceptor")
    public Response updateStatusReturnForPurchase(@RequestParam(required = false) Long formInstanceId,
                                                  @RequestParam(required = false) String fdInstanceId,
                                                  @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                  @RequestParam(required = false) String handlerId,
                                                  @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateStatusReturnForPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目详设发布(提前采购)流程作废")
    @PutMapping("updateStatusAbandonForPurchase/skipSecurityInterceptor")
    public Response updateStatusAbandonForPurchase(@RequestParam(required = false) Long formInstanceId,
                                                   @RequestParam(required = false) String fdInstanceId,
                                                   @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                   @RequestParam(required = false) String handlerId,
                                                   @RequestParam(required = false) Long companyId,
                                                   @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateStatusAbandonForPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目详设发布(提前采购)流程删除")
    @PutMapping("updateStatusDeleteForPurchase/skipSecurityInterceptor")
    public Response updateStatusDeleteForPurchase(@RequestParam(required = false) Long formInstanceId,
                                                  @RequestParam(required = false) String fdInstanceId,
                                                  @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                  @RequestParam(required = false) String handlerId,
                                                  @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateStatusDeleteForPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "处理人通过(采购)")
    @PutMapping("agreeForPurchase/skipSecurityInterceptor")
    public Response agreeForPurchase(@RequestParam(required = false) Long formInstanceId,
                                     @RequestParam(required = false) String fdInstanceId,
                                     @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                     @RequestParam(required = false) String handlerId,
                                     @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/agreeForPurchase/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "发起项目经理确认")
    @PutMapping("updateModelStatusConfirming/skipSecurityInterceptor")
    public Response updateModelStatusConfirming(@RequestParam(required = false) Long formInstanceId,
                                                @RequestParam(required = false) String fdInstanceId,
                                                @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                @RequestParam(required = false) String handlerId,
                                                @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateModelStatusConfirming/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目经理确认通过")
    @PutMapping("updateModelStatusConfirmed/skipSecurityInterceptor")
    public Response updateModelStatusConfirmed(@RequestParam(required = false) Long formInstanceId,
                                               @RequestParam(required = false) String fdInstanceId,
                                               @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                               @RequestParam(required = false) String handlerId,
                                               @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateModelStatusConfirmed/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目经理确认驳回")
    @PutMapping("updateModelStatusUnconfirmed/skipSecurityInterceptor")
    public Response updateModelStatusUnconfirmed(@RequestParam(required = false) Long formInstanceId,
                                                 @RequestParam(required = false) String fdInstanceId,
                                                 @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                 @RequestParam(required = false) String handlerId,
                                                 @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateModelStatusUnconfirmed/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目经理确认撤回")
    @PutMapping("updateModelStatusReturn/skipSecurityInterceptor")
    public Response updateModelStatusReturn(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                            @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                            @RequestParam(required = false) String handlerId,
                                            @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateModelStatusReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目详设进度确认流程作废")
    @PutMapping("updateScheduleStatusAbandonConfirming/skipSecurityInterceptor")
    public Response updateScheduleStatusAbandonConfirming(@RequestParam(required = false) Long formInstanceId,
                                                          @RequestParam(required = false) String fdInstanceId,
                                                          @RequestParam(required = false) String formUrl,
                                                          @RequestParam(required = false) String eventName,
                                                          @RequestParam(required = false) String handlerId,
                                                          @RequestParam(required = false) Long companyId,
                                                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateScheduleStatusAbandonConfirming/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目详设进度确认流程删除")
    @PutMapping("updateScheduleStatusDeleteConfirming/skipSecurityInterceptor")
    public Response updateScheduleStatusDeleteConfirming(@RequestParam(required = false) Long formInstanceId,
                                                         @RequestParam(required = false) String fdInstanceId,
                                                         @RequestParam(required = false) String formUrl,
                                                         @RequestParam(required = false) String eventName,
                                                         @RequestParam(required = false) String handlerId,
                                                         @RequestParam(required = false) Long companyId,
                                                         @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/updateScheduleStatusDeleteConfirming/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "项目详设进度确认流程处理人通过")
    @PutMapping("agreeForConfirm/skipSecurityInterceptor")
    public Response agreeForConfirm(@RequestParam(required = false) Long formInstanceId,
                                    @RequestParam(required = false) String fdInstanceId,
                                    @RequestParam(required = false) String formUrl,
                                    @RequestParam(required = false) String eventName,
                                    @RequestParam(required = false) String handlerId,
                                    @RequestParam(required = false) Long companyId,
                                    @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/agreeForConfirm/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "提前采购审批通过")
    @PutMapping("purchaseInAdvancePass/skipSecurityInterceptor")
    public Response purchaseInAdvancePass(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                          @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                          @RequestParam(required = false) String handlerId,
                                          @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/purchaseInAdvancePass/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "发起提前采购审批")
    @PutMapping("purchaseInAdvancePassing/skipSecurityInterceptor")
    public Response purchaseInAdvancePassing(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                             @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                             @RequestParam(required = false) String handlerId,
                                             @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        // 取提前采购内容信息
        final Map<String, Object> param = new HashMap<>();
        param.put("submitRecordId", formInstanceId);
        String url1 = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/findPurchaseApprovalingDesignPlanDetail", param);
        ResponseEntity<String> responseEntity1 = restTemplate.getForEntity(url1, String.class);
        // 暂用MilepostDesignPlanDetailApprovedVO这个属性
        DataResponse<MilepostDesignPlanDetailApprovedVO> response1 = JSON.parseObject(responseEntity1.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDetailApprovedVO>>() {
                });
        //生成附件
        MultipartFile multipartFile = createAnnexPurchaseInAdvance(response1.getData());
        String fileId = "";
        String fileName = "";
        String fileSize = "";
        //上传附件
        try {
            if (multipartFile != null) {
                JSONArray result = ossService.upload(multipartFile);
                if (!ObjectUtils.isEmpty(result)) {
                    JSONObject jsonObject = result.getJSONObject(0);
                    fileId = jsonObject.getString("fileId");
                    fileName = jsonObject.getString("fileName");
                    fileSize = jsonObject.getString("fileSize");
                }
            }
        } catch (Exception e) {
            logger.error("详细设计部分确认发起提前采购审批生成文件，上传失败", e);
        }

        String url = String.format("%smilepostDesignPlan/purchaseInAdvancePassing/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s" +
                        "&fileId=%s" +
                        "&fileName=%s" +
                        "&fileSize=%s",
                ModelsEnum.CTC.getBaseUrl(),
                formInstanceId,
                fdInstanceId,
                formUrl,
                eventName,
                handlerId,
                companyId,
                createUserId,
                fileId,
                fileName,
                fileSize);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "提前采购审批驳回")
    @PutMapping("purchaseInAdvanceUnconfirmed/skipSecurityInterceptor")
    public Response purchaseInAdvanceUnconfirmed(@RequestParam(required = false) Long formInstanceId,
                                                 @RequestParam(required = false) String fdInstanceId,
                                                 @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                 @RequestParam(required = false) String handlerId,
                                                 @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/purchaseInAdvanceUnconfirmed/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "提前采购审批撤回")
    @PutMapping("purchaseInAdvanceReturn/skipSecurityInterceptor")
    public Response purchaseInAdvanceReturn(@RequestParam(required = false) Long formInstanceId, @RequestParam(required = false) String fdInstanceId,
                                            @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                            @RequestParam(required = false) String handlerId,
                                            @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/purchaseInAdvanceReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "提前采购流程作废")
    @PutMapping("purchaseInAdvanceForInvalid/skipSecurityInterceptor")
    public Response purchaseInAdvanceForInvalid(@RequestParam(required = false) Long formInstanceId,
                                                @RequestParam(required = false) String fdInstanceId,
                                                @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                                @RequestParam(required = false) String handlerId,
                                                @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/purchaseInAdvanceForInvalid/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "提前采购流程删除")
    @PutMapping("purchaseInAdvanceForDelete/skipSecurityInterceptor")
    public Response purchaseInAdvanceForDelete(@RequestParam(required = false) Long formInstanceId,
                                               @RequestParam(required = false) String fdInstanceId,
                                               @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                               @RequestParam(required = false) String handlerId,
                                               @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/purchaseInAdvanceForDelete/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "提前采购流程处理人通过")
    @PutMapping("agreeForPurchaseInAdvance/skipSecurityInterceptor")
    public Response agreeForPurchaseInAdvance(@RequestParam(required = false) Long formInstanceId,
                                              @RequestParam(required = false) String fdInstanceId,
                                              @RequestParam(required = false) String formUrl, @RequestParam(required = false) String eventName,
                                              @RequestParam(required = false) String handlerId,
                                              @RequestParam(required = false) Long companyId, @RequestParam(required = false) Long createUserId) {
        String url = String.format("%smilepostDesignPlan/agreeForPurchaseInAdvance/skipSecurityInterceptor" +
                        "?formInstanceId=%s" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%s" +
                        "&createUserId=%s",
                ModelsEnum.CTC.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "评审通过BOM类型里程碑", response = Boolean.class)
    @GetMapping({"passDeliveriesForBOM"})
    public Response passDeliveriesForBOM(@RequestParam @ApiParam("BOM类型里程碑id") Long projectDeliveriesId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectDeliveriesId", projectDeliveriesId);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/passDeliveriesForBOM", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<Boolean> response = JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
        return response;
    }

    @ApiOperation(value = "批量更新详细设计方案的设计成本", response = Boolean.class)
    @PostMapping({"updateDesignCost"})
    public Response updateDesignCost(@RequestBody @ApiParam("物料成本") MaterialCost materialCost) {
        String url = String.format("%smilepostDesignPlan/updateDesignCost", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, materialCost, String.class);
        DataResponse<String> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<String>>() {
                });
        return response;
    }

    @ApiOperation(value = "导出审批通过的详细设计BOM")
    @GetMapping("exportApprovedDesignPlanDetails")
    public void exportApprovedDesignPlanDetails(HttpServletResponse response, @RequestParam("projectId") @ApiParam("项目ID") Long projectId,
                                                @RequestParam(required = false) @ApiParam("里程碑ID") Long milepostId,
                                                @RequestParam(required = false, defaultValue = "false") @ApiParam("物料是否同步ERP数") boolean isSynchronizedErp,
                                                @RequestParam(required = false, defaultValue = "false") @ApiParam(
                                                        "是否生成采购需求数") boolean isGeneratePurchase) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("milepostId", milepostId);
        param.put("isSynchronizedErp", isSynchronizedErp);
        param.put("isGeneratePurchase", isGeneratePurchase);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/findApprovedDesignPlanDetail", param);

        String screenUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getDynamicPamCodeOrErpCodeList", param);
        String res = restTemplate.getForEntity(screenUrl, String.class).getBody();

        DataResponse<List<String>> screenDataResponse = JSON.parseObject(res, new TypeReference<DataResponse<List<String>>>() {
        });
        List<String> screenData = screenDataResponse.getData();

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<MilepostDesignPlanDetailApprovedVO> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDetailApprovedVO>>() {
                });
        MilepostDesignPlanDetailApprovedVO data = dataResponse.getData();
        List<MilepostDesignPlanDetailDto> dataList = data.getDesignPlanDetailDtos();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        //根据不同编码规则使用不同的Vo
        String materialCodeRule = getMaterialCodeRule(); //根据当前使用单位查询编码规则
        if (materialCodeRule.equals(MaterialCodeRuleEnum.KUNSHAN.getCode())) { //如果是‘昆山物料编码‘,走此逻辑。
            List<KsMilepostDesignPlanDetailApprovedExportVo> excelVos = BeanConverter.copy(dataList,
                    KsMilepostDesignPlanDetailApprovedExportVo.class);
            if (!CollectionUtils.isEmpty(screenData)) {
                if (isSynchronizedErp) {
                    excelVos = excelVos.stream().filter(vo -> screenData.contains(vo.getPamCode())).collect(Collectors.toList());
                }
                if (isGeneratePurchase) {
                    excelVos = excelVos.stream().filter(vo -> screenData.contains(vo.getId().toString()) &&
                            (Objects.isNull(vo.getGenerateRequirement()) || !vo.getGenerateRequirement())).collect(Collectors.toList());
                }
            }
            //导出操作
            ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", KsMilepostDesignPlanDetailApprovedExportVo.class,
                    this.buildExcelName(data.getProjectCode()), response);
        } else if (materialCodeRule.equals(MaterialCodeRuleEnum.KUKAMIA.getCode())
                || materialCodeRule.equals(MaterialCodeRuleEnum.KUKA2022.getCode())) { //如果是美的机器人编码规则，则走此逻辑。
            List<MilepostDesignPlanDetailApprovedExportVo> excelVos = BeanConverter.copy(dataList,
                    MilepostDesignPlanDetailApprovedExportVo.class);
            if (!CollectionUtils.isEmpty(screenData)) {
                if (isSynchronizedErp) {
                    excelVos = excelVos.stream().filter(vo -> screenData.contains(vo.getPamCode())).collect(Collectors.toList());
                }
                if (isGeneratePurchase) {
                    excelVos = excelVos.stream().filter(vo -> screenData.contains(vo.getId().toString()) &&
                            (Objects.isNull(vo.getGenerateRequirement()) || !vo.getGenerateRequirement())).collect(Collectors.toList());
                }
            }
            //导出操作
            ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", MilepostDesignPlanDetailApprovedExportVo.class,
                    this.buildExcelName(data.getProjectCode()), response);
        }
    }

    @ApiOperation(value = "导出提前采购审批中的详细设计BOM")
    @GetMapping("findPurchaseApprovalingDesignPlanDetail")
    public void findPurchaseApprovalingDesignPlanDetail(HttpServletResponse response, @RequestParam @ApiParam("提交记录id"
    ) Long submitRecordId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("submitRecordId", submitRecordId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan" +
                "/findPurchaseApprovalingDesignPlanDetail", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<MilepostDesignPlanDetailApprovedVO> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDetailApprovedVO>>() {
                });
        MilepostDesignPlanDetailApprovedVO data = dataResponse.getData();
        List<MilepostDesignPlanDetailDto> dataList = data.getDesignPlanDetailDtos();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        List<PurchaseInAdvanceExcelVO> excelVos = BeanConverter.copy(dataList, PurchaseInAdvanceExcelVO.class);

        for (PurchaseInAdvanceExcelVO excelVo : excelVos) {
            if (excelVo.getTotalNum() != null) {
                excelVo.setTotalNum(excelVo.getTotalNum().setScale(0));
            }
            if (excelVo.getNumber() != null) {
                excelVo.setNumber(excelVo.getNumber().setScale(0));
            }
            if (excelVo.getRequirementNum() != null) {
                excelVo.setRequirementNum(excelVo.getRequirementNum().setScale(0));
            }
            if (excelVo.getPurchaseNum() != null) {
                excelVo.setPurchaseNum(excelVo.getPurchaseNum().setScale(0));
            }
            if (excelVo.getDesignCost() != null) {
                excelVo.setDesignCost(excelVo.getDesignCost().setScale(2));
            }
            if (excelVo.getDesignSubtotal() != null) {
                excelVo.setDesignSubtotal(excelVo.getDesignSubtotal().setScale(2));
            }
            if (excelVo.getBudgetUnitPrice() != null) {
                excelVo.setBudgetUnitPrice(excelVo.getBudgetUnitPrice().setScale(2));
            }
            if (excelVo.getBudgetSubtotal() != null) {
                excelVo.setBudgetSubtotal(excelVo.getBudgetSubtotal().setScale(2));
            }
            excelVo.setDesignCost(excelVo.getDesignCost() == null ? BigDecimal.ZERO : excelVo.getDesignCost());
        }
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseInAdvanceExcelVO.class,
                "提前采购内容-" + DateUtil.format(DateUtil.getCurrentDate(), DateUtil.DATE_INTEGER_PATTERN) + ".xls",
                response);
    }

    @ApiOperation(value = "导出审批中的详细设计BOM")
    @GetMapping("exportApprovalingDesignPlanDetails")
    public void exportApprovalingDesignPlanDetails(HttpServletResponse response,
                                                   @RequestParam @ApiParam("提交记录id") Long submitId) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("submitId", submitId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/findApprovalingDesignPlanDetail",
                param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<MilepostDesignPlanDetailApprovedVO> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<MilepostDesignPlanDetailApprovedVO>>() {
                });
        MilepostDesignPlanDetailApprovedVO data = dataResponse.getData();
        List<MilepostDesignPlanDetailDto> dataList = data.getDesignPlanDetailDtos();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        //根据不同编码规则使用不同的Vo
        String materialCodeRule = getMaterialCodeRule(); //根据当前使用单位查询编码规则
        if (materialCodeRule.equals(MaterialCodeRuleEnum.KUNSHAN.getCode())) { //如果是‘昆山物料编码‘,走此逻辑。
            List<KsMilepostDesignPlanDetailApprovedExportVo> excelVos = BeanConverter.copy(dataList,
                    KsMilepostDesignPlanDetailApprovedExportVo.class);
            //导出操作
            ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", KsMilepostDesignPlanDetailApprovedExportVo.class,
                    this.buildExcelName(data.getProjectCode()), response);
        } else if (materialCodeRule.equals(MaterialCodeRuleEnum.KUKAMIA.getCode())) { //如果是美的机器人编码规则，则走此逻辑。
            List<MilepostDesignPlanDetailApprovedExportVo> excelVos = BeanConverter.copy(dataList,
                    MilepostDesignPlanDetailApprovedExportVo.class);
            //导出操作
            ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", MilepostDesignPlanDetailApprovedExportVo.class,
                    this.buildExcelName(data.getProjectCode()), response);
        }
    }

    private String buildExcelName(String projectCode) {
        return projectCode + "-详细设计方案-" + DateUtil.format(DateUtil.getCurrentDate(), DateUtil.DATE_INTEGER_PATTERN) + ".xls";
    }

    //根据数据产生Excel
    public MultipartFile createAnnex(MilepostDesignPlanDetailApprovedVO milepostDesignPlanDetailApprovedVO) {
        // 附件名称为：项目编号+“详设发布明细”+“_”+YYYYMMDD MM:HH:SS;举例：IA19108详设发布明细_20191127 14:09:01.xlsx
        MultipartFile multipartFile = null;
        String filePath =
                milepostDesignPlanDetailApprovedVO.getProjectCode() + "详设发布明细_" + DateUtils.format(new Date(),
                        "yyyyMMdd HH:mm:ss") + ".xls";
        try {
            ExportParams exportParams = new ExportParams(filePath, "Sheet1");
            List<MilepostDesignPlanDetailDto> designPlanDetailDtos =
                    milepostDesignPlanDetailApprovedVO.getDesignPlanDetailDtos();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(designPlanDetailDtos)) {
//                List<MilepostDesignPlanDetailApprovedExportVo> excelVos = BeanConverter.copy(designPlanDetailDtos,
//                MilepostDesignPlanDetailApprovedExportVo.class);
                List<MilepostDesignPlanDetailPurchaseExportVo> excelVos = this.buildExcelVos(designPlanDetailDtos);
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams,
                        MilepostDesignPlanDetailPurchaseExportVo.class, excelVos);
                File pdfFile = new File("/apps/pam/gateway/file/" + filePath);
                try (
                        OutputStream out = new FileOutputStream("/apps/pam/gateway/file/" + filePath);
                        FileInputStream fileInputStream = new FileInputStream(pdfFile);
                ) {
                    workbook.write(out);
                    multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(),
                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                } catch (IOException e) {
                    logger.info("详细设计变更生成文件，上传失败:", e.getMessage());
                }

            }
        } catch (Exception e) {
            logger.info("详细设计生成文件，上传失败:", e.getMessage());
        }
        return multipartFile;
    }

    //根据提前采购数据产生Excel
    public MultipartFile createAnnexPurchaseInAdvance(MilepostDesignPlanDetailApprovedVO milepostDesignPlanDetailApprovedVO) {
        // 附件名称为：项目编号+“详设发布明细”+“_”+YYYYMMDD MM:HH:SS;举例：IA19108详设发布明细_20191127 14:09:01.xlsx
        MultipartFile multipartFile = null;
        String filePath =
                milepostDesignPlanDetailApprovedVO.getProjectCode() + "提前采购明细_" + DateUtils.format(new Date(),
                        "yyyyMMdd HH:mm:ss") + ".xls";
        try {
            ExportParams exportParams = new ExportParams(filePath, "Sheet1");
            List<MilepostDesignPlanDetailDto> designPlanDetailDtos =
                    milepostDesignPlanDetailApprovedVO.getDesignPlanDetailDtos();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(designPlanDetailDtos)) {
                List<PurchaseInAdvanceExcelVO> excelVos = BeanConverter.copy(designPlanDetailDtos,
                        PurchaseInAdvanceExcelVO.class);
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, PurchaseInAdvanceExcelVO.class, excelVos);

                File pdfFile = new File("/apps/pam/gateway/file" + filePath);
                try (
                        OutputStream out = new FileOutputStream("/apps/pam/gateway/file/" + filePath);
                        FileInputStream fileInputStream = new FileInputStream(pdfFile);
                ) {
                    workbook.write(out);
                    multipartFile = new MockMultipartFile(pdfFile.getName(), pdfFile.getName(),
                            ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                } catch (IOException e) {
                    logger.info("提前采购生成文件，上传失败:", e.getMessage());
                }

            }
        } catch (Exception e) {
            logger.info("提前采购生成文件，上传失败:", e.getMessage());
        }
        return multipartFile;
    }

    // 最多做三层数据封装
    private List<MilepostDesignPlanDetailPurchaseExportVo> buildExcelVos(List<MilepostDesignPlanDetailDto> designPlanDetailDtos) {
        List<MilepostDesignPlanDetailPurchaseExportVo> milepostDesignPlanDetailPurchaseExportVos = new ArrayList<>();
        int i = 0;
        for (MilepostDesignPlanDetailDto designPlanDetailDto : designPlanDetailDtos) {
            i = i + 1;
            MilepostDesignPlanDetailPurchaseExportVo milepostDesignPlanDetailPurchaseExportVo =
                    new MilepostDesignPlanDetailPurchaseExportVo();
            BeanConverter.copy(designPlanDetailDto, milepostDesignPlanDetailPurchaseExportVo);
            milepostDesignPlanDetailPurchaseExportVo.setSerialNumber(String.valueOf(i));
            //去掉小数点后面的0
            milepostDesignPlanDetailPurchaseExportVo.setNumber(designPlanDetailDto.getNumber() == null ?
                    BigDecimal.ZERO : designPlanDetailDto.getNumber().stripTrailingZeros());
            milepostDesignPlanDetailPurchaseExportVos.add(milepostDesignPlanDetailPurchaseExportVo);
            List<MilepostDesignPlanDetailDto> firstSonDtos = designPlanDetailDto.getSonDtos();  //子模组数据
            int j = 0;
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(firstSonDtos)) {
                for (MilepostDesignPlanDetailDto firstSonDto : firstSonDtos) {
                    j = j + 1;
                    MilepostDesignPlanDetailPurchaseExportVo firstSonExportVo =
                            new MilepostDesignPlanDetailPurchaseExportVo();
                    BeanConverter.copy(firstSonDto, firstSonExportVo);
                    firstSonExportVo.setSerialNumber(String.valueOf(i) + "." + String.valueOf(j));
                    firstSonExportVo.setNumber(firstSonDto.getNumber() == null ? BigDecimal.ZERO :
                            firstSonDto.getNumber().stripTrailingZeros());
                    milepostDesignPlanDetailPurchaseExportVos.add(firstSonExportVo);
                    List<MilepostDesignPlanDetailDto> SecSonDtos = firstSonDto.getSonDtos();  //二级子模组数据
                    int k = 0;
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(SecSonDtos)) {
                        for (MilepostDesignPlanDetailDto secSonDto : SecSonDtos) {
                            k = k + 1;
                            MilepostDesignPlanDetailPurchaseExportVo SecSonExportVo =
                                    new MilepostDesignPlanDetailPurchaseExportVo();
                            BeanConverter.copy(secSonDto, SecSonExportVo);
                            SecSonExportVo.setSerialNumber(String.valueOf(i) + "." + String.valueOf(j) + "." + String.valueOf(k));
                            SecSonExportVo.setNumber(secSonDto.getNumber() == null ? BigDecimal.ZERO :
                                    secSonDto.getNumber().stripTrailingZeros());
                            milepostDesignPlanDetailPurchaseExportVos.add(SecSonExportVo);
                        }
                    }
                }
            }
        }
        return milepostDesignPlanDetailPurchaseExportVos;
    }

    @ApiOperation(value = "项目详设发布(非提前采购)流程作废")
    @GetMapping("getUpdateStatusAbandonForNoPurchase/skipSecurityInterceptor")
    public Response getUpdateStatusAbandonForNoPurchase(@RequestParam(required = false) Long handlerUserId,
                                                        @RequestParam(required = false) String formInstanceId) {
        String url = String.format("%smilepostDesignPlan/getUpdateStatusAbandonForNoPurchase/skipSecurityInterceptor" +
                "?formInstanceId=%s&handlerUserId=%d", ModelsEnum.CTC.getBaseUrl(), formInstanceId, handlerUserId);
        restTemplate.put(url, String.class);
        DataResponse<String> response = Response.dataResponse();
        return response;
    }

    @ApiOperation(value = "检测详细设计下有审批中的详细设计发布和变更流程", response = MilepostDesignPlanDto.class)
    @GetMapping("checkDetailByProjectIdAndMilepostId")
    public Response checkDetailByProjectIdAndMilepostId(@RequestParam @ApiParam("项目id") Long projectId,
                                                        @RequestParam @ApiParam("里程碑id") Long milepostId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("milepostId", milepostId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan" +
                "/checkDetailByProjectIdAndMilepostId", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(res,
                new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
                });
        return response;
    }

    @ApiOperation(value = "根据工单任务表id关联查询详细设计")
    @GetMapping("getMilepostPlanDesignByTicketTasksCode")
    public Response getMilepostPlanDesignByTicketTasksCode(@RequestParam(required = true) Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "milepostDesignPlan" +
                "/getMilepostPlanDesignByTicketTasksCode", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
        });
    }

    @ApiOperation("根据详细设计统计MRP")
    @GetMapping("getMilepostPlanDesignMRP")
    public Response getMilepostPlanDesignMRP(@RequestParam(required = false) Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getMilepostPlanDesignMRP", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<ResponseMap> response = JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
        return response;
    }

    @ApiOperation("根据详细设计统计MRP")
    @GetMapping("getMRP")
    public Response getMRP() {
        final Map<String, Object> param = new HashMap<>();
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getMRP", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MrpDto>>>() {
        });
    }

    @ApiOperation(value = "批量修改交付时间")
    @PostMapping("changeDeliveryTime")
    public Response changeDeliveryTime(@RequestBody UpdateDeliveryTimeDto dto) {
        String url = String.format("%smilepostDesignPlan/changeDeliveryTime", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<String> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<String>>() {
                });
        return response;
    }

    @ApiOperation(value = "批量修改交付时间")
    @PostMapping("updateTickDeliveryTimeDto")
    public Response updateTickDeliveryTimeDto(@RequestBody List<UpdateTickDeliveryTimeDto> dto) {
        String url = String.format("%smilepostDesignPlan/updateTickDeliveryTimeDto", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<String> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<String>>() {
                });
        return response;
    }

    @ApiOperation(value = "查询详细设计信息", response = MilepostDesignPlanDto.class)
    @GetMapping("selectMilepostDesignPlanDtoInfo")
    public Response selectMilepostDesignPlanDtoInfo(@RequestParam(required = false) @ApiParam("项目id") Long projectId,
                                                    @RequestParam(required = false) @ApiParam("里程碑id") Long milepostId,
                                                    @RequestParam(required = false) @ApiParam("预算id") Long projectBudgetMaterialId,
                                                    @RequestParam(required = false) @ApiParam("父节点id") Long parentId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("milepostId", milepostId);
        param.put("projectBudgetMaterialId", projectBudgetMaterialId);
        param.put("parentId", parentId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/selectMilepostDesignPlanDtoInfo",
                param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        DataResponse<List<MilepostDesignPlanDetailDto>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
                });
        return response;
    }

    @ApiOperation(value = "初始化详细设计导入", response = AsyncRequestResult.class)
    @PostMapping("importDesignPlan")
    public Response importDesignPlan(@RequestParam(value = "file") MultipartFile file) {
        List<ImportMilepostDesignPlanDetailExcelVo> detailExcelVos = null;
        try {
            detailExcelVos = FileUtil.importExcel(file, ImportMilepostDesignPlanDetailExcelVo.class, 1, 0);
        } catch (Exception e) {
            detailExcelVos = FileUtil.importExcel(file, ImportMilepostDesignPlanDetailExcelVo.class, 0, 0);
        }
        Iterator<ImportMilepostDesignPlanDetailExcelVo> iterator = detailExcelVos.iterator();
        //移除空行的数据
        while (iterator.hasNext()) {
            ImportMilepostDesignPlanDetailExcelVo ksDetailExcelVo = iterator.next();
            if (StringUtils.isBlank(ksDetailExcelVo.getSerialNumber())
                    && StringUtils.isBlank(ksDetailExcelVo.getPamCode())
                    //&& StringUtils.isBlank(ksDetailExcelVo.getSource())
                    //&& StringUtils.isBlank(ksDetailExcelVo.getModuleStatus())
                    && Objects.isNull(ksDetailExcelVo.getNumber())

            ) {
                iterator.remove();
            }

        }
        Asserts.notEmpty(detailExcelVos, ErrorCode.SYSTEM_FILE_EMPTY);
        Map<String, Object> param = new HashMap<>();

        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/importDesignPlan", param);
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
        httpRequestFactory.setConnectTimeout(600 * 1000);
        httpRequestFactory.setReadTimeout(600 * 1000);
        restTemplate.setRequestFactory(httpRequestFactory);
        String res = restTemplate.postForEntity(url, detailExcelVos, String.class).getBody();
        DataResponse<List<MilepostDesignPlanDetail>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<MilepostDesignPlanDetail>>>() {
                });

        return response;
    }

    @ApiOperation(value = "详细设计导入交付物数据校验(20210906迭代)", response = AsyncRequestResult.class)
    @PostMapping("importDeliverableNew")
    public Response importDeliverableNew(@RequestParam(value = "file") MultipartFile file, @RequestParam(value =
            "storageId") Long storageId, @RequestParam(required = false, value = "markId") Long markId) {
        DataResponse<List<MilepostDesignPlanDetailDto>> response = null;
        List<MilepostDesignPlanDetailImportExcelVo> detailExcelVos = null;
        List<MilepostDesignPlanDetailKsImportExcelVo> ksDetailExcelVos = null;
        String materialCodeRule = getMaterialCodeRule(); //根据当前使用单位查询编码规则
        try {
            //昆山迭代5新增功能，修改上传文件检验逻辑 begin
            if (materialCodeRule.equals(MaterialCodeRuleEnum.KUNSHAN.getCode())) { //如果是‘昆山物料编码‘,走此逻辑。
                try {
                    ksDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKsImportExcelVo.class, 1, 0);
                } catch (Exception e) {
                    ksDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKsImportExcelVo.class, 0, 0);
                }
                Iterator<MilepostDesignPlanDetailKsImportExcelVo> iterator = ksDetailExcelVos.iterator();
                //移除空行的数据
                while (iterator.hasNext()) {
                    MilepostDesignPlanDetailKsImportExcelVo ksDetailExcelVo = iterator.next();
                    if (StringUtils.isBlank(ksDetailExcelVo.getMaterielType())
                            && StringUtils.isBlank(ksDetailExcelVo.getCodingMiddleClass())
                            && StringUtils.isBlank(ksDetailExcelVo.getBrand())
                            && StringUtils.isBlank(ksDetailExcelVo.getName())
                            && StringUtils.isBlank(ksDetailExcelVo.getModel())
                            && StringUtils.isBlank(ksDetailExcelVo.getUnit())
                            && StringUtils.isBlank(ksDetailExcelVo.getFigureNumber())
                            && StringUtils.isBlank(ksDetailExcelVo.getChartVersion())
                            && StringUtils.isBlank(ksDetailExcelVo.getMachiningPartType())
                            && StringUtils.isBlank(ksDetailExcelVo.getMaterial())
                            && ksDetailExcelVo.getUnitWeight() == null
                            && StringUtils.isBlank(ksDetailExcelVo.getMaterialProcessing())
                            && StringUtils.isBlank(ksDetailExcelVo.getBrandMaterialCode())
                            && StringUtils.isBlank(ksDetailExcelVo.getOrSparePartsMask())
                            && StringUtils.isBlank(ksDetailExcelVo.getPamCode())
                            && StringUtils.isBlank(ksDetailExcelVo.getMaterialCategory())
                            && Objects.isNull(ksDetailExcelVo.getNumber())
                    ) {
                        iterator.remove();
                    }
                    if (materialCodeRule.equals(MaterialCodeRuleEnum.KUKAMIA.getCode())) { //如果是美的机器人编码规则，则走此逻辑。
                        if (StringUtils.isEmpty(ksDetailExcelVo.getDeliveryTimeStr())) {
                            Asserts.notEmpty(null, ErrorCode.CTC_MILEPOST_DESIGN_PLAN_DELIVERY_TIME_NULL);
                        }

                    }
                }
                Asserts.notEmpty(ksDetailExcelVos, ErrorCode.SYSTEM_FILE_EMPTY);
                Map<String, Object> param = new HashMap<>(1);
                param.put("storageId", storageId);
                param.put("markId", markId);
                String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan" +
                        "/ksMatchingMaterialCodeEstimateNew", param);
                String res = restTemplate.postForEntity(url, ksDetailExcelVos, String.class).getBody();
                response = JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
                });
                //昆山迭代5新增功能，修改上传文件检验逻辑 end
            } else if (materialCodeRule.equals(MaterialCodeRuleEnum.KUKAMIA.getCode())) { //如果是美的机器人编码规则，则走此逻辑。
                try {
                    detailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailImportExcelVo.class, 1, 0);
                } catch (Exception e) {
                    detailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailImportExcelVo.class, 0, 0);
                }
                Iterator<MilepostDesignPlanDetailImportExcelVo> iterator = detailExcelVos.iterator();
                while (iterator.hasNext()) {
                    MilepostDesignPlanDetailImportExcelVo detailExcelVo = iterator.next();
                    if (StringUtil.isNull(detailExcelVo.getMaterielType())
                            && StringUtil.isNull(detailExcelVo.getName())
                            && StringUtil.isNull(detailExcelVo.getModel())
                            && StringUtil.isNull(detailExcelVo.getUnit())
                            && StringUtil.isNull(detailExcelVo.getBrand())
                            && Objects.isNull(detailExcelVo.getNumber())
                            && Objects.isNull(detailExcelVo.getDeliveryTime())) {
                        iterator.remove();
                    }
                }
                Asserts.notEmpty(detailExcelVos, ErrorCode.SYSTEM_FILE_EMPTY);
                final String url = String.format("%smilepostDesignPlan/matchingMaterialCodeEstimate",
                        ModelsEnum.CTC.getBaseUrl());
                HttpComponentsClientHttpRequestFactory httpRequestFactory =
                        new HttpComponentsClientHttpRequestFactory();
                httpRequestFactory.setConnectionRequestTimeout(600 * 1000);
                httpRequestFactory.setConnectTimeout(600 * 1000);
                httpRequestFactory.setReadTimeout(600 * 1000);
                restTemplate.setRequestFactory(httpRequestFactory);
                String res = restTemplate.postForEntity(url, detailExcelVos, String.class).getBody();
                response = JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
                });
            } else {
                Asserts.success(ErrorCode.WLBMGZ_ERROR);
            }
        } catch (Exception e) {
            logger.info("详细设计生成文件，上传失败:", e.getMessage());
            Asserts.success(ErrorCode.SYSTEM_FILE_ERROR);
        }
        return response;
    }

    @ApiOperation("查询所有最底层数据id的所有上级")
    @PostMapping("findParentForPlanDetail")
    public Response findParentForPlanDetail(@RequestBody List<MilepostDesignPlanDetailDto> bottomPlanDetailDtos) {
        String url = String.format("%smilepostDesignPlan/findParentForPlanDetail", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, bottomPlanDetailDtos, String.class);
        return JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
                });
    }

    @ApiOperation("根据所有id整理上下级关系以及查询对应上级")
    @PostMapping("packagePlanDetail")
    public Response packagePlanDetail(@RequestBody List<MilepostDesignPlanDetailDto> designPlanDetailDtos) {
        String url = String.format("%smilepostDesignPlan/packagePlanDetail", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, designPlanDetailDtos, String.class);
        return JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
                });
    }

    @ApiOperation("根据项目id获取WBS最底层")
    @GetMapping("getWbsLastLayer")
    public Response getWbsLastLayer(@RequestParam Long projectId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "milepostDesignPlan" +
                "/getWbsLastLayer", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        String res = cleanStr(responseEntity.getBody());
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
        });
    }

    @ApiOperation("根据erpCode查询物料是否存在于详设里")
    @PostMapping("checkMaterialExist")
    public Response checkMaterialExist(@RequestBody MilepostDesignPlanDto dto) {
        String url = String.format("%smilepostDesignPlan/checkMaterialExist", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        return JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Boolean>>() {
                });
    }

    @ApiOperation(value = "根据项目ID取详细设计方案WBS层级:所有提交记录/设计信息/附件信息(柔性)", response = MilepostDesignPlanDto.class)
    @GetMapping("getWbsLayerDetailByProjectId")
    public Response getWbsLayerDetailByProjectId(@RequestParam(value = "projectId") Long projectId,
                                                 @RequestParam(value = "editQuery",required = false) Integer editQuery,
                                                 @RequestParam(value = "requirementCode",required = false) String wbsRequirementCode) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("projectId", projectId);
        param.put("editQuery", editQuery);
        param.put("wbsRequirementCode", wbsRequirementCode);
        String getMaterialCodeRuleUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "milepostDesignPlan/getWbsLayerDetailByProjectId", param);
        String res = restTemplate.getForObject(getMaterialCodeRuleUrl, String.class);
        DataResponse<MilepostDesignPlanDto> response = JSON.parseObject(res, new TypeReference<DataResponse<MilepostDesignPlanDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "根据详设ID取详细设计当前以下的所有层级:所有提交记录/设计信息/附件信息(wbs柔性)")
    @GetMapping("getDetailByParentId")
    public Response getDetailByParentId(@RequestParam(value = "detailId") Long detailId,
                                        @RequestParam(value = "editQuery",required = false) Integer editQuery,
                                        @RequestParam(value = "requirementCode",required = false) String wbsRequirementCode) {
        Map<String, Object> param = new HashMap<>(1);
        param.put("detailId", detailId);
        param.put("editQuery", editQuery);
        param.put("wbsRequirementCode", wbsRequirementCode);
        String getMaterialCodeRuleUrl = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "milepostDesignPlan/getDetailByParentId", param);
        String res = restTemplate.getForObject(getMaterialCodeRuleUrl, String.class);
        DataResponse<List<MilepostDesignPlanDetailDto>> response = JSON.parseObject(res,
                new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
                });
        return response;
    }

    @ApiOperation(value = "动态获取获取PAM物料编码或者ERP物料编码详情-柔性单位使用")
    @GetMapping({"getMilepostDesignPlanDetails"})
    public Response getMilepostDesignPlanDetails(@RequestParam @ApiParam("项目id") Long projectId,
                                                   @RequestParam(required = false) @ApiParam("里程碑id") Long milepostId,
                                                   @RequestParam(required = false, defaultValue = "false") @ApiParam(
                                                           "物料是否同步ERP数") boolean isSynchronizedErp,
                                                   @RequestParam(required = false, defaultValue = "false") @ApiParam(
                                                           "是否生成采购需求数") boolean isGeneratePurchase) {
        final Map<String, Object> param = new HashMap<>();
        param.put("projectId", projectId);
        param.put("milepostId", milepostId);
        param.put("isSynchronizedErp", isSynchronizedErp);
        param.put("isGeneratePurchase", isGeneratePurchase);
        String url = buildGetUrl(ModelsEnum.CTC.getBaseUrl(), "/milepostDesignPlan/getMilepostDesignPlanDetails",
                param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
        });
    }

    @ApiOperation(value = "自定义导出里程碑详细设计方案数据")
    @PostMapping("exportCustomDesignPlanDetails")
    public void exportCustomDesignPlanDetails(HttpServletResponse response,
                                              @RequestParam(required = false) @ApiParam("项目编号") String projectCode,
                                              @RequestBody List<MilepostDesignPlanExportRequestDto> dataList) throws Exception {
        logger.info("开始自定义导出里程碑详细设计方案数据，项目编号：{}，数据条数：{}", projectCode, dataList != null ? dataList.size() : 0);

        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据可导出");
        }

        // 1. 过滤掉deletedFlag为true的记录
        List<MilepostDesignPlanExportRequestDto> filteredList = dataList.stream()
                .filter(item -> item.getDeletedFlag() == null || !item.getDeletedFlag())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredList)) {
            throw new Exception("过滤后没有有效数据可导出");
        }

        // 2. 按序号排序
        filteredList.sort(this::compareOrderNo);

        // 3. 转换为Excel导出实体
        List<MilepostDesignPlanCustomExportVo> excelVos = BeanConverter.copy(filteredList, MilepostDesignPlanCustomExportVo.class);

        // 4. 生成文件名（项目编号_里程碑详细设计方案_时间戳）
        String fileName = (StringUtils.isNotBlank(projectCode) ? projectCode + "_" : "") +
                         "里程碑详细设计方案_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls";

        // 5. 导出Excel
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", MilepostDesignPlanCustomExportVo.class, fileName, response);

        logger.info("自定义导出里程碑详细设计方案数据完成，项目编号：{}，导出条数：{}", projectCode, excelVos.size());
    }

    /**
     * 序号排序比较器
     * 支持多层级序号排序，如：1, 1.1, 1.1.1, 1.1.2, 1.1.3
     */
    private int compareOrderNo(MilepostDesignPlanExportRequestDto o1, MilepostDesignPlanExportRequestDto o2) {
        String orderNo1 = o1.getOrderNo();
        String orderNo2 = o2.getOrderNo();

        if (StringUtils.isBlank(orderNo1)) {
            return StringUtils.isBlank(orderNo2) ? 0 : 1;
        }
        if (StringUtils.isBlank(orderNo2)) {
            return -1;
        }

        String[] arr1 = orderNo1.split("\\.");
        String[] arr2 = orderNo2.split("\\.");

        int minLength = Math.min(arr1.length, arr2.length);

        for (int i = 0; i < minLength; i++) {
            try {
                int num1 = Integer.parseInt(arr1[i]);
                int num2 = Integer.parseInt(arr2[i]);

                if (num1 != num2) {
                    return num1 - num2;
                }
            } catch (NumberFormatException e) {
                // 如果不是数字，按字符串比较
                int result = arr1[i].compareTo(arr2[i]);
                if (result != 0) {
                    return result;
                }
            }
        }

        // 如果前面都相同，长度短的排在前面
        return arr1.length - arr2.length;
    }


}
