package com.midea.pam.gateway.basedata.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.KSMatchingMaterialCodeEstimateNewHandDTO;
import com.midea.pam.common.ctc.dto.MilepostDesignPlanDetailDto;
import com.midea.pam.common.ctc.entity.AsyncRequestResult;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailKsImportExcelVo;
import com.midea.pam.common.ctc.excelVo.MilepostDesignPlanDetailKsWbsImportExcelVo;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.Asserts;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 详细设计导入校验控制类
 * @author: ex_xuwj4
 * @create: 2021-09-27
 **/
@RestController
@RequestMapping({"milepostDesignPlanImport", "mobile/app/brandMaintenance"})
@Api("详细设计导入校验")
public class MilepostDesignPlanImportController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(MilepostDesignPlanImportController.class);

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "详细设计导入交付物数据校验WBS", response = AsyncRequestResult.class)
    @PostMapping("ksMatchingMaterialCodeEstimateNewWbs")
    public Response importDeliverableNewWbs(@RequestParam(value = "file") MultipartFile file,
                                            @RequestParam(required = false, value = "storageId") Long storageId,
                                            @RequestParam(required = false, value = "markId") Long markId,
                                            @RequestParam(required = false, value = "wbsSummaryCode") String wbsSummaryCode,
                                            @RequestParam(required = false, value = "designPlanDetailDtoForWebInfo") String designPlanDetailDtoForWebInfo,
                                            @RequestParam(required = false, value = "projectId") Long projectId) {
        DataResponse<List<MilepostDesignPlanDetailDto>> response = null;
        List<MilepostDesignPlanDetailKsWbsImportExcelVo> ksDetailExcelVos = null;
        try {
            try {
                ksDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKsWbsImportExcelVo.class, 1, 0);
            } catch (Exception e) {
                ksDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKsWbsImportExcelVo.class, 0, 0);
            }
            Iterator<MilepostDesignPlanDetailKsWbsImportExcelVo> iterator = ksDetailExcelVos.iterator();
            //移除空行的数据
            while (iterator.hasNext()) {
                MilepostDesignPlanDetailKsWbsImportExcelVo ksDetailExcelVo = iterator.next();
                if (StringUtils.isBlank(ksDetailExcelVo.getMaterielType())
                        && StringUtils.isBlank(ksDetailExcelVo.getCodingMiddleClass())
                        && StringUtils.isBlank(ksDetailExcelVo.getBrand())
                        && StringUtils.isBlank(ksDetailExcelVo.getName())
                        && StringUtils.isBlank(ksDetailExcelVo.getModel())
                        && StringUtils.isBlank(ksDetailExcelVo.getUnit())
                        && StringUtils.isBlank(ksDetailExcelVo.getFigureNumber())
                        && StringUtils.isBlank(ksDetailExcelVo.getChartVersion())
                        && StringUtils.isBlank(ksDetailExcelVo.getMachiningPartType())
                        && StringUtils.isBlank(ksDetailExcelVo.getMaterial())
                        && ksDetailExcelVo.getUnitWeight() == null
                        && StringUtils.isBlank(ksDetailExcelVo.getMaterialProcessing())
                        && StringUtils.isBlank(ksDetailExcelVo.getBrandMaterialCode())
                        && StringUtils.isBlank(ksDetailExcelVo.getOrSparePartsMask())
                        && StringUtils.isBlank(ksDetailExcelVo.getPamCode())
                        && StringUtils.isBlank(ksDetailExcelVo.getErpCode())
                        && StringUtils.isBlank(ksDetailExcelVo.getMaterialCategory())
                        && Objects.isNull(ksDetailExcelVo.getNumber())
                        && Objects.isNull(ksDetailExcelVo.getDispatchIsStr())
                        && Objects.isNull(ksDetailExcelVo.getExtIsStr())
                ) {
                    iterator.remove();
                }
            }
            Asserts.notEmpty(ksDetailExcelVos, ErrorCode.SYSTEM_FILE_EMPTY);
            if (null != designPlanDetailDtoForWebInfo) {
                ksDetailExcelVos.get(0).setDesignPlanDetailDtoForWebInfo(JSONObject.parseArray(designPlanDetailDtoForWebInfo,
                        MilepostDesignPlanDetailDto.class));
            }
            Map<String, Object> param = new HashMap<>(2);
            param.put("storageId", storageId);
            param.put("markId", markId);
            param.put("wbsSummaryCode", wbsSummaryCode);
            param.put("projectId", projectId);
            String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/milepostDesignPlanImport" +
                    "/ksMatchingMaterialCodeEstimateNewWbs", param);
            String res = restTemplate.postForEntity(url, ksDetailExcelVos, String.class).getBody();
            response = JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
            });
        } catch (Exception e) {
            logger.info("详细设计生成文件，上传失败:", e.getMessage());
            Asserts.success(ErrorCode.SYSTEM_FILE_ERROR);
        }
        return response;
    }

    @ApiOperation(value = "详细设计导入交付物数据校验(20210906迭代)", response = AsyncRequestResult.class)
    @PostMapping("ksMatchingMaterialCodeEstimateNew")
    public Response importDeliverableNew(@RequestParam(value = "file") MultipartFile file,
                                         @RequestParam(value = "storageId") Long storageId,
                                         @RequestParam(required = false, value = "markId") Long markId,
                                         @RequestParam(required = false, value = "designPlanDetailDtoForWebInfo") String designPlanDetailDtoForWebInfo,
                                         @RequestParam(required = false, value = "projectTypeId") Long projectTypeId,
                                         @RequestParam(required = false, value = "projectId") Long projectId) {
        List<MilepostDesignPlanDetailKsImportExcelVo> ksDetailExcelVos;
        try {
            ksDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKsImportExcelVo.class, 1, 0);
        } catch (Exception e) {
            ksDetailExcelVos = FileUtil.importExcel(file, MilepostDesignPlanDetailKsImportExcelVo.class, 0, 0);
        }
        Guard.notNullOrEmpty(ksDetailExcelVos, "导入文件有误,模板数据为空");

        //移除空行的数据
        ksDetailExcelVos.removeIf(ksDetailExcelVo -> StringUtils.isBlank(ksDetailExcelVo.getMaterielType())
                && StringUtils.isBlank(ksDetailExcelVo.getCodingMiddleClass())
                && StringUtils.isBlank(ksDetailExcelVo.getBrand())
                && StringUtils.isBlank(ksDetailExcelVo.getName())
                && StringUtils.isBlank(ksDetailExcelVo.getModel())
                && StringUtils.isBlank(ksDetailExcelVo.getUnit())
                && StringUtils.isBlank(ksDetailExcelVo.getFigureNumber())
                && StringUtils.isBlank(ksDetailExcelVo.getChartVersion())
                && StringUtils.isBlank(ksDetailExcelVo.getMachiningPartType())
                && StringUtils.isBlank(ksDetailExcelVo.getMaterial())
                && StringUtils.isBlank(ksDetailExcelVo.getUnitWeightStr())
                && StringUtils.isBlank(ksDetailExcelVo.getMaterialProcessing())
                && StringUtils.isBlank(ksDetailExcelVo.getBrandMaterialCode())
                && StringUtils.isBlank(ksDetailExcelVo.getOrSparePartsMask())
                && StringUtils.isBlank(ksDetailExcelVo.getPamCode())
                && StringUtils.isBlank(ksDetailExcelVo.getErpCode())
                && StringUtils.isBlank(ksDetailExcelVo.getMaterialCategory())
                && StringUtils.isBlank(ksDetailExcelVo.getNumberStr()));
        Guard.notNullOrEmpty(ksDetailExcelVos, "导入文件有误,模板数据为空");

        if (StringUtils.isNotEmpty(designPlanDetailDtoForWebInfo)) {
            ksDetailExcelVos.get(0).setDesignPlanDetailDtoForWebInfo(JSONObject.parseArray(designPlanDetailDtoForWebInfo,
                    MilepostDesignPlanDetailDto.class));
        }
        logger.info("ksMatchingMaterialCodeEstimateNew导入的数据：{}", JSON.toJSONString(ksDetailExcelVos));

        Map<String, Object> param = new HashMap<>(2);
        param.put("storageId", storageId);
        param.put("markId", markId);
        param.put("projectId", projectId);
        param.put("projectTypeId", projectTypeId);
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/milepostDesignPlanImport/ksMatchingMaterialCodeEstimateNew", param);
        String res = restTemplate.postForEntity(url, ksDetailExcelVos, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
        });
    }

    @ApiOperation(value = "详细设计手动新增物料数据校验")
    @PostMapping("ksMatchingMaterialCodeEstimateNewHand")
    public Response ksMatchingMaterialCodeEstimateNewHand(@RequestBody KSMatchingMaterialCodeEstimateNewHandDTO handDTO) {
        Guard.notNull(handDTO, "导致参数为空");
        List<MilepostDesignPlanDetailKsImportExcelVo> excelVoList = handDTO.getExcelVoList();
        String designPlanDetailDtoForWebInfo = handDTO.getDesignPlanDetailDtoForWebInfo();
        Long storageId = handDTO.getStorageId();
        Long markId = handDTO.getMarkId();
        Long projectId = handDTO.getProjectId();
        Guard.notNullOrEmpty(excelVoList, "导入文件有误,模板数据为空");

        if (StringUtils.isNotEmpty(designPlanDetailDtoForWebInfo)) {
            excelVoList.get(0).setDesignPlanDetailDtoForWebInfo(JSONObject.parseArray(designPlanDetailDtoForWebInfo,
                    MilepostDesignPlanDetailDto.class));
        }

        Map<String, Object> param = new HashMap<>(2);
        param.put("storageId", storageId);
        param.put("markId", markId);
        param.put("projectId", projectId);
        param.put("operationType", "1");
        String url = buildGetUrl(ModelsEnum.BASEDATA.getBaseUrl(), "/milepostDesignPlanImport/ksMatchingMaterialCodeEstimateNew", param);
        String res = restTemplate.postForEntity(url, excelVoList, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<MilepostDesignPlanDetailDto>>>() {
        });
    }

}
