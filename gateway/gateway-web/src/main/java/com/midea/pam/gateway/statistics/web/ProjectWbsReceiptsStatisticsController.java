package com.midea.pam.gateway.statistics.web;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.ctc.dto.ProjectWbsReceiptsDto;
import com.midea.pam.common.ctc.excelVo.ProjectWbsReceiptsExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.ProjectWbsReceiptsEmailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("详细设计单据")
@RestController
@RequestMapping({"statistics/projectWbsRequirementPublish", "statistics/projectWbsReceipts"})
public class ProjectWbsReceiptsStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ProjectWbsReceiptsEmailService projectWbsReceiptsEmailService;

    @ApiOperation(value = "查询详细设计单据列表")
    @GetMapping("page")
    public Response list(ProjectWbsReceiptsDto projectWbsReceiptsDto,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(projectWbsReceiptsDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectWbsReceipts/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectWbsReceiptsDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<ProjectWbsReceiptsDto>>>() {
                });

        return response;
    }

    @ApiOperation(value = "查询我的详细设计单据列表")
    @GetMapping("createByMePage")
    public Response createByMePage(ProjectWbsReceiptsDto projectWbsReceiptsDto,
                                   @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        final Map<String, Object> params = buildParam(projectWbsReceiptsDto);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("me", Boolean.TRUE);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectWbsReceipts/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectWbsReceiptsDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<ProjectWbsReceiptsDto>>>() {
                });

        return response;
    }

    private Map buildParam(ProjectWbsReceiptsDto projectWbsReceiptsDto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("requirementCode", projectWbsReceiptsDto.getRequirementCode());
        params.put("requirementStatusStr", projectWbsReceiptsDto.getRequirementStatusStr());
        params.put("requirementTypeStr", projectWbsReceiptsDto.getRequirementTypeStr());
        params.put("confirmMode", projectWbsReceiptsDto.getConfirmMode());
        params.put("producerName", projectWbsReceiptsDto.getProducerName());
        params.put("handleName", projectWbsReceiptsDto.getHandleName());
        params.put("projectCode", projectWbsReceiptsDto.getProjectCode());
        params.put("projectName", projectWbsReceiptsDto.getProjectName());
        params.put("remark", projectWbsReceiptsDto.getRemark());
        params.put("startTimeStr", projectWbsReceiptsDto.getStartTimeStr());
        if (StringUtils.isNotEmpty(projectWbsReceiptsDto.getEndTimeStr())) {
            params.put("endTimeStr", projectWbsReceiptsDto.getEndTimeStr() + " 23:59:59");
        }
        params.put("projectWbsReceiptsId", projectWbsReceiptsDto.getProjectWbsReceiptsId());
        return params;
    }


    @ApiOperation(value = "详细设计单据列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response,
                           ProjectWbsReceiptsDto projectWbsReceiptsDto) {
        final Map<String, Object> params = buildParam(projectWbsReceiptsDto);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectWbsReceipts/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String,
                Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("详细设计单据");
        fileName.append(".xls");

        JSONArray projectWbsReceiptsArr = (JSONArray) resultMap.get("projectWbsReceipts");

        List<ProjectWbsReceiptsExcelVo> projectWbsReceiptsExcelVos =
                JSONObject.parseArray(projectWbsReceiptsArr.toJSONString(), ProjectWbsReceiptsExcelVo.class);
        for (int i = 0; i < projectWbsReceiptsExcelVos.size(); i++) {
            ProjectWbsReceiptsExcelVo projectWbsReceiptsExcelVO = projectWbsReceiptsExcelVos.get(i);
            projectWbsReceiptsExcelVO.setNum(i + 1);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(projectWbsReceiptsExcelVos,
                ProjectWbsReceiptsExcelVo.class, null, "详细设计单据", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "我的详细设计单据列表导出", response = ResponseMap.class)
    @GetMapping("exportMy")
    public void exportMy(HttpServletResponse response,
                         ProjectWbsReceiptsDto projectWbsReceiptsDto) {
        final Map<String, Object> params = buildParam(projectWbsReceiptsDto);
        params.put("me", Boolean.TRUE);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectWbsReceipts/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String,
                Object>>>() {
        });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("我的详细设计单据");
        fileName.append(".xls");

        JSONArray projectWbsReceiptsArr = (JSONArray) resultMap.get("projectWbsReceipts");

        List<ProjectWbsReceiptsExcelVo> projectWbsReceiptsExcelVos =
                JSONObject.parseArray(projectWbsReceiptsArr.toJSONString(), ProjectWbsReceiptsExcelVo.class);
        for (int i = 0; i < projectWbsReceiptsExcelVos.size(); i++) {
            ProjectWbsReceiptsExcelVo projectWbsReceiptsExcelVO = projectWbsReceiptsExcelVos.get(i);
            projectWbsReceiptsExcelVO.setNum(i + 1);
        }
        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(projectWbsReceiptsExcelVos,
                ProjectWbsReceiptsExcelVo.class, null, "我的详细设计单据", true);
        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @GetMapping("pendingSelect")
    public Response pendingSelect(@RequestParam int hours) {
        final Map<String, Object> params = new HashMap<>();
        params.put("hours",hours);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectWbsReceipts/pendingSelect", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ProjectWbsReceiptsDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<ProjectWbsReceiptsDto>>>() {
                });
        List<ProjectWbsReceiptsDto> data = response.getData();
        if (CollectionUtil.isNotEmpty(data)){
            projectWbsReceiptsEmailService.sendPendingEmail(data);
        }
        return response;
    }

    @GetMapping("approvalSelect")
    public Response approvalSelect(@RequestParam int hours) {
        final Map<String, Object> params = new HashMap<>();
        params.put("hours",hours);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/projectWbsReceipts/approvalSelect", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ProjectWbsReceiptsDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<ProjectWbsReceiptsDto>>>() {
                });
        List<ProjectWbsReceiptsDto> data = response.getData();
        if (CollectionUtil.isNotEmpty(data)){
            List<ProjectWbsReceiptsDto> projectWbsReceiptsDtos = projectWbsReceiptsEmailService.senApprovalEmail(data, hours);
            return response.setData(projectWbsReceiptsDtos);
        }
        return response;
    }

}
