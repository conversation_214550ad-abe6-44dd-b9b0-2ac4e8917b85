package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.CustomViewConfigDTO;
import com.midea.pam.common.constants.ProjectWbsCostConstant;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.constants.WbsBudgetFieldConstant;
import com.midea.pam.common.ctc.cache.WbsTemplateRuleCache;
import com.midea.pam.common.ctc.dto.ProjectDto;
import com.midea.pam.common.ctc.dto.ProjectMemberDto;
import com.midea.pam.common.ctc.dto.ProjectProgressPredictDto;
import com.midea.pam.common.ctc.vo.ProjectExcelVO;
import com.midea.pam.common.ctc.vo.ProjectMemberExcelVO;
import com.midea.pam.common.ctc.vo.ProjectMilepostExcelVO;
import com.midea.pam.common.ctc.vo.ProjectProgressPredictExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.statistics.dto.ProjectWbsBudgetVersionDto;
import com.midea.pam.common.statistics.entity.ProjectWbsBudgetVersion;
import com.midea.pam.common.util.BeanConverter;
import com.midea.pam.common.util.ExcelUtil;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.ProjectWbsBudgetUtils;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.service.MipWorkflowInnerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/2/12
 * @description
 */
@Api("统计-项目")
@RestController
@RequestMapping("statistics/project")
public class ProjectStatisticsController extends ControllerHelper {

    private static final String SENSITIVE_WORD_REPLACER = "******";

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkflowInnerService mipWorkflowInnerService;


    @ApiOperation(value = "查询项目列表")
    @GetMapping("page")
    public Response list(ProjectDto projectDto,
                         @RequestParam(required = false) @ApiParam("立项审批通过开始时间") String approvalDateStart,
                         @RequestParam(required = false) @ApiParam("立项审批通过结束时间") String approvalDateEnd,
                         @RequestParam(required = false) @ApiParam("事业部名称") String departmentName,
                         @RequestParam(required = false) @ApiParam("是否项目展示列表") String isView,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                         @RequestParam(required = false) String orderParam,
                         @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                         @RequestParam(required = false) @ApiParam("区域（多选）") String areaStr,
                         @RequestParam(required = false) @ApiParam("事业部（多选）") String businessDepartmentStr,
                         @RequestParam(required = false) @ApiParam("城市（模糊查询）") String city,
                         @RequestParam(required = false) @ApiParam("详细地址（模糊查询）") String detailedAddress) {
        final Map<String, Object> params = buildParam(projectDto);
        // 授权的（数据权限）
        params.put("list", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("approvalDateStart", approvalDateStart);
        params.put("approvalDateEnd", approvalDateEnd);
        params.put("departmentName", departmentName);
        params.put("isView", isView);
        params.put("orderParam", orderParam);
        params.put("orderType", orderType);
        params.put("areaStr", areaStr);
        params.put("businessDepartmentStr", businessDepartmentStr);
        params.put("city", city);
        params.put("detailedAddress", detailedAddress);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/project/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
                });


        return response;
    }

    private Map buildParam(ProjectDto projectDto) {
        final Map<String, Object> params = new HashMap<>();
        // 项目编号、项目名称、业务实体、客户名称、客户CRM编码、销售子合同编号、商机编号、“项目经理”模糊查询
        // "项目类型”、“项目属性”、项目状态、是否预立项使用下拉搜索(多选);
        params.put("code", projectDto.getCode());
        params.put("name", projectDto.getName());
        params.put("ouIds", projectDto.getOuIds());
        params.put("customerName", projectDto.getCustomerName());
        params.put("customerCode", projectDto.getCustomerCode());
        params.put("contractCode", projectDto.getContractCode());
        params.put("businessCode", projectDto.getBusinessCode());
        params.put("businessId", projectDto.getBusinessId());
        params.put("managerName", projectDto.getManagerName());
        params.put("types", projectDto.getTypes());
        params.put("priceTypes", projectDto.getPriceTypes());
        params.put("statuses", projectDto.getStatuses());
        params.put("previewFlags", projectDto.getPreviewFlags());

        params.put("status", projectDto.getStatus());
        params.put("priceType", projectDto.getPriceType());
        params.put("type", projectDto.getType());
        params.put("ouId", projectDto.getOuId());
        params.put("previewFlag", projectDto.getPreviewFlag());

        params.put("typesStr", projectDto.getTypesStr());
        params.put("priceTypesStr", projectDto.getPriceTypesStr());
        params.put("statusesStr", projectDto.getStatusesStr());
        params.put("previewFlagsStr", projectDto.getPreviewFlagsStr());
        params.put("ouIdsStr", projectDto.getOuIdsStr());
        params.put("resourceStatusStr", projectDto.getResourceStatusStr());
        params.put("isObjectiveProject", projectDto.getIsObjectiveProject());
        params.put("projectLevel", projectDto.getProjectLevel());
        params.put("fuzzyLike", projectDto.getFuzzyLike());
        params.put("unitName", projectDto.getUnitName());
        params.put("departmentName", projectDto.getDepartmentName());
        params.put("departmentNameStr", projectDto.getDepartmentNameStr());
        params.put("productIdStr", projectDto.getProductIdStr());
        return params;
    }

    private Map buildSelectProjectBatchParam(ProjectDto projectDto) {
        final Map<String, Object> params = new HashMap<>();
        // 项目编号、项目名称、项目经理，财务经理
        params.put("code", projectDto.getCode());
        params.put("name", projectDto.getName());
        params.put("managerName", projectDto.getManagerName());
        params.put("managerId", projectDto.getManagerId());
        params.put("financial", projectDto.getFinancial());
        params.put("financialName", projectDto.getFinancialName());
        params.put("projectNameOrCode", projectDto.getProjectNameOrCode());

        return params;
    }

    @ApiOperation(value = "查询我的项目列表")
    @GetMapping("createByMePage")
    public Response createByMePage(ProjectDto projectDto,
                                   @RequestParam(required = false) @ApiParam("立项审批通过开始时间") String approvalDateStart,
                                   @RequestParam(required = false) @ApiParam("立项审批通过结束时间") String approvalDateEnd,
                                   @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") final Integer pageSize,
                                   @RequestParam(required = false) String orderParam,
                                   @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                                   @RequestParam(required = false) @ApiParam("区域（多选）") String areaStr,
                                   @RequestParam(required = false) @ApiParam("事业部（多选）") String businessDepartmentStr,
                                   @RequestParam(required = false) @ApiParam("城市（模糊查询）") String city,
                                   @RequestParam(required = false) @ApiParam("详细地址（模糊查询）") String detailedAddress) {
        final Map<String, Object> params = buildParam(projectDto);
        params.put("me", Boolean.TRUE);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        params.put("approvalDateStart", approvalDateStart);
        params.put("approvalDateEnd", approvalDateEnd);
        params.put("orderParam", orderParam);
        params.put("orderType", orderType);
        params.put("areaStr", areaStr);
        params.put("businessDepartmentStr", businessDepartmentStr);
        params.put("city", city);
        params.put("detailedAddress", detailedAddress);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/project/v1/list", params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<PageInfo<ProjectDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<ProjectDto>>>() {
                });


        return response;
    }

    @ApiOperation(value = "查询（兼容我的）项目列表支持批量修改项目经理和财务经理的列表")
    @GetMapping("selectProjectBatch")
    public Response selectProjectBatch(ProjectDto projectDto, @RequestParam(required = false) Boolean isMe) {
        final Map<String, Object> params = buildSelectProjectBatchParam(projectDto);
        params.put("isMe", isMe);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/project/v1/selectProjectBatchList",
                params);

        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<List<ProjectDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<ProjectDto>>>() {
                });

        return response;
    }

    @ApiOperation(value = "统计我的项目列表人力外包rdm项目个数")
    @GetMapping("countRdmProject")
    public Response countRdmProject(ProjectDto projectDto) {
        final Map<String, Object> params = new HashMap<>();
        params.put("type", projectDto.getType());
        params.put("typesStr", projectDto.getTypesStr());
        params.put("me", Boolean.TRUE);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/project/v1/countRdmProject", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Long> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Long>>() {
                });
        return response;
    }

    @ApiOperation(value = "项目列表导出", response = ResponseMap.class)
    @GetMapping("export")
    public void listExport(HttpServletResponse response,
                           @RequestParam(required = false) @ApiParam("立项审批通过开始时间") String approvalDateStart,
                           @RequestParam(required = false) @ApiParam("立项审批通过结束时间") String approvalDateEnd,
                           ProjectDto projectDto,
                           @RequestParam(required = false) String orderParam,
                           @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType) {
        final Map<String, Object> params = buildParam(projectDto);
        // 项目编号、项目名称、业务实体、客户名称、客户CRM编码、销售子合同编号、商机编号、“项目经理”模糊查询
        // "项目类型”、“项目属性”、项目状态、是否预立项使用下拉搜索(多选);

        params.put("list", projectDto.getList());
        params.put("me", projectDto.getMe());
        params.put("approvalDateStart", approvalDateStart);
        params.put("approvalDateEnd", approvalDateEnd);
        params.put("orderParam", orderParam);
        params.put("orderType", orderType);

        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/project/v1/export", params);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Map<String, Object>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<Map<String, Object>>>() {
                });

        Map<String, Object> resultMap = dataResponse.getData();

        //导出操作
        StringBuffer fileName = new StringBuffer();
        fileName.append("项目信息");
        fileName.append(".xls");

        JSONArray projectArr = (JSONArray) resultMap.get("projects");
        JSONArray projectMilepostArr = (JSONArray) resultMap.get("projectMileposts");
        JSONArray projectMemberArr = (JSONArray) resultMap.get("projectMembers");
        JSONArray projectProgressPredict = (JSONArray) resultMap.get("projectProgressPredictDtoList");

        List<ProjectDto> projects = JSONObject.parseArray(projectArr.toJSONString(), ProjectDto.class);
        List<ProjectExcelVO> projectExcelVOS = BeanConverter.copy(projects, ProjectExcelVO.class);
        for (int i = 0; i < projectExcelVOS.size(); i++) {
            ProjectExcelVO projectExcelVO = projectExcelVOS.get(i);
            projectExcelVO.setNum(i + 1);
        }

        List<ProjectMilepostExcelVO> projectMileposts = JSONObject.parseArray(projectMilepostArr.toJSONString(), ProjectMilepostExcelVO.class);
        for (int i = 0; i < projectMileposts.size(); i++) {
            ProjectMilepostExcelVO projectMilepostExcelVO = projectMileposts.get(i);
            projectMilepostExcelVO.setNum(i + 1);
        }

        List<ProjectMemberExcelVO> projectMembers = JSONObject.parseArray(projectMemberArr.toJSONString(), ProjectMemberExcelVO.class);
        for (int i = 0; i < projectMembers.size(); i++) {
            ProjectMemberExcelVO projectMemberExcelVO = projectMembers.get(i);
            projectMemberExcelVO.setNum(i + 1);
            desensitizationExport(projectMemberExcelVO);
        }

        //项目进度预测信息
        List<ProjectProgressPredictDto> projectProgressPredictDtos = JSONObject.parseArray(projectProgressPredict.toJSONString(), ProjectProgressPredictDto.class);
        List<ProjectProgressPredictExcelVO> ProjectProgressPredictExcelList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < projectProgressPredictDtos.size(); i++) {
            ProjectProgressPredictDto projectProgressPredictDto = projectProgressPredictDtos.get(i);
            projectProgressPredictDto.setNum(i + 1);
        }
        for (ProjectProgressPredictDto projectProgressPredictDto : projectProgressPredictDtos) {
            ProjectProgressPredictExcelVO projectProgressPredictExcelVO = new ProjectProgressPredictExcelVO();
            projectProgressPredictExcelVO.setNum(projectProgressPredictDto.getNum());
            projectProgressPredictExcelVO.setProjectCode(projectProgressPredictDto.getProjectCode());
            projectProgressPredictExcelVO.setProjectName(projectProgressPredictDto.getProjectName());
            projectProgressPredictExcelVO.setMonth(projectProgressPredictDto.getMonth());
            projectProgressPredictExcelVO.setCumulativeRevenueString(projectProgressPredictDto.getCumulativeRevenueString());
            projectProgressPredictExcelVO.setCumulativeProgressPredictString(projectProgressPredictDto.getCumulativeProgressPredictString());
            projectProgressPredictExcelVO.setCumulativeIncomeForecastString(projectProgressPredictDto.getCumulativeIncomeForecastString());
            projectProgressPredictExcelVO.setRemark(projectProgressPredictDto.getRemark());
            projectProgressPredictExcelVO.setUpdateByName(projectProgressPredictDto.getUpdateByName());
            String updateAtString = sdf.format(projectProgressPredictDto.getUpdateAt());
            projectProgressPredictExcelVO.setUpdateAt(updateAtString);
            ProjectProgressPredictExcelList.add(projectProgressPredictExcelVO);
        }


        final Workbook workbook = ExportExcelUtil.buildDefaultSheet(projectExcelVOS, ProjectExcelVO.class, null,
                "基础信息", true);
        ExportExcelUtil.addSheet(workbook, projectMileposts, ProjectMilepostExcelVO.class, null, "项目里程碑", true);
        ExportExcelUtil.addSheet(workbook, projectMembers, ProjectMemberExcelVO.class, null, "项目成员", true);
        ExportExcelUtil.addSheet(workbook, ProjectProgressPredictExcelList, ProjectProgressPredictExcelVO.class, null, "项目进度预测", true);

        // 获取配置
        String res = restTemplate.getForEntity(ModelsEnum.BASEDATA.getBaseUrl() + "/resource/user/queryCustomView",
                String.class).getBody();
        DataResponse<CustomViewConfigDTO> customViewResponse = JSON.parseObject(res,
                new TypeReference<DataResponse<CustomViewConfigDTO>>() {
                });
        CustomViewConfigDTO customViewConfigDTO = customViewResponse.getData();
        List<String> fieldList = new ArrayList<>();
        if (customViewConfigDTO != null) {
            // 我的项目列表自定义视图导出
            if (Boolean.TRUE.equals(projectDto.getMe()) && StringUtils.isNotEmpty(customViewConfigDTO.getMyProjectTableTemplate())) {
                JSONArray jsonArray = JSON.parseArray(customViewConfigDTO.getMyProjectTableTemplate());
                for (int i = 0; i < jsonArray.size(); i++) {
                    fieldList.add(jsonArray.getJSONObject(i).getString("label"));
                }
                com.midea.pam.gateway.common.utils.ExportExcelUtil.dynamicExportFirstSheet(workbook, fieldList);
            }
            // 项目列表自定义视图导出
            if (Boolean.TRUE.equals(projectDto.getList()) && StringUtils.isNotEmpty(customViewConfigDTO.getProjectTableTemplate())) {
                JSONArray jsonArray = JSON.parseArray(customViewConfigDTO.getProjectTableTemplate());
                for (int i = 0; i < jsonArray.size(); i++) {
                    fieldList.add(jsonArray.getJSONObject(i).getString("label"));
                }
                com.midea.pam.gateway.common.utils.ExportExcelUtil.dynamicExportFirstSheet(workbook, fieldList);
            }
        }

        ExportExcelUtil.downLoadExcel(fileName.toString(), response, workbook);
    }

    @ApiOperation(value = "查询项目成员投入时间冲突信息", response = ProjectMemberDto.class)
    @GetMapping("getConflictInfo")
    public Response getConflictInfo(@RequestParam String userId,
                                    @RequestParam(required = false) String startDate,
                                    @RequestParam(required = false) String endDate,
                                    @RequestParam(required = false) Long projectId) {
        final Map<String, Object> param = new HashMap<>(4);
        param.put("userId", userId);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("projectId", projectId);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/project/getConflictInfo", param);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<ProjectMemberDto>>() {
        });
    }

    /**
     * 脱敏操作
     */
    private void desensitizationExport(ProjectMemberExcelVO projectMemberExcelVO) {
        if (Objects.isNull(projectMemberExcelVO)) {
            return;
        }
        String telphone = projectMemberExcelVO.getTelphone();
        if (StringUtils.isNotEmpty(telphone)) {
            projectMemberExcelVO.setTelphone(SENSITIVE_WORD_REPLACER);
        }
    }

    @ApiOperation("作废项目")
    @PostMapping("disable")
    public Response disable(@RequestBody ProjectDto dto) {
        String url = String.format("%sproject/disable", ModelsEnum.CTC.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);
        DataResponse<Boolean> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Boolean>>() {
        });
        //同时作废工作流
        if (response.getCode() == 0) {
            Long id = dto.getId();
            mipWorkflowInnerService.draftAbandon("preProjectApp", id);
            mipWorkflowInnerService.draftAbandon("projectApp", id);
            mipWorkflowInnerService.draftAbandon("projectKsYfApp", id);
            mipWorkflowInnerService.draftAbandon("projectTransferQualityApp", id);
        }
        return response;
    }

    @ApiOperation(value = "分页查看wbs预算快照")
    @GetMapping("wbsBudgetVersion/page")
    public Response wbsBudgetVersionPage(
            @RequestParam @ApiParam("项目id") Long projectId,
            @RequestParam(required = false) @ApiParam("类型（多选）") String businessTypeStr,
            @RequestParam(required = false) @ApiParam("版本时间起始") String versionTimeStart,
            @RequestParam(required = false) @ApiParam("版本时间截止") String versionTimeEnd,
            @RequestParam(required = false) @ApiParam("版本号") String versionCode,
            @RequestParam(required = false) @ApiParam("操作人") String createByName,
            @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("businessTypeStr", businessTypeStr);
        params.put("versionTimeStart", versionTimeStart);
        params.put("versionTimeEnd", versionTimeEnd);
        params.put("versionCode", versionCode);
        params.put("createByName", createByName);
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/project/wbsBudgetVersion/page", params);
        String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<ProjectWbsBudgetVersion>>>() {
        });
    }

    @ApiOperation(value = "查看wbs预算明细")
    @PostMapping("wbsBudgetVersion/detail")
    public Response wbsBudgetVersionDetail(@RequestBody Map<String, Object> param) {
        String url = String.format("%sstatistics/project/wbsBudgetVersion/detail", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        String res = responseEntity.getBody();
        DataResponse<ProjectWbsBudgetVersionDto> response = JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsBudgetVersionDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "wbs预算明细导出")
    @PostMapping("wbsBudgetVersion/detailExport")
    public void wbsBudgetVersionDetailExport(@RequestBody Map<String, Object> param, HttpServletResponse response) {
        Long wbsTemplateInfoId = MapUtils.getLong(param, ProjectWbsCostConstant.WBS_TEMPLATE_INFO_ID);
        Guard.notNull(wbsTemplateInfoId, "wbs模板Id不能为空");
        String url = String.format("%sstatistics/project/wbsBudgetVersion/detail", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, param, String.class);
        String res = responseEntity.getBody();
        DataResponse<ProjectWbsBudgetVersionDto> dataResponse = JSON.parseObject(res, new TypeReference<DataResponse<ProjectWbsBudgetVersionDto>>() {
        });
        ProjectWbsBudgetVersionDto dto = dataResponse.getData();

        /**********************************汇总********************************/
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);

        // 设置样式
        HSSFCellStyle cellLeftStyle = workbook.createCellStyle();
        cellLeftStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        cellLeftStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        cellLeftStyle.setFont(font);
        cellLeftStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        cellLeftStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);

        // 设置背景色
        HSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);// 上下居中
        titleStyle.setAlignment(XSSFCellStyle.ALIGN_LEFT);
        titleStyle.setFont(font);
        titleStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        titleStyle.setFillForegroundColor(HSSFColor.PALE_BLUE.index);//背景白色
        titleStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
        titleStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);

        HSSFSheet sheet = workbook.createSheet("预算明细");
        this.createSummarySheet(sheet, dto, wbsTemplateInfoId, cellLeftStyle, titleStyle);

        ExportExcelUtil.downLoadExcel("预算版本_" + dto.getVersionCode() + ".xls", response, workbook);
    }

    private void createSummarySheet(HSSFSheet sheet, ProjectWbsBudgetVersionDto po, Long wbsTemplateInfoId, HSSFCellStyle cellLeftStyle, HSSFCellStyle titleStyle) {
        List<Map<String, Object>> mapList = po.getMapList();
        for (int i = 0; i < mapList.size(); i++) {
            Map<String, Object> dataMap = mapList.get(i);
            dataMap.put(WbsBudgetFieldConstant.ORDER_NO, i + 1);
        }
        // wbs动态列
        List<WbsTemplateRuleCache> wbsCaches = ProjectWbsBudgetUtils.getEligibilityWbsCache(wbsTemplateInfoId);
        /* 导出数据JsonArray格式 */
        JSONArray dataJsonArray = JSONArray.parseArray(JSON.toJSONString(mapList));
        /* 标题 */
        LinkedHashMap<String, String> titleMap = new LinkedHashMap<>();
        titleMap.put(WbsBudgetFieldConstant.ORDER_NO, "序号");
        titleMap.put(WbsBudgetFieldConstant.PROJECT_CODE, "项目");
        if (!org.springframework.util.CollectionUtils.isEmpty(wbsCaches)) {
            for (WbsTemplateRuleCache wbsCacge : wbsCaches) {
                titleMap.put(wbsCacge.getKey(), wbsCacge.getRuleName());
            }
        }
        titleMap.put("wbsSummaryCode", "WBS号");
        titleMap.put(WbsBudgetFieldConstant.DESCRIPTION, "描述");
        titleMap.put(WbsBudgetFieldConstant.ACTIVITY_CODE, "活动事项编码");
        titleMap.put(WbsBudgetFieldConstant.ACTIVITY_NAME, "类别名称");
        titleMap.put(WbsBudgetFieldConstant.ACTIVITY_TYPE, "类别属性");
        titleMap.put(WbsBudgetFieldConstant.PRICE, "预算金额");

        int rowNum = 0;
        //标题
        LinkedList list = new LinkedList();
        Iterator<String> iterator = titleMap.keySet().iterator();
        Row row = sheet.createRow(rowNum);
        int i = 0;
        while (iterator.hasNext()) {
            String key = iterator.next();
            Cell cell = row.createCell(i);
            cell.setCellValue(titleMap.get(key));
            cell.setCellStyle(titleStyle);
            list.add(key);
            i++;
        }
        rowNum++;
        //数据
        ExcelUtil.setJsonData(0, rowNum, sheet, list, dataJsonArray, cellLeftStyle);
    }

}
