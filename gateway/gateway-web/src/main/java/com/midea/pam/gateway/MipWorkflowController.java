package com.midea.pam.gateway;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.mcomponent.core.util.Assert;
import com.midea.mcomponent.core.util.StringUtil;
import com.midea.pam.common.basedata.entity.UserInfo;
import com.midea.pam.common.constants.AduitAtta;
import com.midea.pam.common.constants.ResponseDate;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.crm.dto.QuotationDto;
import com.midea.pam.common.crm.entity.Quotation;
import com.midea.pam.common.enums.DeleteFlagEnum;
import com.midea.pam.common.enums.ErrorCode;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.enums.WorkflowEventType;
import com.midea.pam.common.enums.WorkflowOperationType;
import com.midea.pam.common.enums.WorkflowStatus;
import com.midea.pam.common.exception.BizException;
import com.midea.pam.common.gateway.entity.CurrentContext;
import com.midea.pam.common.gateway.entity.FormInstance;
import com.midea.pam.common.gateway.entity.FormInstanceExample;
import com.midea.pam.common.gateway.entity.FormTemplate;
import com.midea.pam.common.gateway.entity.FormTemplateExample;
import com.midea.pam.common.gateway.entity.MipCallbackLog;
import com.midea.pam.common.util.CacheDataUtils;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ListUtils;
import com.midea.pam.framework.core.exception.Guard;
import com.midea.pam.gateway.common.base.Code;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.constants.WorkflowBranch;
import com.midea.pam.gateway.common.constants.WorkflowResultCode;
import com.midea.pam.gateway.common.utils.DateUtil;
import com.midea.pam.gateway.common.utils.PamCurrentUserUtil;
import com.midea.pam.gateway.contract.service.PurchaseOrderService;
import com.midea.pam.gateway.crm.service.CustomerService;
import com.midea.pam.gateway.crm.service.QuotationService;
import com.midea.pam.gateway.mapper.FormInstanceMapper;
import com.midea.pam.gateway.remote.ProjectRemoteServer;
import com.midea.pam.gateway.service.FormInstanceService;
import com.midea.pam.gateway.service.FormTemplateService;
import com.midea.pam.gateway.service.MipSubmitService;
import com.midea.pam.gateway.service.MipWorkFlowBranchService;
import com.midea.pam.gateway.service.MipWorkflowService;
import com.midea.pam.gateway.service.impl.ExtService;
import com.midea.pam.gateway.service.impl.MipWorkflowBranchHelper;
import com.midea.pam.gateway.vo.NodeHandler;
import com.midea.pam.gateway.vo.WorkflowBaseForm;
import com.midea.pam.gateway.vo.WorkflowCirculateForm;
import com.midea.pam.gateway.vo.WorkflowDraftForm;
import com.midea.pam.gateway.vo.WorkflowRefuseForm;
import com.midea.pam.system.SystemContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 工作流接口
 * @date 2019-3-13
 */
@Api("工作流")
@RestController
@RequestMapping(value = {"workflow", "mobile/app/workflow"})
public class MipWorkflowController {

    private static final Logger logger = LoggerFactory.getLogger(MipWorkflowController.class);

    @Resource
    private MipWorkflowService mipWorkflowService;
    @Resource
    private FormTemplateService formTemplateService;
    @Resource
    private FormInstanceService formInstanceService;
    @Resource
    private CustomerService customerService;
    @Resource
    private QuotationService quotationService;
    @Resource
    private ExtService extService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MipWorkFlowBranchService workFlowBranchService;
    @Resource
    private ProjectRemoteServer projectRemoteServer;
    @Resource
    private PurchaseOrderService purchaseOrderService;
    @Resource
    private FormInstanceMapper formInstanceMapper;
    @Resource
    private MipSubmitService mipSubmitService;

    /**
     * =======================================================================
     * 流程信息
     * =======================================================================
     */

    @ApiOperation("我启动的流程")
    @GetMapping({"getMyStartProcess"})
    public Response getMyStartProcess(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                      @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                      @RequestParam(required = false) @ApiParam(value = "排序字段") String orderBy,
                                      @RequestParam(required = false) @ApiParam(value = "是否升序") Boolean isAsc,
                                      @RequestParam(required = false) @ApiParam(value = "流程到达该节点时间的开始时间") Long fdStartDateBegin,
                                      @RequestParam(required = false) @ApiParam(value = "流程到达该节点时间的结束时间") Long fdStartDateEnd,
                                      @RequestParam(required = false) @ApiParam(value = "标题") String docSubject,
                                      @RequestParam(required = false) @ApiParam(value = "流程状态:草稿：10;待审：20;通过：30") String docStatus,
                                      @RequestParam(required = false) @ApiParam(value = "表单模块ID") String fdModuleId
    ) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            JSONObject params = new JSONObject();
            // 为空或为0时表示查询所有记录
            params.put("page", pageNum);
            params.put("pageSize", pageSize);
            params.put("orderBy", orderBy);
            params.put("isAsc", isAsc);
            params.put("fdStartDateBegin", fdStartDateBegin);
            params.put("fdStartDateEnd", fdStartDateEnd);
            params.put("docSubject", docSubject);
            params.put("docStatus", docStatus);
            params.put("fdModuleId", fdModuleId);

            JSONObject res = mipWorkflowService.getMyStartProcess(loginName, params);
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("待办流程列表")
    @GetMapping({"getMyRunningProcess"})
    public Response getMyRunningProcess(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                        @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                        @RequestParam(required = false) @ApiParam(value = "排序字段") String orderBy,
                                        @RequestParam(required = false) @ApiParam(value = "是否升序") Boolean isAsc,
                                        @RequestParam(required = false) @ApiParam(value = "流程到达该节点时间的开始时间（时间戳传递 例如1504396800）") Long fdStartDateBegin,
                                        @RequestParam(required = false) @ApiParam(value = "标题") String docSubject,
                                        @RequestParam(required = false) @ApiParam(value = "表单模块ID") String fdModuleId,
                                        @RequestParam(required = false) @ApiParam(value = "表单代办url") String formUrl,
                                        @RequestParam(required = false) @ApiParam(value = "发起人姓名") String docCreatorName,
                                        @RequestParam(required = false) @ApiParam(value = "发起人的用户账号") String docCreatorLoginName
    ) {
        try {
            String loginUserName = PamCurrentUserUtil.getCurrentUserName();
            JSONObject params = new JSONObject();
            params.put("page", pageNum);
            params.put("pageSize", pageSize);
            params.put("orderBy", orderBy);
            params.put("isAsc", isAsc);
            params.put("fdStartDateBegin", fdStartDateBegin);
            params.put("docSubject", docSubject);
            params.put("fdModuleId", fdModuleId);
            params.put("docCreatorName", docCreatorName);
            params.put("docCreatorLoginName", docCreatorLoginName);
            if (StringUtil.isNotNull(formUrl) && StringUtil.isNull(fdModuleId)) {
                FormTemplate template = checkFormTemplate(formUrl);
                if (template != null && StringUtil.isNotNull(template.getFdTemplateId())) {
                    params.put("fdModuleId", template.getFdTemplateId());
                }
            }

            JSONObject res = mipWorkflowService.getMyRunningProcess(loginUserName, params);
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("已办流程列表")
    @GetMapping({"getMyWorkedProcess"})
    public Response getMyWorkedProcess(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                       @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                       @RequestParam(required = false) @ApiParam(value = "排序字段") String orderBy,
                                       @RequestParam(required = false) @ApiParam(value = "是否升序") Boolean isAsc,
                                       @RequestParam(required = false) @ApiParam(value = "流程到达该节点时间的开始时间") Long fdStartDateBegin,
                                       @RequestParam(required = false) @ApiParam(value = "标题") String docSubject,
                                       @RequestParam(required = false) @ApiParam(value = "流程状态:草稿：10;待审：20;通过：30") String docStatus,
                                       @RequestParam(required = false) @ApiParam(value = "表单模块ID") String fdModuleId,
                                       @RequestParam(required = false) @ApiParam(value = "表单代办url") String formUrl,
                                       @RequestParam(required = false) @ApiParam(value = "创建人账号") String docCreatorId
    ) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            JSONObject params = new JSONObject();
            params.put("page", pageNum);
            params.put("pageSize", pageSize);
            params.put("orderBy", orderBy);
            params.put("isAsc", isAsc);
            params.put("fdStartDateBegin", fdStartDateBegin);
            params.put("docSubject", docSubject);
            params.put("docStatus", docStatus);
            params.put("fdModuleId", fdModuleId);
            params.put("docCreatorId", docCreatorId);

            if (StringUtil.isNotNull(formUrl) && StringUtil.isNull(fdModuleId)) {
                FormTemplate template = checkFormTemplate(formUrl);
                if (template != null && StringUtil.isNotNull(template.getFdTemplateId())) {
                    params.put("fdModuleId", template.getFdTemplateId());
                }
            }

            JSONObject res = mipWorkflowService.getMyWorkedProcess(loginName, params);
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("抄送(传阅)给我的流程")
    @GetMapping({"getSendNodesToMe"})
    public Response getSendNodesToMe(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                     @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                     @RequestParam(required = false) @ApiParam(value = "排序字段") String orderBy,
                                     @RequestParam(required = false) @ApiParam(value = "是否升序") Boolean isAsc,
                                     @RequestParam(required = false) @ApiParam(value = "流程到达该节点时间的开始时间") Long fdStartDateBegin,
                                     @RequestParam(required = false) @ApiParam(value = "流程到达该节点时间的结束时间") Long fdStartDateEnd,
                                     @RequestParam(required = false) @ApiParam(value = "标题") String docSubject,
                                     @RequestParam(required = false) @ApiParam(value = "流程状态:草稿：10;待审：20;通过：30") String docStatus,
                                     @RequestParam(required = false) @ApiParam(value = "表单模块ID") String fdModuleId,
                                     @RequestParam(required = false) @ApiParam(value = "表单代办url") String formUrl,
                                     @RequestParam(required = false) @ApiParam(value = "起草人账号") String docCreatorId
    ) {
        try {

            String loginName = PamCurrentUserUtil.getCurrentUserName();
            JSONObject params = new JSONObject();
            params.put("page", pageNum);
            params.put("pageSize", pageSize);
            params.put("orderBy", orderBy);
            params.put("isAsc", isAsc);
            params.put("fdStartDateBegin", fdStartDateBegin);
            params.put("fdStartDateEnd", fdStartDateEnd);
            params.put("docSubject", docSubject);
            params.put("docStatus", docStatus);
            params.put("fdModuleId", fdModuleId);
            params.put("docCreatorId", docCreatorId);

            if (StringUtil.isNotNull(formUrl) && StringUtil.isNull(fdModuleId)) {
                FormTemplate template = checkFormTemplate(formUrl);
                if (template != null && StringUtil.isNotNull(template.getFdTemplateId())) {
                    params.put("fdModuleId", template.getFdTemplateId());
                }
            }

            JSONObject res = mipWorkflowService.getSendNodesToMe(loginName, params);
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("获取所有流程列表")
    @GetMapping({"getProcessList"})
    public Response getProcessList(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                   @RequestParam(required = false) @ApiParam(value = "模板编码(如果模板ID和模板编码不是对应关系,则报错)") String fdTemplateCode,
                                   @RequestParam(required = false) @ApiParam(value = "模板ID(如果模板ID和模板编码不是对应关系,则报错)") String fdTemplateId,
                                   @RequestParam(required = false) @ApiParam(value = "流程主题") Long fdSubject,
                                   @RequestParam(required = false) @ApiParam(value = "流程状态编码(草稿：10;待审：20;通过：30)") Long fdStatus,
                                   @RequestParam(required = false) @ApiParam(value = "创建人登录账号") String docCreatorId,
                                   @RequestParam(required = false) @ApiParam(value = "流程提交开始时间(时间戳)") String docCreateStartTime,
                                   @RequestParam(required = false) @ApiParam(value = "流程提交结束时间(时间戳)") String docCreateEndTime,
                                   @RequestParam(required = false) @ApiParam(value = "业务系统模型ID") String fdModuleId,
                                   @RequestParam(required = false) @ApiParam(value = "业务系统模板ID") String formTemplateId,
                                   @RequestParam(required = false) @ApiParam(value = "权限账号(为空查询所有数据,不为空查询该账号有权限的数据)") String fdCurrentUserAccount,
                                   @RequestParam(required = false) @ApiParam(value = "表单代办url") String formUrl
    ) {
        try {
            JSONObject params = new JSONObject();
            params.put("page", pageNum);
            params.put("pageSize", pageSize);

            params.put("fdTemplateCode", fdTemplateCode);
            params.put("fdTemplateId", fdTemplateId);
            params.put("fdSubject", fdSubject);
            params.put("fdStatus", fdStatus);
            params.put("docCreatorId", docCreatorId);
            params.put("docCreateStartTime", docCreateStartTime);
            params.put("docCreateEndTime", docCreateEndTime);
            params.put("fdModuleId", fdModuleId);
            params.put("formTemplateId", formTemplateId);
            params.put("fdCurrentUserAccount", fdCurrentUserAccount);

            if (StringUtil.isNotNull(formUrl) && StringUtil.isNull(fdModuleId)) {
                FormTemplate template = checkFormTemplate(formUrl);
                if (template != null && StringUtil.isNotNull(template.getFdTemplateId())) {
                    params.put("fdModuleId", template.getFdTemplateId());
                }
            }

            JSONObject res = mipWorkflowService.getProcessList(params);
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("获取流程模板类别")
    @GetMapping({"getFlowCategory"})
    public Response getFlowCategory() {
        try {
            String loginUserName = PamCurrentUserUtil.getCurrentUserName();
            JSONObject res = mipWorkflowService.getFlowCategory(loginUserName);
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("获取流程模板")
    @GetMapping({"getFlowTemplate/{docCategoryId}"})
    public Response getFlowTemplate(@PathVariable String docCategoryId) {
        try {
            String loginUserName = PamCurrentUserUtil.getCurrentUserName();
            JSONObject res = mipWorkflowService.getFlowTemplate(loginUserName, docCategoryId);
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("流程图")
    @GetMapping({"getProcessTableInfo/{formTemplateId}/{formInstanceId}"})
    public Response getProcessTableInfo(@PathVariable Long formInstanceId, @PathVariable String formTemplateId,
                                        @RequestParam(required = false) String fdInstanceId) {
        try {
            checkFormTemplate(formTemplateId);
            if (StringUtils.isEmpty(fdInstanceId)) {
                fdInstanceId = getInstanceId2(formTemplateId, formInstanceId);
            }

            JSONObject res = new JSONObject();
            if (StringUtils.isNotBlank(fdInstanceId)) {
                res = mipWorkflowService.getProcessTableInfo(PamCurrentUserUtil.getCurrentUserName(), fdInstanceId);
            }

            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    /**
     * 获取流程实例.
     *
     * @param formInstanceId 表单实例
     * @return 流程实例
     */
    private String getInstanceId(String templateId, Long formInstanceId) {

        String fdId = getInstanceId2(templateId, formInstanceId);
        if (StringUtils.isBlank(fdId)) {
            throw new MipException("流程实例不存在");
        }

        return fdId;
    }

    private String getInstanceId2(String templateId, Long formInstanceId) {
        final FormInstance instance = getInstance(templateId, formInstanceId);

        if (instance != null) {
            return instance.getFdInstanceId();
        } else {
            throw new MipException("流程实例不存在");
        }
    }

    private FormInstance getInstance(String templateId, Long formInstanceId) {
        // 使用单位
//        CurrentContext currentContext = CacheDataUtils.getContextByMip(PamCurrentUserUtil.getCurrentUserName());
//        final Long companyId = currentContext.getCurrentOrgId();
        FormInstanceExample condition = new FormInstanceExample();
        condition.createCriteria().andFormUrlEqualTo(templateId).andFormInstanceIdEqualTo(formInstanceId)
                .andDeletedFlagEqualTo(Boolean.FALSE);
        List<FormInstance> instances = formInstanceService.selectByExample(condition);

        if (instances.size() > 0) {
            instances.sort(Comparator.comparing(FormInstance::getId).reversed());
            return instances.get(0);
        } else {
            return null;
        }
    }

    @ApiOperation("流转日志")
    @GetMapping({"getFlowLog/{formTemplateId}/{formInstanceId}"})
    public Response getFlowLog(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                               @RequestParam(required = false) String fdInstanceId) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            if (StringUtils.isEmpty(fdInstanceId)) {
                fdInstanceId = getInstanceId2(formTemplateId, formInstanceId);
            }
            JSONObject res = new JSONObject();
            if (StringUtils.isNotBlank(fdInstanceId)) {
                res = mipWorkflowService.getFlowLog(loginName, fdInstanceId);
            }
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("获取流程当前审批节点信息（名称、审批人、流转时间）")
    @GetMapping({"getCurrentApproveNode/{formTemplateId}/{formInstanceId}"})
    public Response getCurrentApproveNode(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                               @RequestParam(required = false) String fdInstanceId) {
        try {
            JSONObject res = mipWorkflowService.getCurrentApproveNode(formTemplateId, formInstanceId, fdInstanceId);
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("流程状态")
    @GetMapping({"getProcessStatus/{formTemplateId}/{formInstanceId}"})
    public Response getProcessStatus(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                     @RequestParam(required = false) String fdInstanceId) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);

            if (StringUtils.isEmpty(fdInstanceId)) {
                fdInstanceId = getInstanceId2(formTemplateId, formInstanceId);
            }

            JSONObject res = new JSONObject();
            if (StringUtils.isNotBlank(fdInstanceId)) {
                res = mipWorkflowService.getProcessStatus(loginName, fdInstanceId);
            }
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("获取流程详情")
    @GetMapping({"getProcessInfo/{formTemplateId}/{formInstanceId}"})
    public Response getProcessInfo(@PathVariable Long formInstanceId, @PathVariable String formTemplateId,
                                   @RequestParam(required = false) String fdInstanceId) {
        try {
            checkFormTemplate(formTemplateId);
            if (StringUtils.isEmpty(fdInstanceId)) {
                fdInstanceId = getInstanceId2(formTemplateId, formInstanceId);
            }
            JSONObject res = new JSONObject();
            if (StringUtils.isNotBlank(fdInstanceId)) {
                res = mipWorkflowService.getProcessInfo(PamCurrentUserUtil.getCurrentUserName(), fdInstanceId);
            }
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("获取可用操作")
    @GetMapping({"getOperationList/{formTemplateId}/{formInstanceId}"})
    public Response getOperationList(@PathVariable Long formInstanceId, @PathVariable String formTemplateId,
                                     @RequestParam(required = false) String fdInstanceId) {
        try {
            checkFormTemplate(formTemplateId);
            if (StringUtils.isEmpty(fdInstanceId)) {
                fdInstanceId = getInstanceId2(formTemplateId, formInstanceId);
            }
            JSONObject res = new JSONObject();
            if (StringUtils.isNotBlank(fdInstanceId)) {
                res = mipWorkflowService.getOperationList(PamCurrentUserUtil.getCurrentUserName(), fdInstanceId);
            }
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("获取可驳回节点")
    @GetMapping({"getProcessRefuseNode/{formTemplateId}/{formInstanceId}"})
    public Response getProcessRefuseNode(@PathVariable Long formInstanceId,
                                         @PathVariable String formTemplateId,
                                         @RequestParam @ApiParam(value = "节点实例id") String nodeInstanceId) {
        try {
            checkFormTemplate(formTemplateId);
            String fdId = getInstanceId2(formTemplateId, formInstanceId);
            JSONObject res = new JSONObject();
            if (StringUtils.isNotBlank(fdId)) {
                res = mipWorkflowService.getProcessRefuseNode(PamCurrentUserUtil.getCurrentUserName(), fdId, nodeInstanceId);
            }
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    /**
     * =======================================================================
     * 审批动作
     * =======================================================================
     */
    @ApiOperation("初始化流程")
    @PostMapping({"initProcess/{formTemplateId}/{formInstanceId}"})
    public Response initProcess(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                @RequestBody(required = false) @ApiParam(name = "WorkflowDraftForm", value = "起草form") WorkflowDraftForm form) {
        try {
            final FormTemplate formTemplate = checkFormTemplate(formTemplateId);

            String fdId = null;
            final FormInstance instance = getInstance(formTemplateId, formInstanceId);

            if (instance == null) {
                //fdId为空，表示未初始化流程
                //调用初始化流程接口
                fdId = createProcess(formTemplateId, formInstanceId);
            } else {
                fdId = instance.getFdInstanceId();
            }

            JSONObject body = new JSONObject();

            JSONObject formData = MipWorkflowBranchHelper.buildFormData(formTemplateId, form.getFormData());
            body.put("formData", formData);
            body.put("formUrl", formTemplateId);
            body.put("applyNo", formInstanceId);
            body.put("companyId", SystemContext.getUnitId());
            body.put("formInstanceId", fdId);

            JSONObject flowTable = mipWorkflowService.getProcessTableInfo(PamCurrentUserUtil.getCurrentUserName(), fdId, body);
            DataResponse<JSONObject> response = Response.dataResponse();

            // 未提交的流程实例超过24小时未提交，流程中心会失效此流程实例；
            final JSONObject resultBody = flowTable.getJSONObject("body");
            if (resultBody != null) {
                final String resultCode = resultBody.getString("resultCode");
                // 流程实例不存在，重新创建流程
                if (Objects.equals(resultCode, WorkflowResultCode.INSTANCE_NOT_EXIST.getCode())) {
                    if (instance != null) {
                        // 移除不存在的流程实例
                        final Long id = instance.getId();
                        formInstanceService.deleteByPrimaryKey(id);
                        logger.info("流程实例不存在，移除流程：{}", JSONObject.toJSONString(instance));
                    }

                    fdId = createProcess(formTemplateId, formInstanceId);

                    flowTable = mipWorkflowService.getProcessTableInfo(PamCurrentUserUtil.getCurrentUserName(), fdId, body);
                }
            }

            return response.setData(flowTable);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    private String createProcess(String formTemplateId, Long formInstanceId) {
        //fdId为空，表示未初始化流程
        //调用初始化流程接口
        String fdTemplateId = checkFormTemplate(formTemplateId).getFdTemplateId();
        JSONObject res = mipWorkflowService.initProcess(PamCurrentUserUtil.getCurrentUserName(), fdTemplateId, formTemplateId);
        JSONObject body = (JSONObject) res.get("body");
        JSONObject data = (JSONObject) body.get("data");
        String fdId = data.getString("fdId");

        CurrentContext currentContext = CacheDataUtils.getContextByMip(PamCurrentUserUtil.getCurrentUserName());
        final Long companyId = currentContext.getCurrentOrgId();

        //保存流程信息
        FormInstance instance = new FormInstance();
        instance.setFdInstanceId(fdId);
        instance.setFormUrl(formTemplateId);
        instance.setFormInstanceId(formInstanceId);
        instance.setCompanyId(companyId);
        instance.setDeletedFlag(Boolean.FALSE);
        formInstanceService.insert(instance);

        return fdId;
    }

    /**
     * 获取表单模板id
     *
     * @param formUrl fromUrl
     * @return 表单模板
     */
    private FormTemplate checkFormTemplate(String formUrl) {
        String fdLoginName = PamCurrentUserUtil.getCurrentUserName();
        CurrentContext currentContext = CacheDataUtils.getContextByMip(fdLoginName);
        final Long companyId = currentContext.getCurrentOrgId();
        // 使用单位
        FormTemplateExample cond = new FormTemplateExample();
        cond.createCriteria().andFormUrlEqualTo(formUrl).andCompanyIdEqualTo(companyId);
        List<FormTemplate> forms = formTemplateService.selectByExample(cond);
        Assert.notEmpty(forms, "表单模板未配置");
        return forms.get(0);
    }

    @ApiOperation("通过")
    @PutMapping({"handlerPass/{formTemplateId}/{formInstanceId}"})
    public Response handlerPass(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                @RequestBody @ApiParam(name = "WorkflowBaseForm", value = "流程基础表单") WorkflowBaseForm form) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            String fdId = getInstanceId(formTemplateId, formInstanceId);
            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.HANDLER_PASS.getName()));
            }
            JSONObject res = mipWorkflowService.handlerPass(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("驳回")
    @PutMapping({"handlerRefuse/{formTemplateId}/{formInstanceId}"})
    public Response handlerRefuse(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                  @RequestBody @ApiParam(name = "WorkflowRefuseForm", value = "驳回form") WorkflowRefuseForm form) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            String fdId = getInstanceId(formTemplateId, formInstanceId);
            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.HANDLER_REFUSE.getName()));
            }
            if (form.getRefusePassedToThisNode() == null) {
                form.setRefusePassedToThisNode(false);
            }
            JSONObject res = mipWorkflowService.handlerRefuse(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())),
                    form.getRefusePassedToThisNode(), form.getJumpToNodeId(), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("沟通")
    @PutMapping({"handlerCommunicate/{formTemplateId}/{formInstanceId}"})
    public Response handlerCommunicate(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                       @RequestBody @ApiParam(name = "WorkflowCirculateForm", value = "沟通form") WorkflowCirculateForm form) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.HANDLER_COMMUNICATE.getName()));
            }
            String fdId = getInstanceId(formTemplateId, formInstanceId);
            JSONObject res = mipWorkflowService.handlerCommunicate(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())),
                    form.getToOtherPersons(), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("转办")
    @PutMapping({"handlerCommission/{formTemplateId}/{formInstanceId}"})
    public Response handlerCommission(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                      @RequestBody @ApiParam(name = "WorkflowCirculateForm", value = "转办form") WorkflowCirculateForm form) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.HANDLER_COMMISSION.getName()));
            }
            String fdId = getInstanceId(formTemplateId, formInstanceId);
            JSONObject res = mipWorkflowService.handlerCommission(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())),
                    form.getToOtherPersons(), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("回复沟通")
    @PutMapping({"handlerReturnCommunicate/{formTemplateId}/{formInstanceId}"})
    public Response handlerReturnCommunicate(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                             @RequestBody @ApiParam(name = "WorkflowBaseForm", value = "流程基础表单") WorkflowBaseForm form) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            String fdId = getInstanceId(formTemplateId, formInstanceId);
            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.HANDLER_RETURNCOMMUNICATE.getName()));
            }
            JSONObject res = mipWorkflowService.handlerReturnCommunicate(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("取消沟通")
    @PutMapping({"handlerCancelCommunicate/{formTemplateId}/{formInstanceId}"})
    public Response handlerCancelCommunicate(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                             @RequestBody @ApiParam(name = "WorkflowBaseForm", value = "流程基础表单") WorkflowBaseForm form) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            String fdId = getInstanceId(formTemplateId, formInstanceId);
            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.HANDLER_CANCELCOMMUNICATE.getName()));
            }
            JSONObject res = mipWorkflowService.handlerCancelCommunicate(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("起草人提交")
    @PutMapping({"draftSubmit/{formTemplateId}/{formInstanceId}"})
    public Response draftSubmit(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                @RequestBody @ApiParam(name = "WorkflowDraftForm", value = "起草form") WorkflowDraftForm form) {

        logger.info("需求发布起草人提交draftSubmit开始 formTemplateId:{}, formInstanceId:{}",
                formTemplateId, formInstanceId);
        try {
            String fdId = getInstanceId(formTemplateId, formInstanceId);
            FormTemplate template = checkFormTemplate(formTemplateId);
            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.DRAFT_SUBMIT.getName()));
            }
            //更新订单审批人信息
            if ("purchaseOrderChangeApp".equals(formTemplateId)) {
                updateApproveInfoByPurchaseOrderReceiptsId(formInstanceId, form);
            }
            JSONObject formData = form.getFormData();
            // 流程分支参数处理
            formData = MipWorkflowBranchHelper.buildFormData(formTemplateId, formData);
            // 如果配置了动态审批分支，formData入参要和初始化流程时一致
            FormInstanceExample formInstanceExample = new FormInstanceExample();
            formInstanceExample.setOrderByClause("id desc");
            formInstanceExample.createCriteria().andFdInstanceIdEqualTo(fdId)
                    .andFormUrlEqualTo(formTemplateId)
                    .andFormInstanceIdEqualTo(formInstanceId)
                    .andCompanyIdEqualTo(SystemContext.getUnitId())
                    .andDeletedFlagEqualTo(DeleteFlagEnum.NOT_DELETED.getBolValue());
            List<FormInstance> formInstanceList = formInstanceMapper.selectByExample(formInstanceExample);
            if (!CollectionUtils.isEmpty(formInstanceList) && formInstanceList.get(0) != null && StringUtils.isNotBlank(formInstanceList.get(0).getFromData())) {
                formData = JSONObject.parseObject(formInstanceList.get(0).getFromData());
            }
            //BUG2023072454922  商机产生的报价单的类型为商机报价单
            if (formTemplateId.startsWith("quotation")) {
                Quotation quotation = quotationService.getQuotationById(formInstanceId);
                if (Objects.equals(quotation.getOrNotSimple(), Boolean.TRUE)) {
                    form.setSubject("简化" + template.getModelName() + "：" + form.getSubject());
                } else {
                    form.setSubject(template.getModelName() + "：" + form.getSubject());
                }
            } else {
                form.setSubject(template.getModelName() + "：" + form.getSubject());
            }
            form.setFdTemplateId(template.getFdTemplateId());
            logger.info("需求发布起草人提交前draftSubmit入参：form:{}, username:{}, fdId:{}, formTemplateId:{}, formInstanceId:{}, formData:{}",
                    JSON.toJSONString(form), PamCurrentUserUtil.getCurrentUserName(), fdId, formTemplateId, formInstanceId,
                    JSON.toJSONString(formData));
            //调用业务逻辑检查单据的状态
            mipSubmitService.executeTableDataOperation(formTemplateId, formInstanceId, fdId, "check");
            JSONObject res = mipWorkflowService.draftSubmit(form.getOpt(), PamCurrentUserUtil.getCurrentUserName(), fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())),
                    subSubject(form.getSubject()), form.getFdTemplateId(), form.getFdUrl(),
                    formTemplateId, String.valueOf(formInstanceId), formData, form.getAuditFileDocIdList());
            logger.info("需求发布起草人提交draftSubmit后结果返回res:{}", JSON.toJSONString(res));
            checkResultAndThrowsException(res);
            DataResponse<JSONObject> response = Response.dataResponse();
            //模拟iflow【起草人提交】回调事件
            JSONObject param = initCallbackParams(formTemplateId, String.valueOf(formInstanceId), form.getFdTemplateId(), fdId);
            mipWorkflowService.callback(param);
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    private static JSONObject initCallbackParams(String formTemplateId, String formInstanceId, String fdModuleId, String fdId) {
        // 获取当前时间戳
        long timestamp = System.currentTimeMillis();

        // 创建 JSON 对象
        JSONObject jsonObject = new JSONObject();

        // 创建 header 部分
        JSONObject header = new JSONObject();
        header.put("checkModel", false);
        header.put("timestamp", String.valueOf(timestamp));
        jsonObject.put("header", header);

        // 创建 body 部分
        JSONObject body = new JSONObject();

        // formData
        body.put("formData", "{\"INPUT\":0}");

        // formParam
        JSONObject formParam = new JSONObject();
        formParam.put("fdModuleId", fdModuleId);
        formParam.put("formInstanceId", formInstanceId);
        formParam.put("formTemplateId", formTemplateId);
        body.put("formParam", formParam);

        // processId
        body.put("processId", fdId);

        // processParam
        JSONArray processParam = new JSONArray();
        JSONObject param = new JSONObject();
        param.put("businessParam", "");
        param.put("editable", false);
        param.put("eventType", WorkflowEventType.DRAFT_SUBMIT_EVENT.getCode());
        param.put("extendsParam", "{\"auditNote\":\"\"}");
        param.put("handlerId", SystemContext.getUserMip());
        param.put("handlerName", SystemContext.getUserName());
        param.put("isRetrying", false);
        param.put("nodeId", "N1");
        param.put("operationtype", "draft_submit");
        param.put("processId", fdId);
        param.put("taskUserId", "");
        param.put("taskUserName", "");
        processParam.add(param);

        body.put("processParam", processParam);

        // 将 body 添加到 JSON 对象
        jsonObject.put("body", body);

        return jsonObject;

    }

    /**
     * 标题最大限制长度500
     *
     * @param str
     * @return
     */
    private String subSubject(String str) {
        return str.length() > 500 ? str.substring(0, 499) : str;
    }

    private void updateApproveInfoByPurchaseOrderReceiptsId(Long receiptsId, WorkflowDraftForm form) {
        List<NodeHandler> changeNodeHandler = form.getChangeNodeHandler();
        if (ListUtils.isEmpty(changeNodeHandler)) {
            return;
        }
        List<String> approveNodes = changeNodeHandler.stream().filter(o -> Objects.equals("approveNode", o.getActivityType())).
                map(entity -> entity.getNodeId() + "@" + entity.getHandlerIds()).collect(Collectors.toList());
        String approveInfo = StringUtils.join(approveNodes, ",");
        if (StringUtils.isNotBlank(approveInfo)) {
            //保存审批节点审批人信息
            purchaseOrderService.updateApproveInfoByPurchaseOrderReceiptsId(receiptsId, approveInfo);
        }
    }

    @ApiOperation("保存草稿")
    @PutMapping({"draft/{formTemplateId}/{formInstanceId}"})
    public Response draft(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                          @RequestBody @ApiParam(name = "WorkflowDraftForm", value = "起草form") WorkflowDraftForm form) {
        try {
            checkFormTemplate(formTemplateId);
            String fdId = getInstanceId(formTemplateId, formInstanceId);
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            JSONObject res = mipWorkflowService.draft(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())),
                    form.getSubject(), form.getFdTemplateId(), form.getFdUrl(),
                    formTemplateId, String.valueOf(formInstanceId), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("起草人废弃")
    @PutMapping({"draftAbandon/{formTemplateId}/{formInstanceId}"})
    public Response draftAbandon(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                 @RequestBody @ApiParam(name = "WorkflowBaseForm", value = "流程基础表单") WorkflowBaseForm form) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            String fdId = getInstanceId(formTemplateId, formInstanceId);

            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.DRAFT_ABANDON.getName()));
            }
            //项目作废 业务处理
            if (Objects.equals(formTemplateId, "project")
                    || Objects.equals(formTemplateId, "projectKsYfApp")
                    || Objects.equals(formTemplateId, "preProjectApp")) {
                projectRemoteServer.updateProjectContractRs(formInstanceId);
            }
            JSONObject res = mipWorkflowService.draftAbandon(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("起草人撤回")
    @PutMapping({"draftReturn/{formTemplateId}/{formInstanceId}"})
    public Response draftReturn(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                @RequestBody @ApiParam(name = "WorkflowBaseForm", value = "流程基础表单") WorkflowBaseForm form) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            String fdId = getInstanceId(formTemplateId, formInstanceId);

            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.DRAFT_RETURN.getName()));
            }

            JSONObject res = mipWorkflowService.draftReturn(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("审批人废弃")
    @PutMapping({"handlerAbandon/{formTemplateId}/{formInstanceId}"})
    public Response handlerAbandon(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                   @RequestBody @ApiParam(name = "WorkflowBaseForm", value = "流程基础表单") WorkflowBaseForm form) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.HANDLER_ABANDON.getName()));
            }
            String fdId = getInstanceId(formTemplateId, formInstanceId);
            JSONObject res = mipWorkflowService.handlerAbandon(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("传阅")
    @PutMapping({"circulate/{formTemplateId}/{formInstanceId}"})
    public Response circulate(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                              @RequestBody @ApiParam(name = "WorkflowCirculateForm", value = "传阅form") WorkflowCirculateForm form) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.CIRCULATE.getName()));
            }
            String fdId = getInstanceId(formTemplateId, formInstanceId);
            JSONObject res = mipWorkflowService.circulate(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())),
                    form.getToOtherPersons(), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("催办")
    @PutMapping({"remind/{formTemplateId}/{formInstanceId}"})
    public Response remind(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                           @RequestBody @ApiParam(name = "WorkflowBaseForm", value = "流程基础表单") WorkflowBaseForm form) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            if (form.getOpt() == null) {
                form.setOpt(getOpt(formTemplateId, formInstanceId, WorkflowOperationType.REMIND.getName()));
            }
            String fdId = getInstanceId(formTemplateId, formInstanceId);
            JSONObject res = mipWorkflowService.remind(form.getOpt(), loginName, fdId, form.getAuditNote(),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeHandler())),
                    JSONArray.parseArray(JSONArray.toJSONString(form.getChangeNodeFormUrls())), form.getAuditFileDocIdList());
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("删除流程（慎用）")
    @PutMapping({"deleteProcess/{formTemplateId}/{formInstanceId}"})
    public Response deleteProcess(@PathVariable String formTemplateId, @PathVariable Long formInstanceId) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            FormInstance instance = getInstance(formTemplateId, formInstanceId);
            if (instance == null) {
                throw new BizException(Code.ERROR.getCode(), "流程实例不存在");
            }

            JSONObject processStatusResult = mipWorkflowService.getProcessStatus(loginName, instance.getFdInstanceId());
            boolean success = checkResult(processStatusResult);
            if (success) {
                JSONObject data = processStatusResult.getJSONObject("body").getJSONObject("data");
                if (Objects.nonNull(data)) {
                    String fdStatusCode = data.getString("fdStatusCode");
                    String fdStatus = data.getString("fdStatus");
                    if (!WorkflowStatus.DRAFT.getCode().equals(fdStatusCode) && !WorkflowStatus.ERROR.getCode().equals(fdStatusCode)) {
                        throw new BizException(Code.ERROR.getCode(), "流程状态为" + fdStatus + "，不允许删除");
                    }
                }

                JSONObject res = mipWorkflowService.delete(loginName, instance.getFdInstanceId());
                success = checkResult(res);

                // 流程删除成功，业务处理
                if (success) {
                    MipCallbackLog log = new MipCallbackLog();
                    log.setCallbackTime(new Date());
                    UserInfo userInfo = CacheDataUtils.findUserByMip(loginName);
                    Long userId = userInfo == null ? null : userInfo.getId();
                    // 删除成功之后回调，模拟插入流程回调事件
                    mipWorkflowService.callback(log, formTemplateId, WorkflowEventType.DELETE.getCode(), loginName,
                            userId, formInstanceId.toString(), instance.getFdInstanceId(), null);

                    // 删除流程
                    instance.setDeletedFlag(Boolean.TRUE);
                    instance.setRemark("用户删除流程");
                    formInstanceService.updateByPrimaryKey(instance);
                } else {
                    DataResponse<Object> response = Response.dataResponse();
                    response.setCode(Code.ERROR.getCode());
                    response.setMsg("流程删除失败");
                    response.setData(res);
                    return response;
                }

                DataResponse<JSONObject> response = Response.dataResponse();
                return response.setData(res);
            } else {
                DataResponse<Object> response = Response.dataResponse();
                response.setCode(Code.ERROR.getCode());
                response.setMsg("获取流程信息异常");
                response.setData(processStatusResult);
                return response;
            }
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    private boolean checkResult(JSONObject res) {
        if (Objects.nonNull(res)) {
            JSONObject body = res.getJSONObject("body");
            if (Objects.nonNull(body)) {
                String resultCode = body.getString("resultCode");
                return WorkflowResultCode.SUCCESS.getCode().equals(resultCode);
            }
        }
        return Boolean.FALSE;
    }

    private void checkResultAndThrowsException(JSONObject res) {
        if (Objects.nonNull(res)) {
            JSONObject body = res.getJSONObject("body");
            if (Objects.nonNull(body)) {
                String resultCode = body.getString("resultCode");
                if (!WorkflowResultCode.SUCCESS.getCode().equals(resultCode)) {
                    throw new BizException(Code.ERROR.getCode(), body.getString("resultMsg"));
                }
            }
        }
    }

    @ApiOperation("获取所有流程总数")
    @GetMapping({"getProcessCount"})
    public Response getProcessCount(@ApiParam("模板编码(如果模板ID和模板编码不是对应关系,则报错)") @RequestParam String fdTemplateCode,
                                    @ApiParam("模板ID(如果模板ID和模板编码不是对应关系,则报错)") @RequestParam(required = false) String fdTemplateId,
                                    @ApiParam("流程主题") @RequestParam(required = false) String fdSubject,
                                    @ApiParam("流程状态编码(参考流程状态编码表)") @RequestParam(required = false) String fdStatus,
                                    @ApiParam("创建人登录账号") @RequestParam(required = false) String docCreatorId,
                                    @ApiParam("流程提交开始时间(时间戳)") @RequestParam(required = false) String docCreateStartTime,
                                    @ApiParam("流程提交结束时间(时间戳)") @RequestParam(required = false) String docCreateEndTime,
                                    @ApiParam("业务系统模型ID(批量)") @RequestParam(required = false) String fdModuleIds,
                                    @ApiParam("业务系统模型ID") @RequestParam(required = false) String fdModuleId,
                                    @ApiParam("业务系统模板ID") @RequestParam(required = false) String formTemplateId,
                                    @ApiParam("权限账号(为空查询所有数据,不为空查询该账号有权限的数据)") @RequestParam(required = false) String fdCurrentUserAccount) {
        try {
            JSONObject formParam = new JSONObject();
            formParam.put("fdTemplateCode", fdTemplateCode);
            formParam.put("fdTemplateId", fdTemplateId);
            formParam.put("fdSubject", fdSubject);
            formParam.put("fdStatus", fdStatus);
            formParam.put("docCreatorId", docCreatorId);
            formParam.put("docCreateStartTime", docCreateStartTime);
            formParam.put("docCreateEndTime", docCreateEndTime);
            formParam.put("fdModuleIds", fdModuleIds);
            formParam.put("fdModuleId", fdModuleId);
            formParam.put("formTemplateId", formTemplateId);
            formParam.put("fdCurrentUserAccount", fdCurrentUserAccount);
            JSONObject res = mipWorkflowService.getProcessCount(formParam);
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("回调")
    @PostMapping({"callback"})
    public JSONObject callback(@RequestBody JSONObject param) {
        JSONObject json = new JSONObject();
        try {
            logger.info("callback_param:{}", param.toJSONString());
            mipWorkflowService.callback(param);

            json.put("success", true);
            json.put("msg", "success");
            return json;
        } catch (Exception e) {
            logger.error("error", e);
            json.put("success", false);
            json.put("msg", e.getMessage());
            return json;
        }
    }

    private JSONObject getOpt(String formTemplateId, Long formInstanceId, String optName) {
        if (optName == null) return null;

        checkFormTemplate(formTemplateId);
        String fdId = getInstanceId(formTemplateId, formInstanceId);

        JSONObject res = mipWorkflowService.getOperationList(PamCurrentUserUtil.getCurrentUserName(), fdId);
        logger.info("getOpt res:{}", res.toJSONString());
        JSONObject body = res.getJSONObject("body");
        JSONArray datas = body.getJSONArray("data");

        for (int i = 0; i < datas.size(); i++) {
            JSONObject data = datas.getJSONObject(i);
            JSONArray operationList = data.getJSONArray("operationList");
            for (int j = 0; j < operationList.size(); j++) {
                JSONObject opt = operationList.getJSONObject(j);
                if (optName.equals(opt.getString("name"))) {
                    return opt;
                }
            }
        }

        // 处理流程重复操作，导致操作不成功场景
        // 无对应操作权限，
        throw new BizException(ErrorCode.ERROR.getCode(), "操作失败，流程状态已变更，请刷新页面确认你的操作权限");
    }

    public static void main(String[] args) {
        String optName = "撤回";
        // optName = "通过";
        String str = "{\"header\":{\"serverAddr\":\"***********:60090\",\"sn\":\"7ea207f6-2d42-4720-b7bc-509825155600\"," +
                "\"timestamp\":\"1566978207117\"},\"body\":{\"data\":[{\"operationList\":[{\"name\":\"转办\"," +
                "\"operationTaskList\":[{\"fdTaskId\":\"d1abb8251c0c4b3794232482a5ed6cf5\",\"fdNodeId\":\"N3\",\"description\":\"\"," +
                "\"fdActivityType\":\"approveTask\",\"fdNodeInstanceId\":\"c13f63cb2bfa4d5d9798e0853fddce9a\",\"fdNodeName\":\"审批节点\"}]," +
                "\"definition\":false,\"operationType\":\"handler_commission\",\"order\":0},{\"name\":\"沟通\"," +
                "\"operationTaskList\":[{\"fdTaskId\":\"d1abb8251c0c4b3794232482a5ed6cf5\",\"fdNodeId\":\"N3\",\"description\":\"\"," +
                "\"fdActivityType\":\"approveTask\",\"fdNodeInstanceId\":\"c13f63cb2bfa4d5d9798e0853fddce9a\",\"fdNodeName\":\"审批节点\"}]," +
                "\"definition\":false,\"operationType\":\"handler_communicate\",\"order\":0},{\"name\":\"驳回\"," +
                "\"operationTaskList\":[{\"fdTaskId\":\"d1abb8251c0c4b3794232482a5ed6cf5\",\"fdNodeId\":\"N3\",\"description\":\"\"," +
                "\"fdActivityType\":\"approveTask\",\"fdNodeInstanceId\":\"c13f63cb2bfa4d5d9798e0853fddce9a\",\"fdNodeName\":\"审批节点\"}]," +
                "\"definition\":false,\"operationType\":\"handler_refuse\",\"order\":0},{\"name\":\"废弃\"," +
                "\"operationTaskList\":[{\"fdTaskId\":\"d1abb8251c0c4b3794232482a5ed6cf5\",\"fdNodeId\":\"N3\",\"description\":\"\"," +
                "\"fdActivityType\":\"approveTask\",\"fdNodeInstanceId\":\"c13f63cb2bfa4d5d9798e0853fddce9a\",\"fdNodeName\":\"审批节点\"}]," +
                "\"definition\":false,\"operationType\":\"handler_abandon\",\"order\":0},{\"name\":\"通过\"," +
                "\"operationTaskList\":[{\"fdTaskId\":\"d1abb8251c0c4b3794232482a5ed6cf5\",\"fdNodeId\":\"N3\",\"description\":\"\"," +
                "\"fdActivityType\":\"approveTask\",\"fdNodeInstanceId\":\"c13f63cb2bfa4d5d9798e0853fddce9a\",\"fdNodeName\":\"审批节点\"}]," +
                "\"definition\":false,\"operationType\":\"handler_pass\",\"order\":0}],\"roleType\":\"approve\"}," +
                "{\"operationList\":[{\"name\":\"撤回\",\"operationTaskList\":[{\"fdTaskId\":\"c13f63cb2bfa4d5d9798e0853fddce9a\"," +
                "\"fdNodeId\":\"N3\",\"description\":\"\",\"fdActivityType\":\"approveNode\"," +
                "\"fdNodeInstanceId\":\"c13f63cb2bfa4d5d9798e0853fddce9a\",\"fdNodeName\":\"审批节点\"}],\"definition\":false," +
                "\"operationType\":\"draft_return\",\"order\":2},{\"name\":\"废弃\"," +
                "\"operationTaskList\":[{\"fdTaskId\":\"c13f63cb2bfa4d5d9798e0853fddce9a\",\"fdNodeId\":\"N3\",\"description\":\"\"," +
                "\"fdActivityType\":\"approveNode\",\"fdNodeInstanceId\":\"c13f63cb2bfa4d5d9798e0853fddce9a\",\"fdNodeName\":\"审批节点\"}]," +
                "\"definition\":false,\"operationType\":\"draft_abandon\",\"order\":1},{\"name\":\"催办\"," +
                "\"operationTaskList\":[{\"fdTaskId\":\"c13f63cb2bfa4d5d9798e0853fddce9a\",\"fdNodeId\":\"N3\",\"description\":\"\"," +
                "\"fdActivityType\":\"approveNode\",\"fdNodeInstanceId\":\"c13f63cb2bfa4d5d9798e0853fddce9a\",\"fdNodeName\":\"审批节点\"}]," +
                "\"definition\":false,\"operationType\":\"remind\",\"order\":3}],\"roleType\":\"draft\"}],\"resultCode\":\"010\"," +
                "\"resultMsg\":\"操作成功\"}}";
        final JSONObject res = JSONObject.parseObject(str);

        JSONObject body = res.getJSONObject("body");
        JSONArray datas = body.getJSONArray("data");

        for (int i = 0; i < datas.size(); i++) {
            JSONObject data = datas.getJSONObject(i);
            JSONArray operationList = data.getJSONArray("operationList");
            for (int j = 0; j < operationList.size(); j++) {
                JSONObject opt = operationList.getJSONObject(j);
                if (optName.equals(opt.getString("name"))) {
                    System.out.println(opt);
                }
            }
        }
    }

    @ApiOperation("方案审批分支条件获取")
    @PostMapping({"callback/plan"})
    public JSONObject callbackPlan(@RequestBody JSONObject param) {
        JSONObject json = new JSONObject();
        try {
            logger.debug("callback_param_plan:{}", param != null ? param.toJSONString() : "");

            if (param != null) {
                final String input = param.getString("INPUT");
                if (Objects.equals("深圳美云智数科技有限公司", input)) {
                    json.put("INPUT", "深圳美云智数科技有限公司");
                } else {
                    json.put("INPUT", "美的机器人公司");
                }
            } else {
                json.put("INPUT", "美的机器人公司");
            }

            return json;
        } catch (Exception e) {
            logger.error("error", e);
            json.put("success", false);
            json.put("msg", e.getMessage());
            return json;
        }
    }

    @ApiOperation("报价单待办流程列表")
    @GetMapping({"getMyRunningProcessForQuotation"})
    public Response getMyRunningProcessForQuotation(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                    @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                                    @RequestParam(required = false) @ApiParam(value = "排序字段") String orderBy,
                                                    @RequestParam(required = false) @ApiParam(value = "是否升序") Boolean isAsc,
                                                    @RequestParam(required = false) @ApiParam(value = "流程到达该节点时间的开始时间") Long fdStartDateBegin,
                                                    @RequestParam(required = false) @ApiParam(value = "流程到达该节点时间的结束时间") Long fdStartDateEnd,
                                                    @RequestParam(required = false) @ApiParam(value = "标题") String docSubject,
                                                    @RequestParam(required = false) @ApiParam(value = "流程状态:草稿：10;待审：20;通过：30") String docStatus
    ) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            JSONObject params = new JSONObject();
            params.put("page", pageNum);
            params.put("pageSize", pageSize);
            params.put("orderBy", orderBy);
            params.put("isAsc", isAsc);
            params.put("fdStartDateBegin", fdStartDateBegin);
            params.put("fdStartDateEnd", fdStartDateEnd);
            params.put("docSubject", docSubject);
            params.put("docStatus", docStatus);
            FormTemplate template = checkFormTemplate(WorkflowBranch.QuotationApp.TEMPLATE_NAME);
            if (template != null && StringUtil.isNotNull(template.getFdTemplateId())) {
                params.put("fdModuleId", template.getFdTemplateId());
            } else {
                throw new BizException(ErrorCode.ERROR.getCode(), "获取流程模版id失败,请联系管理员!");
            }

            JSONObject res = mipWorkflowService.getMyRunningProcess(loginName, params);

            //封装业务数据
            List<Long> businessIds = new ArrayList<>();
            JSONArray list = res.getJSONObject("body").getJSONObject("data").getJSONArray("list");
            Iterator<Object> iterator = list.iterator();
            while (iterator.hasNext()) {
                JSONObject businessObject = (JSONObject) iterator.next();
                businessIds.add(Long.valueOf((String) businessObject.get("fdFromInstanceId")));
            }
            businessIds.add(-1L);//防止空值
            List<QuotationDto> quotationDtos = quotationService.listByIds(businessIds);

            iterator = list.iterator();
            while (iterator.hasNext()) {
                JSONObject businessObject = (JSONObject) iterator.next();
                QuotationDto quotationDto = quotationDtos.stream()
                        .filter(o -> o.getId().equals(Long.valueOf((String) businessObject.get("fdFromInstanceId")))).findAny()
                        .orElse(null);

                if (quotationDto != null) {
                    businessObject.put("quotationName", quotationDto.getName());//报价单名称
                    businessObject.put("customerName", quotationDto.getCustomerName());//客户名称
                    businessObject.put("applyUserName", quotationDto.getCreateByName());//申请人名称
                    businessObject.put("applyUserDeptName", quotationDto.getSaleDeptName());//申请人部门
                    businessObject.put("quotationContractAmount", quotationDto.getContractAmount());//报价金额
                    businessObject.put("quotationCost", quotationDto.getCost());//预算金额
                    businessObject.put("quotationReferenceProfit", quotationDto.getProfitWithoutTax());//预期利润
                    if (quotationDto.getSubmitAt() != null) {
                        businessObject.put("quotationSubmitAt", DateUtil.format(quotationDto.getSubmitAt(), DateUtil.DATE_PATTERN));//报价单提交时间
                    }
                }
            }
            DataResponse<JSONObject> response = Response.dataResponse();

            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("客户待办流程列表")
    @GetMapping({"getMyRunningProcessForCustomer"})
    public Response getMyRunningProcessForCustomer(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                                   @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                                                   @RequestParam(required = false) @ApiParam(value = "排序字段") String orderBy,
                                                   @RequestParam(required = false) @ApiParam(value = "是否升序") Boolean isAsc,
                                                   @RequestParam(required = false) @ApiParam(value = "流程到达该节点时间的开始时间") Long fdStartDateBegin,
                                                   @RequestParam(required = false) @ApiParam(value = "流程到达该节点时间的结束时间") Long fdStartDateEnd,
                                                   @RequestParam(required = false) @ApiParam(value = "标题") String docSubject,
                                                   @RequestParam(required = false) @ApiParam(value = "流程状态:草稿：10;待审：20;通过：30") String docStatus
    ) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            JSONObject params = new JSONObject();
            params.put("page", pageNum);
            params.put("pageSize", pageSize);
            params.put("orderBy", orderBy);
            params.put("isAsc", isAsc);
            params.put("fdStartDateBegin", fdStartDateBegin);
            params.put("fdStartDateEnd", fdStartDateEnd);
            params.put("docSubject", docSubject);
            params.put("docStatus", docStatus);
            FormTemplate template = checkFormTemplate(WorkflowBranch.CustomerApp.TEMPLATE_NAME);
            if (template != null && StringUtil.isNotNull(template.getFdTemplateId())) {
                params.put("fdModuleId", template.getFdTemplateId());
            } else {
                throw new BizException(ErrorCode.ERROR.getCode(), "获取流程模版id失败,请联系管理员!");
            }

            JSONObject res = mipWorkflowService.getMyRunningProcess(loginName, params);

            //封装业务数据
            List<Long> businessIds = new ArrayList<>();
            JSONArray list = res.getJSONObject("body").getJSONObject("data").getJSONArray("list");
            Iterator<Object> iterator = list.iterator();
            while (iterator.hasNext()) {
                JSONObject businessObject = (JSONObject) iterator.next();
                businessIds.add(Long.valueOf((String) businessObject.get("fdFromInstanceId")));
            }
            businessIds.add(-1L);//防止空值
            List<CustomerDto> customerDtos = customerService.listByIds(businessIds);

            iterator = list.iterator();
            while (iterator.hasNext()) {
                JSONObject businessObject = (JSONObject) iterator.next();

                CustomerDto customerDto = customerDtos.stream()
                        .filter(o -> o.getId().equals(Long.valueOf((String) businessObject.get("fdFromInstanceId")))).findAny()
                        .orElse(null);

                if (customerDto != null) {
                    businessObject.put("customerName", customerDto.getName());//客户名称
                    businessObject.put("enableBusinessNum", customerDto.getEnableBusinessNum());//在谈商机数
                    businessObject.put("contractNum", customerDto.getContractNum());//合同数
                    businessObject.put("industry", customerDto.getIndustry());//行业
                    businessObject.put("regionName", customerDto.getRegionName());//区域
                    businessObject.put("provinceCityArea", customerDto.getProvinceCityArea());//地址
                    if (customerDto.getUpdateAt() != null) {
                        businessObject.put("updateAt", DateUtils.format(customerDto.getUpdateAt(), DateUtil.DATE_PATTERN));//更新时间
                    }
                }
            }
            DataResponse<JSONObject> response = Response.dataResponse();

            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }


    @ApiOperation("手机移动端审批详情接口")
    @GetMapping({"getToDoContentInfo"})
    public ResponseDate mobileDetatil(@RequestParam(required = true) @ApiParam(value = "待办事项表单模板ID") String templateId,
                                      @RequestParam(required = true) @ApiParam(value = "流程实例ID") String fdId,
                                      @RequestParam(required = true) @ApiParam(value = "用户登录名") String loginName,
                                      @RequestParam(required = true) @ApiParam(value = "扩展参数，json字符串，根据实际需求设值，如\n" +
                                              "{\n" +
                                              "  'todoId':'8c5175a6-e8ae-4efd-b92b-c608c77f0ed9WFWKITEM',\n" +
                                              "  'billId': 'x9oAAAMZ+msWMFXw',\n" +
                                              "   actdefId':'e662883c-84ee-4882-8c9b-3badb2f0ff63WFACTDEF'\n" +
                                              "}") String extend,
                                      @RequestParam(required = true) @ApiParam(value = "语言标识:zh:中文，en：英文，ja:日文") String locale) {

        ResponseDate result = new ResponseDate();
        result.setStatus("0");
        Map<String, Object> map = new HashMap<String, Object>();
        logger.info("手机移动端审批详情接口，templateId:{}, fdId:{}, loginName:{}, extend:{}, locale:{}", templateId, fdId, loginName, extend, locale);
        ResponseMap responseMap = extService.getResponseMap(templateId, fdId, loginName, extend, locale);
        if (responseMap == null) {
            logger.info("移动审批异常,数据返回为null");
            result.setStatus("1");
            return result;
        }

        if (!responseMap.getStatus().equals("success")) {
            result.setStatus("1");
            result.setDesc(responseMap.getMsg());
            return result;
        }

        Map<String, String> headMap = responseMap.getHeadMap();
        if (headMap != null) {
            for (Map.Entry<String, String> entry : headMap.entrySet()) {
                map.put(entry.getKey(), entry.getValue());
            }
        }

        List<Map<String, String>> list1 = responseMap.getList1();
        if (list1 != null && list1.size() != 0) {
            map.put("list1", list1);
        }
        List<Map<String, String>> list2 = responseMap.getList2();
        if (list2 != null && list2.size() != 0) {
            map.put("list2", list2);
        }
        List<Map<String, String>> list3 = responseMap.getList3();
        if (list3 != null && list3.size() != 0) {
            map.put("list3", list3);
        }
        List<Map<String, String>> list4 = responseMap.getList4();
        if (list4 != null && list4.size() != 0) {
            map.put("list4", list4);
        }
        List<Map<String, String>> list5 = responseMap.getList5();
        if (list5 != null && list5.size() != 0) {
            map.put("list5", list5);
        }
        List<Map<String, String>> list6 = responseMap.getList6();
        if (list6 != null && list6.size() != 0) {
            map.put("list6", list6);
        }
        List<AduitAtta> fileList = responseMap.getFileList();
        if (fileList != null) {
            map.put("fileList", fileList);
        }
        result.setData(map);
        return result;
    }

    @ApiOperation("流程状态")
    @GetMapping({"getWorkflowStatus"})
    public Response getWorkflowStatus(@ApiParam("模版类型，多个用,隔开") @RequestParam String formTemplateIds,
                                      @ApiParam("业务ID，多个用,隔开") @RequestParam String formInstanceId) {
        try {
            JSONArray result = new JSONArray();

            String loginName = PamCurrentUserUtil.getCurrentUserName();
            CurrentContext currentContext = CacheDataUtils.getContextByMip(loginName);
            final Long companyId = currentContext.getCurrentOrgId();

            List<String> list = com.midea.pam.common.util.StringUtils.splitToList(formTemplateIds, ",");
            List<String> formInstanceIdStrList = com.midea.pam.common.util.StringUtils.splitToList(formInstanceId, ",");
            List<Long> formInstanceIdList = new ArrayList<>();
            formInstanceIdStrList.forEach(id -> formInstanceIdList.add(Long.valueOf(id)));

            FormTemplateExample cond = new FormTemplateExample();
            if (list.size() == 0)
                return Response.dataResponse().setData(result);
            cond.createCriteria().andFormUrlIn(list).andCompanyIdEqualTo(companyId);
            List<FormTemplate> forms = formTemplateService.selectByExample(cond);
            if (ListUtils.isEmpty(forms)) {
                return Response.dataResponse().setData(result);
            }

            FormInstanceExample condition = new FormInstanceExample();
            condition.createCriteria().andFormUrlIn(list).andFormInstanceIdIn(formInstanceIdList)
                    .andDeletedFlagEqualTo(Boolean.FALSE);
            List<FormInstance> instances = formInstanceService.selectByExample(condition);
            if (ListUtils.isEmpty(instances)) {
                return Response.dataResponse().setData(result);
            }

            for (FormInstance instance : instances) {
                String formUrl = instance.getFormUrl();
                String fdInstanceId = instance.getFdInstanceId();
                Long thisFormInstanceId = instance.getFormInstanceId();
                String wfStatus = instance.getWfStatus();
                Long createBy = instance.getCreateBy();

                // 状态为空，有两种情况：1、代表流程初始化，还暂未提交；2、历史数据，未保存当前数据
                if (StringUtils.isEmpty(wfStatus)) {
                    UserInfo userInfo = CacheDataUtils.findUserById(createBy);
                    // 流程创建人
                    String username = userInfo.getUsername();
                    JSONObject processStatus = mipWorkflowService.getProcessStatus(username, fdInstanceId);
                    JSONObject body = processStatus.getJSONObject("body");
                    if (body != null) {
                        String resultCode = body.getString("resultCode");
                        if (Objects.equals(resultCode, WorkflowResultCode.SUCCESS.getCode())) {
                            if (body.getJSONObject("data") != null
                                    && body.getJSONObject("data").getString("fdStatusCode") != null) {
                                String fdStatusCode = body.getJSONObject("data").getString("fdStatusCode");
                                JSONObject data = new JSONObject();
                                data.put("formTemplateId", formUrl);
                                data.put("workflowStatus", fdStatusCode);
                                data.put("formInstanceId", thisFormInstanceId);
                                result.add(data);

                                // 触发更新，同步状态
                                instance.setWfStatus(fdStatusCode);
                                formInstanceService.updateByPrimaryKey(instance);
                            }
                        }
                    }
                } else {
                    JSONObject data = new JSONObject();
                    data.put("formTemplateId", formUrl);
                    data.put("workflowStatus", wfStatus);
                    data.put("formInstanceId", thisFormInstanceId);
                    result.add(data);
                }
            }

            DataResponse<JSONArray> response = Response.dataResponse();
            return response.setData(result);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("商机审批初始化流程")
    @PostMapping({"businessInitProcess/{formTemplateId}/{formInstanceId}"})
    public Response businessInitProcess(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                        @RequestBody(required = false) @ApiParam(name = "WorkflowDraftForm", value = "起草form") WorkflowDraftForm form) {

        Long sitRobot1 = 567770180474634240L; //美的机器人
        Long sitRobot2 = 581144287652085760L; //广东美的智能机器人
        Long uatRobot1 = 581144287652085760L; //广东美的智能机器人
        Long prodRobot1 = 581144287652085760L; //广东美的智能机器人

        CurrentContext currentContext = CacheDataUtils.getContextByMip(PamCurrentUserUtil.getCurrentUserName());
        final Long companyId = currentContext.getCurrentOrgId();

        // 机器人，直接返回iflow的设置
        if (sitRobot1.equals(companyId) ||
                sitRobot2.equals(companyId) ||
                uatRobot1.equals(companyId) ||
                prodRobot1.equals(companyId)) {
            return initProcess(formTemplateId, formInstanceId, form);
        }

        try {
            DataResponse<JSONObject> response = Response.dataResponse();
            JSONObject workFlow = this.initProcessComment(formTemplateId, formInstanceId, form);

            workFlow.put("businessId", formInstanceId);
            String url = String.format("%sbusiness/chooseApprover", ModelsEnum.CRM.getBaseUrl());
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, workFlow, String.class);
            JSONObject jsonObject = JSON.parseObject(responseEntity.getBody(), new TypeReference<JSONObject>() {
            });
            //判空处理
            if (jsonObject != null && jsonObject.containsKey("data")) {
                JSONObject result = jsonObject.getJSONObject("data");
                result.remove("businessId");
                return response.setData(result);
            }

            return response.setData(workFlow);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    /**
     * 初始化通用
     *
     * @param formTemplateId
     * @param formInstanceId
     * @param form
     * @return
     */
    private JSONObject initProcessComment(String formTemplateId, Long formInstanceId,
                                          WorkflowDraftForm form) throws Exception {
        JSONObject body = new JSONObject();
        final FormTemplate formTemplate = checkFormTemplate(formTemplateId);

        String fdId = null;
        final FormInstance instance = getInstance(formTemplateId, formInstanceId);

        if (instance == null) {
            //fdId为空，表示未初始化流程
            //调用初始化流程接口
            fdId = createProcess(formTemplateId, formInstanceId);
        } else {
            fdId = instance.getFdInstanceId();
        }


        JSONObject formData = MipWorkflowBranchHelper.buildFormData(formTemplateId, form.getFormData());
        body.put("formData", formData);

        JSONObject flowTable = mipWorkflowService.getProcessTableInfo(PamCurrentUserUtil.getCurrentUserName(), fdId, body);

        // 未提交的流程实例超过24小时未提交，流程中心会失效此流程实例；
        final JSONObject resultBody = flowTable.getJSONObject("body");
        if (resultBody != null) {
            final String resultCode = resultBody.getString("resultCode");
            // 流程实例不存在，重新创建流程
            if (Objects.equals(resultCode, WorkflowResultCode.INSTANCE_NOT_EXIST.getCode())) {
                if (instance != null) {
                    // 移除不存在的流程实例
                    final Long id = instance.getId();
                    instance.setDeletedFlag(Boolean.TRUE);
                    formInstanceService.updateByPrimaryKey(instance);
                    logger.info("流程实例不存在，移除流程：{}", JSONObject.toJSONString(instance));
                }

                fdId = createProcess(formTemplateId, formInstanceId);
                flowTable = mipWorkflowService.getProcessTableInfo(PamCurrentUserUtil.getCurrentUserName(), fdId, body);
            }
        }
        return flowTable;
    }

    @ApiOperation("获取流程处理人")
    @GetMapping({"getWorkflowHandler"})
    public Response getWorkflowHandler(@ApiParam("模版类型") @RequestParam String formTemplateId,
                                       @ApiParam("表单ID") @RequestParam Long formInstanceId,
                                       @ApiParam("分支参数") @RequestParam(required = false) String input) {
        Response response = workFlowBranchService.getWorkflowHandler(formTemplateId, formInstanceId, input);
        return response;
    }

    @ApiOperation("获取上次审批人")
    @GetMapping({"getPrevProcessApprovers"})
    public Response getPrevProcessApprovers(@RequestParam @ApiParam(value = "模版类型") String formUrl,
                                            @RequestParam @ApiParam(value = "单位ID") Long companyId) {
        try {
            DataResponse<JSONObject> response = Response.dataResponse();
            FormTemplateExample example = new FormTemplateExample();
            example.createCriteria().andFormUrlEqualTo(formUrl).andCompanyIdEqualTo(companyId);
            List<FormTemplate> templateList = formTemplateService.selectByExample(example);
            if (CollectionUtils.isEmpty(templateList)) {
                // 如果匹配不到模板，返回空
                return response;
            }

            // 模板ID
            String fdTemplateId = templateList.get(0).getFdTemplateId();
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            JSONObject params = new JSONObject();
            params.put("fdTemplateId", fdTemplateId);

            JSONObject res = mipWorkflowService.getPrevProcessApprovers(loginName, params);
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("获取接口获取配置信息")
    @GetMapping("getTemplateSettingByKey/{formTemplateId}/{formInstanceId}")
    public Response getTemplateSettingByKey(@PathVariable Long formInstanceId, @PathVariable String formTemplateId) {
        Guard.notNull(formInstanceId, "流程实例ID为空");
        Guard.notNullOrEmpty(formTemplateId, "流程模板ID为空");

        String fdId = getInstanceId(formTemplateId, formInstanceId);

        DataResponse<JSONObject> response = Response.dataResponse();
        JSONObject res = mipWorkflowService.getTemplateSettingByKey(PamCurrentUserUtil.getCurrentUserName(), fdId, "serviceOption");
        return response.setData(res);
    }

    @ApiOperation(value = "上传附件")
    @PostMapping("uploadFile")
    public Response uploadFile(@RequestParam(value = "file") MultipartFile file) {
        try {
            Guard.notNull(file, "上传附件为空");

            DataResponse<JSONObject> response = Response.dataResponse();
            JSONObject res = mipWorkflowService.uploadFile(file);
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

    @ApiOperation("获取流程审批意见")
    @GetMapping({"getAuditeNote/{formTemplateId}/{formInstanceId}"})
    public Response getAuditeNote(@PathVariable String formTemplateId, @PathVariable Long formInstanceId,
                                  @RequestParam(required = false) String fdInstanceId) {
        try {
            String loginName = PamCurrentUserUtil.getCurrentUserName();
            checkFormTemplate(formTemplateId);
            if (StringUtils.isEmpty(fdInstanceId)) {
                fdInstanceId = getInstanceId2(formTemplateId, formInstanceId);
            }
            JSONObject res = new JSONObject();
            if (StringUtils.isNotBlank(fdInstanceId)) {
                res = mipWorkflowService.getAuditeNote(loginName, fdInstanceId);
            }
            DataResponse<JSONObject> response = Response.dataResponse();
            return response.setData(res);
        } catch (Exception e) {
            logger.error("error", e);
            DataResponse<String> response = Response.dataResponse();
            response.setCode(Code.ERROR.getCode());
            response.setMsg(e.getMessage());
            return response.setData(e.getMessage());
        }
    }

}
