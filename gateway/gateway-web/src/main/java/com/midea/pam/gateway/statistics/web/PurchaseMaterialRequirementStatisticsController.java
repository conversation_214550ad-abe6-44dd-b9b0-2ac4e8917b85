package com.midea.pam.gateway.statistics.web;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.ctc.dto.PurchaseMaterialRequirementDto;
import com.midea.pam.common.ctc.excelVo.PurchaseMaterialRequirementExcelVo;
import com.midea.pam.common.ctc.excelVo.PurchaseMaterialRequirementExtExcelVo;
import com.midea.pam.common.ctc.vo.WbsRequirementPageVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.BeanMapTool;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import com.midea.pam.gateway.common.utils.ListUtil;
import com.midea.pam.gateway.request.PurchaserMaterialRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("采购需求管理")
@RestController
@RequestMapping("statistics/purchaseMaterialRequirement")
public class PurchaseMaterialRequirementStatisticsController extends ControllerHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "物料采购需求导出")
    @GetMapping({"exportlist"})
    public void selectPageWithDetail(HttpServletResponse response,
                                     @RequestParam(required = false) @ApiParam(value = "模糊:ERP物料编码") String fuzzyErpCode,
                                     @RequestParam(required = false) @ApiParam(value = "模糊:物料描述") String fuzzyMaterielDescr,
                                     @RequestParam(required = false) @ApiParam(value = "模糊:PAM物料编码") String fuzzyPamCode,
                                     @RequestParam(required = false) @ApiParam(value = "模糊:项目名称") String fuzzyProjectName,
                                     @RequestParam(required = false) @ApiParam(value = "模糊:项目编号") String fuzzyProjectNum,
                                     @RequestParam(required = false) @ApiParam(value = "模糊:品牌") String brand,
                                     @RequestParam(required = false) @ApiParam(value = "业务实体") String projectOuName,
                                     @RequestParam(required = false) @ApiParam(value = "业务实体ID") String projectOuId,
                                     @RequestParam(required = false) @ApiParam(value = "0：待下达， 1：已下达") String statusStr,
                                     @RequestParam(required = false) @ApiParam(value = "交货开始日期") Date deliveryStartTime,
                                     @RequestParam(required = false) @ApiParam(value = "交货结束日期") Date deliveryEndTime,
                                     @RequestParam(required = false) @ApiParam(value = "发布开始日期") Date publishStartTime,
                                     @RequestParam(required = false) @ApiParam(value = "发布结束日期") Date publishEndTime) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("fuzzyErpCode", fuzzyErpCode);
        param.put("fuzzyMaterielDescr", fuzzyMaterielDescr);
        param.put("fuzzyPamCode", fuzzyPamCode);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("projectOuName", projectOuName);
        param.put("brand", brand);
        param.put("projectOuId", projectOuId);
        param.put("statusStr", statusStr);
        param.put("deliveryStartTime", deliveryStartTime);
        param.put("deliveryEndTime", deliveryEndTime);
        param.put("publishStartTime", publishStartTime);
        param.put("publishEndTime", publishEndTime);
        PurchaseMaterialRequirementDto query = BeanMapTool.mapToBean(param, PurchaseMaterialRequirementDto.class);
        String url = String.format("%sstatistics/purchaseMaterialRequirement/exportlist", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        DataResponse<List<PurchaseMaterialRequirementDto>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<PurchaseMaterialRequirementDto>>>() {
                });

        List<PurchaseMaterialRequirementDto> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        List<PurchaseMaterialRequirementExcelVo> excelVos = ListUtil.map(dataList, new ListUtil.IteratorTask<PurchaseMaterialRequirementExcelVo, PurchaseMaterialRequirementDto>() {
            @Override
            public PurchaseMaterialRequirementExcelVo getValue(PurchaseMaterialRequirementDto item) {
                PurchaseMaterialRequirementExcelVo wifi = new PurchaseMaterialRequirementExcelVo();
                BeanUtils.copyProperties(item, wifi);
                return wifi;
            }
        });

        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseMaterialRequirementExcelVo.class, "物料采购需求_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response);

    }

    @ApiOperation(value = "物料采购需求(WBS)导出")
    @GetMapping("exportlistWbs")
    public void selectPageWithDetailWbs(HttpServletResponse response, PurchaserMaterialRequest request) {
        String url = String.format("%sstatistics/purchaseMaterialRequirement/exportlistWbs", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);
        DataResponse<List<PurchaseMaterialRequirementDto>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<PurchaseMaterialRequirementDto>>>() {
                });

        List<PurchaseMaterialRequirementDto> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataResponse.getData())
                && dataResponse.getCode() == 999) {
            throw new MipException(dataResponse.getMsg());
        }
        List<PurchaseMaterialRequirementExcelVo> excelVos = ListUtil.map(dataList, item -> {
            PurchaseMaterialRequirementExcelVo wifi = new PurchaseMaterialRequirementExcelVo();
            BeanUtils.copyProperties(item, wifi);
            wifi.setDispatchIs(BooleanUtil.isTrue(item.getDispatchIs()) ? "是" : "否");
            return wifi;
        });

        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseMaterialRequirementExcelVo.class, "物料采购需求(wbs)_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response);

    }

    @ApiOperation(value = "外包(整包)需求导出")
    @GetMapping("exportlistExt")
    public void exportExt(HttpServletResponse response,
                          @RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                          @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                          @RequestParam(required = false) @ApiParam(value = "模糊:ERP物料编码") String fuzzyErpCode,
                          @RequestParam(required = false) @ApiParam(value = "模糊:物料描述") String fuzzyMaterielDescr,
                          @RequestParam(required = false) @ApiParam(value = "模糊:PAM物料编码") String fuzzyPamCode,
                          @RequestParam(required = false) @ApiParam(value = "模糊:项目名称") String fuzzyProjectName,
                          @RequestParam(required = false) @ApiParam(value = "模糊:项目编号") String fuzzyProjectNum,
                          @RequestParam(required = false) @ApiParam(value = "模糊:WBS") String wbsSummaryCode,
                          @RequestParam(required = false) @ApiParam(value = "业务实体") String projectOuId,
                          @RequestParam(required = false) @ApiParam(value = "状态(0：待采购，1：已关闭)") String statusStr,
                          @RequestParam(required = false) @ApiParam(value = "交货开始日期") Date deliveryStartTime,
                          @RequestParam(required = false) @ApiParam(value = "交货结束日期") Date deliveryEndTime,
                          @RequestParam(required = false) @ApiParam(value = "发布开始日期") Date publishStartTime,
                          @RequestParam(required = false) @ApiParam(value = "发布结束日期") Date publishEndTime,
                          @RequestParam(required = false) @ApiParam(value = "更新开始日期") Date updateStartDate,
                          @RequestParam(required = false) @ApiParam(value = "更新结束日期") Date updateEndDate,
                          @RequestParam(required = false) @ApiParam(value = "采购类型(1：默认、2：外购物料、3：wbs)") Integer purchaseType,
                          @RequestParam(required = false) @ApiParam(value = "模糊:需求单号") String requirementCode,
                          @RequestParam(required = false) @ApiParam(value = "模糊:设计发布批次号") String designReleaseLotNumber,
                          @RequestParam(required = false) @ApiParam(value = "模糊:活动事项编码") String activityCode,
                          @RequestParam(required = false) @ApiParam(value = "是否急件") Boolean dispatchIs,
                          @RequestParam(required = false) @ApiParam(value = "图号/型号") String model,
                          @RequestParam(required = false) @ApiParam(value = "品牌") String brand,
                          @RequestParam(required = false) @ApiParam(value = "图纸版本号") String chartVersion,
                          @RequestParam(required = false) @ApiParam(value = "需求类型") String requirementTypeStr) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyErpCode", fuzzyErpCode);
        param.put("fuzzyMaterielDescr", fuzzyMaterielDescr);
        param.put("fuzzyPamCode", fuzzyPamCode);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("wbsSummaryCode", wbsSummaryCode);
        param.put("projectOuId", projectOuId);
        param.put("statusStr", statusStr);
        param.put("deliveryStartTime", deliveryStartTime);
        param.put("deliveryEndTime", deliveryEndTime);
        param.put("publishStartTime", publishStartTime);
        param.put("publishEndTime", publishEndTime);
        param.put("updateStartDate", updateStartDate);
        param.put("updateEndDate", updateEndDate);
        param.put("purchaseType", purchaseType);
        param.put("requirementCode", requirementCode);
        param.put("dispatchIs", dispatchIs);
        param.put("designReleaseLotNumber", designReleaseLotNumber);
        param.put("activityCode", activityCode);
        param.put("model", model);
        param.put("brand", brand);
        param.put("chartVersion", chartVersion);
        param.put("requirementTypeStr", requirementTypeStr);
        PurchaseMaterialRequirementDto query = BeanMapTool.mapToBean(param, PurchaseMaterialRequirementDto.class);
        String url = String.format("%sstatistics/purchaseMaterialRequirement/wbsRequirementList", ModelsEnum.STATISTICS.getBaseUrl());
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectionRequestTimeout(6000 * 1000);
        factory.setConnectTimeout(6000 * 1000);
        factory.setReadTimeout(6000 * 1000);
        restTemplate.setRequestFactory(factory);
        long start = System.currentTimeMillis();
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        DataResponse<List<PurchaseMaterialRequirementDto>> dataResponse = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<PurchaseMaterialRequirementDto>>>() {
                });
        long end = System.currentTimeMillis();
        logger.info("exportlistExt 查询外部接口耗时:{}",end -start);

        List<PurchaseMaterialRequirementDto> dataList = dataResponse.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            throw new Exception("没有数据");
        }
        List<PurchaseMaterialRequirementExtExcelVo> excelVos = ListUtil.map(dataList, new ListUtil.IteratorTask<PurchaseMaterialRequirementExtExcelVo, PurchaseMaterialRequirementDto>() {
            @Override
            public PurchaseMaterialRequirementExtExcelVo getValue(PurchaseMaterialRequirementDto item) {
                PurchaseMaterialRequirementExtExcelVo wifi = new PurchaseMaterialRequirementExtExcelVo();
                BeanUtils.copyProperties(item, wifi);
                return wifi;
            }
        });
        for (int i = 0; i < excelVos.size(); i++) {
            PurchaseMaterialRequirementExtExcelVo excelVo = excelVos.get(i);
            excelVo.setNum(i + 1);
            excelVo.setWbsRemainingDemandOsCost(excelVo.getWbsRemainingDemandOsCost().compareTo(BigDecimal.ZERO) < 0 ? new BigDecimal(0).setScale(2) : excelVo.getWbsRemainingDemandOsCost());
        }
        long end2 = System.currentTimeMillis();
        logger.info("exportlistExt 处理数据耗时:{}",end2 - end);
        //导出操作
        ExportExcelUtil.exportExcel(excelVos, null, "Sheet1", PurchaseMaterialRequirementExtExcelVo.class, "外包（整包）需求_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response);

        long end3 = System.currentTimeMillis();
        logger.info("exportlistExt 导出操作耗时:{}",end3 - end2);
    }

    @ApiOperation(value = "物料采购需求")
    @GetMapping({"pagelist"})
    public Response selectPageWithDetail(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                         @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                                         @RequestParam(required = false) @ApiParam(value = "模糊:ERP物料编码") String fuzzyErpCode,
                                         @RequestParam(required = false) @ApiParam(value = "模糊:物料描述") String fuzzyMaterielDescr,
                                         @RequestParam(required = false) @ApiParam(value = "模糊:PAM物料编码") String fuzzyPamCode,
                                         @RequestParam(required = false) @ApiParam(value = "模糊:项目名称") String fuzzyProjectName,
                                         @RequestParam(required = false) @ApiParam(value = "模糊:项目编号") String fuzzyProjectNum,
                                         @RequestParam(required = false) @ApiParam(value = "模糊:品牌") String brand,
                                         @RequestParam(required = false) @ApiParam(value = "业务实体") String projectOuId,
                                         @RequestParam(required = false) @ApiParam(value = "0：待下达， 1：已下达") String statusStr,
                                         @RequestParam(required = false) @ApiParam(value = "待批准供应商数量") Integer approvedSupplierNumber,
                                         @RequestParam(required = false) @ApiParam(value = "交货开始日期") Date deliveryStartTime,
                                         @RequestParam(required = false) @ApiParam(value = "交货结束日期") Date deliveryEndTime,
                                         @RequestParam(required = false) @ApiParam(value = "发布开始日期") Date publishStartTime,
                                         @RequestParam(required = false) @ApiParam(value = "发布结束日期") Date publishEndTime,
                                         @RequestParam(required = false) @ApiParam(value = "收货地址") String deliveryAddress,
                                         @RequestParam(required = false) @ApiParam(value = "联系人") String consignee,
                                         @RequestParam(required = false) @ApiParam(value = "联系方式") String contactPhone) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyErpCode", fuzzyErpCode);
        param.put("fuzzyMaterielDescr", fuzzyMaterielDescr);
        param.put("fuzzyPamCode", fuzzyPamCode);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("brand", brand);
        param.put("projectOuId", projectOuId);
        param.put("statusStr", statusStr);
        param.put("approvedSupplierNumber", approvedSupplierNumber);
        param.put("deliveryStartTime", deliveryStartTime);
        param.put("deliveryEndTime", deliveryEndTime);
        param.put("publishStartTime", publishStartTime);
        param.put("publishEndTime", publishEndTime);
        param.put("deliveryAddress", deliveryAddress);
        param.put("consignee", consignee);
        param.put("contactPhone", contactPhone);
        PurchaseMaterialRequirementDto query = BeanMapTool.mapToBean(param, PurchaseMaterialRequirementDto.class);
        String url = String.format("%sstatistics/purchaseMaterialRequirement/page", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        return JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<PurchaseMaterialRequirementDto>>>() {
                });
    }

    @ApiOperation(value = "物料采购需求(WBS)")
    @PostMapping("wbsRequirementPage")
    public com.midea.pam.gateway.common.base.DataResponse wbsRequirementPage(@RequestBody WbsRequirementPageVo wbsRequirementPageVo) {
        PurchaseMaterialRequirementDto purchaseMaterialRequirementDto = new PurchaseMaterialRequirementDto();
        purchaseMaterialRequirementDto.setModel(wbsRequirementPageVo.getModel());
        purchaseMaterialRequirementDto.setBrand(wbsRequirementPageVo.getBrand());
        purchaseMaterialRequirementDto.setChartVersion(wbsRequirementPageVo.getChartVersion());
        purchaseMaterialRequirementDto.setWbsSummaryCode(wbsRequirementPageVo.getWbsSummaryCode());
        purchaseMaterialRequirementDto.setRequirementCode(wbsRequirementPageVo.getRequirementCode());
        purchaseMaterialRequirementDto.setDispatchIs(wbsRequirementPageVo.getDispatchIs());
        purchaseMaterialRequirementDto.setDesignReleaseLotNumber(wbsRequirementPageVo.getDesignReleaseLotNumber());
        purchaseMaterialRequirementDto.setPageNum(wbsRequirementPageVo.getPageNum());
        purchaseMaterialRequirementDto.setPageSize(wbsRequirementPageVo.getPageSize());
        purchaseMaterialRequirementDto.setFuzzyErpCode(wbsRequirementPageVo.getFuzzyErpCode());
        purchaseMaterialRequirementDto.setFuzzyMaterielDescr(wbsRequirementPageVo.getFuzzyMaterielDescr());
        purchaseMaterialRequirementDto.setFuzzyPamCode(wbsRequirementPageVo.getFuzzyPamCode());
        purchaseMaterialRequirementDto.setFuzzyProjectName(wbsRequirementPageVo.getFuzzyProjectName());
        purchaseMaterialRequirementDto.setFuzzyProjectNum(wbsRequirementPageVo.getFuzzyProjectNum());
        purchaseMaterialRequirementDto.setProjectOuId(wbsRequirementPageVo.getProjectOuId());
        purchaseMaterialRequirementDto.setStatusStr(wbsRequirementPageVo.getStatusStr());
        purchaseMaterialRequirementDto.setApprovedSupplierNumber(wbsRequirementPageVo.getApprovedSupplierNumber());
        purchaseMaterialRequirementDto.setCodingMiddleclass(wbsRequirementPageVo.getCodingMiddleclass());
        purchaseMaterialRequirementDto.setMaterialType(wbsRequirementPageVo.getMaterialType());
        if (wbsRequirementPageVo.getDeliveryStartTime() != null) {
            purchaseMaterialRequirementDto.setDeliveryStartTime(wbsRequirementPageVo.getDeliveryStartTime());
        }
        if (wbsRequirementPageVo.getDeliveryEndTime() != null) {
            purchaseMaterialRequirementDto.setDeliveryEndTime(wbsRequirementPageVo.getDeliveryEndTime());
        }
        if (wbsRequirementPageVo.getPublishStartTime() != null) {
            purchaseMaterialRequirementDto.setPublishStartTime(wbsRequirementPageVo.getPublishStartTime());
        }
        if (wbsRequirementPageVo.getPublishEndTime() != null) {
            purchaseMaterialRequirementDto.setPublishEndTime(wbsRequirementPageVo.getPublishEndTime());
        }
        purchaseMaterialRequirementDto.setUpdateStartDate(wbsRequirementPageVo.getUpdateStartDate());
        purchaseMaterialRequirementDto.setUpdateEndDate(wbsRequirementPageVo.getUpdateEndDate());
        purchaseMaterialRequirementDto.setPurchaseType(wbsRequirementPageVo.getPurchaseType());
        if (wbsRequirementPageVo.getProjectId() != null) {
            purchaseMaterialRequirementDto.setProjectId(Long.valueOf(wbsRequirementPageVo.getProjectId()));
        }
        purchaseMaterialRequirementDto.setRequirementTypeStr(wbsRequirementPageVo.getRequirementTypeStr());
        purchaseMaterialRequirementDto.setFreezeFlag(wbsRequirementPageVo.getFreezeFlag());

        String url = String.format("%sstatistics/purchaseMaterialRequirement/wbsPage", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, purchaseMaterialRequirementDto, String.class);
        final com.midea.pam.gateway.common.base.DataResponse<PageInfo<PurchaseMaterialRequirementDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<com.midea.pam.gateway.common.base.DataResponse<PageInfo<PurchaseMaterialRequirementDto>>>() {
                });
        return response;
    }

    @ApiOperation(value = "外包(整包)需求")
    @GetMapping("wbsRequirementPage")
    public Response purchasingList(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                                   @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "每页记录数") Integer pageSize,
                                   @RequestParam(required = false) @ApiParam(value = "模糊:ERP物料编码") String fuzzyErpCode,
                                   @RequestParam(required = false) @ApiParam(value = "模糊:物料描述") String fuzzyMaterielDescr,
                                   @RequestParam(required = false) @ApiParam(value = "模糊:PAM物料编码") String fuzzyPamCode,
                                   @RequestParam(required = false) @ApiParam(value = "模糊:项目名称") String fuzzyProjectName,
                                   @RequestParam(required = false) @ApiParam(value = "模糊:项目编号") String fuzzyProjectNum,
                                   @RequestParam(required = false) @ApiParam(value = "模糊:WBS") String wbsSummaryCode,
                                   @RequestParam(required = false) @ApiParam(value = "业务实体") String projectOuId,
                                   @RequestParam(required = false) @ApiParam(value = "状态(0：待采购，1：已关闭)") String statusStr,
                                   @RequestParam(required = false) @ApiParam(value = "交货开始日期") Date deliveryStartTime,
                                   @RequestParam(required = false) @ApiParam(value = "交货结束日期") Date deliveryEndTime,
                                   @RequestParam(required = false) @ApiParam(value = "发布开始日期") Date publishStartTime,
                                   @RequestParam(required = false) @ApiParam(value = "发布结束日期") Date publishEndTime,
                                   @RequestParam(required = false) @ApiParam(value = "更新开始日期") Date updateStartDate,
                                   @RequestParam(required = false) @ApiParam(value = "更新结束日期") Date updateEndDate,
                                   @RequestParam(required = false) @ApiParam(value = "采购类型(1：默认、2：外购物料、3：wbs)") Integer purchaseType,
                                   @RequestParam(required = false) @ApiParam(value = "模糊:需求单号") String requirementCode,
                                   @RequestParam(required = false) @ApiParam(value = "模糊:设计发布批次号") String designReleaseLotNumber,
                                   @RequestParam(required = false) @ApiParam(value = "模糊:活动事项编码") String activityCode,
                                   @RequestParam(required = false) @ApiParam(value = "是否急件") Boolean dispatchIs,
                                   @RequestParam(required = false) @ApiParam(value = "图号/型号") String model,
                                   @RequestParam(required = false) @ApiParam(value = "品牌") String brand,
                                   @RequestParam(required = false) @ApiParam(value = "图纸版本号") String chartVersion,
                                   @RequestParam(required = false) @ApiParam(value = "物料中类") String codingMiddleclass,
                                   @RequestParam(required = false) @ApiParam(value = "物料小类") String materialType,
                                   @RequestParam(required = false) @ApiParam(value = "需求类型") String requirementTypeStr,
                                   @RequestParam(required = false) @ApiParam(value = "冻结标志") Integer freezeFlag) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyErpCode", fuzzyErpCode);
        param.put("fuzzyMaterielDescr", fuzzyMaterielDescr);
        param.put("fuzzyPamCode", fuzzyPamCode);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("wbsSummaryCode", wbsSummaryCode);
        param.put("projectOuId", projectOuId);
        param.put("statusStr", statusStr);
        param.put("deliveryStartTime", deliveryStartTime);
        param.put("deliveryEndTime", deliveryEndTime);
        param.put("publishStartTime", publishStartTime);
        param.put("publishEndTime", publishEndTime);
        param.put("updateStartDate", updateStartDate);
        param.put("updateEndDate", updateEndDate);
        param.put("purchaseType", purchaseType);
        param.put("requirementCode", requirementCode);
        param.put("dispatchIs", dispatchIs);
        param.put("designReleaseLotNumber", designReleaseLotNumber);
        param.put("activityCode", activityCode);
        param.put("model", model);
        param.put("brand", brand);
        param.put("chartVersion", chartVersion);
        param.put("codingMiddleclass", codingMiddleclass);
        param.put("materialType", materialType);
        param.put("requirementTypeStr", requirementTypeStr);
        param.put("freezeFlag",freezeFlag);
        PurchaseMaterialRequirementDto query = BeanMapTool.mapToBean(param, PurchaseMaterialRequirementDto.class);
        String url = String.format("%sstatistics/purchaseMaterialRequirement/wbsRequirementPage", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        DataResponse<PageInfo<PurchaseMaterialRequirementDto>> response = JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<PageInfo<PurchaseMaterialRequirementDto>>>() {
                });
        return response;
    }

    @ApiOperation(value = "合同录入-关联项目需求")
    @GetMapping("wbsRequirementList")
    public Response getPurchasingExportList(HttpServletResponse response,
                                            @RequestParam(required = false) @ApiParam(value = "模糊:ERP物料编码") String fuzzyErpCode,
                                            @RequestParam(required = false) @ApiParam(value = "模糊:物料描述") String fuzzyMaterielDescr,
                                            @RequestParam(required = false) @ApiParam(value = "模糊:PAM物料编码") String fuzzyPamCode,
                                            @RequestParam(required = false) @ApiParam(value = "模糊:项目名称") String fuzzyProjectName,
                                            @RequestParam(required = false) @ApiParam(value = "模糊:项目编号") String fuzzyProjectNum,
                                            @RequestParam(required = false) @ApiParam(value = "模糊:WBS") String wbsSummaryCode,
                                            @RequestParam(required = false) @ApiParam(value = "业务实体") String projectOuId,
                                            @RequestParam(required = false) @ApiParam(value = "状态(0：待采购，1：已关闭)") String statusStr,
                                            @RequestParam(required = false) @ApiParam(value = "交货开始日期") Date deliveryStartTime,
                                            @RequestParam(required = false) @ApiParam(value = "交货结束日期") Date deliveryEndTime,
                                            @RequestParam(required = false) @ApiParam(value = "发布开始日期") Date publishStartTime,
                                            @RequestParam(required = false) @ApiParam(value = "发布结束日期") Date publishEndTime,
                                            @RequestParam(required = false) @ApiParam(value = "更新开始日期") Date updateStartDate,
                                            @RequestParam(required = false) @ApiParam(value = "更新结束日期") Date updateEndDate,
                                            @RequestParam(required = false) @ApiParam(value = "采购类型(1：默认、2：外购物料、3：wbs)") Integer purchaseType,
                                            @RequestParam(required = false) @ApiParam(value = "模糊:需求单号") String requirementCode,
                                            @RequestParam(required = false) @ApiParam(value = "模糊:设计发布批次号") String designReleaseLotNumber,
                                            @RequestParam(required = false) @ApiParam(value = "模糊:活动事项编码") String activityCode,
                                            @RequestParam(required = false) @ApiParam(value = "是否急件") Boolean dispatchIs,
                                            @RequestParam(required = false) @ApiParam(value = "图号/型号") String model,
                                            @RequestParam(required = false) @ApiParam(value = "品牌") String brand,
                                            @RequestParam(required = false) @ApiParam(value = "图纸版本号") String chartVersion,
                                            @RequestParam(required = false) @ApiParam(value = "需求类型") String requirementTypeStr,
                                            @RequestParam(required = false) @ApiParam(value = "是否点工(0：否，1：是)") Integer ifHro,
                                            @RequestParam(required = false) @ApiParam(value = "项目id") Long projectId,
                                            @RequestParam(required = false) @ApiParam(value = "冻结标志") Integer freezeFlag) throws Exception {
        final Map<String, Object> param = new HashMap<>();
        param.put("fuzzyErpCode", fuzzyErpCode);
        param.put("fuzzyMaterielDescr", fuzzyMaterielDescr);
        param.put("fuzzyPamCode", fuzzyPamCode);
        param.put("fuzzyProjectName", fuzzyProjectName);
        param.put("fuzzyProjectNum", fuzzyProjectNum);
        param.put("wbsSummaryCode", wbsSummaryCode);
        param.put("projectOuId", projectOuId);
        param.put("statusStr", statusStr);
        param.put("deliveryStartTime", deliveryStartTime);
        param.put("deliveryEndTime", deliveryEndTime);
        param.put("publishStartTime", publishStartTime);
        param.put("publishEndTime", publishEndTime);
        param.put("updateStartDate", updateStartDate);
        param.put("updateEndDate", updateEndDate);
        param.put("purchaseType", purchaseType);
        param.put("requirementCode", requirementCode);
        param.put("dispatchIs", dispatchIs);
        param.put("designReleaseLotNumber", designReleaseLotNumber);
        param.put("activityCode", activityCode);
        param.put("model", model);
        param.put("brand", brand);
        param.put("chartVersion", chartVersion);
        param.put("requirementTypeStr", requirementTypeStr);
        param.put("ifHro", ifHro);
        param.put("projectId", projectId);
        param.put("freezeFlag",freezeFlag);
        PurchaseMaterialRequirementDto query = BeanMapTool.mapToBean(param, PurchaseMaterialRequirementDto.class);
        String url = String.format("%sstatistics/purchaseMaterialRequirement/wbsRequirementList", ModelsEnum.STATISTICS.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, query, String.class);
        return JSON.parseObject(responseEntity.getBody(),
                new TypeReference<DataResponse<List<PurchaseMaterialRequirementDto>>>() {
                });
    }

    @ApiOperation(value = "点工关联项目需求查询")
    @GetMapping("getHroPurchaseRequirement")
    public Response getHroPurchaseRequirement(PurchaseMaterialRequirementDto requirementDto) {
        Map<String, Object> param = new HashMap<>(9);
        param.put("projectId", requirementDto.getProjectId());
        param.put("pamCode", requirementDto.getPamCode());
        param.put("roleName", requirementDto.getRoleName());
        param.put("startDate", requirementDto.getStartDate());
        param.put("endDate", requirementDto.getEndDate());
        param.put("requirementCode", requirementDto.getRequirementCode());
        param.put("wbsSummaryCode", requirementDto.getWbsSummaryCode());
        param.put("activityCode", requirementDto.getActivityCode());
        param.put("freezeFlag",requirementDto.getFreezeFlag());
        String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "statistics/purchaseMaterialRequirement/getHroPurchaseRequirement", param);
        return restTemplate.getForObject(url, DataResponse.class);
    }
}

