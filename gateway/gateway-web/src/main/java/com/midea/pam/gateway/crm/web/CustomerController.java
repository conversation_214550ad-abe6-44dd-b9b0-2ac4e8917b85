package com.midea.pam.gateway.crm.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.pam.common.basedata.dto.OperatingUnitDto;
import com.midea.pam.common.basedata.dto.TeamUserDto;
import com.midea.pam.common.basedata.dto.TeamUsersDto;
import com.midea.pam.common.basedata.query.CustomerQuery;
import com.midea.pam.common.constants.ResponseMap;
import com.midea.pam.common.crm.dto.BusinessDto;
import com.midea.pam.common.crm.dto.CustomerContactDto;
import com.midea.pam.common.crm.dto.CustomerDto;
import com.midea.pam.common.crm.entity.CustomerBankAccount;
import com.midea.pam.common.crm.entity.CustomerModifyHistory;
import com.midea.pam.common.crm.excelVo.CustomerExcelVo;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.gateway.common.base.ControllerHelper;
import com.midea.pam.gateway.common.base.DataResponse;
import com.midea.pam.gateway.common.base.PageResponse;
import com.midea.pam.gateway.common.base.Response;
import com.midea.pam.gateway.common.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Api("客户模块")
@RestController
@RequestMapping(value = {"customer", "mobile/app/customer"})
public class CustomerController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;

    @ApiOperation(value = "发起审批")
    @PutMapping("/updateStatusApproval/skipSecurityInterceptor")
    public Response draftSubmitCallback(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scustomer/updateStatusApproval/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CRM.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "通过")
    @PutMapping("/updateStatusEnable/skipSecurityInterceptor")
    public Response passCallback(@RequestParam(required = false) Long formInstanceId,
                                 @RequestParam(required = false) String fdInstanceId,
                                 @RequestParam(required = false) String formUrl,
                                 @RequestParam(required = false) String eventName,
                                 @RequestParam(required = false) String handlerId,
                                 @RequestParam(required = false) Long companyId,
                                 @RequestParam(required = false) Long createUserId,
                                 @RequestParam(required = false) String timestamp) {
        String url = String.format("%scustomer/updateStatusEnable/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d" +
                        "&timestamp=%s",
                ModelsEnum.CRM.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId, timestamp);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "驳回")
    @PutMapping("/updateStatusRefuse/skipSecurityInterceptor")
    public Response refuseCallback(@RequestParam(required = false) Long formInstanceId,
                                   @RequestParam(required = false) String fdInstanceId,
                                   @RequestParam(required = false) String formUrl,
                                   @RequestParam(required = false) String eventName,
                                   @RequestParam(required = false) String handlerId,
                                   @RequestParam(required = false) Long companyId,
                                   @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scustomer/updateStatusRefuse/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CRM.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "撤回")
    @PutMapping("/updateStatusReturn/skipSecurityInterceptor")
    public Response draftReturnCallback(@RequestParam(required = false) Long formInstanceId,
                                        @RequestParam(required = false) String fdInstanceId,
                                        @RequestParam(required = false) String formUrl,
                                        @RequestParam(required = false) String eventName,
                                        @RequestParam(required = false) String handlerId,
                                        @RequestParam(required = false) Long companyId,
                                        @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scustomer/updateStatusReturn/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CRM.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "作废")
    @PutMapping("abandonChange/skipSecurityInterceptor")
    public Response abandon(@RequestParam(required = false) Long formInstanceId,
                            @RequestParam(required = false) String fdInstanceId,
                            @RequestParam(required = false) String formUrl,
                            @RequestParam(required = false) String eventName,
                            @RequestParam(required = false) String handlerId,
                            @RequestParam(required = false) Long companyId,
                            @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scustomer/abandonChange/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CRM.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "删除")
    @PutMapping("/deleteChange/skipSecurityInterceptor")
    public Response delete(@RequestParam(required = false) Long formInstanceId,
                           @RequestParam(required = false) String fdInstanceId,
                           @RequestParam(required = false) String formUrl,
                           @RequestParam(required = false) String eventName,
                           @RequestParam(required = false) String handlerId,
                           @RequestParam(required = false) Long companyId,
                           @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scustomer/deleteChange/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CRM.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation(value = "处理人通过")
    @PutMapping("agree/skipSecurityInterceptor")
    public Response agree(@RequestParam(required = false) Long formInstanceId,
                          @RequestParam(required = false) String fdInstanceId,
                          @RequestParam(required = false) String formUrl,
                          @RequestParam(required = false) String eventName,
                          @RequestParam(required = false) String handlerId,
                          @RequestParam(required = false) Long companyId,
                          @RequestParam(required = false) Long createUserId) {
        String url = String.format("%scustomer/agree/skipSecurityInterceptor" +
                        "?formInstanceId=%d" +
                        "&fdInstanceId=%s" +
                        "&formUrl=%s" +
                        "&eventName=%s" +
                        "&handlerId=%s" +
                        "&companyId=%d" +
                        "&createUserId=%d",
                ModelsEnum.CRM.getBaseUrl(), formInstanceId, fdInstanceId, formUrl, eventName, handlerId, companyId, createUserId);
        restTemplate.put(url, String.class);
        return Response.dataResponse();
    }

    @ApiOperation("根据客户ID判断当前登录用户是否创建人,true是 false否")
    @GetMapping("/isCreater")
    public Response isCreater(@RequestParam(required = true) Long customerId) {
        String url = String.format("%scustomer/isCreater?customerId=%s", ModelsEnum.CRM.getBaseUrl(), customerId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据客户ID判断当前登录用户是否所有者,true是 false否")
    @GetMapping("/isOwner")
    public Response isOwner(@RequestParam(required = true) Long customerId) {
        String url = String.format("%scustomer/isOwner?customerId=%s", ModelsEnum.CRM.getBaseUrl(), customerId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据客户ID判断当前登录用户是否团队成员,true是 false否")
    @GetMapping("/isMember")
    public Response isMember(@RequestParam(required = true) Long customerId) {
        String url = String.format("%scustomer/isMember?customerId=%s", ModelsEnum.CRM.getBaseUrl(), customerId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("根据客户ID判断当前登录用户是否有数据权限,true是 false否")
    @GetMapping("/isDataAuthor")
    public Response isAuthor(@RequestParam(required = true) Long customerId) {
        String url = String.format("%scustomer/isDataAuthor?customerId=%s", ModelsEnum.CRM.getBaseUrl(), customerId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }

    @ApiOperation("通过ID获取客户信息")
    @GetMapping("/view")
    public Response getCustomerById(@RequestParam(required = false) Long customerId) {
        String url = String.format("%scustomer/view?customerId=%s", ModelsEnum.CRM.getBaseUrl(), customerId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<CustomerDto>>() {
        });
    }

    @ApiOperation("客户新增审批详情")
    @GetMapping("/viewByUnitRel")
    public Response getCustomerByUnitRel(@RequestParam(required = false) Long unitRelId) {
        String url = String.format("%scustomer/viewByUnitRel?unitRelId=%s", ModelsEnum.CRM.getBaseUrl(), unitRelId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<CustomerDto>>() {
        });
    }

    @ApiOperation("移动端通过ID获取客户信息")
    @GetMapping("/getCustomerApp")
    public Response getCustomerApp(@RequestParam(required = true) Long unitRelId) {
        String url = String.format("%scustomer/getCustomerApp?unitRelId=%s", ModelsEnum.CRM.getBaseUrl(), unitRelId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
    }

    @ApiOperation("移动端通过ID获取客户财务信息完善")
    @GetMapping("/getCustomerModifyApp")
    public Response getCustomerModifyApp(@RequestParam(required = true) Long customerId) {
        String url = String.format("%scustomer/getCustomerModifyApp?customerId=%s", ModelsEnum.CRM.getBaseUrl(), customerId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
    }

    @ApiOperation("移动端通过ID查客户业务信息变更")
    @GetMapping("/getCustomerBusinessModifyApp")
    public Response getCustomerBusinessModifyApp(@RequestParam(required = true) Long customerModifyId) {
        String url = String.format("%scustomer/getCustomerBusinessModifyApp?customerModifyId=%s", ModelsEnum.CRM.getBaseUrl(), customerModifyId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
    }

    @ApiOperation(value = "移动端通过ID查客户基础信息变更")
    @GetMapping("/getCustomerBaseInfoModifyApp")
    public Response getCustomerBaseInfoModifyApp(@RequestParam(required = true) Long customerModifyId) {
        String url = String.format("%scustomer/getCustomerBaseInfoModifyApp?customerModifyId=%s", ModelsEnum.CRM.getBaseUrl(), customerModifyId);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<ResponseMap>>() {
        });
    }

    @ApiOperation("通过name精确查询客户信息")
    @GetMapping("/getCustomerByName")
    public Response getCustomerByName(@RequestParam(required = false) String customerName) {
        String url = String.format("%scustomer/getCustomerByName?customerName=%s", ModelsEnum.CRM.getBaseUrl(), customerName);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<CustomerDto>>() {
        });
    }

    @ApiOperation("分页获取客户信息")
    @GetMapping("list")
    public Response list(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                         @RequestParam(required = false) @ApiParam(value = "模糊搜索") String fuzzyLike,
                         @RequestParam(required = false) @ApiParam(value = "区域") String region,
                         @RequestParam(required = false) @ApiParam(value = "部门") String roleDeptId,
                         @RequestParam(required = false) @ApiParam(value = "客户名称") String name,
                         @RequestParam(required = false) @ApiParam(value = "状态") String status,
                         @RequestParam(required = false) @ApiParam(value = "创建开始时间") Date createBeginDate,
                         @RequestParam(required = false) @ApiParam(value = "创建结束时间") Date createEndDate,
                         @RequestParam(required = false) @ApiParam(value = "更新开始时间") Date updateBeginDate,
                         @RequestParam(required = false) @ApiParam(value = "更新结束时间") Date updateEndDate,
                         @RequestParam(required = false) @ApiParam(value = "OU") Long ouId,
                         @RequestParam(required = false) @ApiParam(value = "编号") String crmCode,
                         @RequestParam(required = false) @ApiParam(value = "CRM同步状态") Integer syncStatus,
                         @RequestParam(required = false) @ApiParam(value = "是否过滤权限") Boolean isAll,
                         @RequestParam(required = false) @ApiParam(value = "内外部") Boolean isInner) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyLike", fuzzyLike);
        param.put("region", region);
        param.put("roleDeptId", roleDeptId);
        param.put("name", name);
        param.put("status", status);
        param.put("createBeginDate", createBeginDate == null ? "" : createBeginDate.getTime());
        param.put("createEndDate", createEndDate == null ? "" : createEndDate.getTime());
        param.put("updateBeginDate", updateBeginDate == null ? "" : updateBeginDate.getTime());
        param.put("updateEndDate", updateEndDate == null ? "" : updateEndDate.getTime());
        param.put("ouId", ouId);
        param.put("crmCode", crmCode);
        param.put("syncStatus", syncStatus);
        param.put("isInner", isInner);
        param.put("isAll", isAll);
        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/list", param);

        //String res = cleanStr(restTemplate.getForObject(url, String.class));
        String res = restTemplate.getForEntity(fromHttpUrl(url), String.class).getBody();
        PageInfo<CustomerDto> data = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CustomerDto>>>() {
        }).getData();
        PageResponse<CustomerDto> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation("我的客户信息")
    @GetMapping("mylist")
    public Response mylist(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                           @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                           @RequestParam(required = false) @ApiParam(value = "模糊搜索") String fuzzyLike,
                           @RequestParam(required = false) @ApiParam(value = "区域") String region,
                           @RequestParam(required = false) @ApiParam(value = "部门") String roleDeptId,
                           @RequestParam(required = false) @ApiParam(value = "客户名称") String name,
                           @RequestParam(required = false) @ApiParam(value = "状态") String status,
                           @RequestParam(required = false) @ApiParam(value = "创建开始时间") Date createBeginDate,
                           @RequestParam(required = false) @ApiParam(value = "创建结束时间") Date createEndDate,
                           @RequestParam(required = false) @ApiParam(value = "更新开始时间") Date updateBeginDate,
                           @RequestParam(required = false) @ApiParam(value = "更新结束时间") Date updateEndDate,
                           @RequestParam(required = false) @ApiParam(value = "OU") Long ouId,
                           @RequestParam(required = false) @ApiParam(value = "编号") String crmCode,
                           @RequestParam(required = false) @ApiParam(value = "CRM同步状态") Integer syncStatus,
                           @RequestParam(required = false) @ApiParam(value = "是否过滤权限") Boolean isAll,
                           @RequestParam(required = false) @ApiParam(value = "内外部") Boolean isInner) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyLike", fuzzyLike);
        param.put("region", region);
        param.put("roleDeptId", roleDeptId);
        param.put("name", name);
        param.put("status", status);
        param.put("createBeginDate", createBeginDate == null ? "" : createBeginDate.getTime());
        param.put("createEndDate", createEndDate == null ? "" : createEndDate.getTime());
        param.put("updateBeginDate", updateBeginDate == null ? "" : updateBeginDate.getTime());
        param.put("updateEndDate", updateEndDate == null ? "" : updateEndDate.getTime());
        param.put("ouId", ouId);
        param.put("crmCode", crmCode);
        param.put("syncStatus", syncStatus);
        param.put("isInner", isInner);
        param.put("isAll", isAll);

        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/mylist", param);

        //String res = cleanStr(restTemplate.getForObject(url, String.class));
        String res = restTemplate.getForEntity(fromHttpUrl(url), String.class).getBody();
        PageInfo<CustomerDto> data = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CustomerDto>>>() {
        }).getData();
        PageResponse<CustomerDto> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation("根据业务主体分页查询客户信息")
    @GetMapping("listByOu")
    public Response listByOu(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                             @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                             @RequestParam(required = false) @ApiParam(value = "模糊搜索") String fuzzyLike,
                             @RequestParam(required = false) @ApiParam(value = "区域") String region,
                             @RequestParam(required = false) @ApiParam(value = "部门") String roleDeptId,
                             @RequestParam(required = false) @ApiParam(value = "客户名称") String name,
                             @RequestParam(required = false) @ApiParam(value = "状态") String status,
                             @RequestParam(required = false) @ApiParam(value = "创建开始时间") Date createBeginDate,
                             @RequestParam(required = false) @ApiParam(value = "创建结束时间") Date createEndDate,
                             @RequestParam(required = false) @ApiParam(value = "更新开始时间") Date updateBeginDate,
                             @RequestParam(required = false) @ApiParam(value = "更新结束时间") Date updateEndDate,
                             @RequestParam(required = false) @ApiParam(value = "OU") Long ouId,
                             @RequestParam(required = false) @ApiParam(value = "编号") String crmCode,
                             @RequestParam(required = false) @ApiParam(value = "CRM同步状态") Integer syncStatus,
                             @RequestParam(required = false) @ApiParam(value = "内外部") Boolean isInner) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyLike", fuzzyLike);
        param.put("region", region);
        param.put("roleDeptId", roleDeptId);
        param.put("name", name);
        param.put("status", status);
        param.put("createBeginDate", createBeginDate == null ? "" : createBeginDate.getTime());
        param.put("createEndDate", createEndDate == null ? "" : createEndDate.getTime());
        param.put("updateBeginDate", updateBeginDate == null ? "" : updateBeginDate.getTime());
        param.put("updateEndDate", updateEndDate == null ? "" : updateEndDate.getTime());
        param.put("ouId", ouId);
        param.put("crmCode", crmCode);
        param.put("syncStatus", syncStatus);
        param.put("isInner", isInner);

        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/listByOu", param);

        //String res = cleanStr(restTemplate.getForObject(url, String.class));
        String res = restTemplate.getForEntity(fromHttpUrl(url), String.class).getBody();
        PageInfo<CustomerDto> data = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CustomerDto>>>() {
        }).getData();
        PageResponse<CustomerDto> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation("根据客户名称和客户编号查询客户信息")
    @GetMapping("listByNameOrCrmCode")
    public Response list(@RequestParam(required = false, defaultValue = "1") @ApiParam(value = "页码") Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") @ApiParam(value = "分页大小") Integer pageSize,
                         @RequestParam(required = false) @ApiParam(value = "状态") String status,
                         @RequestParam(required = false) @ApiParam(value = "是否来源合同接口") Boolean contractFlag,
                         @RequestParam(required = false) @ApiParam(value = "模糊搜索") String fuzzyLike) {
        final Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("fuzzyLike", fuzzyLike);
        param.put("status", status);
        param.put("contractFlag", contractFlag);
        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/listByNameOrCrmCode", param);

        //String res = cleanStr(restTemplate.getForObject(url, String.class));
        String res = restTemplate.getForEntity(fromHttpUrl(url), String.class).getBody();
        PageInfo<CustomerDto> data = JSON.parseObject(res, new TypeReference<DataResponse<PageInfo<CustomerDto>>>() {
        }).getData();
        PageResponse<CustomerDto> response = Response.pageResponse();
        return response.convert(data);
    }

    @ApiOperation("根据客户名称查询客户信息(无需过滤数据)")
    @GetMapping("listCustomerByName")
    public Response listCustomerByName(@RequestParam(required = false) @ApiParam(value = "模糊搜索") String fuzzyLike,
                                       @RequestParam(required = false) @ApiParam(value = "统一信用代码") String registrationNumber) {
        final Map<String, Object> param = new HashMap<>();
        param.put("fuzzyLike", fuzzyLike);
        param.put("registrationNumber", registrationNumber);
        final String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "/customer/listCustomerByName", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<List<CustomerDto>>>() {
        });
    }

    @ApiOperation("根据客户名称和客户编号查询客户信息")
    @GetMapping("queryOuByCustomer")
    public Response queryOuByCustomer(@RequestParam(required = false) @ApiParam(value = "客户ID") Long customerId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("customerId", customerId);
        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/queryOuByCustomer", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Set<OperatingUnitDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Set<OperatingUnitDto>>>() {
        });
        return response;
    }

    @ApiOperation("根据使用单位查询业务实体")
    @GetMapping("queryOuByUnitId")
    public Response queryOuByUnitId(@RequestParam(required = false) @ApiParam(value = "使用单位") Long unitId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/queryOuByUnitId", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        DataResponse<Set<OperatingUnitDto>> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Set<OperatingUnitDto>>>() {
        });
        return response;
    }

    @ApiOperation("新增客户")
    @PostMapping("add")
    public Response addCustomer(@RequestBody CustomerDto customerDto) {
        String url = String.format("%scustomer/add", ModelsEnum.CRM.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, customerDto, String.class);
        String res = responseEntity.getBody();
        DataResponse<CustomerDto> response = JSON.parseObject(res, new TypeReference<DataResponse<CustomerDto>>() {
        });
        return response;
    }

    @ApiOperation("编辑客户")
    @PostMapping("update")
    public Response updateCustomer(@RequestBody CustomerDto customerDto) {
        String url = String.format("%scustomer/update", ModelsEnum.CRM.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, customerDto, String.class);
        String res = responseEntity.getBody();
        DataResponse<CustomerDto> response = JSON.parseObject(res, new TypeReference<DataResponse<CustomerDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "团队成员列表")
    @RequestMapping(value = {"/listTeamUsers"}, method = {RequestMethod.GET})
    public Response listTeamUsersByCustomerId(@RequestParam("customerId") @ApiParam(value = "客户ID") Long customerId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("customerId", customerId);
        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/listTeamUsers", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<List<TeamUserDto>> response = JSON.parseObject(res, new TypeReference<DataResponse<List<TeamUserDto>>>() {
        });
        return response;
    }

    @ApiOperation(value = "增加团队成员")
    @RequestMapping(value = {"/saveTeamUser"}, method = {RequestMethod.POST})
    @ResponseBody
    public Response saveTeamUser(@RequestBody TeamUsersDto teamUsersDto) {
        String url = String.format("%scustomer/saveTeamUser", ModelsEnum.CRM.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, teamUsersDto, String.class);
        String res = responseEntity.getBody();
        DataResponse<TeamUsersDto> response = JSON.parseObject(res, new TypeReference<DataResponse<TeamUsersDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "删除团队成员")
    @RequestMapping(value = {"/deleteTeamUser"}, method = {RequestMethod.GET})
    public Response deleteTeamUser(@RequestParam("teamUserId") @ApiParam(value = "团队成员ID") String teamUserId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("teamUserId", teamUserId);
        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/deleteTeamUser", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }


    @ApiOperation(value = "转让")
    @RequestMapping(value = "/changeOwner", method = {RequestMethod.POST})
    @ResponseBody
    public Response changeOwner(@RequestBody Map<String, Object> params) {
        String url = String.format("%scustomer/changeOwner", ModelsEnum.CRM.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, params, String.class);
        String res = responseEntity.getBody();
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "作废")
    @RequestMapping(value = "/disableCustomer", method = {RequestMethod.GET})
    public Response disableCustomer(@RequestParam("id") @ApiParam(value = "客户ID") Long id) {
        final Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/disableCustomer", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "客户导出")
    @RequestMapping(value = "/excel/export", method = {RequestMethod.GET})
    public void export(CustomerQuery query, HttpServletResponse response) {
        List<CustomerExcelVo> dataResult = getDataResult(query, "customer/excelList");
        ExportExcelUtil.exportExcel(dataResult, null, "Sheet1", CustomerExcelVo.class, "客户信息导出.xls", response);
    }

    @ApiOperation(value = "我的客户导出")
    @RequestMapping(value = "/excel/myexport", method = {RequestMethod.GET})
    public void myExport(CustomerQuery query, HttpServletResponse response) {
        List<CustomerExcelVo> dataResult = getDataResult(query, "customer/myexport");
        ExportExcelUtil.exportExcel(dataResult, null, "Sheet1", CustomerExcelVo.class, "客户信息导出.xls", response);
    }

    protected List<CustomerExcelVo> getDataResult(CustomerQuery customerQuery, String ul) {
        List<CustomerExcelVo> contentList = new ArrayList<>();
        final Map<String, Object> param = new HashMap<>();
        param.put("fuzzyLike", customerQuery.getFuzzyLike());
        param.put("region", customerQuery.getRegion());
        param.put("roleDeptId", customerQuery.getRoleDeptId());
        param.put("name", customerQuery.getName());
        param.put("status", customerQuery.getStatus());
        param.put("isAll", customerQuery.getIsAll());
        param.put("createBeginDate", customerQuery.getCreateBeginDate() == null ? "" : customerQuery.getCreateBeginDate().getTime());
        param.put("createEndDate", customerQuery.getCreateEndDate() == null ? "" : customerQuery.getCreateEndDate().getTime());
        param.put("updateBeginDate", customerQuery.getUpdateBeginDate() == null ? "" : customerQuery.getUpdateBeginDate().getTime());
        param.put("updateEndDate", customerQuery.getUpdateEndDate() == null ? "" : customerQuery.getUpdateEndDate().getTime());
        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), ul, param);
        String res = restTemplate.getForObject(fromHttpUrl(url), String.class);
        PageInfo<CustomerDto> customerVoPage = JSON.parseObject(res, new TypeReference<PageInfo<CustomerDto>>() {
        });
        if (customerVoPage.getList() != null) {
            for (CustomerDto customerDto : customerVoPage.getList()) {
                CustomerExcelVo excelVo = new CustomerExcelVo();
                BeanUtils.copyProperties(customerDto, excelVo);
                final Map<String, Object> businessParam = new HashMap<>();
                businessParam.put("customerId", customerDto.getId());
                businessParam.put("pageSize", Integer.MAX_VALUE);
                businessParam.put("status", Integer.valueOf("1"));
                businessParam.put("onlyList", true);
                String url1 = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "business/businessTotal", businessParam);
                String res1 = restTemplate.getForObject(fromHttpUrl(url1), String.class);
                PageInfo<BusinessDto> businessPage = JSON.parseObject(res1, new TypeReference<PageInfo<BusinessDto>>() {
                });
                excelVo.setBusinessTotal(businessPage.getTotal()); //有效商机数量

                final Map<String, Object> businessParam2 = new HashMap<>();
                businessParam2.put("customerId", customerDto.getId());
                businessParam2.put("pageSize", Integer.MAX_VALUE);
                businessParam2.put("status", Integer.valueOf("2"));
                businessParam.put("onlyList", true);
                String url2 = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "business/businessTotal", businessParam2);
                String res2 = restTemplate.getForObject(fromHttpUrl(url2), String.class);
                PageInfo<BusinessDto> businessPage2 = JSON.parseObject(res2, new TypeReference<PageInfo<BusinessDto>>() {
                });
                excelVo.setBusinessWinTotal(businessPage2.getTotal()); //已赢单商机数量

                contentList.add(excelVo);
            }
        }
        return contentList;
    }

    @ApiOperation(value = "录入小助手查询")
    @RequestMapping(value = {"/assistant/query"}, method = {RequestMethod.GET})
    public Response assistantQuery(@RequestParam("name") @ApiParam(value = "公司全称") String name) {
        final Map<String, Object> param = new HashMap<>();
        param.put("name", name);
        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/assistant/query", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<CustomerDto> response = JSON.parseObject(res, new TypeReference<DataResponse<CustomerDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "校验客户名称或统一信用代码")
    @GetMapping("/checkCustomerNameOrRn")
    public DataResponse checkCustomerNameOrRn(@RequestParam(required = false) @ApiParam("客户名称") String name,
                                              @RequestParam(required = false) @ApiParam("客户ID") Long customerId,
                                              @RequestParam(required = false) @ApiParam("统一信用代码") String registrationNumber) {
        final Map<String, Object> param = new HashMap<>();
        param.put("name", name);
        param.put("registrationNumber", registrationNumber);
        param.put("customerId", customerId);

        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/checkCustomerNameOrRn", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "校验客户是否已引用")
    @GetMapping("/checkCustomerExisting")
    public DataResponse checkCustomerExisting(@RequestParam @ApiParam("客户ID") Long customerId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("customerId", customerId);

        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/checkCustomerExisting", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "查询对客户信息操作的分类类型")
    @GetMapping("/checkCustomerModifyType")
    public DataResponse checkCustomerModifyType(@RequestParam @ApiParam("客户ID") Long customerId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("customerId", customerId);

        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/checkCustomerModifyType", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @Deprecated // BUG2022080248221 CRM推客户过来PAM时，不需要校验注册地址唯一
    @ApiOperation(value = "校验客户注册地址")
    @GetMapping("/checkCustomerAddress")
    public DataResponse checkCustomerAddress(@RequestParam(required = false) @ApiParam("客户名称") Long customerId,
                                             @RequestParam(required = false) @ApiParam("注册地址") String registeredAddress) {
        final Map<String, Object> param = new HashMap<>();
        param.put("customerId", customerId);
        param.put("registeredAddress", registeredAddress);

        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/checkCustomerAddress", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "校验客户三证")
    @GetMapping("/checkCustomerBusinessLicenseNumber")
    public DataResponse checkCustomerBusinessLicenseNumber(@RequestParam(required = false) @ApiParam("客户名称") Long customerId,
                                                           @RequestParam(required = false) @ApiParam("营业执照号") String businessLicenseNumber,
                                                           @RequestParam(required = false) @ApiParam("组织机构代码证") String organizationCode,
                                                           @RequestParam(required = false) @ApiParam("纳税人登记号") String registrationMark) {
        final Map<String, Object> param = new HashMap<>();
        param.put("businessLicenseNumber", businessLicenseNumber);
        param.put("organizationCode", organizationCode);
        param.put("registrationMark", registrationMark);
        param.put("customerId", customerId);

        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/checkCustomerBusinessLicenseNumber", param);
        String res = restTemplate.getForObject(url, String.class);
        DataResponse<Integer> response = JSON.parseObject(res, new TypeReference<DataResponse<Integer>>() {
        });
        return response;
    }

    @ApiOperation(value = "新增或更新联系人")
    @PostMapping("/contact/saveOrUpdate")
    public DataResponse saveOrUpdateContact(@RequestBody CustomerContactDto customerContactDto) {
        String url = String.format("%scustomer/contact/saveOrUpdate", ModelsEnum.CRM.getBaseUrl());
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, customerContactDto, String.class);
        final String res = responseEntity.getBody();
        DataResponse<CustomerContactDto> response = JSON.parseObject(res, new TypeReference<DataResponse<CustomerContactDto>>() {
        });
        return response;
    }

    @ApiOperation(value = "删除联系人")
    @DeleteMapping("/contact/delete")
    public DataResponse deleteContact(@RequestParam @ApiParam("联系人ID") Long id) {
        DataResponse<Integer> response = Response.dataResponse();
        String url = String.format("%scustomer/contact/delete", ModelsEnum.CRM.getBaseUrl());
        restTemplate.exchange(url + "?id=" + id, HttpMethod.DELETE, null, String.class);
        return response.setData(1);
    }

    @ApiOperation(value = "pushCustomerToCrm")
    @GetMapping("/pushCustomerToCrm")
    public Response pushCustomerToCrm(@RequestParam(required = false) final Long customerId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("customerId", customerId);
        final String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "/customer/pushCustomerToCrm", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<String>>() {
        });
    }

    @ApiOperation(value = "获取客户开票信息完善")
    @GetMapping("/getCustomerModifyHistories")
    public Response getCustomerModifyHistories(@RequestParam final Long customerId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("customerId", customerId);
        final String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "/customer/getCustomerModifyHistories", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<CustomerModifyHistory>>() {
        });
    }

    @ApiOperation("通过crmCode获取客户银行信息")
    @GetMapping("/getCustomerBankInfo")
    public Response getCustomerBankInfo(@RequestParam(required = true) String crmCode,
                                        @RequestParam(required = true) Long ouId) {
        final Map<String, Object> param = new HashMap<>();
        param.put("crmCode", crmCode);
        param.put("ouId", ouId);
        final String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "/customer/getCustomerBankInfo", param);
        final String res = restTemplate.getForEntity(url, String.class).getBody();
        return JSON.parseObject(res, new TypeReference<DataResponse<CustomerBankAccount>>() {
        });
    }

    @ApiOperation(value = "根据业务实体id查询客户(支持模糊搜索)")
    @GetMapping("/selectByOuIdAndName")
    public DataResponse selectByOuIdAndName(@RequestParam(required = false) Long unitId,
                                            @RequestParam(required = false) Long ouId,
                                            @RequestParam(required = false) String name,
                                            @RequestParam(required = false) Integer pageNum,
                                            @RequestParam(required = false) Integer pageSize) {
        final Map<String, Object> param = new HashMap<>();
        param.put("unitId", unitId);
        param.put("ouId", ouId);
        param.put("name", name);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);

        String url = buildGetUrl(ModelsEnum.CRM.getBaseUrl(), "customer/selectByOuIdAndName", param);
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<List<CustomerDto>>>() {
        });
    }


    @ApiOperation(value = "客户企业性质初始化")
    @GetMapping("init/customerEnterpriseNature")
    public Response initCustomerEnterpriseNature() {
        String url = String.format("%scustomer/init/customerEnterpriseNature", ModelsEnum.CRM.getBaseUrl());
        String res = restTemplate.getForObject(url, String.class);
        return JSON.parseObject(res, new TypeReference<DataResponse<Boolean>>() {
        });
    }
}
