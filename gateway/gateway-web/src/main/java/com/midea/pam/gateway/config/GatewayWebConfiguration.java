package com.midea.pam.gateway.config;

import com.midea.pam.ctc.web.ProjectReopenController;
import com.midea.pam.gateway.AuthorityController;
import com.midea.pam.gateway.DeadLetterNewController;
import com.midea.pam.gateway.DeliveryAddressController;
import com.midea.pam.gateway.FileController;
import com.midea.pam.gateway.FormTemplateController;
import com.midea.pam.gateway.IFlowExceptionReBuildController;
import com.midea.pam.gateway.MipCallbackLogController;
import com.midea.pam.gateway.MipWorkflowController;
import com.midea.pam.gateway.NoticeController;
import com.midea.pam.gateway.PurchaseMaterialRequirementDeliveryAddressHistoryController;
import com.midea.pam.gateway.RocketMQMsgConsumerRecordController;
import com.midea.pam.gateway.SpELController;
import com.midea.pam.gateway.SystemController;
import com.midea.pam.gateway.SystemSetController;
import com.midea.pam.gateway.TemplateController;
import com.midea.pam.gateway.TestSystemController;
import com.midea.pam.gateway.WorkFlowDraftSubmitTemporaryRecordController;
import com.midea.pam.gateway.basedata.web.AccountAliasController;
import com.midea.pam.gateway.basedata.web.AgingSegmentController;
import com.midea.pam.gateway.basedata.web.AnnouncementController;
import com.midea.pam.gateway.basedata.web.AreaController;
import com.midea.pam.gateway.basedata.web.AreaUnitRelController;
import com.midea.pam.gateway.basedata.web.BankAccountController;
import com.midea.pam.gateway.basedata.web.BaseDataOperationController;
import com.midea.pam.gateway.basedata.web.BomController;
import com.midea.pam.gateway.basedata.web.BrandMaintenanceController;
import com.midea.pam.gateway.basedata.web.BrandMaintenanceIntermediateController;
import com.midea.pam.gateway.basedata.web.BudgetDepController;
import com.midea.pam.gateway.basedata.web.BudgetTreeController;
import com.midea.pam.gateway.basedata.web.CacheDataOuUnitExtController;
import com.midea.pam.gateway.basedata.web.CoaSubjectController;
import com.midea.pam.gateway.basedata.web.ContractIndustryController;
import com.midea.pam.gateway.basedata.web.ContractSigningCenterController;
import com.midea.pam.gateway.basedata.web.CostCoefficientController;
import com.midea.pam.gateway.basedata.web.CurrencyController;
import com.midea.pam.gateway.basedata.web.CustTrxTypeController;
import com.midea.pam.gateway.basedata.web.EmployeeInfoController;
import com.midea.pam.gateway.basedata.web.EsbMassQueryRecordController;
import com.midea.pam.gateway.basedata.web.FeeItemController;
import com.midea.pam.gateway.basedata.web.FeeTypeController;
import com.midea.pam.gateway.basedata.web.FormConfigController;
import com.midea.pam.gateway.basedata.web.GlDailyRateController;
import com.midea.pam.gateway.basedata.web.GlPeriodController;
import com.midea.pam.gateway.basedata.web.HroRoleController;
import com.midea.pam.gateway.basedata.web.InventoryController;
import com.midea.pam.gateway.basedata.web.LaborCostController;
import com.midea.pam.gateway.basedata.web.LaborCostRealMonthController;
import com.midea.pam.gateway.basedata.web.LaborExternalCostController;
import com.midea.pam.gateway.basedata.web.LtcDictController;
import com.midea.pam.gateway.basedata.web.MaterialController;
import com.midea.pam.gateway.basedata.web.MaterialCostController;
import com.midea.pam.gateway.basedata.web.MaterialPriceConfigController;
import com.midea.pam.gateway.basedata.web.MaterialPriceController;
import com.midea.pam.gateway.basedata.web.MaterialPriceReceiptController;
import com.midea.pam.gateway.basedata.web.MenuManageController;
import com.midea.pam.gateway.basedata.web.MenuManageGrantController;
import com.midea.pam.gateway.basedata.web.MilepostDesignPlanImportController;
import com.midea.pam.gateway.basedata.web.OperatingUnitController;
import com.midea.pam.gateway.basedata.web.OrgLaborCostTypeSetController;
import com.midea.pam.gateway.basedata.web.OrgUnitController;
import com.midea.pam.gateway.basedata.web.OrganizationRelController;
import com.midea.pam.gateway.basedata.web.OrginizationController;
import com.midea.pam.gateway.basedata.web.ProjectProductMaintenanceController;
import com.midea.pam.gateway.basedata.web.ProvinceAreaRelController;
import com.midea.pam.gateway.basedata.web.ReceiptMethodController;
import com.midea.pam.gateway.basedata.web.ReceivableTrxController;
import com.midea.pam.gateway.basedata.web.ResourceController;
import com.midea.pam.gateway.basedata.web.RevenueTargetInfoController;
import com.midea.pam.gateway.basedata.web.RoleGrantUserController;
import com.midea.pam.gateway.basedata.web.RoleInfoController;
import com.midea.pam.gateway.basedata.web.RoleOuRelController;
import com.midea.pam.gateway.basedata.web.StorageController;
import com.midea.pam.gateway.basedata.web.StorageInventoryController;
import com.midea.pam.gateway.basedata.web.SystemParamController;
import com.midea.pam.gateway.basedata.web.TargetDescriptionController;
import com.midea.pam.gateway.basedata.web.TaxInfoController;
import com.midea.pam.gateway.basedata.web.TestApolloController;
import com.midea.pam.gateway.basedata.web.UnitBudgetDepRelController;
import com.midea.pam.gateway.basedata.web.UnitController;
import com.midea.pam.gateway.basedata.web.UserCollectController;
import com.midea.pam.gateway.basedata.web.UserExtendAttrController;
import com.midea.pam.gateway.basedata.web.UserUnitController;
import com.midea.pam.gateway.basedata.web.VendorController;
import com.midea.pam.gateway.basedata.web.VendorProportionController;
import com.midea.pam.gateway.basedata.web.VendorSiteBankController;
import com.midea.pam.gateway.basedata.web.WbsConstraintController;
import com.midea.pam.gateway.crm.web.AchievementGoalController;
import com.midea.pam.gateway.crm.web.AttributionUnitController;
import com.midea.pam.gateway.crm.web.BusinessCallbackController;
import com.midea.pam.gateway.crm.web.BusinessController;
import com.midea.pam.gateway.crm.web.BusinessFollowRecordController;
import com.midea.pam.gateway.crm.web.BusinessQuotationFileWorkflowController;
import com.midea.pam.gateway.crm.web.BusinessRiskStrategyWorkflowController;
import com.midea.pam.gateway.crm.web.CustomerBaseInfoModifyCallbackController;
import com.midea.pam.gateway.crm.web.CustomerBusinessModifyCallbackController;
import com.midea.pam.gateway.crm.web.CustomerController;
import com.midea.pam.gateway.crm.web.CustomerModifyHistoryController;
import com.midea.pam.gateway.crm.web.CustomerModifyWfCallbackController;
import com.midea.pam.gateway.crm.web.LeadController;
import com.midea.pam.gateway.crm.web.PlanController;
import com.midea.pam.gateway.crm.web.ProductAttributeController;
import com.midea.pam.gateway.crm.web.ProductController;
import com.midea.pam.gateway.crm.web.ProductOrganizationController;
import com.midea.pam.gateway.crm.web.QuotationController;
import com.midea.pam.gateway.crm.web.RoleDeptController;
import com.midea.pam.gateway.crm.web.TaxRateConfigurationController;
import com.midea.pam.gateway.crm.web.TianYanChaController;
import com.midea.pam.gateway.ctc.web.ApplicationIndustryController;
import com.midea.pam.gateway.ctc.web.AssetDeprnAccountingController;
import com.midea.pam.gateway.ctc.web.AsyncRequestResultController;
import com.midea.pam.gateway.ctc.web.BasicCacheController;
import com.midea.pam.gateway.ctc.web.BudgetItemController;
import com.midea.pam.gateway.ctc.web.BusiSceneNonSaleController;
import com.midea.pam.gateway.ctc.web.BusinessDocumentLibraryController;
import com.midea.pam.gateway.ctc.web.CarryoverBillAccountingController;
import com.midea.pam.gateway.ctc.web.CarryoverBillController;
import com.midea.pam.gateway.ctc.web.CheckConfigController;
import com.midea.pam.gateway.ctc.web.CheckItemController;
import com.midea.pam.gateway.ctc.web.CheckResultController;
import com.midea.pam.gateway.ctc.web.CloudPrintController;
import com.midea.pam.gateway.ctc.web.CnapsInfoController;
import com.midea.pam.gateway.ctc.web.CodeRuleController;
import com.midea.pam.gateway.ctc.web.CollectionConfigurationController;
import com.midea.pam.gateway.ctc.web.ContractClassificationController;
import com.midea.pam.gateway.ctc.web.ContractController;
import com.midea.pam.gateway.ctc.web.ContractDrafterChangeCallBackController;
import com.midea.pam.gateway.ctc.web.ContractProductChanageCallBackController;
import com.midea.pam.gateway.ctc.web.ContractReceiptPlanChanageCallBackController;
import com.midea.pam.gateway.ctc.web.ContractTerminationCallbackController;
import com.midea.pam.gateway.ctc.web.ContractWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.CostCollectionController;
import com.midea.pam.gateway.ctc.web.CustomerTransferController;
import com.midea.pam.gateway.ctc.web.CustomerTransferReverseWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.CustomerTransferWorkflowCallBackController;
import com.midea.pam.gateway.ctc.web.DifferenceShareAccountController;
import com.midea.pam.gateway.ctc.web.DifferenceShareController;
import com.midea.pam.gateway.ctc.web.DocumentLibraryController;
import com.midea.pam.gateway.ctc.web.EamPurchaseInfoController;
import com.midea.pam.gateway.ctc.web.ErpCodeRuleController;
import com.midea.pam.gateway.ctc.web.EsbResultReturnController;
import com.midea.pam.gateway.ctc.web.ExchangeAccountController;
import com.midea.pam.gateway.ctc.web.FormInstanceController;
import com.midea.pam.gateway.ctc.web.GSCInvoiceController;
import com.midea.pam.gateway.ctc.web.GSCInvoiceDetailWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.GSCInvoiceIspWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.GlegalContractChangeController;
import com.midea.pam.gateway.ctc.web.GlegalContractWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.GlegalPurchaseContractWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.HroRequirementController;
import com.midea.pam.gateway.ctc.web.HroWorkingHourBillController;
import com.midea.pam.gateway.ctc.web.HroWorkingHourController;
import com.midea.pam.gateway.ctc.web.IhrAttendDetailController;
import com.midea.pam.gateway.ctc.web.ImportDataController;
import com.midea.pam.gateway.ctc.web.InnerSwapApplyController;
import com.midea.pam.gateway.ctc.web.InvoiceApplyController;
import com.midea.pam.gateway.ctc.web.InvoicePlanController;
import com.midea.pam.gateway.ctc.web.InvoiceReceivableController;
import com.midea.pam.gateway.ctc.web.InvoiceReceivableReverseCallBackController;
import com.midea.pam.gateway.ctc.web.LegalContractController;
import com.midea.pam.gateway.ctc.web.MRPController;
import com.midea.pam.gateway.ctc.web.MaterialAdjustController;
import com.midea.pam.gateway.ctc.web.MaterialAttributeController;
import com.midea.pam.gateway.ctc.web.MaterialChangeTypeController;
import com.midea.pam.gateway.ctc.web.MaterialCostTransferController;
import com.midea.pam.gateway.ctc.web.MaterialCustomDictController;
import com.midea.pam.gateway.ctc.web.MaterialDelistController;
import com.midea.pam.gateway.ctc.web.MaterialGetController;
import com.midea.pam.gateway.ctc.web.MaterialOutsourcingContractConfigController;
import com.midea.pam.gateway.ctc.web.MaterialReturnController;
import com.midea.pam.gateway.ctc.web.MaterialTransferController;
import com.midea.pam.gateway.ctc.web.MilepostDesignMemberController;
import com.midea.pam.gateway.ctc.web.MilepostDesignPlanChangeController;
import com.midea.pam.gateway.ctc.web.MilepostDesignPlanChangeWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.MilepostDesignPlanController;
import com.midea.pam.gateway.ctc.web.MilepostDesignPlanDetailChangeRecordController;
import com.midea.pam.gateway.ctc.web.MilepostDesignPlanNotPublishRequirementController;
import com.midea.pam.gateway.ctc.web.MilepostTemplateController;
import com.midea.pam.gateway.ctc.web.MobileAppController;
import com.midea.pam.gateway.ctc.web.OrganizationCustomDictController;
import com.midea.pam.gateway.ctc.web.PaymentApplyController;
import com.midea.pam.gateway.ctc.web.PaymentApplyWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.PaymentInvoiceController;
import com.midea.pam.gateway.ctc.web.PaymentInvoiceDetailController;
import com.midea.pam.gateway.ctc.web.PaymentPlanController;
import com.midea.pam.gateway.ctc.web.PaymentRecordController;
import com.midea.pam.gateway.ctc.web.PaymentWriteOffRecordController;
import com.midea.pam.gateway.ctc.web.PreProjectCallBackController;
import com.midea.pam.gateway.ctc.web.ProductTaxSettingController;
import com.midea.pam.gateway.ctc.web.ProjectActivityController;
import com.midea.pam.gateway.ctc.web.ProjectAssetChangeCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectAwardController;
import com.midea.pam.gateway.ctc.web.ProjectAwardDeductionController;
import com.midea.pam.gateway.ctc.web.ProjectBaseInfoBatchChangeCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectBaseInfoChangeCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectBaselineBatchContractRsController;
import com.midea.pam.gateway.ctc.web.ProjectBaselineBatchController;
import com.midea.pam.gateway.ctc.web.ProjectBudgetChangeWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.ProjectBudgetCostController;
import com.midea.pam.gateway.ctc.web.ProjectBudgetMaterialController;
import com.midea.pam.gateway.ctc.web.ProjectBudgetTargetChangeCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectBusinessController;
import com.midea.pam.gateway.ctc.web.ProjectCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectCheckController;
import com.midea.pam.gateway.ctc.web.ProjectCloseMilestoneController;
import com.midea.pam.gateway.ctc.web.ProjectContractChangeCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectCustomerSatisfactionController;
import com.midea.pam.gateway.ctc.web.ProjectDeliveriesController;
import com.midea.pam.gateway.ctc.web.ProjectFundsDeployController;
import com.midea.pam.gateway.ctc.web.ProjectGanttChartController;
import com.midea.pam.gateway.ctc.web.ProjectMilepostChangeCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectMilepostChangeHistoryController;
import com.midea.pam.gateway.ctc.web.ProjectMilepostController;
import com.midea.pam.gateway.ctc.web.ProjectPreviewCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectProblemCommentController;
import com.midea.pam.gateway.ctc.web.ProjectProblemController;
import com.midea.pam.gateway.ctc.web.ProjectProblemOperationRecordController;
import com.midea.pam.gateway.ctc.web.ProjectReopenCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectRiskSettingController;
import com.midea.pam.gateway.ctc.web.ProjectRoleController;
import com.midea.pam.gateway.ctc.web.ProjectSecurityIncidentController;
import com.midea.pam.gateway.ctc.web.ProjectTerminationWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.ProjectTransferQualityCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectTransferQualityController;
import com.midea.pam.gateway.ctc.web.ProjectTypeController;
import com.midea.pam.gateway.ctc.web.ProjectWbsBaselineChangeCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectWbsBaselineController;
import com.midea.pam.gateway.ctc.web.ProjectWbsBudgetBaselineChangeHistoryController;
import com.midea.pam.gateway.ctc.web.ProjectWbsBudgetChangeHistoryController;
import com.midea.pam.gateway.ctc.web.ProjectWbsBudgetChangeWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.ProjectWbsBudgetController;
import com.midea.pam.gateway.ctc.web.ProjectWbsBudgetSummaryChangeHistoryController;
import com.midea.pam.gateway.ctc.web.ProjectWbsBudgetSummaryController;
import com.midea.pam.gateway.ctc.web.ProjectWbsReceiptsController;
import com.midea.pam.gateway.ctc.web.ProjectWbsReceiptsRequirementChangeRecordController;
import com.midea.pam.gateway.ctc.web.ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.ProjectWbsReceiptsWorkflowCallBackController;
import com.midea.pam.gateway.ctc.web.ProjectWbsSubmitReceiptsWorkflowCallBackController;
import com.midea.pam.gateway.ctc.web.PurchaseBpaPriceController;
import com.midea.pam.gateway.ctc.web.PurchaseContractBudgetController;
import com.midea.pam.gateway.ctc.web.PurchaseContractController;
import com.midea.pam.gateway.ctc.web.PurchaseContractDetailChangeCallBackController;
import com.midea.pam.gateway.ctc.web.PurchaseContractInfoChangeCallBackController;
import com.midea.pam.gateway.ctc.web.PurchaseContractPlanChangeCallBackController;
import com.midea.pam.gateway.ctc.web.PurchaseContractProgressWorkflowCallBackController;
import com.midea.pam.gateway.ctc.web.PurchaseContractPunishmentConfigController;
import com.midea.pam.gateway.ctc.web.PurchaseContractPunishmentController;
import com.midea.pam.gateway.ctc.web.PurchaseContractPunishmentWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.PurchaseContractWorkflowCallbackController;
import com.midea.pam.gateway.ctc.web.PurchaseMaterialCloseDetailController;
import com.midea.pam.gateway.ctc.web.PurchaseMaterialReleaseDetailController;
import com.midea.pam.gateway.ctc.web.PurchaseMaterialRequirementController;
import com.midea.pam.gateway.ctc.web.PurchaseOrderCallBackController;
import com.midea.pam.gateway.ctc.web.PurchaseOrderChangeCallBackController;
import com.midea.pam.gateway.ctc.web.PurchaseOrderChangeController;
import com.midea.pam.gateway.ctc.web.PurchaseOrderController;
import com.midea.pam.gateway.ctc.web.PurchaseOrderDetailDeliveryAddressHistoryController;
import com.midea.pam.gateway.ctc.web.RdmResourcePlanController;
import com.midea.pam.gateway.ctc.web.RdmSettlementSheetController;
import com.midea.pam.gateway.ctc.web.RdmWorkingHourController;
import com.midea.pam.gateway.ctc.web.ReceiptClaimController;
import com.midea.pam.gateway.ctc.web.ReceiptPlanController;
import com.midea.pam.gateway.ctc.web.ReceiptWorkOrderController;
import com.midea.pam.gateway.ctc.web.RefundApplyController;
import com.midea.pam.gateway.ctc.web.ResendExecuteController;
import com.midea.pam.gateway.ctc.web.RevenueCostOrderController;
import com.midea.pam.gateway.ctc.web.SdpBuyersController;
import com.midea.pam.gateway.ctc.web.SdpController;
import com.midea.pam.gateway.ctc.web.SdpLogController;
import com.midea.pam.gateway.ctc.web.SealAdministratorController;
import com.midea.pam.gateway.ctc.web.StandardTermsCallbackController;
import com.midea.pam.gateway.ctc.web.StandardTermsController;
import com.midea.pam.gateway.ctc.web.SupplierQualityDeactivationController;
import com.midea.pam.gateway.ctc.web.SwapExecuteController;
import com.midea.pam.gateway.ctc.web.TicketTasksController;
import com.midea.pam.gateway.ctc.web.TicketWorkingHourImportController;
import com.midea.pam.gateway.ctc.web.VendorAslController;
import com.midea.pam.gateway.ctc.web.VendorPenaltyConfigController;
import com.midea.pam.gateway.ctc.web.VendorPenaltyController;
import com.midea.pam.gateway.ctc.web.WbsTemplateInfoController;
import com.midea.pam.gateway.ctc.web.WbsTemplateRuleController;
import com.midea.pam.gateway.ctc.web.WbsTemplateRuleDetailController;
import com.midea.pam.gateway.ctc.web.WorkingHourAccountingController;
import com.midea.pam.gateway.ctc.web.WorkingHourController;
import com.midea.pam.gateway.ctc.web.WorkingHourCostChangeHeaderController;
import com.midea.pam.gateway.ctc.web.WorkingHourDistributeController;
import com.midea.pam.gateway.ctc.web.WorkingHourRemindWhiteListController;
import com.midea.pam.gateway.ctc.web.WriteOffController;
import com.midea.pam.gateway.externalsys.glegal.ContractModuleController;
import com.midea.pam.gateway.externalsys.glegal.GlegalWorkflowController;
import com.midea.pam.gateway.externalsys.ocr.MediaOcrController;
import com.midea.pam.gateway.filter.MobileAppAuthorityFilter;
import com.midea.pam.gateway.mdw.web.DmFactController;
import com.midea.pam.gateway.statistics.web.BudgetItemStatisticsController;
import com.midea.pam.gateway.statistics.web.CodeRuleStatisticsController;
import com.midea.pam.gateway.statistics.web.ContractStatisticsController;
import com.midea.pam.gateway.statistics.web.CustomerTransferStatisticsController;
import com.midea.pam.gateway.statistics.web.DepartmentUnitIncomingTargetController;
import com.midea.pam.gateway.statistics.web.GscPaymentInvoiceStatisticsController;
import com.midea.pam.gateway.statistics.web.IncomeCalculateController;
import com.midea.pam.gateway.statistics.web.IncomeCalculateProductTaskController;
import com.midea.pam.gateway.statistics.web.IncomeCalculateProjectTaskController;
import com.midea.pam.gateway.statistics.web.InvoicePlanStaController;
import com.midea.pam.gateway.statistics.web.MaterialCostStatisticsController;
import com.midea.pam.gateway.statistics.web.MaterialGetReturnOutsideController;
import com.midea.pam.gateway.statistics.web.NotRemindRecordController;
import com.midea.pam.gateway.statistics.web.PersonalPortalController;
import com.midea.pam.gateway.statistics.web.PortalTargetController;
import com.midea.pam.gateway.statistics.web.ProjectAwardStatisticsController;
import com.midea.pam.gateway.statistics.web.ProjectCostController;
import com.midea.pam.gateway.statistics.web.ProjectCustomerSatisfactionStatisticsController;
import com.midea.pam.gateway.statistics.web.ProjectExtraInfoStatisticsController;
import com.midea.pam.gateway.statistics.web.ProjectMilepostDesignStatisticsController;
import com.midea.pam.gateway.statistics.web.ProjectProblemStatisticsController;
import com.midea.pam.gateway.statistics.web.ProjectSecurityIncidentStatisticsController;
import com.midea.pam.gateway.statistics.web.ProjectWbsCostController;
import com.midea.pam.gateway.statistics.web.ProjectWbsReceiptsStatisticsController;
import com.midea.pam.gateway.statistics.web.PurchaseMaterialRequirementStatisticsController;
import com.midea.pam.gateway.statistics.web.PurchaseOrderStatisticsController;
import com.midea.pam.gateway.statistics.web.PurchaseStatisticsController;
import com.midea.pam.gateway.statistics.web.QuotationStatisticsController;
import com.midea.pam.gateway.statistics.web.RolePortalTargetRelController;
import com.midea.pam.gateway.statistics.web.VendorProportionStatisticsController;
import com.midea.pam.gateway.statistics.web.WorkingHourAccountingStaController;
import com.midea.pam.gateway.statistics.web.WorkingHourAccountingStatisticsController;
import com.midea.pam.gateway.statistics.web.WorkingHourExceptionStatisticsController;
import com.netflix.zuul.ZuulFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;

import javax.annotation.Resource;

@Configuration
public class GatewayWebConfiguration {

    @Resource
    private MobileAppAuthorityConfig mobileAppAuthorityConfig;

    @Bean
    public FilterRegistrationBean setFilter() {
        FilterRegistrationBean filterBean = new FilterRegistrationBean();
        filterBean.setFilter(new MobileAppAuthorityFilter(mobileAppAuthorityConfig));
        filterBean.setName("gatewayWebFilter");
        filterBean.setOrder(0);//设置该过滤器的优先级，数字越小，优先级越高
        filterBean.addUrlPatterns("/*");
        return filterBean;
    }

//    @Bean
//    public FilterRegistrationBean<SSOAuthInterceptor> ssoAuthInterceptor() {
//        FilterRegistrationBean filterBean = new FilterRegistrationBean();
//        filterBean.setFilter(new SSOAuthInterceptor());
//        filterBean.setName("ssoAuthInterceptor");
//        filterBean.setOrder(11);//设置该过滤器的优先级，数字越小，优先级越高
//        filterBean.addUrlPatterns("/*");
//        return filterBean;
//    }

    @Bean
    public RequestTimeConsumingFilter requestTimeConsumingFilter() {
        return new RequestTimeConsumingFilter();
    }

    @Bean
    public UnitAuthInterceptor unitAuthInterceptor() {
        return new UnitAuthInterceptor();
    }

    @Bean
    public CommonExceptionHandler commonExceptionHandler() {
        return new CommonExceptionHandler();
    }

    @Bean
    public OrginizationController orgizationController() {
        return new OrginizationController();
    }

    @Bean
    public ResourceController resourceController() {
        return new ResourceController();
    }

    @Bean
    public RoleGrantUserController roleGrantUserController() {
        return new RoleGrantUserController();
    }

    @Bean
    public RoleInfoController roleInfoController() {
        return new RoleInfoController();
    }

    @Bean
    public MenuManageController menuManageController() {
        return new MenuManageController();
    }

    @Bean
    public LtcDictController ltcDictController() {
        return new LtcDictController();
    }

    @Bean
    public EsbMassQueryRecordController esbMassQueryRecordController() {
        return new EsbMassQueryRecordController();
    }

    @Bean
    public RevenueTargetInfoController revenueTargetInfoController() {
        return new RevenueTargetInfoController();
    }

    @Bean
    public CustomerController customerController() {
        return new CustomerController();
    }

    @Bean
    public AreaController areaController() {
        return new AreaController();
    }

    @Bean
    public GlPeriodController glPeriodController() {
        return new GlPeriodController();
    }

    @Bean
    public BankAccountController bankAccountController() {
        return new BankAccountController();
    }

    @Bean
    public GlDailyRateController glDailyRateController() {
        return new GlDailyRateController();
    }

    @Bean
    public CustTrxTypeController custTrxTypeController() {
        return new CustTrxTypeController();
    }

    @Bean
    public InventoryController inventoryController() {
        return new InventoryController();
    }

    @Bean
    public OrganizationRelController organizationRelController() {
        return new OrganizationRelController();
    }

    @Bean
    public LeadController leadController() {
        return new LeadController();
    }

    @Bean
    public ReceiptMethodController receiptMethodController() {
        return new ReceiptMethodController();
    }

    @Bean
    public MipWorkflowController mipWorkflowController() {
        return new MipWorkflowController();
    }

    @Bean
    public AuthorityController authorityController() {
        return new AuthorityController();
    }

    @Bean
    public CurrencyController currencyController() {
        return new CurrencyController();
    }

    @Bean
    public ContractIndustryController contractIndustryController() {
        return new ContractIndustryController();
    }

    @Bean
    public ContractSigningCenterController contractSigningCenterController() {
        return new ContractSigningCenterController();
    }

    @Bean
    public TaxInfoController taxInfoController() {
        return new TaxInfoController();
    }

    @Bean
    public VendorController vendorController() {
        return new VendorController();
    }

    @Bean
    public VendorProportionController vendorProportionController() {
        return new VendorProportionController();
    }

    @Bean
    public VendorAslController vendorAslController() {
        return new VendorAslController();
    }

    @Bean
    public MenuManageGrantController menuManageGrantController() {
        return new MenuManageGrantController();
    }

    @Bean
    public AccountAliasController accountAliasController() {
        return new AccountAliasController();
    }

    @Bean
    public BusinessController businessController() {
        return new BusinessController();
    }

    @Bean
    public BusinessCallbackController businessCallbackController() {
        return new BusinessCallbackController();
    }

    @Bean
    public RoleDeptController roleDeptController() {
        return new RoleDeptController();
    }

    @Bean
    public AchievementGoalController achievementGoalController() {
        return new AchievementGoalController();
    }

    @Bean
    public OrgUnitController orgUnitController() {
        return new OrgUnitController();
    }

    @Bean
    public UnitController unitController() {
        return new UnitController();
    }

    @Bean
    public UserUnitController userUnitController() {
        return new UserUnitController();
    }

    @Bean
    public ClientHttpRequestInterceptor clientHttpRequestInterceptor() {
        return new ActionTrackInterceptor();
    }

    @Bean
    public ZuulFilter zuulFilter() {
        return new ZuulInterceptor();
    }

    @Bean
    public MaterialController materialController() {
        return new MaterialController();
    }

    @Bean
    public BomController bomController() {
        return new BomController();
    }

    @Bean
    public SpELController spELController() {
        return new SpELController();
    }

    @Bean
    public LaborCostController laborCostController() {
        return new LaborCostController();
    }

    @Bean
    public LaborCostRealMonthController laborCostRealMonthController() {
        return new LaborCostRealMonthController();
    }

    @Bean
    public TaxRateConfigurationController taxRateConfigurationController() {
        return new TaxRateConfigurationController();
    }

    @Bean
    public ProductController productController() {
        return new ProductController();
    }

    @Bean
    public AttributionUnitController attributionUnitController() {
        return new AttributionUnitController();
    }

    @Bean
    public ProductAttributeController productAttributeController() {
        return new ProductAttributeController();
    }

    @Bean
    public ProductOrganizationController productOrganizationController() {
        return new ProductOrganizationController();
    }

    @Bean
    public BudgetTreeController budgetTreeController() {
        return new BudgetTreeController();
    }

    @Bean
    public BudgetDepController budgetDepController() {
        return new BudgetDepController();
    }

    @Bean
    public FeeTypeController feeTypeController() {
        return new FeeTypeController();
    }

    @Bean
    public SystemParamController systemParamController() {
        return new SystemParamController();
    }

    @Bean
    public FileController fileController() {
        return new FileController();
    }

    @Bean
    public SystemController systemController() {
        return new SystemController();
    }

    @Bean
    public ProjectTypeController projectTypeController() {
        return new ProjectTypeController();
    }

    @Bean
    public CheckItemController checkItemController() {
        return new CheckItemController();
    }

    @Bean
    public BudgetItemController budgetItemController() {
        return new BudgetItemController();
    }

    @Bean
    public BusiSceneNonSaleController busiSceneNonSaleController() {
        return new BusiSceneNonSaleController();
    }

    @Bean
    public CnapsInfoController cnapsInfoController() {
        return new CnapsInfoController();
    }

    @Bean
    public CodeRuleController codeRuleController() {
        return new CodeRuleController();
    }

    @Bean
    public FeeItemController feeItemController() {
        return new FeeItemController();
    }

    @Bean
    public PlanController planController() {
        return new PlanController();
    }

    @Bean
    public BusinessFollowRecordController businessFollowRecordController() {
        return new BusinessFollowRecordController();
    }

    @Bean
    public WorkingHourController workingHourController() {
        return new WorkingHourController();
    }

    @Bean
    public WorkingHourDistributeController workingHourDistributeController() {
        return new WorkingHourDistributeController();
    }

    @Bean
    public MilepostTemplateController milepostTemplateController() {
        return new MilepostTemplateController();
    }

    @Bean
    public ProjectBusinessController projectBusinessController() {
        return new ProjectBusinessController();
    }

    @Bean
    public ProjectCheckController projectCheckController() {
        return new ProjectCheckController();
    }

    @Bean
    public QuotationController quotationController() {
        return new QuotationController();
    }

    @Bean
    public ReceiptClaimController receiptClaimController() {
        return new ReceiptClaimController();
    }

    @Bean
    public ProjectRoleController projectRoleController() {
        return new ProjectRoleController();
    }

    @Bean
    public WorkingHourRemindWhiteListController workingHourRemindWhiteListController() {
        return new WorkingHourRemindWhiteListController();
    }

    @Bean
    public MilepostDesignPlanController milepostDesignPlanDetailController() {
        return new MilepostDesignPlanController();
    }

    @Bean
    public ImportDataController importDataController() {
        return new ImportDataController();
    }

    @Bean
    public ProjectDeliveriesController projectDeliveriesController() {
        return new ProjectDeliveriesController();
    }

    @Bean
    public ProjectFundsDeployController projectFundsDeployController() {
        return new ProjectFundsDeployController();
    }

    @Bean
    public ProductTaxSettingController productTaxSettingController() {
        return new ProductTaxSettingController();
    }

    @Bean
    public ProjectCloseMilestoneController projectCloseMilestoneController() {
        return new ProjectCloseMilestoneController();
    }

    @Bean
    public UnitBudgetDepRelController unitBudgetDepRelController() {
        return new UnitBudgetDepRelController();
    }

    @Bean
    public TianYanChaController tianYanChaController() {
        return new TianYanChaController();
    }

    @Bean
    public ContractController contractController() {
        return new ContractController();
    }

    @Bean
    public ProjectCallBackController projectCallBackController() {
        return new ProjectCallBackController();
    }

    @Bean
    public InvoiceApplyController invoiceApplyController() {
        return new InvoiceApplyController();
    }

    @Bean
    public InvoiceReceivableController invoiceReceivableController() {
        return new InvoiceReceivableController();
    }

    @Bean
    public ReceivableTrxController receivableTrxController() {
        return new ReceivableTrxController();
    }

    @Bean
    public UserExtendAttrController userExtendAttrController() {
        return new UserExtendAttrController();
    }

    @Bean
    public MaterialCostController materialCostController() {
        return new MaterialCostController();
    }

    @Bean
    public WriteOffController writeOffController() {
        return new WriteOffController();
    }

    @Bean
    public LegalContractController legalContractController() {
        return new LegalContractController();
    }

    @Bean
    public ContractWorkflowCallbackController contractWorkflowCallbackController() {
        return new ContractWorkflowCallbackController();
    }

    @Bean
    public MaterialGetController materialGetController() {
        return new MaterialGetController();
    }

    @Bean
    public MaterialCostTransferController materialCostTransferController() {
        return new MaterialCostTransferController();
    }

    @Bean
    public MaterialReturnController materialReturnController() {
        return new MaterialReturnController();
    }

    @Bean
    public PurchaseMaterialReleaseDetailController purchaseMaterialReleaseDetailController() {
        return new PurchaseMaterialReleaseDetailController();
    }

    @Bean
    public PurchaseMaterialCloseDetailController purchaseMaterialCloseDetailController() {
        return new PurchaseMaterialCloseDetailController();
    }

    @Bean
    public PurchaseMaterialRequirementController purchaseMaterialRequirementController() {
        return new PurchaseMaterialRequirementController();
    }

    @Bean
    public OrganizationCustomDictController organizationCustomDictController() {
        return new OrganizationCustomDictController();
    }

    @Bean
    public MaterialCustomDictController materialCustomDictController() {
        return new MaterialCustomDictController();
    }

    @Bean
    public PurchaseOrderController purchaseOrderController() {
        return new PurchaseOrderController();
    }

    @Bean
    public RdmResourcePlanController rdmResourcePlanController() {
        return new RdmResourcePlanController();
    }

    @Bean
    public EamPurchaseInfoController eamPurchaseInfoController() {
        return new EamPurchaseInfoController();
    }

    @Bean
    public RdmSettlementSheetController rdmSettlementSheetController() {
        return new RdmSettlementSheetController();
    }

    @Bean
    public RdmWorkingHourController rdmWorkingHourController() {
        return new RdmWorkingHourController();
    }

    @Bean
    public StorageInventoryController storageInventoryController() {
        return new StorageInventoryController();
    }

    @Bean
    public StorageController storageController() {
        return new StorageController();
    }

    @Bean
    public MaterialTransferController materialTransferController() {
        return new MaterialTransferController();
    }

    @Bean
    public CustomerModifyHistoryController customerModifyHistoryController() {
        return new CustomerModifyHistoryController();
    }

    @Bean
    public CustomerBusinessModifyCallbackController customerBusinessModifyCallbackController() {
        return new CustomerBusinessModifyCallbackController();
    }

    @Bean
    public CustomerBaseInfoModifyCallbackController customerBaseInfoModifyCallbackController() {
        return new CustomerBaseInfoModifyCallbackController();
    }

    @Bean
    public CustomerModifyWfCallbackController customerModifyWfCallbackController() {
        return new CustomerModifyWfCallbackController();
    }

    @Bean
    public PurchaseContractController purchaseContractController() {
        return new PurchaseContractController();
    }

    @Bean
    public ProjectBaseInfoChangeCallBackController projectBaseInfoChangeCallBackController() {
        return new ProjectBaseInfoChangeCallBackController();
    }

    @Bean
    public ProjectBudgetTargetChangeCallBackController projectTargetCostChangeCallBackController() {
        return new ProjectBudgetTargetChangeCallBackController();
    }

    @Bean
    public ContractProductChanageCallBackController contractProductChanageCallBackController() {
        return new ContractProductChanageCallBackController();
    }

    @Bean
    public ContractReceiptPlanChanageCallBackController contractReceiptPlanChanageCallBackController() {
        return new ContractReceiptPlanChanageCallBackController();
    }

    @Bean
    public ProjectMilepostChangeCallBackController projectMilepostChangeCallBackController() {
        return new ProjectMilepostChangeCallBackController();
    }

    @Bean
    public ProjectPreviewCallBackController projectPreviewCallBackController() {
        return new ProjectPreviewCallBackController();
    }

    @Bean
    public CostCollectionController costCollectionController() {
        return new CostCollectionController();
    }

    @Bean
    public CarryoverBillController carryoverBillController() {
        return new CarryoverBillController();
    }

    @Bean
    public PurchaseContractWorkflowCallbackController purchaseContractWorkflowCallbackController() {
        return new PurchaseContractWorkflowCallbackController();
    }

    @Bean
    public PurchaseContractInfoChangeCallBackController purchaseContractInfoChangeCallBackController() {
        return new PurchaseContractInfoChangeCallBackController();
    }

    @Bean
    public PurchaseContractDetailChangeCallBackController purchaseContractDetailChangeCallBackController() {
        return new PurchaseContractDetailChangeCallBackController();
    }

    @Bean
    public PurchaseContractPlanChangeCallBackController purchaseContractPlanChangeCallBackController() {
        return new PurchaseContractPlanChangeCallBackController();
    }

    @Bean
    public PurchaseBpaPriceController purchaseBpaPriceController() {
        return new PurchaseBpaPriceController();
    }

    @Bean
    public ProjectBudgetCostController ProjectBudgetCostController() {
        return new ProjectBudgetCostController();
    }

    @Bean
    public ProjectBudgetMaterialController projectBudgetMaterialController() {
        return new ProjectBudgetMaterialController();
    }

    @Bean
    public CarryoverBillAccountingController carryoverBillAccountingController() {
        return new CarryoverBillAccountingController();
    }

    @Bean
    public MilepostDesignPlanChangeController milepostDesignPlanChangeController() {
        return new MilepostDesignPlanChangeController();
    }

    @Bean
    public RevenueCostOrderController revenueCostOrderController() {
        return new RevenueCostOrderController();
    }

    @Bean
    public ProjectBudgetChangeWorkflowCallbackController projectBudgetChangeWorkflowCallbackController() {
        return new ProjectBudgetChangeWorkflowCallbackController();
    }

    @Bean
    public WorkingHourAccountingController workingHourAccountingController() {
        return new WorkingHourAccountingController();
    }

    @Bean
    public WorkingHourCostChangeHeaderController workingHourCostChangeHeaderController() {
        return new WorkingHourCostChangeHeaderController();
    }

    @Bean
    public RoleOuRelController roleOuRelController() {
        return new RoleOuRelController();
    }

    @Bean
    public AsyncRequestResultController asyncRequestResultController() {
        return new AsyncRequestResultController();
    }

    @Bean
    public CoaSubjectController coaSubjectController() {
        return new CoaSubjectController();
    }

    @Bean
    public MaterialOutsourcingContractConfigController materialOutsourcingContractConfigController() {
        return new MaterialOutsourcingContractConfigController();
    }

    @Bean
    public PaymentPlanController paymentPlanController() {
        return new PaymentPlanController();
    }

    @Bean
    public PaymentRecordController paymentRecordController() {
        return new PaymentRecordController();
    }

    @Bean
    public PaymentWriteOffRecordController paymentWriteOffRecordController() {
        return new PaymentWriteOffRecordController();
    }

    @Bean
    public BaseDataOperationController baseDataOperationController() {
        return new BaseDataOperationController();
    }

    @Bean
    public PaymentApplyController paymentApplyController() {
        return new PaymentApplyController();
    }

    @Bean
    public MobileAppController mobileAppController() {
        return new MobileAppController();
    }

    @Bean
    public PaymentApplyWorkflowCallbackController paymentApplyWorkflowCallbackController() {
        return new PaymentApplyWorkflowCallbackController();
    }

    @Bean
    public PaymentInvoiceDetailController paymentInvoiceDetailController() {
        return new PaymentInvoiceDetailController();
    }

    @Bean
    public PaymentInvoiceController paymentInvoiceController() {
        return new PaymentInvoiceController();
    }

    @Bean
    public OperatingUnitController operatingUnitController() {
        return new OperatingUnitController();
    }

    @Bean
    public VendorSiteBankController vendorSiteBankController() {
        return new VendorSiteBankController();
    }

    @Bean
    public ResendExecuteController resendExecuteController() {
        return new ResendExecuteController();
    }

    @Bean
    public ErpCodeRuleController erpCodeRuleController() {
        return new ErpCodeRuleController();
    }

    @Bean
    public EsbResultReturnController esbResultReturnController() {
        return new EsbResultReturnController();
    }

    @Bean
    public IhrAttendDetailController ihrAttendDetailController() {
        return new IhrAttendDetailController();
    }

    @Bean
    public HroWorkingHourController hroWorkingHourController() {
        return new HroWorkingHourController();
    }

    @Bean
    public HroWorkingHourBillController hroWorkingHourBillController() {
        return new HroWorkingHourBillController();
    }

    @Bean
    public PreProjectCallBackController preProjectCallBackController() {
        return new PreProjectCallBackController();
    }

    @Bean
    public DifferenceShareAccountController differenceShareAccountController() {
        return new DifferenceShareAccountController();
    }

    @Bean
    public DifferenceShareController differenceShareController() {
        return new DifferenceShareController();
    }

    @Bean
    public AnnouncementController announcementController() {
        return new AnnouncementController();
    }

    @Bean
    public AreaUnitRelController areaUnitRelController() {
        return new AreaUnitRelController();
    }

    @Bean
    public ProvinceAreaRelController provinceAreaRelController() {
        return new ProvinceAreaRelController();
    }

    @Bean
    public ProjectCostController projectCostController() {
        return new ProjectCostController();
    }

    @Bean
    public ProjectWbsCostController projectWbsCostController() {
        return new ProjectWbsCostController();
    }

    @Bean
    public PortalTargetController portalTargetController() {
        return new PortalTargetController();
    }

    @Bean
    public RolePortalTargetRelController rolePortalTargetRelController() {
        return new RolePortalTargetRelController();
    }

    @Bean
    public PersonalPortalController personalPortalController() {
        return new PersonalPortalController();
    }

    @Bean
    public ContractStatisticsController contractStatisticsController() {
        return new ContractStatisticsController();
    }

    @Bean
    public PurchaseStatisticsController purchaseStatisticsController() {
        return new PurchaseStatisticsController();
    }

    @Bean
    public MaterialCostStatisticsController materialCostStatisticsController() {
        return new MaterialCostStatisticsController();
    }

    @Bean
    public PurchaseMaterialRequirementStatisticsController purchaseMaterialRequirementStatisticsController() {
        return new PurchaseMaterialRequirementStatisticsController();
    }

    @Bean
    public WorkingHourAccountingStaController workingHourAccountingStaController() {
        return new WorkingHourAccountingStaController();
    }

    @Bean
    public PurchaseOrderStatisticsController purchaseOrderStatisticsController() {
        return new PurchaseOrderStatisticsController();
    }

    @Bean
    public WorkingHourAccountingStatisticsController workingHourAccountingStatisticsController() {
        return new WorkingHourAccountingStatisticsController();
    }

    @Bean
    public WorkingHourExceptionStatisticsController workingHourExceptionStatisticsController() {
        return new WorkingHourExceptionStatisticsController();
    }

    @Bean
    public NoticeController noticeController() {
        return new NoticeController();
    }

    @Bean
    public SystemSetController systemSetController() {
        return new SystemSetController();
    }

    @Bean
    public ReSubmitAspect reSubmitAspect() {
        return new ReSubmitAspect();
    }

    @Bean
    public OrgLaborCostTypeSetController orgLaborCostTypeSetController() {
        return new OrgLaborCostTypeSetController();
    }

    @Bean
    public NotRemindRecordController notRemindRecordController() {
        return new NotRemindRecordController();
    }

    @Bean
    public ProjectTerminationWorkflowCallbackController projectTerminationWorkflowCallbackController() {
        return new ProjectTerminationWorkflowCallbackController();
    }

    @Bean
    public EmployeeInfoController employeeInfoController() {
        return new EmployeeInfoController();
    }

    @Bean
    public InvoicePlanStaController invoicePlanStaController() {
        return new InvoicePlanStaController();
    }

    @Bean
    public DepartmentUnitIncomingTargetController departmentUnitIncomingTargetController() {
        return new DepartmentUnitIncomingTargetController();
    }

    @Bean
    public BudgetItemStatisticsController budgetItemStatisticsController() {
        return new BudgetItemStatisticsController();
    }

    @Bean
    public RefundApplyController refundApplyController() {
        return new RefundApplyController();
    }

    @Bean
    public ReceiptPlanController receiptPlanController() {
        return new ReceiptPlanController();
    }

    @Bean
    public QuotationStatisticsController quotationStatisticsController() {
        return new QuotationStatisticsController();
    }

    @Bean
    public IncomeCalculateProductTaskController incomeCalculateProductTaskController() {
        return new IncomeCalculateProductTaskController();
    }

    @Bean
    public ContractModuleController contractModuleController() {
        return new ContractModuleController();
    }

    @Bean
    public IncomeCalculateController incomeCalculateController() {
        return new IncomeCalculateController();
    }

    @Bean
    public IncomeCalculateProjectTaskController incomeCalculateProjectTaskController() {
        return new IncomeCalculateProjectTaskController();
    }

    @Bean
    public SealAdministratorController sealAdministratorController() {
        return new SealAdministratorController();
    }

    @Bean
    public GlegalWorkflowController glegalWorkflowController() {
        return new GlegalWorkflowController();
    }

    @Bean
    public GlegalContractWorkflowCallbackController glegalContractWorkflowCallbackController() {
        return new GlegalContractWorkflowCallbackController();
    }

    @Bean
    public GlegalPurchaseContractWorkflowCallbackController glegalPurchaseContractWorkflowCallbackController() {
        return new GlegalPurchaseContractWorkflowCallbackController();
    }

    @Bean
    public HroRequirementController hroRequirementController() {
        return new HroRequirementController();
    }

    @Bean
    public ProjectBaseInfoBatchChangeCallBackController projectBaseInfoBatchChangeCallBackController() {
        return new ProjectBaseInfoBatchChangeCallBackController();
    }

    @Bean
    public ProjectAwardDeductionController projectAwardDeductionController() {
        return new ProjectAwardDeductionController();
    }

    @Bean
    public ProjectAwardController projectAwardController() {
        return new ProjectAwardController();
    }

    @Bean
    public ProjectGanttChartController projectGanttChartController() {
        return new ProjectGanttChartController();
    }

    @Bean
    public TargetDescriptionController targetDescriptionController() {
        return new TargetDescriptionController();
    }

    @Bean
    public FormConfigController formConfigController() {
        return new FormConfigController();
    }

    @Bean
    public ApplicationIndustryController applicationIndustryController() {
        return new ApplicationIndustryController();
    }

    @Bean
    public SdpBuyersController sdpBuyersController() {
        return new SdpBuyersController();
    }

    @Bean
    public BusinessRiskStrategyWorkflowController businessRiskStrategyWorkflowController() {
        return new BusinessRiskStrategyWorkflowController();
    }

    @Bean
    public CacheDataOuUnitExtController cacheDataOuUnitExtController() {
        return new CacheDataOuUnitExtController();
    }

    @Bean
    public InnerSwapApplyController innerSwapApplyController() {
        return new InnerSwapApplyController();
    }

    @Bean
    public ProjectAwardStatisticsController projectAwardStatisticsController() {
        return new ProjectAwardStatisticsController();
    }

    @Bean
    public ProjectRiskSettingController projectRiskSettingController() {
        return new ProjectRiskSettingController();
    }

    @Bean
    public SwapExecuteController swapExecuteController() {
        return new SwapExecuteController();
    }

    @Bean
    public CostCoefficientController costCoefficientController() {
        return new CostCoefficientController();
    }

    @Bean
    public LaborExternalCostController laborExternalCostController() {
        return new LaborExternalCostController();
    }

    @Bean
    public CodeRuleStatisticsController codeRuleStatisticsController() {
        return new CodeRuleStatisticsController();
    }

    @Bean
    public MaterialAttributeController materialAttributeController() {
        return new MaterialAttributeController();
    }

    @Bean
    public MaterialAdjustController materialAdjustController() {
        return new MaterialAdjustController();
    }

    @Bean
    public MilepostDesignMemberController mileposDesignMemberController() {
        return new MilepostDesignMemberController();
    }

    @Bean
    public ProjectMilepostDesignStatisticsController projectMilepostDesignStatisticsController() {
        return new ProjectMilepostDesignStatisticsController();
    }

    @Bean
    public TicketTasksController ticketTasksController() {
        return new TicketTasksController();
    }

    @Bean
    public MaterialChangeTypeController materialChangeTypeController() {
        return new MaterialChangeTypeController();
    }

    @Bean
    public TicketWorkingHourImportController ticketWorkingHourImportController() {
        return new TicketWorkingHourImportController();
    }

    @Bean
    public CloudPrintController cloudPrintController() {
        return new CloudPrintController();
    }

    @Bean
    public MRPController mRPController() {
        return new MRPController();
    }

    @Bean
    public PurchaseContractBudgetController purchaseContractBudgetController() {
        return new PurchaseContractBudgetController();
    }

    @Bean
    public ProjectProblemController projectProblemController() {
        return new ProjectProblemController();
    }

    @Bean
    public DocumentLibraryController documentLibraryController() {
        return new DocumentLibraryController();
    }

    @Bean
    public ProjectProblemStatisticsController projectProblemStatisticsController() {
        return new ProjectProblemStatisticsController();
    }

    @Bean
    public ProjectProblemCommentController projectProblemCommentController() {
        return new ProjectProblemCommentController();
    }

    @Bean
    public ProjectProblemOperationRecordController projectProblemOperationRecordController() {
        return new ProjectProblemOperationRecordController();
    }

    @Bean
    public CollectionConfigurationController collectionConfigurationController() {
        return new CollectionConfigurationController();
    }

    @Bean
    public ProjectProductMaintenanceController projectProductMaintenanceController() {
        return new ProjectProductMaintenanceController();
    }

    @Bean
    public UserCollectController userCollectController() {
        return new UserCollectController();
    }

    @Bean
    public BusinessDocumentLibraryController businessDocumentLibraryController() {
        return new BusinessDocumentLibraryController();
    }

    @Bean
    public BusinessQuotationFileWorkflowController businessQuotationFileWorkflowController() {
        return new BusinessQuotationFileWorkflowController();
    }

    @Bean
    public BrandMaintenanceController brandMaintenanceController() {
        return new BrandMaintenanceController();
    }

    @Bean
    public BrandMaintenanceIntermediateController brandMaintenanceIntermediateController() {
        return new BrandMaintenanceIntermediateController();
    }

    @Bean
    public MilepostDesignPlanImportController milepostDesignPlanImportController() {
        return new MilepostDesignPlanImportController();
    }

    @Bean
    public ContractClassificationController contractClassificationController() {
        return new ContractClassificationController();
    }

    @Bean
    public ProjectTransferQualityController projectTransferQualityController() {
        return new ProjectTransferQualityController();
    }

    @Bean
    public ProjectTransferQualityCallBackController projectTransferQualityCallBackController() {
        return new ProjectTransferQualityCallBackController();
    }

    @Bean
    public ProjectReopenController projectReopenController() {
        return new ProjectReopenController();
    }

    @Bean
    public ProjectReopenCallBackController projectReopenCallBackController() {
        return new ProjectReopenCallBackController();
    }

    @Bean
    public InvoicePlanController invoicePlanController() {
        return new InvoicePlanController();
    }

    @Bean
    public VendorPenaltyConfigController vendorPenaltyConfigController() {
        return new VendorPenaltyConfigController();
    }

    @Bean
    public VendorPenaltyController vendorPenaltyController() {
        return new VendorPenaltyController();
    }

    @Bean
    public WbsTemplateInfoController wbsTemplateInfoController() {
        return new WbsTemplateInfoController();
    }

    @Bean
    public WbsTemplateRuleDetailController wbsTemplateRuleDetailController() {
        return new WbsTemplateRuleDetailController();
    }

    @Bean
    public ProjectActivityController projectActivityController() {
        return new ProjectActivityController();
    }

    @Bean
    public TestApolloController testApolloController() {
        return new TestApolloController();
    }

    @Bean
    public WbsTemplateRuleController wbsTemplateRuleController() {
        return new WbsTemplateRuleController();
    }

    @Bean
    public ProjectWbsBudgetController projectWbsBudgetController() {
        return new ProjectWbsBudgetController();
    }

    @Bean
    public ProjectWbsBudgetSummaryController projectWbsBudgetSummaryController() {
        return new ProjectWbsBudgetSummaryController();
    }

    @Bean
    public ProjectWbsBudgetBaselineChangeHistoryController projectWbsBudgetBaselineChangeHistoryController() {
        return new ProjectWbsBudgetBaselineChangeHistoryController();
    }

    @Bean
    public ProjectWbsReceiptsStatisticsController projectWbsReceiptsStatisticsController() {
        return new ProjectWbsReceiptsStatisticsController();
    }

    @Bean
    public ProjectWbsReceiptsWorkflowCallBackController projectWbsReceiptsWorkflowCallBackController() {
        return new ProjectWbsReceiptsWorkflowCallBackController();
    }

    @Bean
    public ProjectWbsBudgetChangeWorkflowCallbackController projectWbsBudgetChangeWorkflowCallbackController() {
        return new ProjectWbsBudgetChangeWorkflowCallbackController();
    }

    @Bean
    public ProjectWbsReceiptsController projectWbsReceiptsController() {
        return new ProjectWbsReceiptsController();
    }

    @Bean
    public PurchaseContractProgressWorkflowCallBackController purchaseContractProgressWorkflowCallBackController() {
        return new PurchaseContractProgressWorkflowCallBackController();
    }

    @Bean
    public ProjectWbsSubmitReceiptsWorkflowCallBackController projectWbsSubmitReceiptsWorkflowCallBackController() {
        return new ProjectWbsSubmitReceiptsWorkflowCallBackController();
    }

    @Bean
    public MilepostDesignPlanChangeWorkflowCallbackController milepostDesignPlanChangeWorkflowCallbackController() {
        return new MilepostDesignPlanChangeWorkflowCallbackController();
    }

    @Bean
    public PurchaseOrderCallBackController purchaseOrderCallBackController() {
        return new PurchaseOrderCallBackController();
    }

    @Bean
    public PurchaseOrderChangeCallBackController purchaseOrderChangeCallBackController() {
        return new PurchaseOrderChangeCallBackController();
    }

    @Bean
    public WbsConstraintController wbsConstraintController() {
        return new WbsConstraintController();
    }

    @Bean
    public ProjectWbsBaselineController projectWbsBaselineController() {
        return new ProjectWbsBaselineController();
    }

    @Bean
    public ProjectWbsBudgetChangeHistoryController projectWbsBudgetChangeHistoryController() {
        return new ProjectWbsBudgetChangeHistoryController();
    }

    @Bean
    public ProjectWbsBudgetSummaryChangeHistoryController projectWbsBudgetSummaryChangeHistoryController() {
        return new ProjectWbsBudgetSummaryChangeHistoryController();
    }

    @Bean
    public CustomerTransferStatisticsController customerTransferStatisticsController() {
        return new CustomerTransferStatisticsController();
    }

    @Bean
    public CustomerTransferController customerTransferController() {
        return new CustomerTransferController();
    }

    @Bean
    public ProjectBaselineBatchController projectBaselineBatchController() {
        return new ProjectBaselineBatchController();
    }

    @Bean
    public CustomerTransferWorkflowCallBackController customerTransferWorkflowCallBackController() {
        return new CustomerTransferWorkflowCallBackController();
    }

    @Bean
    public CustomerTransferReverseWorkflowCallbackController customerTransferReverseWorkflowCallbackController() {
        return new CustomerTransferReverseWorkflowCallbackController();
    }

    @Bean
    public ProjectMilepostController projectMilepostController() {
        return new ProjectMilepostController();
    }

    @Bean
    public ProjectMilepostChangeHistoryController projectMilepostChangeHistoryController() {
        return new ProjectMilepostChangeHistoryController();
    }

    @Bean
    public SupplierQualityDeactivationController supplierQualityDeactivationController() {
        return new SupplierQualityDeactivationController();
    }

    @Bean
    public ProjectContractChangeCallBackController projectContractChangeCallBackController() {
        return new ProjectContractChangeCallBackController();
    }

    @Bean
    public PurchaseOrderChangeController purchaseOrderChangeController() {
        return new PurchaseOrderChangeController();
    }

    @Bean
    public ProjectBaselineBatchContractRsController projectBaselineBatchContractRsController() {
        return new ProjectBaselineBatchContractRsController();
    }

    @Bean
    public BasicCacheController basicCacheController() {
        return new BasicCacheController();
    }

    @Bean
    public WorkFlowDraftSubmitTemporaryRecordController workFlowDraftSubmitTemporaryRecordController() {
        return new WorkFlowDraftSubmitTemporaryRecordController();
    }

    @Bean
    public TestSystemController testSystemController() {
        return new TestSystemController();
    }

    @Bean
    public HroRoleController hroRoleController() {
        return new HroRoleController();
    }

    @Bean
    public DmFactController dmFactController() {
        return new DmFactController();
    }

    @Bean
    public MediaOcrController mediaOcrController() {
        return new MediaOcrController();
    }

    @Bean
    public ProjectExtraInfoStatisticsController projectExtraInfoController() {
        return new ProjectExtraInfoStatisticsController();
    }

    @Bean
    public ProjectCustomerSatisfactionController projectCustomerSatisfactionController() {
        return new ProjectCustomerSatisfactionController();
    }

    @Bean
    public ProjectSecurityIncidentController projectSecurityIncidentController() {
        return new ProjectSecurityIncidentController();
    }

    @Bean
    public ProjectCustomerSatisfactionStatisticsController projectCustomerSatisfactionStatisticsController() {
        return new ProjectCustomerSatisfactionStatisticsController();
    }

    @Bean
    public ProjectSecurityIncidentStatisticsController projectSecurityIncidentStatisticsController() {
        return new ProjectSecurityIncidentStatisticsController();
    }

    @Bean
    public AgingSegmentController agingSegmentController() {
        return new AgingSegmentController();
    }

    @Bean
    public MipCallbackLogController mipCallbackLogController() {
        return new MipCallbackLogController();
    }

    @Bean
    public MaterialDelistController materialDelistController() {
        return new MaterialDelistController();
    }

    @Bean
    public MaterialPriceConfigController materialPriceConfigController() {
        return new MaterialPriceConfigController();
    }

    @Bean
    public MaterialPriceController materialPriceController() {
        return new MaterialPriceController();
    }

    @Bean
    public MaterialPriceReceiptController materialPriceReceiptController() {
        return new MaterialPriceReceiptController();
    }

    @Bean
    public MilepostDesignPlanNotPublishRequirementController milepostDesignPlanNotPublishRequirementController() {
        return new MilepostDesignPlanNotPublishRequirementController();
    }

    @Bean
    public ReceiptWorkOrderController receiptWorkOrderController() {
        return new ReceiptWorkOrderController();
    }

    @Bean
    public VendorProportionStatisticsController vendorProportionStatisticsController() {
        return new VendorProportionStatisticsController();
    }

    @Bean
    public FormInstanceController formInstanceController() {
        return new FormInstanceController();
    }

    @Bean
    public RocketMQMsgConsumerRecordController rocketMQMsgConsumerRecordController() {
        return new RocketMQMsgConsumerRecordController();
    }

    @Bean
    public DeadLetterNewController deadLetterNewController() {
        return new DeadLetterNewController();
    }

    @Bean
    public FormTemplateController formTemplateController() {
        return new FormTemplateController();
    }

    @Bean
    public MilepostDesignPlanDetailChangeRecordController milepostDesignPlanDetailChangeRecordController() {
        return new MilepostDesignPlanDetailChangeRecordController();
    }

    @Bean
    public ProjectWbsReceiptsRequirementChangeRecordController projectWbsReceiptsRequirementChangeRecordController() {
        return new ProjectWbsReceiptsRequirementChangeRecordController();
    }

    @Bean
    public ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackController projectWbsReceiptsRequirementChangeRecordWorkflowCallbackController() {
        return new ProjectWbsReceiptsRequirementChangeRecordWorkflowCallbackController();
    }

    @Bean
    public SdpController sdpController() {
        return new SdpController();
    }

    @Bean
    public CheckConfigController checkConfigController() {
        return new CheckConfigController();
    }

    @Bean
    public CheckResultController checkResultController() {
        return new CheckResultController();
    }

    @Bean
    public ContractTerminationCallbackController contractTerminationCallbackController() {
        return new ContractTerminationCallbackController();
    }

    @Bean
    public PurchaseContractPunishmentController purchaseContractPunishmentController() {
        return new PurchaseContractPunishmentController();
    }

    @Bean
    public PurchaseContractPunishmentConfigController purchaseContractPunishmentConfigController() {
        return new PurchaseContractPunishmentConfigController();
    }

    @Bean
    public PurchaseContractPunishmentWorkflowCallbackController purchaseContractPunishmentWorkflowCallbackController() {
        return new PurchaseContractPunishmentWorkflowCallbackController();
    }

    @Bean
    public GlegalContractChangeController glegalContractChangeController() {
        return new GlegalContractChangeController();
    }

    @Bean
    public ContractDrafterChangeCallBackController contractDrafterChangeCallBackController() {
        return new ContractDrafterChangeCallBackController();
    }

    @Bean
    public ExchangeAccountController exchangeAccountController() {
        return new ExchangeAccountController();
    }

    @Bean
    public ProjectAssetChangeCallBackController projectAssetChangeCallBackController() {
        return new ProjectAssetChangeCallBackController();
    }

    @Bean
    public AssetDeprnAccountingController assetDeprnAccountingController() {
        return new AssetDeprnAccountingController();
    }

    @Bean
    public ProjectWbsBaselineChangeCallBackController projectWbsBaselineChangeCallBackController() {
        return new ProjectWbsBaselineChangeCallBackController();
    }


    @Bean
    public SdpLogController sdpLogController() {
        return new SdpLogController();
    }

    @Bean
    public GSCInvoiceController gscInvoiceController(){
        return new GSCInvoiceController();
    }

    @Bean
    public GSCInvoiceIspWorkflowCallbackController gscInvoiceIspWorkflowCallbackController(){
        return new GSCInvoiceIspWorkflowCallbackController();
    }
    @Bean
    public GscPaymentInvoiceStatisticsController gscPaymentInvoiceStatisticsController(){
        return new GscPaymentInvoiceStatisticsController();
    }

    @Bean
    public GSCInvoiceDetailWorkflowCallbackController gscInvoiceDetailWorkflowCallbackController(){
        return new GSCInvoiceDetailWorkflowCallbackController();
    }
    @Bean
    public InvoiceReceivableReverseCallBackController invoiceReceivableReverseCallBackController() {
        return new InvoiceReceivableReverseCallBackController();
    }

    @Bean
    public DeliveryAddressController deliveryAddressController() {
        return new DeliveryAddressController();
    }

    @Bean
    public PurchaseMaterialRequirementDeliveryAddressHistoryController purchaseMaterialRequirementDeliveryAddressHistoryController() {
        return new PurchaseMaterialRequirementDeliveryAddressHistoryController();
    }

    @Bean
    public PurchaseOrderDetailDeliveryAddressHistoryController purchaseOrderDetailDeliveryAddressHistoryController() {
        return new PurchaseOrderDetailDeliveryAddressHistoryController();
    }
    @Bean
    public MaterialGetReturnOutsideController materialGetReturnOutsideController() {
        return new MaterialGetReturnOutsideController();
    }

    @Bean
    public IFlowExceptionReBuildController iFlowExceptionReBuildController() {
        return new IFlowExceptionReBuildController();
    }

    @Bean
    public TemplateController templateController() {
        return new TemplateController();
    }

    @Bean
    public StandardTermsController standardTermsController() {
        return new StandardTermsController();
    }

    @Bean
    public StandardTermsCallbackController standardTermsCallbackController() {
        return new StandardTermsCallbackController();
    }
}
