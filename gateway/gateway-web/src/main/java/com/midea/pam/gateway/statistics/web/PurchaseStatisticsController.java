package com.midea.pam.gateway.statistics.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.midea.mcomponent.core.exception.MipException;
import com.midea.pam.common.base.DataResponse;
import com.midea.pam.common.base.Response;
import com.midea.pam.common.basedata.dto.CustomViewConfigDTO;
import com.midea.pam.common.constants.Constants;
import com.midea.pam.common.ctc.excelVo.PaymentPlanExportExcelVo;
import com.midea.pam.common.ctc.vo.PurchaseContractDetailExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractProgressExcelVO;
import com.midea.pam.common.ctc.vo.PurchaseContractVO;
import com.midea.pam.common.ctc.vo.PurchaseContractWbsExcelVO;
import com.midea.pam.common.enums.ModelsEnum;
import com.midea.pam.common.util.DateUtils;
import com.midea.pam.common.util.ExportExcelUtil;
import com.midea.pam.common.util.StringUtils;
import com.midea.pam.gateway.common.base.ControllerHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("采购合同")
@RestController
@RequestMapping("statistics/PurchaseContract")
public class PurchaseStatisticsController extends ControllerHelper {

    @Resource
    private RestTemplate restTemplate;


    @ApiOperation(value = "采购合同导出")
    @GetMapping("excel/export")
    public void listContracts(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                              @RequestParam(required = false) @ApiParam("合同类型，多个逗号分隔") final String typeNameStr,
                              @RequestParam(required = false) @ApiParam("合同名称") final String name,
                              @RequestParam(required = false) @ApiParam("供应商名称") final String vendorName,
                              @RequestParam(required = false) @ApiParam("供应商编码") final String vendorCode,
                              @RequestParam(required = false) @ApiParam("采购跟进人") final String purchasingFollowerName,
                              @RequestParam(required = false) @ApiParam("关联项目") final String projectName,
                              @RequestParam(required = false) @ApiParam("关联项目编号") final String projectCode,
                              @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                              @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                              @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                              @RequestParam(required = false) @ApiParam("业务实体(多选)") final String manyOuName,
                              @RequestParam(required = false) @ApiParam("法务合同编号") final String legalContractNum,
                              @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                              @RequestParam(required = false) @ApiParam("法务合同状态(多选)") final String contractStatusStr,
                              @RequestParam(required = false) @ApiParam("创建开始日期") final String createStartDate,
                              @RequestParam(required = false) @ApiParam("创建结束日期") final String createEndDate,
                              @RequestParam(required = false) @ApiParam("承诺交期开始日期") final String deliveryStartDate,
                              @RequestParam(required = false) @ApiParam("承诺交期结束日期") final String deliveryEndDate,
                              @RequestParam(required = false) @ApiParam("追踪货期开始日期") final String trackLeadStartTime,
                              @RequestParam(required = false) @ApiParam("追踪货期结束日期") final String trackLeadEndTime,
                              @RequestParam(required = false) @ApiParam("货期跟进备注") final String trackLeadTimeRemark,
                              @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                              @RequestParam(required = false, defaultValue = "false") @ApiParam("是否wbs类型") final Boolean wbsEnabled,
                              @RequestParam(required = false) @ApiParam("是否已双签") final Boolean isGleSign,
                              HttpServletResponse response) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("typeNameStr", typeNameStr);
        param.put("name", name);
        param.put("vendorName", vendorName);
        param.put("vendorCode", vendorCode);
        param.put("purchasingFollowerName", purchasingFollowerName);
        param.put("projectName", projectName);
        param.put("projectCode", projectCode);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", manyOuName);
        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("legalContractNum", legalContractNum);
        param.put("contractStatusStr", contractStatusStr);
        param.put("createStartDate", createStartDate);
        param.put("createEndDate", createEndDate);
        param.put("deliveryStartDate", deliveryStartDate);
        param.put("deliveryEndDate", deliveryEndDate);
        param.put("trackLeadStartTime", trackLeadStartTime);
        param.put("trackLeadEndTime", trackLeadEndTime);
        param.put("trackLeadTimeRemark", trackLeadTimeRemark);
        param.put("isGleSign", isGleSign);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "purchaseContract/exportPurchaseContract", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        // 导出excel
        exportExcel(dataResponse, wbsEnabled, false, response);
    }

    /**
     * 合同列表导出
     *
     * @param dataResponse 数据源
     * @param wbsEnabled   是否WBS
     * @param isMe         true：我的采购合同  false：采购合同
     * @param response
     */
    private void exportExcel(DataResponse<Map<String, Object>> dataResponse, Boolean wbsEnabled, Boolean isMe, HttpServletResponse response) {
        Map<String, Object> resultMap = dataResponse.getData();
        JSONArray purchaseContractArr = (JSONArray) resultMap.get("purchaseContractVOList");
        JSONArray purchaseDetailArr = (JSONArray) resultMap.get("purchaseContractDetailList");
        JSONArray paymentPlanArr = (JSONArray) resultMap.get("paymentPlanDTOList");
        JSONArray purchaseContractProgressArr = (JSONArray) resultMap.get("purchaseContractProgressList");
        int size = purchaseDetailArr != null ? purchaseDetailArr.size() : 0;
        size = size + (paymentPlanArr != null ? paymentPlanArr.size() : 0);
        size = size + (purchaseContractProgressArr != null ? purchaseContractProgressArr.size() : 0);
        if (size >= 50000) {
            throw new MipException("明细数据超过5万行，请调整搜索条件分多次导出");
        }

        List<PurchaseContractExcelVO> purchaseContractExcelVOList = new ArrayList<>();
        List<PurchaseContractWbsExcelVO> purchaseContractWbsExcelVOList = new ArrayList<>();
        List<PurchaseContractDetailExcelVO> purchaseContractDetailExcelVOList = new ArrayList<>();
        List<PaymentPlanExportExcelVo> paymentPlanExportExcelVoList = new ArrayList<>();
        List<PurchaseContractProgressExcelVO> purchaseContractProgressExcelVOList = new ArrayList<>();

        if (purchaseContractArr != null) {
            if (wbsEnabled) {
                purchaseContractWbsExcelVOList = JSONObject.parseArray(purchaseContractArr.toJSONString(), PurchaseContractWbsExcelVO.class);
                for (int i = 0; i < purchaseContractWbsExcelVOList.size(); i++) {
                    PurchaseContractWbsExcelVO excel = purchaseContractWbsExcelVOList.get(i);
                    excel.setNumb(i + 1);
                    if (excel.getAmount() != null) {
                        excel.setAmount(excel.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    if (excel.getExcludingTaxAmount() != null) {
                        excel.setExcludingTaxAmount(excel.getExcludingTaxAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    if (excel.getExecuteAmountTotal() != null) {
                        excel.setExecuteAmountTotal(excel.getExecuteAmountTotal().setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    if (excel.getQualityReportNumber() > 0) {
                        excel.setQualityReportComplete("是");
                    } else {
                        excel.setQualityReportComplete("否");
                    }
                }
            } else {
                purchaseContractExcelVOList = JSONObject.parseArray(purchaseContractArr.toJSONString(), PurchaseContractExcelVO.class);
                for (int i = 0; i < purchaseContractExcelVOList.size(); i++) {
                    PurchaseContractExcelVO excel = purchaseContractExcelVOList.get(i);
                    excel.setNumb(i + 1);
                    if (excel.getAmount() != null) {
                        excel.setAmount(excel.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    if (excel.getExcludingTaxAmount() != null) {
                        excel.setExcludingTaxAmount(excel.getExcludingTaxAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
                        excel.setExcludingTaxTotalBudgetAmountOriginal(excel.getExcludingTaxAmount()); //预算占用总额（不含税）-原币取合同不含税金额
                    }
                    if (excel.getExecuteAmountTotal() != null) {
                        excel.setExecuteAmountTotal(excel.getExecuteAmountTotal().setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    if (excel.getExcludingTaxTotalBudgetAmount() != null) {
                        excel.setExcludingTaxTotalBudgetAmount(excel.getExcludingTaxTotalBudgetAmount().setScale(2, RoundingMode.HALF_UP));
                    }
                }
            }
        }
        if (purchaseDetailArr != null) {
            purchaseContractDetailExcelVOList = JSONObject.parseArray(purchaseDetailArr.toString(), PurchaseContractDetailExcelVO.class);
            for (int i = 0; i < purchaseContractDetailExcelVOList.size(); i++) {
                PurchaseContractDetailExcelVO purchaseContractDetailExcelVO = purchaseContractDetailExcelVOList.get(i);
                purchaseContractDetailExcelVO.setNumb(i + 1);
            }
        }

        if (paymentPlanArr != null) {
            paymentPlanExportExcelVoList = JSON.parseArray(paymentPlanArr.toString(), PaymentPlanExportExcelVo.class);
            for (int i = 0; i < paymentPlanExportExcelVoList.size(); i++) {
                PaymentPlanExportExcelVo paymentPlanExportExcelVo = paymentPlanExportExcelVoList.get(i);
                paymentPlanExportExcelVo.setNumb(i + 1);
            }
        }

        if (purchaseContractProgressArr != null) {
            purchaseContractProgressExcelVOList = JSON.parseArray(purchaseContractProgressArr.toString(), PurchaseContractProgressExcelVO.class);
            for (int i = 0; i < purchaseContractProgressExcelVOList.size(); i++) {
                PurchaseContractProgressExcelVO purchaseContractProgressExcelVO = purchaseContractProgressExcelVOList.get(i);
                purchaseContractProgressExcelVO.setNumb(i + 1);
            }
        }

        Workbook workbook;
        if (wbsEnabled) {
            workbook = ExportExcelUtil.buildDefaultSheet(purchaseContractWbsExcelVOList, PurchaseContractExcelVO.class, null, "采购合同信息", true);
        } else {
            workbook = ExportExcelUtil.buildDefaultSheet(purchaseContractExcelVOList, PurchaseContractExcelVO.class, null, "采购合同信息", true);
        }
        // 获取配置
        String res = restTemplate.getForEntity(ModelsEnum.BASEDATA.getBaseUrl() + "/resource/user/queryCustomView", String.class).getBody();
        com.midea.pam.gateway.common.base.DataResponse<CustomViewConfigDTO> customViewResponse = JSON.parseObject(res, new TypeReference<com.midea.pam.gateway.common.base.DataResponse<CustomViewConfigDTO>>() {
        });
        CustomViewConfigDTO customViewConfigDTO = customViewResponse.getData();
        List<String> fieldList = new ArrayList<>();
        if (customViewConfigDTO != null) {
            String tableTemplate = isMe ? customViewConfigDTO.getMyPurchaseTableTemplate() : customViewConfigDTO.getPurchaseTableTemplate();
            // 自定义视图导出
            if (StringUtils.isNotEmpty(tableTemplate)) {
                JSONArray jsonArray = JSON.parseArray(tableTemplate);
                for (int i = 0; i < jsonArray.size(); i++) {
                    fieldList.add(jsonArray.getJSONObject(i).getString("label"));
                }
                //固定列
                List<String> fixedFieldList = Arrays.asList("供应商地点", "税率", "合同金额（不含税）", "罚扣", "剩余付款", "合同开始日期", "合同结束日期", "项目经理", "法务合同名称", "是否参与结转", "创建人", "创建日期");
                fieldList.addAll(fixedFieldList);
                com.midea.pam.gateway.common.utils.ExportExcelUtil.dynamicExportFirstSheet(workbook, fieldList);
            }
        }
        ExportExcelUtil.addSheet(workbook, purchaseContractDetailExcelVOList, PurchaseContractDetailExcelVO.class, null, "合同明细", true);
        ExportExcelUtil.addSheet(workbook, paymentPlanExportExcelVoList, PaymentPlanExportExcelVo.class, null, "付款计划", true);
        ExportExcelUtil.addSheet(workbook, purchaseContractProgressExcelVOList, PurchaseContractProgressExcelVO.class, null, "合同进度", true);
        ExportExcelUtil.downLoadExcel("采购合同_" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".xls", response, workbook);
    }

    @ApiOperation(value = "采购合同分页查询")
    @GetMapping("page")
    public Response page(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                         @RequestParam(required = false) @ApiParam("合同类型，多个逗号分隔") String typeNameStr,
                         @RequestParam(required = false) @ApiParam("合同名称") final String name,
                         @RequestParam(required = false) @ApiParam("供应商名称") final String vendorName,
                         @RequestParam(required = false) @ApiParam("供应商编码") final String vendorCode,
                         @RequestParam(required = false) @ApiParam("采购跟进人") final String purchasingFollowerName,
                         @RequestParam(required = false) @ApiParam("关联项目") final String projectName,
                         @RequestParam(required = false) @ApiParam("关联项目编号") final String projectCode,
                         @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                         @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                         @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                         @RequestParam(required = false) @ApiParam("业务实体(多选)") final String manyOuName,
                         @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                         @RequestParam(required = false) @ApiParam("法务合同编号") final String legalContractNum,
                         @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                         @RequestParam(required = false) @ApiParam("采购合同累计进度执行百分比最小值") final BigDecimal executeContractPercentTotalMin,
                         @RequestParam(required = false) @ApiParam("采购合同累计进度执行百分比最大值") final BigDecimal executeContractPercentTotalMax,
                         @RequestParam(required = false) @ApiParam("是否完成质检") final Integer qualityFlag,
                         @RequestParam(required = false) @ApiParam("法务合同状态(多选)") final String contractStatusStr,
                         @RequestParam(required = false) @ApiParam("创建开始日期") final String createStartDate,
                         @RequestParam(required = false) @ApiParam("创建结束日期") final String createEndDate,
                         @RequestParam(required = false) @ApiParam("承诺交期开始日期") final String deliveryStartDate,
                         @RequestParam(required = false) @ApiParam("承诺交期结束日期") final String deliveryEndDate,
                         @RequestParam(required = false) @ApiParam("追踪货期开始日期") final String trackLeadStartTime,
                         @RequestParam(required = false) @ApiParam("追踪货期结束日期") final String trackLeadEndTime,
                         @RequestParam(required = false) @ApiParam("货期跟进备注") final String trackLeadTimeRemark,
                         @RequestParam(required = false) @ApiParam("项目经理") final String managerName,
                         @RequestParam(required = false) @ApiParam("是否罚扣查询") final Integer punishmentFlag,
                         @RequestParam(required = false) @ApiParam("是否已双签") final Boolean isGleSign,
                         @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                         @RequestParam(required = false, defaultValue = "10") final Integer pageSize) throws UnsupportedEncodingException {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        if (StringUtils.isNotEmpty(typeNameStr)) {
            typeNameStr = URLDecoder.decode(typeNameStr, StandardCharsets.UTF_8.name());
            param.put("typeNameStr", URLEncoder.encode(typeNameStr, StandardCharsets.UTF_8.name()));
        }
        param.put("name", name);
        param.put("vendorName", vendorName);
        param.put("vendorCode", vendorCode);
        param.put("purchasingFollowerName", purchasingFollowerName);
        param.put("projectName", projectName);
        param.put("projectCode", projectCode);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", manyOuName);
        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("legalContractNum", legalContractNum);
        param.put("executeContractPercentTotalMin", executeContractPercentTotalMin);
        param.put("executeContractPercentTotalMax", executeContractPercentTotalMax);
        param.put("qualityFlag", qualityFlag);
        param.put("contractStatusStr", contractStatusStr);
        param.put("createStartDate", createStartDate);
        param.put("createEndDate", createEndDate);
        param.put("deliveryStartDate", deliveryStartDate);
        param.put("deliveryEndDate", deliveryEndDate);
        param.put("trackLeadStartTime", trackLeadStartTime);
        param.put("trackLeadEndTime", trackLeadEndTime);
        param.put("trackLeadTimeRemark", trackLeadTimeRemark);
        param.put("managerName", managerName);
        param.put("punishmentFlag", punishmentFlag);
        param.put("isGleSign", isGleSign);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "purchaseContract/page", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PurchaseContractVO>>>() {
        });

    }

    @ApiOperation(value = "我的采购合同导出")
    @GetMapping("excel/exportMyPurchaseContract")
    public void exportMyPurchaseContract(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                                         @RequestParam(required = false) @ApiParam("合同名称") final String name,
                                         @RequestParam(required = false) @ApiParam("供应商名称") final String vendorName,
                                         @RequestParam(required = false) @ApiParam("供应商编码") final String vendorCode,
                                         @RequestParam(required = false) @ApiParam("采购跟进人") final String purchasingFollowerName,
                                         @RequestParam(required = false) @ApiParam("关联项目") final String projectName,
                                         @RequestParam(required = false) @ApiParam("关联项目编号") final String projectCode,
                                         @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                                         @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                                         @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                                         @RequestParam(required = false) @ApiParam("业务实体(多选)") final String manyOuName,
                                         @RequestParam(required = false) @ApiParam("法务合同编号") final String legalContractNum,
                                         @RequestParam(required = false) @ApiParam("法务合同状态(多选)") final String contractStatusStr,
                                         @RequestParam(required = false) @ApiParam("创建开始日期") final String createStartDate,
                                         @RequestParam(required = false) @ApiParam("创建结束日期") final String createEndDate,
                                         @RequestParam(required = false) @ApiParam("承诺交期开始日期") final String deliveryStartDate,
                                         @RequestParam(required = false) @ApiParam("承诺交期结束日期") final String deliveryEndDate,
                                         @RequestParam(required = false) @ApiParam("追踪货期开始日期") final String trackLeadStartTime,
                                         @RequestParam(required = false) @ApiParam("追踪货期结束日期") final String trackLeadEndTime,
                                         @RequestParam(required = false) @ApiParam("货期跟进备注") final String trackLeadTimeRemark,
                                         @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                                         @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                                         @RequestParam(required = false, defaultValue = "false") @ApiParam("是否wbs类型") final Boolean wbsEnabled,
                                         @RequestParam(required = false) @ApiParam("是否已双签") final Boolean isGleSign,
                                         HttpServletResponse response) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("name", name);
        param.put("vendorName", vendorName);
        param.put("vendorCode", vendorCode);
        param.put("purchasingFollowerName", purchasingFollowerName);
        param.put("projectName", projectName);
        param.put("projectCode", projectCode);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", manyOuName);
        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("legalContractNum", legalContractNum);
        param.put("contractStatusStr", contractStatusStr);
        param.put("createStartDate", createStartDate);
        param.put("createEndDate", createEndDate);
        param.put("deliveryStartDate", deliveryStartDate);
        param.put("deliveryEndDate", deliveryEndDate);
        param.put("trackLeadStartTime", trackLeadStartTime);
        param.put("trackLeadEndTime", trackLeadEndTime);
        param.put("trackLeadTimeRemark", trackLeadTimeRemark);
        param.put("isGleSign", isGleSign);
        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "purchaseContract/exportMyPurchaseContract", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        final DataResponse<Map<String, Object>> dataResponse =
                JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<Map<String, Object>>>() {
                });
        // 导出excel
        exportExcel(dataResponse, wbsEnabled, true, response);
    }

    @ApiOperation(value = "我的采购合同分页查询")
    @GetMapping("findMyPurchaseContractPage")
    public Response findMyPurchaseContractPage(@RequestParam(required = false) @ApiParam("合同编号") final String code,
                                               @RequestParam(required = false) @ApiParam("合同名称") final String name,
                                               @RequestParam(required = false) @ApiParam("供应商名称") final String vendorName,
                                               @RequestParam(required = false) @ApiParam("供应商编码") final String vendorCode,
                                               @RequestParam(required = false) @ApiParam("采购跟进人") final String purchasingFollowerName,
                                               @RequestParam(required = false) @ApiParam("关联项目") final String projectName,
                                               @RequestParam(required = false) @ApiParam("关联项目编号") final String projectCode,
                                               @RequestParam(required = false) @ApiParam("归档开始时间") final String filingStartDate,
                                               @RequestParam(required = false) @ApiParam("归档结束时间") final String filingEndDate,
                                               @RequestParam(required = false) @ApiParam("合同状态(多选)") final String manyStatus,
                                               @RequestParam(required = false) @ApiParam("业务实体(多选)") final String manyOuName,
                                               @RequestParam(required = false) @ApiParam("排序") final String orderParam,
                                               @RequestParam(required = false) @ApiParam("法务合同编号") final String legalContractNum,
                                               @RequestParam(required = false) @ApiParam("法务合同状态(多选)") final String contractStatusStr,
                                               @RequestParam(required = false) @ApiParam("创建开始日期") final String createStartDate,
                                               @RequestParam(required = false) @ApiParam("创建结束日期") final String createEndDate,
                                               @RequestParam(required = false) @ApiParam("承诺交期开始日期") final String deliveryStartDate,
                                               @RequestParam(required = false) @ApiParam("承诺交期结束日期") final String deliveryEndDate,
                                               @RequestParam(required = false) @ApiParam("追踪货期开始日期") final String trackLeadStartTime,
                                               @RequestParam(required = false) @ApiParam("追踪货期结束日期") final String trackLeadEndTime,
                                               @RequestParam(required = false) @ApiParam("货期跟进备注") final String trackLeadTimeRemark,
                                               @RequestParam(required = false) @ApiParam("排序类型(1:升序；2：降序)") final Integer orderType,
                                               @RequestParam(required = false) @ApiParam("采购合同累计进度执行百分比最小值") final BigDecimal executeContractPercentTotalMin,
                                               @RequestParam(required = false) @ApiParam("采购合同累计进度执行百分比最大值") final BigDecimal executeContractPercentTotalMax,
                                               @RequestParam(required = false) @ApiParam("是否完成质检") final Integer qualityFlag,
                                               @RequestParam(required = false) @ApiParam("是否已双签") final Boolean isGleSign,
                                               @RequestParam(required = false, defaultValue = "1") final Integer pageNum,
                                               @RequestParam(required = false, defaultValue = "10") final Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("code", code);
        param.put("name", name);
        param.put("vendorName", vendorName);
        param.put("vendorCode", vendorCode);
        param.put("purchasingFollowerName", purchasingFollowerName);
        param.put("projectName", projectName);
        param.put("projectCode", projectCode);
        param.put("manyStatus", manyStatus);
        param.put("manyOuName", manyOuName);
        param.put("filingStartDate", filingStartDate);
        param.put("filingEndDate", filingEndDate);
        param.put("orderParam", orderParam);
        param.put("orderType", orderType);
        param.put("legalContractNum", legalContractNum);
        param.put("executeContractPercentTotalMin", executeContractPercentTotalMin);
        param.put("executeContractPercentTotalMax", executeContractPercentTotalMax);
        param.put("qualityFlag", qualityFlag);
        param.put("contractStatusStr", contractStatusStr);
        param.put("createStartDate", createStartDate);
        param.put("createEndDate", createEndDate);
        param.put("deliveryStartDate", deliveryStartDate);
        param.put("deliveryEndDate", deliveryEndDate);
        param.put("trackLeadStartTime", trackLeadStartTime);
        param.put("trackLeadEndTime", trackLeadEndTime);
        param.put("trackLeadTimeRemark", trackLeadTimeRemark);
        param.put("isGleSign", isGleSign);
        param.put(Constants.Page.PAGE_NUM, pageNum);
        param.put(Constants.Page.PAGE_SIZE, pageSize);

        final String url = buildGetUrl(ModelsEnum.STATISTICS.getBaseUrl(), "purchaseContract/findMyPurchaseContractPage", param);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
        return JSON.parseObject(responseEntity.getBody(), new TypeReference<DataResponse<PageInfo<PurchaseContractVO>>>() {
        });
    }

}

